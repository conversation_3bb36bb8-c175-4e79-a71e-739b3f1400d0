import englishTranslation from '../locales/en/translation.json';
import spanishTranslation from '../locales/es/translation.json';
import englishBusiness from '../locales/en/business.json';
import spanishBusiness from '../locales/es/business.json';
import englishAlerts from '../locales/en/alerts.json';
import spanishAlerts from '../locales/es/alerts.json';

describe('Translation Files Validation', () => {
  describe('Translation File Structure', () => {
    it('should have all translation files present', () => {
      expect(englishTranslation).toBeDefined();
      expect(spanishTranslation).toBeDefined();
      expect(englishBusiness).toBeDefined();
      expect(spanishBusiness).toBeDefined();
      expect(englishAlerts).toBeDefined();
      expect(spanishAlerts).toBeDefined();
    });

    it('should have valid JSON structure', () => {
      expect(typeof englishTranslation).toBe('object');
      expect(typeof spanishTranslation).toBe('object');
      expect(typeof englishBusiness).toBe('object');
      expect(typeof spanishBusiness).toBe('object');
      expect(typeof englishAlerts).toBe('object');
      expect(typeof spanishAlerts).toBe('object');
    });

    it('should not be empty objects', () => {
      expect(Object.keys(englishTranslation).length).toBeGreaterThan(0);
      expect(Object.keys(spanishTranslation).length).toBeGreaterThan(0);
      expect(Object.keys(englishBusiness).length).toBeGreaterThan(0);
      expect(Object.keys(spanishBusiness).length).toBeGreaterThan(0);
      expect(Object.keys(englishAlerts).length).toBeGreaterThan(0);
      expect(Object.keys(spanishAlerts).length).toBeGreaterThan(0);
    });
  });

  describe('Key Consistency', () => {
    // Helper function to get all nested keys
    const getAllKeys = (obj, prefix = '') => {
      let keys = [];
      for (const key in obj) {
        const fullKey = prefix ? `${prefix}.${key}` : key;
        if (typeof obj[key] === 'object' && obj[key] !== null) {
          keys = keys.concat(getAllKeys(obj[key], fullKey));
        } else {
          keys.push(fullKey);
        }
      }
      return keys;
    };

    it('should have matching keys between English and Spanish translation files', () => {
      const englishKeys = getAllKeys(englishTranslation).sort();
      const spanishKeys = getAllKeys(spanishTranslation).sort();
      
      expect(englishKeys).toEqual(spanishKeys);
    });

    it('should have matching keys between English and Spanish business files', () => {
      const englishKeys = getAllKeys(englishBusiness).sort();
      const spanishKeys = getAllKeys(spanishBusiness).sort();
      
      expect(englishKeys).toEqual(spanishKeys);
    });

    it('should have matching keys between English and Spanish alert files', () => {
      const englishKeys = getAllKeys(englishAlerts).sort();
      const spanishKeys = getAllKeys(spanishAlerts).sort();
      
      expect(englishKeys).toEqual(spanishKeys);
    });

    it('should not have duplicate keys within the same file', () => {
      const checkDuplicates = (obj, seen = new Set(), prefix = '') => {
        for (const key in obj) {
          const fullKey = prefix ? `${prefix}.${key}` : key;
          if (typeof obj[key] === 'object' && obj[key] !== null) {
            checkDuplicates(obj[key], seen, fullKey);
          } else {
            expect(seen.has(fullKey)).toBe(false);
            seen.add(fullKey);
          }
        }
      };

      checkDuplicates(englishTranslation);
      checkDuplicates(spanishTranslation);
      checkDuplicates(englishBusiness);
      checkDuplicates(spanishBusiness);
      checkDuplicates(englishAlerts);
      checkDuplicates(spanishAlerts);
    });
  });

  describe('Required Translation Keys', () => {
    const requiredKeys = {
      authentication: [
        'username',
        'password',
        'signIn',
        'forgotPassword',
        'invalidCredentials',
        'networkError',
        'loginError'
      ],
      navigation: [
        'dashboard',
        'moments',
        'people',
        'settings'
      ],
      dashboard: [
        'upNext',
        'arrivingSoon',
        'announcements',
        'outlook'
      ],
      settings: [
        'selectLanguage',
        'english',
        'spanish'
      ]
    };

    Object.entries(requiredKeys).forEach(([namespace, keys]) => {
      describe(`${namespace} namespace`, () => {
        keys.forEach(key => {
          it(`should have required key: ${namespace}.${key} in English`, () => {
            expect(englishTranslation[namespace]).toBeDefined();
            expect(englishTranslation[namespace][key]).toBeDefined();
            expect(typeof englishTranslation[namespace][key]).toBe('string');
            expect(englishTranslation[namespace][key].trim().length).toBeGreaterThan(0);
          });

          it(`should have required key: ${namespace}.${key} in Spanish`, () => {
            expect(spanishTranslation[namespace]).toBeDefined();
            expect(spanishTranslation[namespace][key]).toBeDefined();
            expect(typeof spanishTranslation[namespace][key]).toBe('string');
            expect(spanishTranslation[namespace][key].trim().length).toBeGreaterThan(0);
          });
        });
      });
    });
  });

  describe('Translation Value Validation', () => {
    const validateValues = (obj, language, filename) => {
      const getAllValues = (obj, prefix = '') => {
        let values = [];
        for (const key in obj) {
          const fullKey = prefix ? `${prefix}.${key}` : key;
          if (typeof obj[key] === 'object' && obj[key] !== null) {
            values = values.concat(getAllValues(obj[key], fullKey));
          } else {
            values.push({ key: fullKey, value: obj[key] });
          }
        }
        return values;
      };

      const values = getAllValues(obj);
      
      values.forEach(({ key, value }) => {
        it(`should have non-empty string value for ${key} in ${language} ${filename}`, () => {
          expect(typeof value).toBe('string');
          expect(value.trim().length).toBeGreaterThan(0);
        });

        it(`should not have placeholder text for ${key} in ${language} ${filename}`, () => {
          const placeholders = ['FIXME', 'PLACEHOLDER'];
          placeholders.forEach(placeholder => {
            expect(value.toUpperCase()).not.toContain(placeholder);
          });
          
                    // Special check for TODO - only fail if it's a standalone word (exclude valid Spanish words)
          if (value.toUpperCase().includes('TODO') && 
              value.toUpperCase() === 'TODO' && 
              !['foodAmounts.all', 'people.allGroups'].includes(key)) {
            throw new Error(`Should not contain placeholder TODO in ${key}`);
          }
          
          // Special check for MISSING - only fail if it's in context of incomplete work
          if (value.toUpperCase().includes('MISSING') && 
              /\b(REQUIRED.*MISSING|MISSING.*REQUIRED|FIELDS.*MISSING|MISSING.*FIELDS)\b/.test(value.toUpperCase())) {
            // This is acceptable as it's part of a valid error message
          } else if (value.toUpperCase().includes('MISSING') && value.toUpperCase() === 'MISSING') {
                         throw new Error(`Should not contain placeholder MISSING in ${key}`);
          }
        });
      });
    };

    describe('English translations', () => {
      validateValues(englishTranslation, 'English', 'translation.json');
      validateValues(englishBusiness, 'English', 'business.json');
      validateValues(englishAlerts, 'English', 'alerts.json');
    });

    describe('Spanish translations', () => {
      validateValues(spanishTranslation, 'Spanish', 'translation.json');
      validateValues(spanishBusiness, 'Spanish', 'business.json');
      validateValues(spanishAlerts, 'Spanish', 'alerts.json');
    });
  });

  describe('Interpolation Validation', () => {
    const findInterpolationKeys = (text) => {
      const matches = text.match(/\{\{([^}]+)\}\}/g);
      return matches ? matches.map(match => match.slice(2, -2).trim()) : [];
    };

    const validateInterpolationConsistency = (englishObj, spanishObj, namespace) => {
      const getAllKeyValuePairs = (obj, prefix = '') => {
        let pairs = [];
        for (const key in obj) {
          const fullKey = prefix ? `${prefix}.${key}` : key;
          if (typeof obj[key] === 'object' && obj[key] !== null) {
            pairs = pairs.concat(getAllKeyValuePairs(obj[key], fullKey));
          } else {
            pairs.push({ key: fullKey, value: obj[key] });
          }
        }
        return pairs;
      };

      const englishPairs = getAllKeyValuePairs(englishObj);
      const spanishPairs = getAllKeyValuePairs(spanishObj);

      englishPairs.forEach(({ key, value: englishValue }) => {
        const spanishPair = spanishPairs.find(pair => pair.key === key);
        
        if (spanishPair) {
          it(`should have consistent interpolation keys for ${key} in ${namespace}`, () => {
            const englishInterpolations = findInterpolationKeys(englishValue);
            const spanishInterpolations = findInterpolationKeys(spanishPair.value);
            
            expect(englishInterpolations.sort()).toEqual(spanishInterpolations.sort());
          });
        }
      });
    };

    describe('translation.json interpolation consistency', () => {
      validateInterpolationConsistency(englishTranslation, spanishTranslation, 'translation');
    });

    describe('business.json interpolation consistency', () => {
      validateInterpolationConsistency(englishBusiness, spanishBusiness, 'business');
    });

    describe('alerts.json interpolation consistency', () => {
      validateInterpolationConsistency(englishAlerts, spanishAlerts, 'alerts');
    });
  });

  describe('Character Encoding and Special Characters', () => {
    const validateEncoding = (obj, language, filename) => {
      const getAllValues = (obj) => {
        let values = [];
        for (const key in obj) {
          if (typeof obj[key] === 'object' && obj[key] !== null) {
            values = values.concat(getAllValues(obj[key]));
          } else {
            values.push(obj[key]);
          }
        }
        return values;
      };

      const values = getAllValues(obj);

      it(`should have proper UTF-8 encoding in ${language} ${filename}`, () => {
        values.forEach(value => {
          // Check that the string is properly encoded
          expect(() => JSON.stringify(value)).not.toThrow();
        });
      });

      if (language === 'Spanish') {
        it(`should use proper Spanish characters in ${filename}`, () => {
          const spanishText = values.join(' ');
          // Check for common Spanish characters
          const hasSpanishChars = /[áéíóúüñ¿¡]/i.test(spanishText);
          
          // If there's substantial Spanish text, it should contain Spanish characters
          if (spanishText.length > 50) {
            expect(hasSpanishChars).toBe(true);
          }
        });
      }
    };

    validateEncoding(englishTranslation, 'English', 'translation.json');
    validateEncoding(spanishTranslation, 'Spanish', 'translation.json');
    validateEncoding(englishBusiness, 'English', 'business.json');
    validateEncoding(spanishBusiness, 'Spanish', 'business.json');
    validateEncoding(englishAlerts, 'English', 'alerts.json');
    validateEncoding(spanishAlerts, 'Spanish', 'alerts.json');
  });

  describe('Translation Quality', () => {
    it('should not have identical English and Spanish translations', () => {
      const compareObjects = (englishObj, spanishObj, path = '') => {
        for (const key in englishObj) {
          const currentPath = path ? `${path}.${key}` : key;
          
          if (typeof englishObj[key] === 'object' && englishObj[key] !== null) {
            if (spanishObj[key]) {
              compareObjects(englishObj[key], spanishObj[key], currentPath);
            }
          } else {
            if (spanishObj[key] && englishObj[key] === spanishObj[key]) {
              // Allow some exceptions for technical terms, proper nouns, or words that are the same in both languages
              const exceptions = ['ID', 'URL', 'API', 'JSON', 'HTTP', 'HTTPS', 'No', 'Error', 'cereal', 'Tortal', 'Intranet'];
              const isException = exceptions.some(exception => 
                englishObj[key].toUpperCase().includes(exception.toUpperCase())
              );
              
              if (!isException) {
                throw new Error(`Translation should not be identical for key: ${currentPath}`);
              }
            }
          }
        }
      };

      compareObjects(englishTranslation, spanishTranslation);
      compareObjects(englishBusiness, spanishBusiness);
      compareObjects(englishAlerts, spanishAlerts);
    });

    it('should have reasonable translation length differences', () => {
      const compareObjectLengths = (englishObj, spanishObj, path = '') => {
        for (const key in englishObj) {
          const currentPath = path ? `${path}.${key}` : key;
          
          if (typeof englishObj[key] === 'object' && englishObj[key] !== null) {
            if (spanishObj[key]) {
              compareObjectLengths(englishObj[key], spanishObj[key], currentPath);
            }
          } else {
            if (spanishObj[key]) {
              const englishLength = englishObj[key].length;
              const spanishLength = spanishObj[key].length;
              
              // Spanish text is typically 20-30% longer than English
              // Allow for reasonable variation (0.5x to 3x the original length)
              const ratio = spanishLength / englishLength;
              
              expect(ratio).toBeGreaterThan(0.5);
              expect(ratio).toBeLessThan(3);
            }
          }
        }
      };

      compareObjectLengths(englishTranslation, spanishTranslation);
      compareObjectLengths(englishBusiness, spanishBusiness);
      compareObjectLengths(englishAlerts, spanishAlerts);
    });
  });

  describe('File Size and Performance', () => {
    it('should have reasonable file sizes', () => {
      const maxReasonableSize = 50000; // 50KB in characters
      
      expect(JSON.stringify(englishTranslation).length).toBeLessThan(maxReasonableSize);
      expect(JSON.stringify(spanishTranslation).length).toBeLessThan(maxReasonableSize);
      expect(JSON.stringify(englishBusiness).length).toBeLessThan(maxReasonableSize);
      expect(JSON.stringify(spanishBusiness).length).toBeLessThan(maxReasonableSize);
      expect(JSON.stringify(englishAlerts).length).toBeLessThan(maxReasonableSize);
      expect(JSON.stringify(spanishAlerts).length).toBeLessThan(maxReasonableSize);
    });

    it('should have efficient nesting levels', () => {
      const getMaxDepth = (obj, currentDepth = 0) => {
        let maxDepth = currentDepth;
        for (const key in obj) {
          if (typeof obj[key] === 'object' && obj[key] !== null) {
            const depth = getMaxDepth(obj[key], currentDepth + 1);
            maxDepth = Math.max(maxDepth, depth);
          }
        }
        return maxDepth;
      };

      // Reasonable nesting limit for translation files
      const maxDepth = 4;
      
      expect(getMaxDepth(englishTranslation)).toBeLessThanOrEqual(maxDepth);
      expect(getMaxDepth(spanishTranslation)).toBeLessThanOrEqual(maxDepth);
      expect(getMaxDepth(englishBusiness)).toBeLessThanOrEqual(maxDepth);
      expect(getMaxDepth(spanishBusiness)).toBeLessThanOrEqual(maxDepth);
      expect(getMaxDepth(englishAlerts)).toBeLessThanOrEqual(maxDepth);
      expect(getMaxDepth(spanishAlerts)).toBeLessThanOrEqual(maxDepth);
    });
  });
}); 