import Meteor, { withTracker } from 'react-native-meteor';
import _ from '../shared/ExtendedUnderscore';
import i18n from '../localization/i18n';

var numeral = require('numeral');

function mapCurriculumTypes(types, prefix, expanded) {
	let output = [];
	const curPrefix = prefix ?  prefix + "`" : "";
	_.each(types, (t) => {
		if (typeof t === 'object' && t !== null) {
			if (t.children) {
				if (expanded) output.push( curPrefix + t.label);
				output = output.concat( mapCurriculumTypes(t.children, curPrefix + t.label, expanded) );
			} else {
				output.push( curPrefix + t.label);
			}
		} else {
			output.push( curPrefix + t);
		}
	});
	return output.sort();
}

const OrgMethods = {
	customizationExists: function(customizationName) {
		if (this.customizations && this.customizations[customizationName] != undefined)	return true;
		return false;
	},
	hasCustomization: function(customizationName) {
		if (customizationName == "moments/potty/enabled" && (!this.customizations || this.customizations["moments/potty/enabled"] == undefined)) return true;
		if (customizationName == "moments/sleep/enabled" && (!this.customizations || this.customizations["moments/sleep/enabled"] == undefined)) return true;
		if (customizationName == "moments/food/enabled" && (!this.customizations ||  this.customizations["moments/food/enabled"] == undefined)) return true;
		if (customizationName == "moments/alert/enabled" && (!this.customizations ||  this.customizations["moments/alert/enabled"] == undefined)) return true;

		if (_.contains(["moments/sleep/showEndSleepButton"], customizationName)) return true;

		if (customizationName == "moments/learning/enabled" && this.customizations &&
				( this.customizations["moments/learning/enabled"] ||
				 (this.customizations["moments/learning/enabled"] == undefined && (this.customizations["modules/curriculum/hidden"] == undefined || !this.customizations["modules/curriculum/hidden"]))
				)
			)
				return true;


		if (this.customizations && this.customizations[customizationName])
			return this.customizations[customizationName];
		else
			return false;
	},
	findMetric: function(metricName) {
		if (!this.metrics) return 0;
		switch (metricName) {
			case "messagesSent":
				return (this.metrics.messagesSent) || 0;
			case "averageMoments":
				var staffCount = People.find({orgId: this._id, inActive:{$ne:true},
						$or: [{"type": "staff"}, {"type": "admin"}]
					}).count();
				return ((this.metrics.momentsPosted || 0) / staffCount).toFixed(1);
		}
	},
	updateMetric: function(metricName, amt) {
		var foo = {};
		foo["metrics." + metricName] = amt;
		Orgs.update(this._id, {$inc: foo});

	},
	planInfo: function() {
		if (this.planDetails) {
			return this.planDetails;
		} else
			return {
				plan: "trial",
				title: "Trial",
				description: "Try all of LineLeader's features before deciding to purchase"
			};
	},
	getTimezone: function() {
		return (this.timezone || "America/New_York");
	},
	'availableCurriculumTypes': function(options) {
		//return ['','Creative Arts', 'Language & Literacy', 'Math & Numbers', 'Science & Sensory'];
		const types = (this.valueOverrides && this.valueOverrides.curriculumTypes)
			? this.valueOverrides.curriculumTypes
		    :
			[
				'Assessment of Child Progress',
				'Community Relationships',
				'Curriculum',
				'Families',
				'Health',
				'Leadership and Management',
				'Physical Environment',
				'Relationship',
				'Teachers',
				'Teaching',
			];
		return options && options.mapped ? mapCurriculumTypes(types, null, options.expanded) : types;
	},
	'availableCheckInHealthCheckTypes': function() {
		return ['Eyes', 'Ears', 'Nose', 'Throat', 'Chest', 'Head', 'Leg', 'Asthma', 'Skin', 'Behavior Change'];
	},
	'availableNavItems': function() {
		var navItems = [
			{name: "Dashboard", location: "/", icon:"tachometer", route: "dashboard"},
			{name: "People", location: "/people", icon:"user", route:"people"},
			{name: "Calendar", location: "/calendar", icon:"calendar", route:"calendar"},
			{name: "Groups", location: "/groups", icon:"users", route:"groups"},
			{name: "Announcements", location: "/announcements", icon:"bullhorn", route:"announcements"},
			{name: "Curriculum", location: "/curriculum", icon:"graduation-cap", route:"curriculum"},
			{name: "Food", location:"/food", icon:"cutlery", route:"food"},
			{name: "Scheduling", location:"/scheduling", icon: "calendar-check-o", route: "scheduling"},
			{name: "Inquiries", location: "/inquiries", icon: "address-book-o", route: "inquiries"},
			{name: "Forms", location: "/forms", icon: "check-square-o", route: "forms"},
			{name: "Reports", location: "/reports", icon: "bar-chart", route: "reports"},
			{name: "Admin/Account", location: "/admin/org", icon: "building", route: "org"}
		];
		return navItems;
	},
	'availableMomentTypes': function() {
		var availableTypes = [];
		availableTypes.push({momentType:"comment", prettyName: this.translate("momentTypes.comment", "Comment"), icon: "fa-comment"});
		if (this.hasCustomization("moments/potty/enabled"))
			availableTypes.push({momentType:"potty", prettyName: this.translate("momentTypes.potty", "Potty"), icon: "fa-child"});
		if (this.hasCustomization("moments/food/enabled"))
			availableTypes.push({momentType:"food", prettyName: this.translate("momentTypes.food", "Food"), icon:"fa-cutlery"});
		if (this.hasCustomization("moments/sleep/enabled"))
			availableTypes.push({momentType:"sleep", prettyName: this.translate("momentTypes.sleep", "Sleep"), icon: "fa-bed"});
		if (this.hasCustomization("moments/activity/enabled"))
			availableTypes.push({momentType:"activity", prettyName: this.translate("momentTypes.activity", "Activity"), icon: "fa-bicycle"});
		if (this.hasCustomization("moments/medical/enabled"))
			availableTypes.push({momentType:"medical", prettyName: this.translate("momentTypes.medical", "Medical"), icon: "fa-medkit"});
		if (this.hasCustomization("moments/mood/enabled"))
			availableTypes.push({momentType:"mood", prettyName: this.translate("momentTypes.mood", "Mood"), icon: "fa-smile-o"});
		if (this.hasCustomization("moments/incident/enabled"))
			availableTypes.push({momentType:"incident", prettyName: this.translate("momentTypes.incident", "Incident"), icon: 'fa-exclamation-circle'});
		if (this.hasCustomization("moments/alert/enabled"))
			availableTypes.push({momentType:"alert", prettyName: this.translate("momentTypes.alert", "Alert"), icon: "fa-bell"});
		if (this.hasCustomization("moments/supplies/enabled"))
			availableTypes.push({momentType:"supplies", prettyName: this.translate("momentTypes.supplies", "Supplies"), icon: "fa-shopping-bag"});
		if (this.hasCustomization("moments/learning/enabled"))
			availableTypes.push({momentType:"learning", prettyName: this.translate("momentTypes.learning", "Learning"), icon: "fa-leanpub"});
		if (this.hasCustomization("moments/illness/enabled"))
			availableTypes.push({momentType:"illness", prettyName: this.translate("momentTypes.illness", "Illness"), icon: "fa-thermometer-empty"});
		if (this.hasCustomization("moments/ouch/enabled"))
			availableTypes.push({momentType:"ouch", prettyName: this.translate("momentTypes.ouch", "Ouch"), icon: "fa-hand-stop-o"});
		const orgMomentTypes = this.enabledMomentTypes;
 		if (orgMomentTypes && Array.isArray(orgMomentTypes) && orgMomentTypes.length > 0) {
			var mds= Meteor.collection('momentDefinitions').find({momentType:{$in: orgMomentTypes}});
			_.each(mds, (md) => {
				let pushType = true;
				if (this.customizationExists(`moments/${md.momentType}/enabled`)) pushType = this.hasCustomization(`moments/${md.momentType}/enabled`);
				if (pushType) availableTypes.push({momentType: md.momentType, prettyName: md.momentTypePretty, icon: md.icon, adminOnly: this.hasCustomization(`moments/${md.momentType}/adminOnly`)});
			});
		}
		return _.sortBy(availableTypes, "prettyName");
	},
	'getLongName': function() {
		return this.longName || this.name;
	},
	'availableCustomPayTypes': function() {
		const types = (this.customStaffPaySettings && this.customStaffPaySettings.types) ? this.customStaffPaySettings.types : [];
		const customTypes = _.reject(types, (t) => t.archived == true);
		return [{ type: "Standard", _id: "standard" }].concat(customTypes);
	},
	'availableDynamicMomentTypes': function() {
		var availableTypes = [];
		var orgMomentTypes = this.enabledMomentTypes || [];
		// if (this.hasCustomization("moments/covidHealth/enabled")) {
		// 	orgMomentTypes.push("covidHealth");
		// }

		if (orgMomentTypes && Array.isArray(orgMomentTypes) && orgMomentTypes.length > 0) {
			var mds = Meteor.collection('momentDefinitions').find({momentType:{$in: orgMomentTypes}});;
			_.each(mds, (md) => {
				let pushType = true;
				if (this.customizationExists(`moments/${md.momentType}/enabled`)) pushType = this.hasCustomization(`moments/${md.momentType}/enabled`);
				if (pushType) availableTypes.push(md);
			});
		}
		return availableTypes;
	},
	'availableDiscountTypes': function() {
		if (this.valueOverrides && this.valueOverrides.discountTypes)
			return this.valueOverrides.discountTypes;
		else
			return [
				{type:"customerSpecific", description:"Customer-specific"},
				{type:"multipleFamily", description:"Multiple family "},
				{type:"scholarship", description: "Scholarship"},
				{type:"other", description:"Other"}
		];
	},
	'availablePayerSources': function() {
		if (this.valueOverrides && this.valueOverrides.payerSources)
			return this.valueOverrides.payerSources;
		else
			return [
				{type: "ccdf", description: "CCDF"},
				{type: "onmywayprek", description: "On My Way Pre-K"},
				{type: "unitedway", description: "United Way"}
			];
	},
	'availableCreditMemoTypes': function() {
		let creditMemoTypes = [];
		if (this.valueOverrides && this.valueOverrides.creditMemoTypes)
			creditMemoTypes = creditMemoTypes.concat(this.valueOverrides.creditMemoTypes);
		else
			creditMemoTypes = creditMemoTypes.concat([
				{type: "manualCard", description: "Manual Credit Card"},
				{type: "check", description: "Check"},
				{type: "cash", description: "Cash"},
				{type: "refund", description: "Refund"},
			]);
		creditMemoTypes = creditMemoTypes.concat(_.map(this.availablePayerSources(), (ps) => {
			return {type: "prepaid_" + ps.type, description: "Prepaid " + ps.description};
		}));
		creditMemoTypes.push({type: "systemOverpayment", description: "Overpayment"});
		creditMemoTypes.push({type: "other", description: "Other"});
		return creditMemoTypes;
	},
	'nextCounterValue': function(label) {
		var currentOrg = this;
		if (!Meteor.isServer) return;

		var incQuery = {};
		incQuery["counters." + label] = 1;
		const doAutoincrement = function(callback) {
			Orgs.rawCollection().findAndModify({
		    		_id: currentOrg._id
				}, [], {
		    	$inc: incQuery}, {
		    		'new': true
		  		}, callback);
		};
		const nextCounterValue = Meteor.wrapAsync(doAutoincrement);
		var modifiedOrg = nextCounterValue();
		return modifiedOrg.value.counters[label];
	},
	'billingStatus': function() {
		let status = "inactive", message = "Inactive";
		if (this.billing && this.billing.enabled) {
			if (this.billing.stripeInfo && !this.billing.stripeInfo.charges_enabled) {
				status = "charges_disabled"; message = "Charges Disabled";
			} else if (this.billing.stripeInfo && this.billing.stripeInfo.external_accounts.data.length > 0) {
				const active_account = _.find(this.billing.stripeInfo.external_accounts.data, (a) => { return a.default_for_currency;});
				if (active_account && _.contains(["new", "validated", "verified"], active_account.status )) {
					status = "active"; message = "Active";
				} else {
					status = "bank_issue"; message = "Bank Account Issue";
				}
			} else if (this.billing.stripeInfo) {
				status = "missing_account"; message = "Needs Bank Account";
			} else {
				status = "invoice_only"; message = "Invoice Only";
			}
		} else if (this.billing && !this.billing.enabled) {
			status = "client_disabled"; message = "Disabled";
		}
		return { status: status, message: message };
	},
	'hasActiveTrial': function() {
		return this.registrationSource == 'app' && !this.planDetails;
	},
	'trialDaysRemaining': function() {
		if (this.registrationSource == 'app' && !this.planDetails) {
			let trialLength = this.trialLength || 14;
			const elapsedDays = new moment().diff(new moment(this.createdAt), "days");
			let remainingDays = elapsedDays <= trialLength ? trialLength - elapsedDays : 0;
			return remainingDays;
		}
	},
	'pinCodeCheckinFields': function() {
		const overrideFields = _.deep(this, "valueOverrides.pinCodeCheckinFields");
		return overrideFields || [ { fieldType: "text", label:"Notes for Staff", dataId: "notes"},
			{ 	fieldType: "buttons", label:"Mood", dataId:"mood",
				fieldValues: [
					{fieldValue:"Happy", fieldValueIcon: "fa-smile-o"},
					{fieldValue:"SoSo", fieldValueIcon: "fa-meh-o"},
					{fieldValue:"Sad", fieldValueIcon: "fa-frown-o"}
				]
			}
		];
	},
	'availableImmunizations': function() {
		const defaultTypes = [
			{type: "HepB", description: "Hepatitis B", monthsRequired:[0,2,18], exempt: false},
			{type: "RV", description: "Rotavirus", monthsRequired:[2,4,6], exempt: false},
			{type: "DTaP", description: "Diptheria, tetanus, & acellular pertussis", monthsRequired:[2,4,6,18,72], exempt: false},
			{type: "Hib", description: "Haemophilus influenzae type b", monthsRequired:[2,4,6,15], exempt: false},
			{type: "PCV13", description: "Pneumococcal conjugate", monthsRequired:[2,4,6,15], exempt: false},
			{type: "IPV", description: "Inactivated poliovirus", monthsRequired:[2,4,18,72], exempt: false},
			{type: "IIV", description: "Influenza", monthsRequired: [6,18,30,42,54,66,78,90,102], exempt: false},
			{type: "MMR", description: "Measles, mumps, rubella", monthsRequired:[15,72], exempt: false},
			{type: "VAR", description: "Varicella", monthsRequired:[15,72], exempt: false},
			{type: "HepA", description: "Hepatitis A", monthsRequired:[23, 23], exempt: false},
			{type: "MenACWY", description: "Meningococcal", exempt: false},
			{type: "PPSV23", description: "Pneumococcal polysaccharide", exempt: false},
			{type: "UCHR", description: "UCHR", exempt: false},
			{type: "H1N1", description: "H1N1", exempt: false}
		];

		const defaultOverride = [];
		_.each(defaultTypes, (dt) => {
			const override = _.find(this.immunizationOverrides || [], (i) => {
				if (i.type == dt.type) return true;
			})
			if (override) return defaultOverride.push(Object.assign({}, dt, override));
			defaultOverride.push(dt);
		});

		const customTypes = _.reject(this.immunizationDefinitions || [], (i) => i.archived == true);
		return defaultOverride.concat(customTypes);
	},
	'queryData': function(queryName, queryData) {
		switch( queryName ) {
			case "staffList":
				return _.map(People.find({orgId:this._id, type:{"$in": ["staff", "admin"]},  inActive:{$ne:true}}),
								(p) => { return {value: p.lastName + ", " + p.firstName, id: p._id}});
			case "groupList":
				return _.map(Groups.find({orgId:this._id}),
								(g) => { return {value: g.name, id: g._id}});
			case "primaryFamily":
				const person = queryData && queryData.personId && People.findOne(queryData.personId), relatedPeople = person && person.findOwnedRelationships();
				return _.map(relatedPeople,
								(r) => { const p = People.findOne(r.personId); if (p) return {value: p.firstName + ' ' + p.lastName, id: p._id}});
		}
	},
	'foodUnits': function(foodType) {
		switch(foodType) {
			case 'babyFood':
				return (this.valueOverrides && this.valueOverrides["foodUnits"] && this.valueOverrides["foodUnits"]["babyFood"]) || "cups";
			case 'cereal':
				return (this.valueOverrides && this.valueOverrides["foodUnits"] && this.valueOverrides["foodUnits"]["cereal"]) || "tsp";
		}
	},
	'profileDataPrefix': function() {
		return (this.hasCustomization("people/types/advancedProfileFields") ||
			(this.hasCustomization("people/types/customerSpecificProfileFields") && this.createdAt && this.createdAt >=  1571702400000) )
			&& "profileData";
	},
	'learningAssessmentLevels': function() {
		let levels = [];
		const customAssessmentLevels = this && _.deep(this, "valueOverrides.assessmentLevels");
		if (customAssessmentLevels)
			levels = customAssessmentLevels;
		else
			for (var i=0; i<=5; i++) { levels.push({value: i, label: i}); }
		return levels;
	},
	"getAvailableAssessmentLevels": function() {
		return this.availableAssessmentLevels ||
			[
				{
					"label" : "Not Measured",
					"value" : 0
				},
				{
					"label" : "Introduced",
					"value" : 1
				},
				{
					"label" : "Meets",
					"value" : 2
				},
				{
					"label" : "Exceeds",
					"value" : 3
				}
			];
	},
	"isChildCare": function() {
		return _.contains(["translationsEnChildCare", "translationsEnLightbridge", "translationsEnMontessori"], this.language );
	},
	"translate": function(itemKey, defaultValue) {
		// First try the legacy system for backward compatibility
		const legacyKey = "translations." + itemKey;
		const legacyValue = _.deep(this.valueOverrides, legacyKey);
		if (legacyValue) return legacyValue;

		// Then try the new i18next system
		// Determine namespace from key structure
		let namespace = 'business'; // Default for momentTypes
		let translationKey = itemKey;

		// Handle different key patterns
		if (itemKey.startsWith('momentTypes.')) {
			namespace = 'business';
			translationKey = itemKey; // Keep as is: momentTypes.food
		} else if (itemKey.includes('.')) {
			// For other namespaced keys, try to detect namespace
			const parts = itemKey.split('.');
			if (parts[0] === 'authentication' || parts[0] === 'navigation' || parts[0] === 'forms') {
				namespace = 'translation';
			} else if (parts[0] === 'alerts' || parts[0] === 'errors') {
				namespace = 'alerts';
			}
		}

		// Try i18next translation
		try {
			const i18nValue = i18n.t(`${namespace}:${translationKey}`, { defaultValue: null });
			if (i18nValue && i18nValue !== translationKey) {
				return i18nValue;
			}
		} catch (error) {
			console.warn('Translation error for key:', itemKey, error);
		}

		// Fallback to default value
		return defaultValue;
	},
	'billingCardProviderName': function() {
		if (this.billing && this.billing.enabled) {
			if (this.billing.adyenInfo)
				return "adyen";
			if (this.billing.stripeInfo)
				return "stripe";
		}
	},
	"subscriptionTypes": function() {
		const currentOrg = this,
			typesList = [{label: this.translate("momentTypes.comment", "Comment"), type:"comment"},
				{label: this.translate("momentTypes.food", "Food"), type: "food"},
				{label: this.translate("momentTypes.sleep", "Sleep"), type: "sleep"},
				{label: this.translate("momentTypes.potty", "Potty"), type: "potty"}];
		if (!currentOrg ) return;
		if (currentOrg.hasCustomization("moments/activity/enabled")) typesList.push({label: this.translate("momentTypes.activity", "Activity"), type:"activity"});
		if (currentOrg.hasCustomization("moments/medical/enabled")) typesList.push({label: this.translate("momentTypes.medical", "Medical"), type:"medical"});
		if (currentOrg.hasCustomization("moments/learning/enabled")) typesList.push({label: this.translate("momentTypes.learning", "Learning"), type:"learning"});
		if (currentOrg.hasCustomization("moments/mood/enabled")) typesList.push({label: this.translate("momentTypes.mood", "Mood"), type:"mood"});

		_.chain( currentOrg.availableDynamicMomentTypes() )
		 .filter( cmd => cmd.availableForRealtime)
		 .each( cmd => typesList.push({label: cmd.momentTypePretty, type: cmd.momentType}));


		return typesList;
	},
	cardFeeNotice(amount) {
		const fees = this.billing && this.billing.paymentFees;
		const platformFee = this.hasCustomization('billing/platformFees');
		const showFeeNotice =
      this.billing &&
      this.billing.passthroughFees &&
      (!this.billing.passthroughFeesAccountTypes ||
        _.contains(this.billing.passthroughFeesAccountTypes, 'card'));
		if (showFeeNotice) {
			if (fees && fees.cardRate) {
					if (platformFee && amount !== null) {
							const totalFee = (fees.cardRate * amount) + fees.cardFee;
							return `$${totalFee.toFixed(2)}`;
					} else {
							return (fees.cardRate * 100.0).toFixed(2) + "%" + " + $" + fees.cardFee.toFixed(2);
					}
			} else {
					if (platformFee && amount !== null) {
							const defaultRate = 0.029; // 2.9%
							const defaultFee = 0.30;   // $0.30
							const totalFee = (defaultRate * amount) + defaultFee;
							return `$${totalFee.toFixed(2)}`;
					} else {
							return "2.9% + $0.30";
					}
			}
    }
	},
	achFeeNotice() {
		const fees = this.billing && this.billing.paymentFees;
		if (fees && fees.achFee) {
			return `$${fees.achFee.toFixed(2)}`;
		} else {
			return this.billingCardProviderName() == "stripe" ? ".8% ($5 maximum)" : "$0.40";
		}
	},
	getAlternateServiceChargeFeeDescription() {
    return this.valueOverrides.alternateServiceChargeFeeDescription;
  }
};

Orgs = new Meteor.Collection('orgs', {
	transform: function(doc) {
		return _.extend(doc, OrgMethods);
	}
});

_.extend(Orgs, {
	'current': function() {
		const meteorUser = Meteor.user();

		if (meteorUser)
			return Orgs.findOne(meteorUser.orgId);
	}
});

export default Orgs;
