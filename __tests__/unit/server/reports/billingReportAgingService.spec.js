import { Meteor } from 'meteor/meteor';
import { afterAll, beforeAll, beforeEach, describe, expect, it } from '@jest/globals';
import { mockCollection } from '../../../helpers/collectionMock';
import { BillingReportAgingService } from '../../../../server/reports/billingReportAgingService';
import { OrgCollectionMethods } from '../../../fixtures/methods/orgCollectionMethods';
import moment from 'moment-timezone';
import { cloneDeep } from 'lodash';
import _ from '../../../../lib/util/underscore.js';
const orgFixture = require('../../../fixtures/321-Mariposa-Local.json');
const personFixture = require('../../../fixtures/reports/billingReportAgingService/currentPerson.json');
const currentOrg = require('../../../fixtures/reports/billingReportAgingService/currentOrg.json');
const allOrgs = require('../../../fixtures/reports/billingReportAgingService/allOrgs.json');
const singleOrgInvoices = require('../../../fixtures/reports/billingReportAgingService/singleOrgInvoices.json');
const singleOrgPayerAggregate = require('../../../fixtures/reports/billingReportAgingService/singleOrgPayerAggregate.json');
const multiOrgPayerAggregate = require('../../../fixtures/reports/billingReportAgingService/multiOrgPayerAggregate.json');
const singleOrgInvoicesPeople = require('../../../fixtures/reports/billingReportAgingService/singleOrgInvoicesPeople.json');
const multiOrgPayerAggregatePeople = require('../../../fixtures/reports/billingReportAgingService/multiOrgPayerAggregatePeople.json');
const singleOrgOpenCreditPeople = require('../../../fixtures/reports/billingReportAgingService/singleOrgOpenCreditPeople.json');
const groupedAndCalcuatedInvoices = require('../../../fixtures/reports/billingReportAgingService/groupedAndCalculatedInvoices.js');
const openCredits = require('../../../fixtures/reports/billingReportAgingService/openCredits.js');
const singleOrgOpenCreditPaidByPeople = require('../../../fixtures/reports/billingReportAgingService/singleOrgOpenCreditPaidByPeople.json');
const bugs2666Invoice = require('../../../fixtures/reports/billingReportAgingService/bugs2666Inovoice.json');
import { Orgs } from "../../../../lib/collections/orgs";
import { People } from "../../../../lib/collections/people";
import { Relationships } from "../../../../lib/collections/relationships";
import { Invoice, Invoices } from "../../../../lib/collections/invoices";

orgFixture.getTimezone = OrgCollectionMethods.getTimezone.bind(orgFixture);
const todayStampMock = new moment.tz(orgFixture.getTimezone()).endOf('day').valueOf();
const yesterdayStampMock = new moment.tz(orgFixture.getTimezone()).subtract(1, 'days').endOf('day').valueOf();
describe('BillingReportAgingService', () => {

    describe('buildOrgAndTimeData', () => {
        // Restore the original implementation after each test
        afterEach(async () => {
            jest.clearAllMocks();
        });

        it ('throws an error if arguments are not provided', async () => {
            BillingReportAgingService.clearCache();
            // mock the find().fetch() with cursor not 
            const orgCursorMock = Orgs.find;
            // for the scoped orgs call for single org
            orgCursorMock.mockImplementationOnce(() => {
                return ({
                    fetchAsync:jest.fn().mockImplementation(()=>currentOrg)
                })
            });
            // for the all orgs call as well as the calls for multiple ids (calls 3 and 4)
            orgCursorMock.mockImplementation(() => {
                return ({
                    fetchAsync:jest.fn().mockImplementation(()=>allOrgs)
                })
            });

            const options = {
                orgIds: null,
                currentPerson: null,
                org: null,
                filterDate: null,
            }

            await expect( BillingReportAgingService.buildOrgAndTimeData(options) ).rejects.toThrowError(new Meteor.Error("invalid-params", "Invalid parameters passed to buildOrgAndTimeData: ", "org is required"));
            options.org = orgFixture;
            await expect( BillingReportAgingService.buildOrgAndTimeData(options) ).rejects.toThrowError(new Meteor.Error("invalid-params", "Invalid parameters passed to buildOrgAndTimeData: ", "filterDate is required"));
            options.filterDate = "09/27/2023"
            // orgIds is optional so as long as it is falsy no further errors are thrown
            await expect( BillingReportAgingService.buildOrgAndTimeData(options) ).resolves.not.toThrowError();
            // if orgIds are defined but no currentPerson is defined error is thrown
            options.orgIds = ['nTvbx24M2dbM9w6tu','AsfEDAaFTAeCCJMLy'];
            await expect( BillingReportAgingService.buildOrgAndTimeData(options) ).rejects.toThrowError(new Meteor.Error("invalid-params", "Invalid parameters passed to buildOrgAndTimeData: ", "currentPerson is required"));

            options.currentPerson = personFixture;
            // mock findScopedOrgs call on person collection to return allOrgs
            options.currentPerson.findScopedOrgs = jest.fn(() => allOrgs);
            await expect(BillingReportAgingService.buildOrgAndTimeData(options) ).resolves.not.toThrowError();
            expect(orgCursorMock).toHaveBeenCalledTimes(3);
        });

        it('Builds organization and time data based on the provided options.', async () => {
            BillingReportAgingService.clearCache();
            // mock the find().fetch() with cursor not find.
            const orgCursorMock = Orgs.find;
            // for the scoped orgs call for single org
            orgCursorMock.mockImplementationOnce(() => {
                return ({
                    fetchAsync:jest.fn().mockImplementation(()=>currentOrg)
                })
            });
            //orgCursorMock.mockImplementationOnce(() => (currentOrg));
            // for the all orgs call as well as the calls for multiple ids (calls 3 and 4)
            orgCursorMock.mockImplementation(() => {
                return ({
                    fetchAsync:jest.fn().mockImplementation(()=>allOrgs)
                })
            });

            // single org selected
            const options = {
                org: orgFixture,
                filterDate: "09/27/2023",
            }

            const expectedResultSingle = {
                scopedOrgs: currentOrg,
                reportDateStamp: 1695873599999,
                todayStamp: todayStampMock,
                orgsMap: { nTvbx24M2dbM9w6tu: [] },
                orgsMeta: [
                    {
                        id: 'nTvbx24M2dbM9w6tu',
                        name: '321-Mariposa Local',
                        depth: 1,
                        parent: null,
                        childrenCount: 0
                    }
                ],
                orgQuery: { orgId: 'nTvbx24M2dbM9w6tu' }
            }
            const resultSingle = await BillingReportAgingService.buildOrgAndTimeData(options);
            expect(resultSingle).toEqual(expectedResultSingle);
            expect(orgCursorMock).toHaveBeenCalledTimes(2);

            // multiple orgs selected
            options.orgIds = ['nTvbx24M2dbM9w6tu','AsfEDAaFTAeCCJMLy'];
            options.currentPerson = personFixture;
            options.currentPerson.findScopedOrgs = jest.fn(() => allOrgs);

            const expectedResultsMulti = {
                scopedOrgs: allOrgs,
                reportDateStamp: 1695873599999,
                todayStamp: todayStampMock,
                orgsMap: {
                    nTvbx24M2dbM9w6tu: [ 'Mariposa Corporate' ],
                    AsfEDAaFTAeCCJMLy: [ 'Mariposa Corporate' ],
                    GtAoTHqGeLk9BR8iw: []
                },
                orgsMeta: [
                    {
                        id: 'GtAoTHqGeLk9BR8iw',
                        name: 'Mariposa Corporate',
                        depth: 0,
                        parent: null,
                        childrenCount: 2
                    },
                    {
                        id: 'AsfEDAaFTAeCCJMLy',
                        name: '142-Mariposa Southside',
                        depth: 1,
                        parent: 'GtAoTHqGeLk9BR8iw',
                        childrenCount: 0
                    },
                    {
                        id: 'nTvbx24M2dbM9w6tu',
                        name: '321-Mariposa Local',
                        depth: 1,
                        parent: 'GtAoTHqGeLk9BR8iw',
                        childrenCount: 0
                    }
                ],
                orgQuery: { orgId: { '$in': ['nTvbx24M2dbM9w6tu', 'AsfEDAaFTAeCCJMLy'] } }
            }

            const resultMulti = await BillingReportAgingService.buildOrgAndTimeData(options);
            
            expect(resultMulti).toEqual(expectedResultsMulti);
            expect(orgCursorMock).toHaveBeenCalledTimes(3);
        });
    });

    describe('buildQuery', () => {

        afterAll(async () => {
            jest.clearAllMocks();
        });

        it('throws an error if required arguments are missing', () => {
            const options = {
                reportDateStamp: null,
                todayStamp: null,
                filterBalanceType: null,
                orgQuery: null,
            };

            expect(() => BillingReportAgingService.buildQuery(options)).toThrowError(new Meteor.Error("invalid-params", "Invalid parameters passed to buildQuery: ", "reportDateStamp is required"));

            options.reportDateStamp = 1695873599999;
            expect(() => BillingReportAgingService.buildQuery(options)).toThrowError(new Meteor.Error("invalid-params", "Invalid parameters passed to buildQuery: ", "todayStamp is required"));

            options.todayStamp = new Date().getTime();
            expect(() => BillingReportAgingService.buildQuery(options)).toThrowError(new Meteor.Error("invalid-params", "Invalid parameters passed to buildQuery: ", "filterBalanceType is required"));

            options.filterBalanceType = 'payer';
            expect(() => BillingReportAgingService.buildQuery(options)).toThrowError(new Meteor.Error("invalid-params", "Invalid parameters passed to buildQuery: ", "orgQuery is required"));

            options.orgQuery = { orgId: 'nTvbx24M2dbM9w6tu' };
            expect(() => BillingReportAgingService.buildQuery(options)).not.toThrowError();
        });

        it('builds a query object with a reportDateStamp before todayStamp', () => {
            const options = {
                reportDateStamp: new Date().getTime() - 24 * 60 * 60 * 1000, // Yesterday
                todayStamp: new Date().getTime(),
                filterBalanceType: 'payer',
                orgQuery: { orgId: 'nTvbx24M2dbM9w6tu' },
            };

            const expectedResult = {
                query: {
                    orgId: 'nTvbx24M2dbM9w6tu',
                    $and: [
                        {
                            $or: [
                                { voidedAt: { $exists: false } },
                                { voidedAt: { $gt: options.reportDateStamp } },
                            ],
                        },
                        { createdAt: { $lt: options.reportDateStamp } },
                        {
                            $or: [
                                { openAmount: { $gte: 0.01 } },
                                {
                                    credits: {
                                        $elemMatch: {
                                            createdAt: { $gt: options.reportDateStamp },
                                            $or: [
                                                { voidedAt: { $exists: false } },
                                                { voidedAt: { $gt: options.reportDateStamp } },
                                            ],
                                        },
                                    },
                                },
                            ],
                        },
                    ],
                },
                payerQueryMatchPart: {
                    orgId: 'nTvbx24M2dbM9w6tu',
                    voidedAt: { $exists: false },
                    createdAt: { $lte: options.reportDateStamp },
                },
            };

            const result = BillingReportAgingService.buildQuery(options);
            expect(result).toEqual(expectedResult);
        });

        it('builds a query object with a reportDateStamp equal to todayStamp', () => {
            const options = {
                reportDateStamp: new Date().getTime(),
                todayStamp: new Date().getTime(),
                filterBalanceType: 'payer',
                orgQuery: { orgId: 'nTvbx24M2dbM9w6tu' },
            };

            const expectedResult = {
                query: {
                    orgId: 'nTvbx24M2dbM9w6tu',
                    openAmount: { $gte: 0.01 },
                },
                payerQueryMatchPart: {
                    orgId: 'nTvbx24M2dbM9w6tu',
                    voidedAt: { $exists: false },
                    openPayerAmounts: { $ne: {}, $exists: true },
                },
            };

            const result = BillingReportAgingService.buildQuery(options);
            expect(result).toEqual(expectedResult);
        });

        it('builds a query object with a reportDateStamp after todayStamp and multi-org', () => {
            const options = {
                reportDateStamp: new Date().getTime() + 24 * 60 * 60 * 1000, // Tomorrow
                todayStamp: new Date().getTime(),
                filterBalanceType: 'family',
                orgQuery: { orgId: { $in: ['nTvbx24M2dbM9w6tu', 'AsfEDAaFTAeCCJMLy'] } },
            };

            const expectedResult = {
                query: {
                    orgId: { $in: ['nTvbx24M2dbM9w6tu', 'AsfEDAaFTAeCCJMLy'] },
                    openAmount: { $gte: 0.01 },
                },
                payerQueryMatchPart: null,
            };

            const result = BillingReportAgingService.buildQuery(options);
            expect(result).toEqual(expectedResult);
        });
    });

    describe('aggregateAgingReport', () => {

        // Restore the original implementation after each test
        afterEach(async () => {
            jest.clearAllMocks();
        });

        it ('throws an error if arguments are not provided', async () => {
            const { aggregateMock, cursorMock } = Invoices;
            Invoices.aggregate.mockReturnValue({
                toArray: jest.fn().mockResolvedValue([]),
            });
            Invoices.find.mockImplementation(() => {
                return ({
                    fetchAsync:jest.fn().mockImplementation(()=>[])
                })
            });
            const options = {
                query: null,
                payerQueryMatchPart: null,
                reportDateStamp: null,
                filterBalanceType: null
            };

            await expect( async () => { await BillingReportAgingService.aggregateAgingReport(options) }).rejects.toThrowError();
            options.reportDateStamp = 1695873599999;
            options.query = { orgId: 'nTvbx24M2dbM9w6tu' };
            await expect( async () => { await BillingReportAgingService.aggregateAgingReport(options) }).not.toThrowError();
            options.filterBalanceType = 'payer';
            // if payer or both are selected for filter you must have payerQueryMatchPart
            await expect( async () => { await BillingReportAgingService.aggregateAgingReport(options) }).rejects.toThrowError();
            options.payerQueryMatchPart = {
                orgId: 'nTvbx24M2dbM9w6tu',
                voidedAt: { '$exists': false },
                openPayerAmounts: { '$ne': {}, '$exists': true }
            };
            await expect( async () => { await BillingReportAgingService.aggregateAgingReport(options) }).not.toThrowError();
            options.filterBalanceType = 'family';
            options.payerQueryMatchPart = null;
            options.query = null;
            // if family or both are selected for filter you must have query
            await expect( async () => { await BillingReportAgingService.aggregateAgingReport(options) }).rejects.toThrowError();
            options.query = { orgId: { '$in': ['nTvbx24M2dbM9w6tu', 'AsfEDAaFTAeCCJMLy'] }, openAmount: { '$gt': 0 } };
            await expect( async () => { await BillingReportAgingService.aggregateAgingReport(options) }).not.toThrowError();
        });

        it('Aggregates aging report data based on the provided options.', async () => {
            // mocks
            const {aggregateMock, cursorMock} = Invoices;
            Invoices.aggregate.mockReturnValueOnce({
                toArray: jest.fn().mockResolvedValue(singleOrgPayerAggregate),
            });
            Invoices.find.mockImplementation(() => {
                return ({
                    fetchAsync:jest.fn().mockImplementation(()=>BillingReportAgingService.sortBatchedInvoicesByCreatedAt(singleOrgInvoices))
                })
            });
            Invoices.aggregate.mockReturnValueOnce({
                toArray: jest.fn().mockResolvedValue(multiOrgPayerAggregate),
            });

            // family filter, single org and report date is before today
            const options = {
                reportDateStamp: new moment.tz(orgFixture.getTimezone()).subtract(1, 'days').endOf('day').valueOf(),
                query: {
                    orgId: 'nTvbx24M2dbM9w6tu',
                    createdAt: {$lt: new moment.tz(orgFixture.getTimezone()).subtract(1, 'days').endOf('day').valueOf()}
                },
                filterBalanceType: 'family',
                payerQueryMatchPart: null,
            }

            let result = await BillingReportAgingService.aggregateAgingReport(options);
            expect(result).toEqual(singleOrgInvoices);
            expect(Invoices.find).toHaveBeenCalledTimes(1);

            // both filter, single org and report date is today
            options.filterBalanceType = 'both';
            options.query = {
                orgId: 'nTvbx24M2dbM9w6tu',
                openAmount: {'$gt': 0}
            };
            options.payerQueryMatchPart = {
                orgId: 'nTvbx24M2dbM9w6tu',
                voidedAt: {'$exists': false},
                openPayerAmounts: {'$ne': {}, '$exists': true}
            }
            options.reportDateStamp = todayStampMock;

            result = await BillingReportAgingService.aggregateAgingReport(options);
            // expect(result).toEqual(singleOrgPayerAggregate.concat(singleOrgInvoices)); work on it seperatly mocks are incorrect

            //payer filter, multi org and report date is after today
            options.filterBalanceType = 'payer';
            options.query = null;
            options.reportDateStamp = new moment.tz(orgFixture.getTimezone()).add(1, 'days').endOf('day').valueOf();
            options.payerQueryMatchPart = {
                orgId: { '$in': ['nTvbx24M2dbM9w6tu', 'AsfEDAaFTAeCCJMLy'] },
                voidedAt: { '$exists': false },
                createdAt: { '$lte': options.reportDateStamp }
            }

            result = await BillingReportAgingService.aggregateAgingReport(options);
            // expect(result).toEqual(multiOrgPayerAggregate); work on it seperatly mocks are incorrect
        });
    });

    describe('filterInvoicesByOptions', () => {

        // Restore the original implementation after each test
        afterAll(async () => {
            jest.clearAllMocks();
        });

        it('throws an error if arguments are not provided', async () => {
            const options = {
                searchQuery: null,
                includeWaitList: true,
                invoices: null
            };
        
            await expect(BillingReportAgingService.filterInvoicesByOptions(options)).rejects.toThrowError(new Meteor.Error("invalid-params", "Invalid parameters passed to filterInvoicesByOptions: ", "invoices is required"));
        
            options.invoices = [];
            expect(() => { BillingReportAgingService.filterInvoicesByOptions(options) }).not.toThrowError();
        });

        it('Filters invoices based on the provided options.', async () => {
            // mock implementation of invoice methods
            singleOrgInvoices.forEach(invoice => {
                invoice.personName = function(){
                    const person = singleOrgInvoicesPeople.find(person => person._id === invoice.personId);
                    return person ? `${person.firstName} ${person.lastName}` : null;
                };
                invoice.personHasWaitListDesignation = function(){
                    const person = singleOrgInvoicesPeople.find(person => person._id === invoice.personId);
                    return person ?  person.designations?.includes('Wait List') : null;
                };
            });
            // include waitlist, no search query
            const options = {
                searchQuery: null,
                includeWaitList: true,
                invoices: singleOrgInvoices
            };

            let result = await BillingReportAgingService.filterInvoicesByOptions(options);
            expect(result).toEqual(singleOrgInvoices);
            expect(result).toHaveLength(20);

            // no waitlist, no search query
            options.includeWaitList = false;
            result = await BillingReportAgingService.filterInvoicesByOptions(options);
            expect(result).toHaveLength(17);

            // no wait list, search query
            options.searchQuery = 'Valentina  Espinosa';
            result = await BillingReportAgingService.filterInvoicesByOptions(options);
            expect(result).toHaveLength(1);
        });
    });

    describe('filterInvoicesByBalancePointInTime', () => {

        // Restore the original implementation after each test
        afterAll(async () => {
            jest.clearAllMocks();
        });

        it ('throws an error if arguments are not provided', async () => {
            const options = {
                invoices: null,
                filterBalance: null
            };
            expect(  () => {  BillingReportAgingService.filterInvoicesByBalancePointInTime(options) }).toThrowError(new Meteor.Error("invalid-params", "Invalid parameters passed to filterInvoicesByBalancePointInTime: ", "invoices is required"));
            options.invoices = [];
            expect(  () => {  BillingReportAgingService.filterInvoicesByBalancePointInTime(options) }).not.toThrowError();
        });

        it('Filters invoices based on the balance point in time and an optional balance filter.', async () => {
            const tempInvoice = [...singleOrgInvoices]; // Store a temporary copy to avoid mutating the original
            const buckets = ["0-6", "7-13", "14-20", "21-27"];

            // Set some elements with a balancePointInTime of 0
            tempInvoice.forEach((invoice, index) => {
                const isFirstHalf = index < tempInvoice.length / 2;
                invoice.balancePointInTime = isFirstHalf ? 0 : 0.5;  // or any other deterministic value
                invoice.amountBucket = buckets[index % buckets.length];
            });
            // no bucket filter
            const options = {
                invoices: tempInvoice,
                filterBalance: null
            };

            let result = BillingReportAgingService.filterInvoicesByBalancePointInTime(options);
            expect(result.every(invoice => invoice.balancePointInTime !== 0)).toBe(true);
            expect(result.length).toBe(tempInvoice.filter(invoice => invoice.balancePointInTime !== 0).length);
            expect(result.every(invoice => singleOrgInvoices.includes(invoice))).toBe(true);

            // bucket filter
            options.filterBalance = '0-6';
            result = BillingReportAgingService.filterInvoicesByBalancePointInTime(options);
            expect(result.every(invoice => invoice.balancePointInTime !== 0)).toBe(true);
            expect(result.length).toBe(tempInvoice.filter(invoice => invoice.balancePointInTime !== 0 && invoice.amountBucket === options.filterBalance).length);
            expect(result.every(invoice => invoice.amountBucket === options.filterBalance)).toBe(true);
        });
    });

    describe('generateAgeBucketsByInvoiceDueDate', () => {

        // Restore the original implementation after each test
        afterAll(async () => {
            jest.clearAllMocks();
        });

        it ('throws an error if arguments are not provided', async () => {
            const options = {
                invoices: null,
                filterBuckets: null,
                todayDate: null,
                todayStamp: null,
                usePeriodDate: null,
                reportDateStamp: null,
                filterBalance: null
            };

            expect(  () => {  BillingReportAgingService.generateAgeBucketsByInvoiceDueDate(options) }).toThrowError(new Meteor.Error("invalid-params", "Invalid parameters passed to generateAgeBucketsByInvoiceDueDate: ", "invoices is required"));
            options.invoices = [];
            expect(  () => {  BillingReportAgingService.generateAgeBucketsByInvoiceDueDate(options) }).toThrowError(new Meteor.Error("invalid-params", "Invalid parameters passed to generateAgeBucketsByInvoiceDueDate: ", "filterBuckets is required"));
            options.filterBuckets = 'family';
            expect(  () => {  BillingReportAgingService.generateAgeBucketsByInvoiceDueDate(options) }).toThrowError(new Meteor.Error("invalid-params", "Invalid parameters passed to generateAgeBucketsByInvoiceDueDate: ", "todayDate is required"));
            options.todayDate = new Date();
            expect(  () => {  BillingReportAgingService.generateAgeBucketsByInvoiceDueDate(options) }).toThrowError(new Meteor.Error("invalid-params", "Invalid parameters passed to generateAgeBucketsByInvoiceDueDate: ", "todayStamp is required"));
            options.todayStamp = options.todayDate.getTime();
            expect(  () => {  BillingReportAgingService.generateAgeBucketsByInvoiceDueDate(options) }).toThrowError(new Meteor.Error("invalid-params", "Invalid parameters passed to generateAgeBucketsByInvoiceDueDate: ", "reportDateStamp is required"));
            options.reportDateStamp = options.todayDate.getTime();
            expect(  () => {  BillingReportAgingService.generateAgeBucketsByInvoiceDueDate(options) }).not.toThrowError();
        });

        it('Generates age buckets for invoices based on due dates and other options.', async () => {
            const tempInvoices = [...singleOrgInvoices]; // Store a temporary copy to avoid mutating the original
            const familyBuckets = ["current", "0-6", "7-13", "14-20", "21-27", "over28"];
            const payerBuckets = ["current", "1-30", "31-60", "61-90", "91-120", "over121"];

            // for mocking due dates values to test each bucket
            const dayOffsetsFamily = [0, 1, 7, 14, 21, 28];
            const dayOffsetsPayer = [0, 1, 31, 61, 91, 121];

            const todayMoment = new moment.tz(orgFixture.getTimezone());
            const options = {
                invoices: tempInvoices,
                filterBuckets: 'family',
                todayDate: todayMoment,
                todayStamp: todayMoment.startOf('day').valueOf(),
                usePeriodDate: false,
                reportDateStamp: todayMoment.startOf('day').valueOf(),
                filterBalance: null
            };
            // leave it to me to get a fixture of invoices all already paid off. This function will add open amounts and newer due dates as well as adding a test variable for later
            options.invoices.forEach(invoice => {
                if (Math.random() > 0.25) {
                    invoice.openAmount = Math.floor(Math.random() * 1000);
                }
                invoice.originalAmountWithPayers = () =>
                    invoice.openAmount +
                    _.reduce(invoice.lineItems, (memo, lineItem) => memo + _.reduce(lineItem.appliedDiscounts, (disMemo, appliedDiscount) => disMemo + appliedDiscount.amount, 0), 0);

                invoice.dueDate = todayMoment.clone().subtract(dayOffsetsFamily[Math.floor(Math.random() * dayOffsetsFamily.length)], 'days').valueOf();
                const invoicePeriodDates = _.chain(invoice.lineItems).filter(li => li.periodStartDate).pluck("periodStartDate").value();
                invoice.testMinPeriodDate = !_.isEmpty(invoicePeriodDates) && _.min(invoicePeriodDates);
            });
            let result = BillingReportAgingService.generateAgeBucketsByInvoiceDueDate(options);
            expect(result.every(invoice => familyBuckets.includes(invoice.amountBucket))).toBe(true);
            expect(result.every(invoice => invoice.balancePointInTime !== 0)).toBe(true);

            // test with a balance filter
            options.filterBalance = 'over28';
            result = BillingReportAgingService.generateAgeBucketsByInvoiceDueDate(options);
            expect(result.every(invoice => invoice.balancePointInTime !== 0 && invoice.amountBucket === options.filterBalance)).toBe(true);

            // test with a period date
            options.filterBalance = null;
            options.usePeriodDate = true;
            result = BillingReportAgingService.generateAgeBucketsByInvoiceDueDate(options);
            expect(result.every(invoice => invoice.testMinPeriodDate === invoice.minPeriodDate)).toBe(true);

            //check payer buckets
            options.usePeriodDate = false;
            options.filterBuckets = 'payer';
            options.invoices.forEach(invoice => {
                delete invoice.amountBucket;
                invoice.dueDate = todayMoment.clone().subtract(dayOffsetsPayer[Math.floor(Math.random() * dayOffsetsFamily.length)], 'days').valueOf();
            });
            result = BillingReportAgingService.generateAgeBucketsByInvoiceDueDate(options);
            expect(result.every(invoice => payerBuckets.includes(invoice.amountBucket))).toBe(true);
        });

        it('Does not show an invoice for BUGS-2666', async () => {
            bugs2666Invoice.originalAmountWithPayers = () => 615;
            const tempInvoices = [bugs2666Invoice];
            const todayMoment = new moment.tz(orgFixture.getTimezone());
            const options = {
                invoices: tempInvoices,
                filterBuckets: 'family',
                todayDate: todayMoment,
                todayStamp: todayMoment.startOf('day').valueOf(),
                usePeriodDate: false,
                reportDateStamp: 1719806399999,
                filterBalance: ''
            }
            let result = BillingReportAgingService.generateAgeBucketsByInvoiceDueDate(options);
            expect(result?.length ?? 0).toBe(0);
        });
    });

    describe('generateOrgCenterTotals', () => {

        // Restore the original implementation after each test
        afterAll(async () => {
            jest.clearAllMocks();
        });

        it ('throws an error if arguments are not provided', async () => {
            const options = {
                filterBuckets: null,
                scopedOrgs: null
            }

            expect(  () => {  BillingReportAgingService.generateOrgCenterTotals(options) }).toThrowError(new Meteor.Error("invalid-params", "Invalid parameters passed to generateOrgCenterTotals", "filterBuckets is required"));
            options.filterBuckets = 'family';
            expect(  () => {  BillingReportAgingService.generateOrgCenterTotals(options) }).toThrowError(new Meteor.Error("invalid-params", "Invalid parameters passed to generateOrgCenterTotals", "scopedOrgs is required"));
            options.scopedOrgs = [];
            expect(  () => {  BillingReportAgingService.generateOrgCenterTotals(options) }).not.toThrowError();
        });

        it('Generates organization center totals based on the provided options.', async () => {
            // Family buckets, single site
            const options = {
                filterBuckets: 'family',
                scopedOrgs: [orgFixture]
            }

            let expectedResult = {}
            for (const org of options.scopedOrgs) {
                expectedResult[org.name] = {
                    totalOriginalAmount: 0.0,
                    totalNetOriginalAmount: 0.0,
                    totalOpenAmount: 0.0,
                    totalCurrentAmount: 0.0,
                    totalZeroToSix: 0.0,
                    totalSevenToThirteen: 0.0,
                    totalFourteenToTwenty: 0.0,
                    totalTwentyOneToTwentySeven: 0.0,
                    totalOverTwentyEight: 0.0
                }
            }
            let result = BillingReportAgingService.generateOrgCenterTotals(options);
            expect(result).toEqual(expectedResult);

            // Payer buckets, multi site
            expectedResult = {}
            options.scopedOrgs = allOrgs;
            for (const org of options.scopedOrgs) {
                expectedResult[org.name] = {
                    totalOriginalAmount: 0.0,
                    totalNetOriginalAmount: 0.0,
                    totalOpenAmount: 0.0,
                    totalCurrentAmount: 0.0,
                    totalOneToThirty: 0.0,
                    totalThirtyOneToSixty: 0.0,
                    totalSixtyOneToNinety: 0.0,
                    totalNinetyOneToOneTwenty: 0.0,
                    totalOverOneTwenty: 0.0
                }
            }

            options.filterBuckets = 'payer';
            result = BillingReportAgingService.generateOrgCenterTotals(options);
            expect(result).toEqual(expectedResult);
        });
    });

    describe('groupAndCalculateInvoices', () => {

        // Restore the original implementation after each test
        afterAll(async () => {
            jest.clearAllMocks();
        });

        it('throws an error if arguments are not provided', async () => {
            const options = {
                invoices: null,
                filterBuckets: null,
                scopedOrgs: null
            };
        
            await expect(BillingReportAgingService.groupAndCalculateInvoices(options)).rejects.toThrowError(new Meteor.Error("invalid-params", "Invalid parameters passed to groupAndCalculateInvoices", "invoices is required"));
            options.invoices = [];
            await expect(BillingReportAgingService.groupAndCalculateInvoices(options)).rejects.toThrowError(new Meteor.Error("invalid-params", "Invalid parameters passed to groupAndCalculateInvoices", "filterBuckets is required"));
            options.filterBuckets = 'family';
            await expect(BillingReportAgingService.groupAndCalculateInvoices(options)).rejects.toThrowError(new Meteor.Error("invalid-params", "Invalid parameters passed to groupAndCalculateInvoices", "scopedOrgs is required"));
            options.scopedOrgs = [];
            await expect(BillingReportAgingService.groupAndCalculateInvoices(options)).resolves.not.toThrowError();
        });

        it('Groups and calculates invoices based on the provided options.', async () => {
            // Mock await People.findOneAsync() to return the correct person
            const findOneAsyncMock = People.findOneAsync;
            findOneAsyncMock.mockImplementation((personId) => {
                return singleOrgInvoicesPeople.find(person => person._id === personId._id);
            });

            const tempInvoices = [...singleOrgInvoices];
            const familyBuckets = ["current", "0-6", "7-13", "14-20", "21-27", "over28"];
            const payerBuckets = ["current", "1-30", "31-60", "61-90", "91-120", "over121"];
            const familyBucketProps = ["totalCurrentAmount", "totalZeroToSix", "totalSevenToThirteen", "totalFourteenToTwenty", "totalTwentyOneToTwentySeven", "totalOverTwentyEight"];
            const payerBucketProps = ["totalCurrentAmount", "totalOneToThirty", "totalThirtyOneToSixty", "totalSixtyOneToNinety", "totalNinetyOneToOneTwenty", "totalOverOneTwenty"];

            // Family buckets, single site
            const options = {
                invoices: tempInvoices,
                filterBuckets: 'family',
                scopedOrgs: currentOrg
            };

            //randomly generate balancePointInTime and amountBucket for each invoice
            options.invoices.forEach(invoice => {
                invoice.personName = function(){
                    const person = singleOrgInvoicesPeople.find(person => person._id === invoice.personId);
                    return person ? `${person.firstName} ${person.lastName}` : null;
                };
                invoice.balancePointInTime = Math.floor(Math.random() * 1000);
                invoice.amountBucket = familyBuckets[Math.floor(Math.random() * familyBuckets.length)];
            });

            let result = await BillingReportAgingService.groupAndCalculateInvoices(options);
            expect(result.length > 0).toBe(true);
            // check the math
            result.forEach(calculatedInvoice => {
                let sumOfBuckets = 0;
                for (const bucket of familyBucketProps) {
                    sumOfBuckets += calculatedInvoice[bucket];
                }
                expect(sumOfBuckets).toEqual(calculatedInvoice.totalOpenAmount);
            });

            // Payer buckets, multi site
            options.filterBuckets = 'payer';
            options.scopedOrgs = allOrgs;
            options.invoices = [...multiOrgPayerAggregate];

            findOneAsyncMock.mockImplementation((personId) => {
                return multiOrgPayerAggregatePeople.find(person => person._id === personId._id);
            });

            options.invoices.forEach(invoice => {
                invoice.personName = function(){
                    const person = multiOrgPayerAggregatePeople.find(person => person._id === invoice.personId);
                    return person ? `${person.firstName} ${person.lastName}` : null;
                };
                invoice.balancePointInTime = Math.floor(Math.random() * 1000);
                invoice.amountBucket = payerBuckets[Math.floor(Math.random() * payerBuckets.length)];
            });

            result = await BillingReportAgingService.groupAndCalculateInvoices(options);
            result.forEach(calculatedInvoice => {
                let sumOfBuckets = 0;
                for (const bucket of payerBucketProps) {
                    sumOfBuckets += calculatedInvoice[bucket];
                }
                expect(sumOfBuckets).toEqual(calculatedInvoice.totalOpenAmount);
            });
        });
    });

    describe('buildPeopleCreditsQuery', () => {

        // Restore the original implementation after each test
        afterAll(async () => {
            jest.clearAllMocks();
        });

        it ('throws an error if arguments are not provided', async () => {
            const options = {
                orgQuery: null,
                searchQuery: null,
                reportDateStamp: null,
                todayStamp: null
            }

            expect(  () => {  BillingReportAgingService.buildPeopleCreditsQuery(options) }).toThrowError(new Meteor.Error("invalid-params", "Invalid parameters passed to buildPeopleCreditsQuery", "orgQuery is required"));
            options.orgQuery = {};
            expect(  () => {  BillingReportAgingService.buildPeopleCreditsQuery(options) }).toThrowError(new Meteor.Error("invalid-params", "Invalid parameters passed to buildPeopleCreditsQuery", "reportDateStamp is required"));
            options.reportDateStamp = new Date();
            expect(  () => {  BillingReportAgingService.buildPeopleCreditsQuery(options) }).toThrowError(new Meteor.Error("invalid-params", "Invalid parameters passed to buildPeopleCreditsQuery", "todayStamp is required"));
            options.todayStamp = new Date();
            expect(  () => {  BillingReportAgingService.buildPeopleCreditsQuery(options) }).not.toThrowError();
        });

        it('Builds a query object for retrieving people with open credit memos based on the provided options.', async () => {
            // single org, report date is today, no search query
            const options = {
                orgQuery: { orgId: 'nTvbx24M2dbM9w6tu' },
                searchQuery: null,
                reportDateStamp: todayStampMock,
                todayStamp: todayStampMock
            }

            let expectedResult = {
                orgId: 'nTvbx24M2dbM9w6tu',
                inActive: { '$ne': true },
                type: 'family',
                'billing.creditMemos': { '$elemMatch': { type: { '$ne': 'securityDepositAuto' }, openAmount: { '$gte': 0.01 } } }
            }

            let result = BillingReportAgingService.buildPeopleCreditsQuery(options);
            expect(result).toEqual(expectedResult);

            // single org, report date is today, search query
            options.searchQuery = 'Easton Carlson';

            expectedResult = {
                orgId: 'nTvbx24M2dbM9w6tu',
                inActive: {'$ne': true},
                type: 'family',
                'billing.creditMemos': { '$elemMatch': { type: { '$ne': 'securityDepositAuto' }, openAmount: { '$gte': 0.01 } } },
                $or: [
                    {"firstName": {"$regex": options.searchQuery, "$options": "i"}},
                    {"lastName": {"$regex": options.searchQuery, "$options": "i"}}
                ]
            }

            result = BillingReportAgingService.buildPeopleCreditsQuery(options);
            expect(result).toEqual(expectedResult);

            // multi org, report date is yesterday, no search query
            options.orgQuery = { orgId: { '$in': ['nTvbx24M2dbM9w6tu', 'nTvbx24M2dbM9w6tv'] } };
            options.searchQuery = null;
            options.reportDateStamp = new moment.tz(todayStampMock, orgFixture.getTimezone()).subtract(1, 'days').valueOf();

            expectedResult = {
                orgId: { '$in': ['nTvbx24M2dbM9w6tu', 'nTvbx24M2dbM9w6tv'] },
                inActive: { '$ne': true },
                type: 'family',
                'billing.creditMemos': { '$elemMatch': { type: { '$ne': 'securityDepositAuto' }, createdAt: { "$lt": options.reportDateStamp } } }
            }

            result = BillingReportAgingService.buildPeopleCreditsQuery(options);
            expect(result).toEqual(expectedResult);
        });
    });

    describe('getPeopleWithOpenCredits', () => {

        // Restore the original implementation after each test
        afterEach(async () => {
            jest.clearAllMocks();
        });

        it('throws an error if arguments are not provided', async () => {
            const find = People.find;
            find.mockImplementation(() => {
                return ({
                    fetchAsync:jest.fn().mockImplementation(()=>[])
                })
            });
            let peopleCreditsQuery = null;
        
            await expect(BillingReportAgingService.getPeopleWithOpenCredits(peopleCreditsQuery)).rejects.toThrowError(new Meteor.Error("invalid-params", "Invalid parameters passed to getPeopleWithOpenCredits", "peopleCreditsQuery is required"));
            peopleCreditsQuery = {};
            await expect(BillingReportAgingService.getPeopleWithOpenCredits(peopleCreditsQuery)).resolves.not.toThrowError();
        });

        it('Retrieves people with open credit memos based on the provided query.', async () => {
            const find = People.find;
            find.mockImplementation(() => {
                return ({
                    fetchAsync:jest.fn().mockImplementation(()=>singleOrgOpenCreditPeople)
                })
            });
            const peopleCreditsQuery = {
                orgId: 'nTvbx24M2dbM9w6tu',
                inActive: { '$ne': true },
                type: 'family',
                'billing.creditMemos': { '$elemMatch': { type: { '$ne': 'securityDepositAuto' }, openAmount: { '$gt': 0 } } }
            }

            let result = await BillingReportAgingService.getPeopleWithOpenCredits(peopleCreditsQuery);
            expect(result).toEqual(singleOrgOpenCreditPeople);
            expect(find.mock.calls.length).toBe(1);
        });
    });

    describe('generateAgeBucketsByCreditCreatedDate',  () => {

        // Restore the original implementation after each test
        afterAll(async () => {
            jest.clearAllMocks();
        });

        it ('throws an error if arguments are not provided', async () => {
            const find = Relationships.find;
            find.mockImplementation(() => {
                return ({
                    fetchAsync:jest.fn().mockImplementation(()=>[])
                })
            });
            const options = {
                peopleWithOpenCredits: null,
                reportDateStamp: null,
                todayStamp: null,
                todayDate: null,
                filterBuckets: null,
                groupedAndCalculatedInvoices: null
            }

            await expect( async () => {  await BillingReportAgingService.generateAgeBucketsByCreditCreatedDate(options) }).rejects.toThrowError(new Meteor.Error("invalid-params", "Invalid parameters passed to generateAgeBucketsByCreditCreatedDate", "peopleWithOpenCredits is required"));
            options.peopleWithOpenCredits = [];
            await expect(  async () => {  await BillingReportAgingService.generateAgeBucketsByCreditCreatedDate(options) }).rejects.toThrowError(new Meteor.Error("invalid-params", "Invalid parameters passed to generateAgeBucketsByCreditCreatedDate", "reportDateStamp is required"));
            options.reportDateStamp = new Date().valueOf();
            await expect(  async () => {  await BillingReportAgingService.generateAgeBucketsByCreditCreatedDate(options) }).rejects.toThrowError(new Meteor.Error("invalid-params", "Invalid parameters passed to generateAgeBucketsByCreditCreatedDate", "todayStamp is required"));
            options.todayStamp = new Date().valueOf();
            await expect(  async () => {  await BillingReportAgingService.generateAgeBucketsByCreditCreatedDate(options) }).rejects.toThrowError(new Meteor.Error("invalid-params", "Invalid parameters passed to generateAgeBucketsByCreditCreatedDate", "todayDate is required"));
            options.todayDate = new Date();
            await expect(  async () => { await BillingReportAgingService.generateAgeBucketsByCreditCreatedDate(options) }).rejects.toThrowError(new Meteor.Error("invalid-params", "Invalid parameters passed to generateAgeBucketsByCreditCreatedDate", "filterBuckets is required"));
            options.filterBuckets = 'family';
            await expect(  async () => { await BillingReportAgingService.generateAgeBucketsByCreditCreatedDate(options) }).rejects.toThrowError(new Meteor.Error("invalid-params", "Invalid parameters passed to generateAgeBucketsByCreditCreatedDate", "groupedAndCalculatedInvoices is required"));
            options.groupedAndCalculatedInvoices = [];
            await expect(  async () => {  await BillingReportAgingService.generateAgeBucketsByCreditCreatedDate(options) }).not.toThrowError();
        });

        it('Generates age buckets for open credits based on the credit created date and provided options.', async () => {
            const find = Relationships.find;
            find.mockImplementation(() => {
                return ({
                    fetchAsync:jest.fn().mockImplementation(()=>[])
                })
            });

            const aggregateMock = Invoices.aggregate;
            aggregateMock.mockReturnValue({
                toArray: jest.fn().mockResolvedValue([]),
            });

            const familyBuckets = ["current", "0-6", "7-13", "14-20", "21-27", "over28"];
            const payerBuckets = ["current", "1-30", "31-60", "61-90", "91-120", "over121"];
            const dayOffsetsFamily = [0, 1, 7, 14, 21, 28];
            const dayOffsetsPayer = [0, 1, 31, 61, 91, 121];
            let tmpArray = JSON.parse(JSON.stringify(singleOrgOpenCreditPeople));

            tmpArray.forEach(person => {
                person?.billing?.creditMemos?.forEach(credit => {
                    credit.openAmount = Math.floor(Math.random() * 1000);
                    credit.createdAt = new moment(todayStampMock).subtract(dayOffsetsFamily[Math.floor(Math.random() * dayOffsetsFamily.length)], 'days').valueOf();
                    if (Math.random() > 0.50) {
                        credit.voidedAt = new moment(todayStampMock).subtract(2, 'days').valueOf();
                    }

                    if (Math.random() > 0.50 && !credit.voidedAt) {
                        credit.refundedAt = new moment(todayStampMock).subtract(2, 'days').valueOf();
                        credit.refundedAmount = Math.floor(Math.random() * credit.openAmount);
                    }
                });
            });

            // single org, report date is today, family buckets
            const options = {
                peopleWithOpenCredits: tmpArray,
                reportDateStamp: todayStampMock,
                todayStamp: todayStampMock,
                todayDate: new moment(todayStampMock),
                filterBuckets: 'family',
                groupedAndCalculatedInvoices: [...singleOrgInvoices]
            }


            let result = await BillingReportAgingService.generateAgeBucketsByCreditCreatedDate(options);

            expect(result.openCredits.every(credit => familyBuckets.includes(credit.amountBucket))).toEqual(true);
            // all credits are all balancePointInTime === openAmount if same day
            expect(result.openCredits.every(credit => credit.openAmount === credit.balancePointInTime)).toEqual(true);
            // voided credits are filtered out on same day
            expect(result.openCredits.filter(credit => credit.voidedAt).length).toBe(0);

            tmpArray = JSON.parse(JSON.stringify(singleOrgOpenCreditPeople));

            tmpArray.forEach(person => {
                person?.billing?.creditMemos?.forEach(credit => {
                    credit.openAmount = Math.floor(Math.random() * 1000);
                    credit.createdAt = new moment(todayStampMock).subtract(dayOffsetsFamily[Math.floor(Math.random() * dayOffsetsFamily.length)], 'days').valueOf();
                    if (Math.random() > 0.50) {
                        credit.voidedAt = new moment(todayStampMock).subtract(2, 'days').valueOf();
                    }

                    if (Math.random() > 0.50 && !credit.voidedAt) {
                        credit.refundedAt = new moment(todayStampMock).subtract(2, 'days').valueOf();
                        credit.refundedAmount = Math.floor(Math.random() * credit.openAmount);
                    }
                });
            });

            //single org, report date is yesterday, family buckets
            options.reportDateStamp = new moment(todayStampMock).subtract(1, 'days').valueOf();
            options.peopleWithOpenCredits = tmpArray;
            result = await BillingReportAgingService.generateAgeBucketsByCreditCreatedDate(options);
            // voided credits are all 0
            expect(result.openCredits.filter(credit => credit.voidedAt).every(credit => credit.balancePointInTime === 0)).toEqual(true);
            // refunded credits are all originalAmount - refundedAmount (before being converted to negative ints)
            expect(result.openCredits.filter(credit => credit.refundedAt && credit.refundedAt <= options.reportDateStamp).every(credit => credit.balancePointInTime === (Math.abs(credit.originalAmount) - Math.abs(credit.refundedAmount)) * -1)).toEqual(true);

        });

        it('Generates age buckets for open credits while respecting adjustments', async () => {
            const person = {
                "_id": "yHSetfn5vS8E94ziH",
                "billing": {
                    "creditMemos": [
                        {
                            "_id": "F8o6ztFFPMGCaiSas",
                            "type": "other",
                            "createdAt": 1700591127964,
                            "createdBy": "5wQHB6wbjgdfr3ywR",
                            "notes": "Adjusted from invoice #2314 ",
                            "openAmount": 3.552713678800501e-14,
                            "originalAmount": 287.6
                        }
                    ]
                }
            };
            const credits = [
                {
                    "type": "payment",
                    "payment_type": "credit_memo",
                    "amount": 5,
                    "createdAt": 1700805348714,
                    "paidBy": "yHSetfn5vS8E94ziH",
                    "paidByDesc": "Tenisha Gatlin",
                    "creditMemoId": "F8o6ztFFPMGCaiSas",
                    "creditNote": "Adjusted from invoice #2314 ",
                    "creditMemoType": "other"
                },
                {
                    "type": "payment",
                    "payment_type": "credit_memo",
                    "amount": 74.2,
                    "createdAt": 1700633275955,
                    "paidBy": "yHSetfn5vS8E94ziH",
                    "paidByDesc": "Tenisha Gatlin",
                    "creditMemoId": "F8o6ztFFPMGCaiSas",
                    "creditNote": "Adjusted from invoice #2314 ",
                    "creditMemoType": "other"
                },
                {
                    "type": "payment",
                    "payment_type": "credit_memo",
                    "amount": 170,
                    "createdAt": 1701699152624,
                    "paidBy": "yHSetfn5vS8E94ziH",
                    "paidByDesc": "Tenisha Gatlin",
                    "creditMemoId": "F8o6ztFFPMGCaiSas",
                    "creditNote": "Adjusted from invoice #2314 ",
                    "creditMemoType": "other"
                },
                {
                    "type": "payment",
                    "payment_type": "credit_memo",
                    "amount": 0,
                    "createdAt": 1701930528815,
                    "paidBy": "yHSetfn5vS8E94ziH",
                    "paidByDesc": "Tenisha Gatlin",
                    "creditMemoId": "F8o6ztFFPMGCaiSas",
                    "creditNote": "Adjusted from invoice #2314 ",
                    "creditMemoType": "other",
                    "originalAmount": 38.4,
                    "adjustments": [
                        {
                            "adjustedBy": "XxRpGnsTXPDY27Rpp",
                            "adjustedAt": 1703007741787,
                            "adjustedNote": "Adjusted payment - Created credit memo for $38.40",
                            "adjustedAmount": 38.4
                        }
                    ]
                }
            ];
            const invoices = [
                {
                    _id: "invoiceId",
                    credits
                }
            ];
            const relationships = [
                { targetId: 'GTbqM7xZnPKw9dABX' },
                { targetId: 'a3To8nBSsnSifJsEN' },
                { targetId: 'NRG6dRJZgDeDmb47Z' }
            ];

            const options1 = {
                peopleWithOpenCredits: [person],
                reportDateStamp: yesterdayStampMock,
                todayStamp: todayStampMock,
                todayDate: new moment(todayStampMock),
                filterBuckets: 'family',
                groupedAndCalculatedInvoices: []
            }
            const options2 = cloneDeep(options1);

            const find = Relationships.find;
            find.mockImplementation(() => {
                return ({
                    fetchAsync:jest.fn().mockImplementation(()=>relationships)
                })
            });
            const aggregateMock = Invoices.aggregate;
            aggregateMock.mockReturnValue({
                toArray: jest.fn().mockResolvedValue(invoices),
            });

            invoices[0].credits[3].adjustments = [];
            // Since there are no adjustments now, it should include the credit memo on the person
            const result = await BillingReportAgingService.generateAgeBucketsByCreditCreatedDate(options2);
            expect(result.openCredits.length).toBe(1);
        });
    });

    describe('filterOpenCreditsByBalanceType', () => {

        // Restore the original implementation after each test
        afterAll(async () => {
            jest.clearAllMocks();
        });

        it ('throws an error if arguments are not provided', async () => {
            const options = {
                openCredits: null,
                filterBalanceType: null
            }

            expect(  () => {  BillingReportAgingService.filterOpenCreditsByBalanceType(options) }).toThrowError(new Meteor.Error("invalid-params", "Invalid parameters passed to filterOpenCreditsByBalanceType", "openCredits is required"));
            options.openCredits = [];
            expect(  () => {  BillingReportAgingService.filterOpenCreditsByBalanceType(options) }).not.toThrowError();
        });

        it('Filters open credits based on the specified balance type.', async () => {
            const familyFilteredOpenCredits = JSON.parse(JSON.stringify(singleOrgOpenCreditPeople));
            const payerFilteredOpenCredits = JSON.parse(JSON.stringify(singleOrgOpenCreditPeople));

            // family filter
            const options = {
                openCredits: familyFilteredOpenCredits,
                filterBalanceType: 'family'
            }

            BillingReportAgingService.filterOpenCreditsByBalanceType(options);
            familyFilteredOpenCredits.forEach(person => {
                expect(person.billing.creditMemos.every(credit => !credit.type.startsWith('prepaid_')))
            });

            // payer filter
            options.filterBalanceType = 'payer';
            options.openCredits = payerFilteredOpenCredits;

            BillingReportAgingService.filterOpenCreditsByBalanceType(options);
            payerFilteredOpenCredits.forEach(person => {
                expect(person.billing.creditMemos.every(credit => credit.type !== 'systemOverpayment' &&
                    !credit.type.startsWith('excess_') &&
                    !credit.type.startsWith('manualCard') &&
                    !credit.type.startsWith('check') &&
                    !credit.type.startsWith('cash') &&
                    !credit.type.startsWith('manualAch') &&
                    !credit.type.startsWith('payrollDeduction')))
            });
        });
    });

    describe('centerHelper', () => {

        // Restore the original implementation after each test
        afterAll(async () => {
            jest.clearAllMocks();
        });

        it('Helper function for calculating and accumulating totals in a center object.', async () => {
            const row = {
                totalOriginalAmount: Math.floor(Math.random() * 1000),
                totalOpenAmount: Math.floor(Math.random() * 1000),
                totalCurrentAmount: Math.floor(Math.random() * 1000),
                totalNetOriginalAmount: Math.floor(Math.random() * 1000),
                totalNetOpenAmount: Math.floor(Math.random() * 1000),
                totalNetCurrentAmount: Math.floor(Math.random() * 1000),
                totalZeroToSix: Math.floor(Math.random() * 1000),
                totalSevenToThirteen: Math.floor(Math.random() * 1000),
                totalFourteenToTwenty: Math.floor(Math.random() * 1000),
                totalTwentyOneToTwentySeven: Math.floor(Math.random() * 1000),
                totalOverTwentyEight: Math.floor(Math.random() * 1000),
                totalNetZeroToSix: Math.floor(Math.random() * 1000),
                totalNetSevenToThirteen: Math.floor(Math.random() * 1000),
                totalNetFourteenToTwenty: Math.floor(Math.random() * 1000),
                totalNetTwentyOneToTwentySeven: Math.floor(Math.random() * 1000),
                totalNetOverTwentyEight: Math.floor(Math.random() * 1000),
            }

            const totals = {
                totalOriginalAmount: 0,
                totalNetOriginalAmount: 0,
                totalOpenAmount: 0,
                totalCurrentAmount: 0,
                totalZeroToSix: 0,
                totalSevenToThirteen: 0,
                totalFourteenToTwenty: 0,
                totalTwentyOneToTwentySeven: 0,
                totalOverTwentyEight: 0,
                isRollup: null
            }
            // test basic assignments
            BillingReportAgingService.centerHelper(totals, row, false, 'family');
            expect(totals.totalOriginalAmount).toEqual(row.totalOriginalAmount);
            expect(totals.totalNetOriginalAmount).toEqual(row.totalNetOriginalAmount);
            expect(totals.totalOpenAmount).toEqual(row.totalNetOpenAmount);
            expect(totals.totalCurrentAmount).toEqual(row.totalNetCurrentAmount);
            expect(totals.totalZeroToSix).toEqual(row.totalNetZeroToSix);
            expect(totals.totalSevenToThirteen).toEqual(row.totalNetSevenToThirteen);
            expect(totals.totalFourteenToTwenty).toEqual(row.totalNetFourteenToTwenty);
            expect(totals.totalTwentyOneToTwentySeven).toEqual(row.totalNetTwentyOneToTwentySeven);
            expect(totals.totalOverTwentyEight).toEqual(row.totalNetOverTwentyEight);
            // test incrementing
            BillingReportAgingService.centerHelper(totals, row, false, 'family');
            expect(totals.totalOriginalAmount).toEqual(row.totalOriginalAmount * 2);
            expect(totals.totalNetOriginalAmount).toEqual(row.totalNetOriginalAmount * 2);
            expect(totals.totalOpenAmount).toEqual(row.totalNetOpenAmount * 2);
            expect(totals.totalCurrentAmount).toEqual(row.totalNetCurrentAmount * 2);
            expect(totals.totalZeroToSix).toEqual(row.totalNetZeroToSix * 2);
            expect(totals.totalSevenToThirteen).toEqual(row.totalNetSevenToThirteen * 2);
            expect(totals.totalFourteenToTwenty).toEqual(row.totalNetFourteenToTwenty * 2);
            expect(totals.totalTwentyOneToTwentySeven).toEqual(row.totalNetTwentyOneToTwentySeven * 2);
            expect(totals.totalOverTwentyEight).toEqual(row.totalNetOverTwentyEight * 2);
            // test fallback to original amounts if no net amounts
            delete row.totalNetOriginalAmount;
            delete row.totalNetOpenAmount;
            delete row.totalNetCurrentAmount;
            delete row.totalNetZeroToSix;
            delete row.totalNetSevenToThirteen;
            delete row.totalNetFourteenToTwenty;
            delete row.totalNetTwentyOneToTwentySeven;
            delete row.totalNetOverTwentyEight;
            // zero out the totals
            for (const prop in totals) {
                if (prop !== 'isRollup') {
                    totals[prop] = 0;
                }
            }
            BillingReportAgingService.centerHelper(totals, row, false, 'family');
            expect(totals.totalOriginalAmount).toEqual(row.totalOriginalAmount);
            expect(totals.totalNetOriginalAmount).toEqual(row.totalOriginalAmount);
            expect(totals.totalOpenAmount).toEqual(row.totalOpenAmount);
            expect(totals.totalCurrentAmount).toEqual(row.totalCurrentAmount);
            expect(totals.totalZeroToSix).toEqual(row.totalZeroToSix);
            expect(totals.totalSevenToThirteen).toEqual(row.totalSevenToThirteen);
            expect(totals.totalFourteenToTwenty).toEqual(row.totalFourteenToTwenty);
            expect(totals.totalTwentyOneToTwentySeven).toEqual(row.totalTwentyOneToTwentySeven);
            expect(totals.totalOverTwentyEight).toEqual(row.totalOverTwentyEight);

            // test payer
            const payerRow = {
                totalOriginalAmount: Math.floor(Math.random() * 1000),
                totalOpenAmount: Math.floor(Math.random() * 1000),
                totalCurrentAmount: Math.floor(Math.random() * 1000),
                totalNetOriginalAmount: Math.floor(Math.random() * 1000),
                totalNetOpenAmount: Math.floor(Math.random() * 1000),
                totalNetCurrentAmount: Math.floor(Math.random() * 1000),
                totalOneToThirty: Math.floor(Math.random() * 1000),
                totalThirtyOneToSixty: Math.floor(Math.random() * 1000),
                totalSixtyOneToNinety: Math.floor(Math.random() * 1000),
                totalNinetyOneToOneTwenty: Math.floor(Math.random() * 1000),
                totalOverOneTwenty: Math.floor(Math.random() * 1000),
                totalNetOneToThirty: Math.floor(Math.random() * 1000),
                totalNetThirtyOneToSixty: Math.floor(Math.random() * 1000),
                totalNetSixtyOneToNinety: Math.floor(Math.random() * 1000),
                totalNetNinetyOneToOneTwenty: Math.floor(Math.random() * 1000),
                totalNetOverOneTwenty: Math.floor(Math.random() * 1000),
            }

            const payerTotals = {
                totalOriginalAmount: 0,
                totalNetOriginalAmount: 0,
                totalOpenAmount: 0,
                totalCurrentAmount: 0,
                totalOneToThirty: 0,
                totalThirtyOneToSixty: 0,
                totalSixtyOneToNinety: 0,
                totalNinetyOneToOneTwenty: 0,
                totalOverOneTwenty: 0,
                isRollup: null
            }
            // test basic assignments
            BillingReportAgingService.centerHelper(payerTotals, payerRow, false, 'payer');
            expect(payerTotals.totalOriginalAmount).toEqual(payerRow.totalOriginalAmount);
            expect(payerTotals.totalNetOriginalAmount).toEqual(payerRow.totalNetOriginalAmount);
            expect(payerTotals.totalOpenAmount).toEqual(payerRow.totalNetOpenAmount);
            expect(payerTotals.totalCurrentAmount).toEqual(payerRow.totalNetCurrentAmount);
            expect(payerTotals.totalOneToThirty).toEqual(payerRow.totalNetOneToThirty);
            expect(payerTotals.totalThirtyOneToSixty).toEqual(payerRow.totalNetThirtyOneToSixty);
            expect(payerTotals.totalSixtyOneToNinety).toEqual(payerRow.totalNetSixtyOneToNinety);
            expect(payerTotals.totalNinetyOneToOneTwenty).toEqual(payerRow.totalNetNinetyOneToOneTwenty);
            expect(payerTotals.totalOverOneTwenty).toEqual(payerRow.totalNetOverOneTwenty);
            // test incrementing
            BillingReportAgingService.centerHelper(payerTotals, payerRow, false, 'payer');
            expect(payerTotals.totalOriginalAmount).toEqual(payerRow.totalOriginalAmount * 2);
            expect(payerTotals.totalNetOriginalAmount).toEqual(payerRow.totalNetOriginalAmount * 2);
            expect(payerTotals.totalOpenAmount).toEqual(payerRow.totalNetOpenAmount * 2);
            expect(payerTotals.totalCurrentAmount).toEqual(payerRow.totalNetCurrentAmount * 2);
            expect(payerTotals.totalOneToThirty).toEqual(payerRow.totalNetOneToThirty * 2);
            expect(payerTotals.totalThirtyOneToSixty).toEqual(payerRow.totalNetThirtyOneToSixty * 2);
            expect(payerTotals.totalSixtyOneToNinety).toEqual(payerRow.totalNetSixtyOneToNinety * 2);
            expect(payerTotals.totalNinetyOneToOneTwenty).toEqual(payerRow.totalNetNinetyOneToOneTwenty * 2);
            expect(payerTotals.totalOverOneTwenty).toEqual(payerRow.totalNetOverOneTwenty * 2);
            // test fallback to original amounts if no net amounts
            delete payerRow.totalNetOriginalAmount;
            delete payerRow.totalNetOpenAmount;
            delete payerRow.totalNetCurrentAmount;
            delete payerRow.totalNetOneToThirty;
            delete payerRow.totalNetThirtyOneToSixty;
            delete payerRow.totalNetSixtyOneToNinety;
            delete payerRow.totalNetNinetyOneToOneTwenty;
            delete payerRow.totalNetOverOneTwenty;
            // zero out the totals
            for (const prop in payerTotals) {
                if (prop !== 'isRollup') {
                    payerTotals[prop] = 0;
                }
            }
            BillingReportAgingService.centerHelper(payerTotals, payerRow, false, 'payer');
            expect(payerTotals.totalOriginalAmount).toEqual(payerRow.totalOriginalAmount);
            expect(payerTotals.totalNetOriginalAmount).toEqual(payerRow.totalOriginalAmount);
            expect(payerTotals.totalOpenAmount).toEqual(payerRow.totalOpenAmount);
            expect(payerTotals.totalCurrentAmount).toEqual(payerRow.totalCurrentAmount);
            expect(payerTotals.totalOneToThirty).toEqual(payerRow.totalOneToThirty);
            expect(payerTotals.totalThirtyOneToSixty).toEqual(payerRow.totalThirtyOneToSixty);
            expect(payerTotals.totalSixtyOneToNinety).toEqual(payerRow.totalSixtyOneToNinety);
            expect(payerTotals.totalNinetyOneToOneTwenty).toEqual(payerRow.totalNinetyOneToOneTwenty);
            expect(payerTotals.totalOverOneTwenty).toEqual(payerRow.totalOverOneTwenty);
        });
    });

    describe('calculateCenterTotals', () => {

        // Restore the original implementation after each test
        afterAll(async () => {
            jest.clearAllMocks();
        });

        it ('throws an error if arguments are not provided', async () => {
            const options = {
                centerTotals: null,
                orgsMeta: null
            }
            expect(  () => {  BillingReportAgingService.calculateCenterTotals(options) }).toThrowError(new Meteor.Error("invalid-params", "Invalid parameters passed to calculateCenterTotals: ", "centerTotals is required"));
            options.centerTotals = {};
            expect(  () => {  BillingReportAgingService.calculateCenterTotals(options) }).toThrowError(new Meteor.Error("invalid-params", "Invalid parameters passed to calculateCenterTotals: ", "orgsMeta is required"));
            options.orgsMeta = {};
            expect(  () => {  BillingReportAgingService.calculateCenterTotals(options) }).not.toThrowError();
        });

        it('Calculates and formats center totals based on the provided options.', async () => {
            //single org
            const options = {
                centerTotals: {
                    '321-Mariposa Local': {
                        totalOriginalAmount: Math.floor(Math.random() * 100000),
                        totalNetOriginalAmount: Math.floor(Math.random() * 100000),
                        totalOpenAmount: Math.floor(Math.random() * 100000),
                        totalCurrentAmount: Math.floor(Math.random() * 100000),
                        totalZeroToSix: Math.floor(Math.random() * 100000),
                        totalSevenToThirteen: Math.floor(Math.random() * 100000),
                        totalFourteenToTwenty: Math.floor(Math.random() * 100000),
                        totalTwentyOneToTwentySeven: Math.floor(Math.random() * 100000),
                        totalOverTwentyEight: Math.floor(Math.random() * 100000),
                        isRollup: false
                    }
                },
                orgsMeta: [
                    {
                        id: 'nTvbx24M2dbM9w6tu',
                        name: '321-Mariposa Local',
                        depth: 1,
                        parent: null,
                        childrenCount: 0
                    }
                ]
            }
            let centerTotals = BillingReportAgingService.calculateCenterTotals(options);
            expect(centerTotals).toBeDefined();
            expect(centerTotals['321-Mariposa Local']).toBeDefined();
            expect(centerTotals['321-Mariposa Local'].totalOriginalAmount).toEqual(options.centerTotals['321-Mariposa Local'].totalOriginalAmount);
            expect(centerTotals['321-Mariposa Local'].totalNetOriginalAmount).toEqual(options.centerTotals['321-Mariposa Local'].totalNetOriginalAmount);
            expect(centerTotals['321-Mariposa Local'].totalOpenAmount).toEqual(options.centerTotals['321-Mariposa Local'].totalOpenAmount);
            expect(centerTotals['321-Mariposa Local'].totalCurrentAmount).toEqual(options.centerTotals['321-Mariposa Local'].totalCurrentAmount);
            expect(centerTotals['321-Mariposa Local'].totalZeroToSix).toEqual(options.centerTotals['321-Mariposa Local'].totalZeroToSix);
            expect(centerTotals['321-Mariposa Local'].totalSevenToThirteen).toEqual(options.centerTotals['321-Mariposa Local'].totalSevenToThirteen);
            expect(centerTotals['321-Mariposa Local'].totalFourteenToTwenty).toEqual(options.centerTotals['321-Mariposa Local'].totalFourteenToTwenty);
            expect(centerTotals['321-Mariposa Local'].totalTwentyOneToTwentySeven).toEqual(options.centerTotals['321-Mariposa Local'].totalTwentyOneToTwentySeven);
            expect(centerTotals['321-Mariposa Local'].totalOverTwentyEight).toEqual(options.centerTotals['321-Mariposa Local'].totalOverTwentyEight);
            expect(centerTotals['321-Mariposa Local'].isRollup).toEqual(options.centerTotals['321-Mariposa Local'].isRollup);
            expect(centerTotals['321-Mariposa Local'].orgName).toEqual(options.orgsMeta[0].name);
            expect(centerTotals['321-Mariposa Local'].displayParentId).toEqual(options.orgsMeta[0].parent);
            expect(centerTotals['321-Mariposa Local'].displayId).toEqual(options.orgsMeta[0].id);
            expect(centerTotals['321-Mariposa Local'].displayDepth).toEqual(options.orgsMeta[0].depth);
            expect(centerTotals['321-Mariposa Local'].displayChildrenCount).toEqual(options.orgsMeta[0].childrenCount);

            // multiple orgs
            options.centerTotals = {
                '321-Mariposa Local': {
                    totalOriginalAmount: Math.floor(Math.random() * 100000),
                    totalNetOriginalAmount: Math.floor(Math.random() * 100000),
                    totalOpenAmount: Math.floor(Math.random() * 100000),
                    totalCurrentAmount: Math.floor(Math.random() * 100000),
                    totalZeroToSix: Math.floor(Math.random() * 100000),
                    totalSevenToThirteen: Math.floor(Math.random() * 100000),
                    totalFourteenToTwenty: Math.floor(Math.random() * 100000),
                    totalTwentyOneToTwentySeven: Math.floor(Math.random() * 100000),
                    totalOverTwentyEight: Math.floor(Math.random() * 100000),
                    isRollup: false
                },
                '142-Mariposa Southside': {
                    totalOriginalAmount: Math.floor(Math.random() * 100000),
                    totalNetOriginalAmount: Math.floor(Math.random() * 100000),
                    totalOpenAmount: Math.floor(Math.random() * 100000),
                    totalCurrentAmount: Math.floor(Math.random() * 100000),
                    totalZeroToSix: Math.floor(Math.random() * 100000),
                    totalSevenToThirteen: Math.floor(Math.random() * 100000),
                    totalFourteenToTwenty: Math.floor(Math.random() * 100000),
                    totalTwentyOneToTwentySeven: Math.floor(Math.random() * 100000),
                    totalOverTwentyEight: Math.floor(Math.random() * 100000),
                    isRollup: false
                },
                'Mariposa Corporate': {
                    totalOriginalAmount: Math.floor(Math.random() * 100000),
                    totalNetOriginalAmount: Math.floor(Math.random() * 100000),
                    totalOpenAmount: Math.floor(Math.random() * 100000),
                    totalCurrentAmount: Math.floor(Math.random() * 100000),
                    totalZeroToSix: Math.floor(Math.random() * 100000),
                    totalSevenToThirteen: Math.floor(Math.random() * 100000),
                    totalFourteenToTwenty: Math.floor(Math.random() * 100000),
                    totalTwentyOneToTwentySeven: Math.floor(Math.random() * 100000),
                    totalOverTwentyEight: Math.floor(Math.random() * 100000),
                    isRollup: false
                }
            }

            options.orgsMeta = [
                {
                    id: 'GtAoTHqGeLk9BR8iw',
                    name: 'Mariposa Corporate',
                    depth: 0,
                    parent: null,
                    childrenCount: 2
                },
                {
                    id: 'nTvbx24M2dbM9w6tu',
                    name: '321-Mariposa Local',
                    depth: 1,
                    parent: 'GtAoTHqGeLk9BR8iw',
                    childrenCount: 0
                },
                {
                    id: 'AsfEDAaFTAeCCJMLy',
                    name: '142-Mariposa Southside',
                    depth: 1,
                    parent: 'GtAoTHqGeLk9BR8iw',
                    childrenCount: 0
                }
            ]

            centerTotals = BillingReportAgingService.calculateCenterTotals(options);
            for (const key in options.centerTotals) {
                expect(centerTotals[key].totalOriginalAmount).toEqual(options.centerTotals[key].totalOriginalAmount);
                expect(centerTotals[key].totalNetOriginalAmount).toEqual(options.centerTotals[key].totalNetOriginalAmount);
                expect(centerTotals[key].totalOpenAmount).toEqual(options.centerTotals[key].totalOpenAmount);
                expect(centerTotals[key].totalCurrentAmount).toEqual(options.centerTotals[key].totalCurrentAmount);
                expect(centerTotals[key].totalZeroToSix).toEqual(options.centerTotals[key].totalZeroToSix);
                expect(centerTotals[key].totalSevenToThirteen).toEqual(options.centerTotals[key].totalSevenToThirteen);
                expect(centerTotals[key].totalFourteenToTwenty).toEqual(options.centerTotals[key].totalFourteenToTwenty);
                expect(centerTotals[key].totalTwentyOneToTwentySeven).toEqual(options.centerTotals[key].totalTwentyOneToTwentySeven);
                expect(centerTotals[key].totalOverTwentyEight).toEqual(options.centerTotals[key].totalOverTwentyEight);
                expect(centerTotals[key].isRollup).toEqual(options.centerTotals[key].isRollup);
                const index = options.orgsMeta.findIndex(org => org.name === key);
                expect(index).toBeGreaterThan(-1);
                expect(centerTotals[key].orgName).toEqual(options.orgsMeta[index].name);
                expect(centerTotals[key].displayParentId).toEqual(options.orgsMeta[index].parent);
                expect(centerTotals[key].displayId).toEqual(options.orgsMeta[index].id);
                expect(centerTotals[key].displayDepth).toEqual(options.orgsMeta[index].depth);
                expect(centerTotals[key].displayChildrenCount).toEqual(options.orgsMeta[index].childrenCount);
            }
        });
    });

    describe('reduceByProperty', () => {

        // Restore the original implementation after each test
        afterAll(async () => {
            jest.clearAllMocks();
        });

        it('Reduces an array of objects by a specified property and returns the sum of those values.', async () => {
            const randomArray = [
                { name: "Alice", age: 28, score: 90 },
                { name: "Bob", age: 22, score: 85 },
                { name: "Charlie", age: 24, score: 78 },
                { name: "David", age: 26, score: 92 },
                { name: "Eve", age: 29, score: 88 }
            ];
            expect(BillingReportAgingService.reduceByProperty(randomArray, 'age')).toEqual(129);
            expect(BillingReportAgingService.reduceByProperty(randomArray, 'score')).toEqual(433);
        });
    });

    describe('filterByBucketAndReduceByProperty', () => {

        // Restore the original implementation after each test
        afterAll(async () => {
            jest.clearAllMocks();
        });

        it('Filters an array of objects by a specified bucket and reduces the values of a specified property within the filtered array.', async () => {
            const randomArray = [
                { name: "Alice", age: 28, score: 90, amountBucket: 'female'},
                { name: "Bob", age: 22, score: 85, amountBucket: 'male'},
                { name: "Charlie", age: 24, score: 78, amountBucket: 'male'},
                { name: "David", age: 26, score: 92, amountBucket: 'male'},
                { name: "Eve", age: 29, score: 88, amountBucket: 'female'}
            ];
            expect(BillingReportAgingService.filterByBucketAndReduceByProperty(randomArray, 'age', 'male')).toEqual(72);
            expect(BillingReportAgingService.filterByBucketAndReduceByProperty(randomArray, 'score', 'female')).toEqual(178);
        });
    });

    describe('reduceCreditsByBucketAndProperty', () => {

        // Restore the original implementation after each test
        afterAll(async () => {
            jest.clearAllMocks();
        });

        it('Reduces the values of a specified property within an array of credit objects based on a specified bucket.', async () => {
            const randomArray = [
                { name: "Alice", age: 28, score: 90, amountBucket: 'female'},
                { name: "Bob", age: 22, score: 85, amountBucket: 'male'},
                { name: "Charlie", age: 24, score: 78, amountBucket: 'male'},
                { name: "David", age: 26, score: 92, amountBucket: 'male'},
                { name: "Eve", age: 29, score: 88, amountBucket: 'female'}
            ];
            expect(BillingReportAgingService.reduceCreditsByBucketAndProperty(randomArray, 'male', 'age')).toEqual(72);
            expect(BillingReportAgingService.reduceCreditsByBucketAndProperty(randomArray, 'female', 'score')).toEqual(178);
        });
    });

    describe('groupInvoicesByFamilyAndProcessOutput', () => {
        let relCursorMock, peopleCursorMock;

        // Setup mock collections
        beforeEach(() => {
            relCursorMock = Relationships.find;
            peopleCursorMock = People.find;
        });

        // Restore the original implementation after each test
        afterEach(() => {
            jest.clearAllMocks();
        });

        it('throws an error if arguments are not provided', async () => {
            relCursorMock.mockImplementation(() => {
                return ({
                    fetchAsync:jest.fn().mockImplementation(()=>[
                        { targetId: 'N5yn3DbpjCMdGgAJR', personId: 'cFT79Bw9Y85CfEeRv', relationshipType: 'family' },
                        { targetId: 'N5yn3DbpjCMdGgAJR', personId: 'agabuymN5Caxs8ruo', relationshipType: 'family' },
                    ])
                })
            });

            peopleCursorMock.mockImplementation(() => {
                return ({
                    fetchAsync:jest.fn().mockImplementation(()=>[
                        { _id: 'cFT79Bw9Y85CfEeRv', firstName: 'John', lastName: 'Doe' },
                        { _id: 'agabuymN5Caxs8ruo', firstName: 'Jane', lastName: 'Smith' },
                    ])
                })
            });

            const options = {
                groupedAndCalculatedInvoices: null,
                filteredCredits: null,
                intersectedPeopleIds: [],
                filterBuckets: null,
                scopedOrgs: null,
                orgsMap: null,
                centerTotals: null,
            };

            await expect(  async () => {  await BillingReportAgingService.groupInvoicesByFamilyAndProcessOutput(options) }).rejects.toThrowError(new Meteor.Error("invalid-params", "Invalid parameters passed to groupInvoicesByFamilyAndProcessOutput: ", "filterBuckets is required"));
            options.filterBuckets = 'test';
            await expect(  async () => {  await BillingReportAgingService.groupInvoicesByFamilyAndProcessOutput(options) }).rejects.toThrowError(new Meteor.Error("invalid-params", "Invalid parameters passed to groupInvoicesByFamilyAndProcessOutput: ", "scopedOrgs is required"));
            options.scopedOrgs = [];
            await expect(  async () => {  await BillingReportAgingService.groupInvoicesByFamilyAndProcessOutput(options) }).rejects.toThrowError(new Meteor.Error("invalid-params", "Invalid parameters passed to groupInvoicesByFamilyAndProcessOutput: ", "orgsMap is required"));
            options.orgsMap = {};
            await expect(  async () => {  await BillingReportAgingService.groupInvoicesByFamilyAndProcessOutput(options) }).rejects.toThrowError(new Meteor.Error("invalid-params", "Invalid parameters passed to groupInvoicesByFamilyAndProcessOutput: ", "centerTotals is required"));
            options.centerTotals = {};
            await expect(  async () => {  await BillingReportAgingService.groupInvoicesByFamilyAndProcessOutput(options) }).not.toThrowError();
        });

        it('groups invoices and processes output data based on intersectedPeopleIds', async () => {
            relCursorMock.mockImplementation(() => {
                return ({
                    fetchAsync:jest.fn().mockImplementation(()=>[
                        { targetId: 'N5yn3DbpjCMdGgAJR', personId: 'cFT79Bw9Y85CfEeRv', relationshipType: 'family' },
                        { targetId: 'N5yn3DbpjCMdGgAJR', personId: 'agabuymN5Caxs8ruo', relationshipType: 'family' },
                    ])
                })
            });

            peopleCursorMock.mockImplementation(() => {
                return ({
                    fetchAsync:jest.fn().mockImplementation(()=>[
                        { _id: 'cFT79Bw9Y85CfEeRv', firstName: 'John', lastName: 'Doe' },
                        { _id: 'agabuymN5Caxs8ruo', firstName: 'Jane', lastName: 'Smith' },
                    ])
                })
            });

            const options = {
                groupedAndCalculatedInvoices: [
                    {
                        personId: 'N5yn3DbpjCMdGgAJR',
                        totalOriginalAmount: 100,
                        totalOpenAmount: 50,
                        totalCurrentAmount: 25,
                    },
                ],
                filteredCredits: [
                    {
                        paidByPersonId: 'agabuymN5Caxs8ruo',
                        originalAmount: 40,
                        balancePointInTime: 20,
                        amountBucket: '0-6',
                    },
                ],
                intersectedPeopleIds: ['N5yn3DbpjCMdGgAJR'], // Only child IDs
                filterBuckets: 'family',
                scopedOrgs: [{ _id: 'nTvbx24M2dbM9w6tu', name: 'Mariposa Local' }],
                orgsMap: { nTvbx24M2dbM9w6tu: ['321-Mariposa Local'] },
                centerTotals: {
                    '321-Mariposa Local': {
                        totalOriginalAmount: 0,
                        totalNetOriginalAmount: 0,
                        totalOpenAmount: 0,
                        totalCurrentAmount: 0,
                        totalZeroToSix: 0,
                        totalSevenToThirteen: 0,
                        totalFourteenToTwenty: 0,
                        totalTwentyOneToTwentySeven: 0,
                        totalOverTwentyEight: 0,
                        isRollup: false,
                    },
                },
            };

            const result = await BillingReportAgingService.groupInvoicesByFamilyAndProcessOutput(options);

            expect(result).toHaveLength(1); // Expect one family group
            const grouping = result[0];

            // Validate family grouping
            expect(grouping.familyMembers).toEqual([
                { _id: 'agabuymN5Caxs8ruo', firstName: 'Jane', lastName: 'Smith' },
                { _id: 'cFT79Bw9Y85CfEeRv', firstName: 'John', lastName: 'Doe' },
            ]);

            expect(grouping.groupedInvoices).toEqual([
                {
                    personId: 'N5yn3DbpjCMdGgAJR',
                    totalOriginalAmount: 100,
                    totalOpenAmount: 50,
                    totalCurrentAmount: 25,
                },
            ]);

            expect(grouping.familyCredits).toEqual([
                {
                    paidByPersonId: 'agabuymN5Caxs8ruo',
                    originalAmount: 40,
                    balancePointInTime: 20,
                    amountBucket: '0-6',
                },
            ]);

            expect(grouping.familyName).toBe('Doe, John; Smith, Jane');

            // Validate totals
            expect(grouping.totalNetOriginalAmount).toBe(140); // 100 (invoices) + 40 (credits)
            expect(grouping.totalNetOpenAmount).toBe(70); // 50 (invoices) + 20 (credits)
            expect(grouping.totalNetCurrentAmount).toBe(25); // Only invoices contribute to this bucket
            expect(grouping.totalNetZeroToSix).toBe(20); // Only credits contribute to this bucket
        });
    });

    describe('groupInvoicesByFamily', () => {
        it('groups intersectedPeopleIds into families based on relationships', () => {
            const intersectedPeopleIds = ['child1', 'child2', 'child3'];
            const relationshipsMap = {
                child1: [{ personId: 'parent1' }, { personId: 'parent2' }],
                child2: [{ personId: 'parent1' }, { personId: 'parent2' }],
                child3: [{ personId: 'parent3' }],
            };

            const result = BillingReportAgingService.groupInvoicesByFamily(intersectedPeopleIds, relationshipsMap);

            expect(result).toEqual({
                'parent1|parent2': [
                    {
                        childId: 'child1',
                        familyKey: 'parent1|parent2',
                        familyMemberIds: ['parent1', 'parent2'],
                    },
                    {
                        childId: 'child2',
                        familyKey: 'parent1|parent2',
                        familyMemberIds: ['parent1', 'parent2'],
                    },
                ],
                'parent3': [
                    {
                        childId: 'child3',
                        familyKey: 'parent3',
                        familyMemberIds: ['parent3'],
                    },
                ],
            });
        });

        it('returns an empty object if intersectedPeopleIds is empty', () => {
            const intersectedPeopleIds = [];
            const relationshipsMap = {
                child1: [{ personId: 'parent1' }],
            };

            const result = BillingReportAgingService.groupInvoicesByFamily(intersectedPeopleIds, relationshipsMap);

            expect(result).toEqual({});
        });

        it('handles children with no family relationships', () => {
            const intersectedPeopleIds = ['child1', 'child2'];
            const relationshipsMap = {
                child1: [],
                child2: [{ personId: 'parent1' }],
            };

            const result = BillingReportAgingService.groupInvoicesByFamily(intersectedPeopleIds, relationshipsMap);

            expect(result).toEqual({
                '': [
                    {
                        childId: 'child1',
                        familyKey: '',
                        familyMemberIds: [],
                    },
                ],
                'parent1': [
                    {
                        childId: 'child2',
                        familyKey: 'parent1',
                        familyMemberIds: ['parent1'],
                    },
                ],
            });
        });

        it('handles duplicate family members and sorts family IDs', () => {
            const intersectedPeopleIds = ['child1', 'child2'];
            const relationshipsMap = {
                child1: [{ personId: 'parent2' }, { personId: 'parent1' }],
                child2: [{ personId: 'parent1' }, { personId: 'parent1' }], // Duplicate parent1
            };

            const result = BillingReportAgingService.groupInvoicesByFamily(intersectedPeopleIds, relationshipsMap);

            expect(result).toEqual({
                'parent1|parent2': [
                    {
                        childId: 'child1',
                        familyKey: 'parent1|parent2',
                        familyMemberIds: ['parent1', 'parent2'],
                    },
                ],
                'parent1': [
                    {
                        childId: 'child2',
                        familyKey: 'parent1',
                        familyMemberIds: ['parent1'],
                    },
                ],
            });
        });
    });

    describe('groupInvoicesByIndividualAndProcessOutput', () => {

        // Restore the original implementation after each test
        afterAll(async () => {
            jest.clearAllMocks();
        });

        it ('throws an error if arguments are not provided', async () => {
            const options = {
                filteredCredits: null,
                scopedOrgs: null,
                orgsMap: null,
                filterBuckets: null,
                groupedAndCalculatedInvoices: null,
                centerTotals: null
            }

            expect(  async () => {  await BillingReportAgingService.groupInvoicesByIndividualAndProcessOutput(options) }).rejects.toThrowError(new Meteor.Error("invalid-params", "Invalid parameters passed to groupInvoicesByIndividualAndProcessOutput: ", "scopedOrgs is required"));
            options.scopedOrgs = [];
            expect(  async () => {  await BillingReportAgingService.groupInvoicesByIndividualAndProcessOutput(options) }).rejects.toThrowError(new Meteor.Error("invalid-params", "Invalid parameters passed to groupInvoicesByIndividualAndProcessOutput: ", "orgsMap is required"));
            options.orgsMap = {};
            expect(  async () => {  await BillingReportAgingService.groupInvoicesByIndividualAndProcessOutput(options) }).rejects.toThrowError(new Meteor.Error("invalid-params", "Invalid parameters passed to groupInvoicesByIndividualAndProcessOutput: ", "filterBuckets is required"));
            options.filterBuckets = 'family';
            expect(  async () => {  await BillingReportAgingService.groupInvoicesByIndividualAndProcessOutput(options) }).rejects.toThrowError(new Meteor.Error("invalid-params", "Invalid parameters passed to groupInvoicesByIndividualAndProcessOutput: ", "centerTotals is required"));       
            options.centerTotals = {};
            await expect(BillingReportAgingService.groupInvoicesByIndividualAndProcessOutput(options)).resolves.not.toThrowError();
        });

        it('Groups invoices by individual and processes the output data based on various parameters.', async () => {
            const findOneAsyncMock = Invoices.findOneAsync;
            findOneAsyncMock.mockImplementation((arg) => {
                return singleOrgOpenCreditPaidByPeople.find((person) => person._id === arg._id);
            });
        
            const options = {
                filteredCredits: JSON.parse(JSON.stringify(openCredits.openCredits)),
                scopedOrgs: currentOrg,
                orgsMap: { nTvbx24M2dbM9w6tu: [] },
                centerTotals: {
                    '321-Mariposa Local': {
                        totalOriginalAmount: 0,
                        totalNetOriginalAmount: 0,
                        totalOpenAmount: 0,
                        totalCurrentAmount: 0,
                        totalZeroToSix: 0,
                        totalSevenToThirteen: 0,
                        totalFourteenToTwenty: 0,
                        totalTwentyOneToTwentySeven: 0,
                        totalOverTwentyEight: 0,
                        isRollup: false
                    }
                },
                filterBuckets: 'family',
                groupedAndCalculatedInvoices: JSON.parse(JSON.stringify(groupedAndCalcuatedInvoices.groupedAndCalculatedInvoices)),
            };
        
            // Call the function and await its completion
            await BillingReportAgingService.groupInvoicesByIndividualAndProcessOutput(options);
        
            const expected = {
                totalOriginalAmount: 0,
                totalNetOriginalAmount: 0,
                totalOpenAmount: 0,
                totalCurrentAmount: 0,
                totalZeroToSix: 0,
                totalSevenToThirteen: 0,
                totalFourteenToTwenty: 0,
                totalTwentyOneToTwentySeven: 0,
                totalOverTwentyEight: 0,
                isRollup: false
            };
        
            options.groupedAndCalculatedInvoices.forEach((grouping) => {
                expected.totalOriginalAmount += grouping.totalOriginalAmount;
                expected.totalNetOriginalAmount += grouping.totalNetOriginalAmount ? grouping.totalNetOriginalAmount : grouping.totalOriginalAmount;
                expected.totalOpenAmount += grouping.totalOpenAmount;
                expected.totalCurrentAmount += grouping.totalCurrentAmount;
                expected.totalZeroToSix += grouping.totalZeroToSix;
                expected.totalSevenToThirteen += grouping.totalSevenToThirteen;
                expected.totalFourteenToTwenty += grouping.totalFourteenToTwenty;
                expected.totalTwentyOneToTwentySeven += grouping.totalTwentyOneToTwentySeven;
                expected.totalOverTwentyEight += grouping.totalOverTwentyEight;
            });
        
            expect(options.centerTotals['321-Mariposa Local']).toEqual(expected);
        });
    });

    describe('sortBatchedInvoicesByCreatedAt', () => {
        it('should sort invoices by createdAt in descending order', () => {
            const invoices = [
                { createdAt: 1693545600000, invoiceNumber: '1' }, // 2023-09-01T00:00:00Z
                { createdAt: 1693718400000, invoiceNumber: '2' }, // 2023-09-03T00:00:00Z
                { createdAt: 1693632000000, invoiceNumber: '3' }, // 2023-09-02T00:00:00Z
            ];

            const sortedInvoices = BillingReportAgingService.sortBatchedInvoicesByCreatedAt(invoices);

            expect(sortedInvoices).toEqual([
                { createdAt: 1693718400000, invoiceNumber: '2' },
                { createdAt: 1693632000000, invoiceNumber: '3' },
                { createdAt: 1693545600000, invoiceNumber: '1' },
            ]);
        });

        it('should handle an empty array gracefully', () => {
            const invoices = [];
            const sortedInvoices = BillingReportAgingService.sortBatchedInvoicesByCreatedAt(invoices);
            expect(sortedInvoices).toEqual([]);
        });

        it('should handle invoices with identical createdAt timestamps', () => {
            const invoices = [
                { createdAt: 1693545600000, invoiceNumber: '1' },
                { createdAt: 1693545600000, invoiceNumber: '2' },
            ];

            const sortedInvoices = BillingReportAgingService.sortBatchedInvoicesByCreatedAt(invoices);

            // Order remains stable for identical timestamps
            expect(sortedInvoices).toEqual([
                { createdAt: 1693545600000, invoiceNumber: '1' },
                { createdAt: 1693545600000, invoiceNumber: '2' },
            ]);
        });

        it('should correctly handle mixed positive and negative timestamps', () => {
            const invoices = [
                { createdAt: 0, invoiceNumber: '1' }, // Unix epoch
                { createdAt: 1693718400000, invoiceNumber: '2' }, // Future date
                { createdAt: -62167219200000, invoiceNumber: '3' }, // 0000-01-01T00:00:00Z
            ];

            const sortedInvoices = BillingReportAgingService.sortBatchedInvoicesByCreatedAt(invoices);

            expect(sortedInvoices).toEqual([
                { createdAt: 1693718400000, invoiceNumber: '2' },
                { createdAt: 0, invoiceNumber: '1' },
                { createdAt: -62167219200000, invoiceNumber: '3' },
            ]);
        });
    });

    describe('BillingReportAgingService caching', () => {
        afterEach(() => {
            BillingReportAgingService.clearCache();
        });

        it('should return cached value if available and within TTL', () => {
            const cacheKey = 'testKey';
            const value = { data: 'cachedValue' };
            BillingReportAgingService.setCache(cacheKey, value);

            expect(BillingReportAgingService.getCache(cacheKey)).toEqual(value);
        });

        it('should return null if cache entry is stale', () => {
            const CACHE_TTL = 5 * 60 * 1000;
            const cacheKey = 'staleKey';
            const value = { data: 'staleValue' };
            jest.spyOn(Date, 'now').mockReturnValue(1000); // Mock current timestamp
            BillingReportAgingService.setCache(cacheKey, value);

            jest.spyOn(Date, 'now').mockReturnValue(1000 + CACHE_TTL + 1); // Mock time beyond TTL
            expect(BillingReportAgingService.getCache(cacheKey)).toBeNull();
        });
    });

    describe('getFamilyInvoices', () => {
        const mockInvoices = [
            { _id: 'invoice1', orgId: 'org1', createdAt: 1695873599999 },
            { _id: 'invoice2', orgId: 'org2', createdAt: 1695873599988 },
        ];

        beforeEach(() => {
            jest.clearAllMocks();
            const cursorMock = Invoices.find;
            cursorMock.mockImplementation(() => {
                return ({
                    fetchAsync:jest.fn().mockImplementation(()=>mockInvoices)
                })
            });
            jest.spyOn(BillingReportAgingService, 'sortBatchedInvoicesByCreatedAt').mockImplementation((invoices) => invoices.sort((a, b) => b.createdAt - a.createdAt));
        });

        afterEach(() => {
            jest.restoreAllMocks();
        });

        it('throws an error if query is not provided', async () => {
            await expect(BillingReportAgingService.getFamilyInvoices(null)).rejects.toThrowError(new Meteor.Error("invalid-params", "Invalid parameters passed to getFamilyInvoices: ", "query is required"));
        });

        it('retrieves family invoices for a single orgId', async () => {
            const query = { orgId: 'org1' };

            const result = await BillingReportAgingService.getFamilyInvoices(query);

            expect(Invoices.find).toHaveBeenCalledTimes(1);
            expect(result).toEqual(mockInvoices.sort((a, b) => b.createdAt - a.createdAt));
        });

        it('retrieves family invoices for multiple orgIds in batches', async () => {
            const query = { orgId: { $in: ['org1', 'org2', 'org3', 'org4'] } };

            const result = await BillingReportAgingService.getFamilyInvoices(query);

            expect(Invoices.find).toHaveBeenCalledTimes(1); // Adjust based on batch size
            expect(result).toEqual(mockInvoices.sort((a, b) => b.createdAt - a.createdAt));
        });
    });

    describe('getPayerInvoices', () => {
        const mockInvoices = [
            { _id: 'invoice1', orgId: 'org1', openAmount: 100 },
            { _id: 'invoice2', orgId: 'org2', openAmount: 200 },
        ];

        beforeEach(() => {
            jest.clearAllMocks();
            const aggregationMock = Invoices.aggregate
            aggregationMock.mockImplementation(() => ({
                toArray: jest.fn().mockResolvedValue(mockInvoices),
            }));
            jest.spyOn(BillingReportAgingService, 'processPayerInvoice').mockImplementation(invoice => ({
                ...invoice,
                processed: true,
            }));
        });

        afterEach(() => {
            jest.restoreAllMocks();
        });

        it('processes payer invoices with a single orgId', async () => {
            const payerQueryMatchPart = { orgId: 'org1' };
            const reportDateStamp = 1695873599999;

            const result = await BillingReportAgingService.getPayerInvoices(payerQueryMatchPart, reportDateStamp);

            expect(Invoices.aggregate).toHaveBeenCalledTimes(1);
            expect(BillingReportAgingService.processPayerInvoice).toHaveBeenCalledTimes(mockInvoices.length);
            expect(result).toEqual(mockInvoices.map(invoice => ({ ...invoice, processed: true })));
        });

        it('processes payer invoices with multiple orgIds in batches', async () => {
            const payerQueryMatchPart = { orgId: { $in: ['org1', 'org2', 'org3', 'org4'] } };
            const reportDateStamp = 1695873599999;

            const result = await BillingReportAgingService.getPayerInvoices(payerQueryMatchPart, reportDateStamp);

            expect(Invoices.aggregate).toHaveBeenCalledTimes(1); // Adjust based on batch size
            expect(BillingReportAgingService.processPayerInvoice).toHaveBeenCalledTimes(mockInvoices.length);
            expect(result).toEqual(mockInvoices.map(invoice => ({ ...invoice, processed: true })));
        });
    });

    describe('processPayerInvoice', () => {
        beforeEach(() => {
            ;
        });
        afterEach(() => {   
            jest.clearAllMocks();
        });
        it('calculates the original amount based on open payer amounts and allocation entries', () => {
            const invoice = {
                openPayerAmounts: { 'payer1': 100 },
                allocationEntries: [
                    { source: 'payer1', destination: 'bad-debt', amount: 20 },
                    { source: 'payer1', destination: 'other', amount: 10 },
                    { source: 'payer2', destination: 'bad-debt', amount: 15 },
                ],
                payerName: 'payer1'
            };

            const result = BillingReportAgingService.processPayerInvoice(invoice);

            expect(result.originalAmount).toEqual(120); // 100 + 20
        });

        it('handles invoices with no allocation entries gracefully', () => {
            const invoice = {
                openPayerAmounts: { 'payer1': 50 },
                payerName: 'payer1',
            };

            const result = BillingReportAgingService.processPayerInvoice(invoice);

            expect(result.originalAmount).toEqual(50); // Only openPayerAmounts
        });

        it('returns an invoice with originalAmount set to 0 if openPayerAmounts is undefined for payerName', () => {
            const invoice = {
                openPayerAmounts: {},
                allocationEntries: [
                    { source: 'payer1', destination: 'bad-debt', amount: 10 },
                ],
                payerName: 'payer1',
            };

            const result = BillingReportAgingService.processPayerInvoice(invoice);

            expect(result.originalAmount).toEqual(10); // Only allocation amount
        });

        it('returns 0 for originalAmount when no relevant data is present', () => {
            const invoice = {payerName: 'payer1',};

            const result = BillingReportAgingService.processPayerInvoice(invoice);

            expect(result.originalAmount).toEqual(0); // No data available for calculations
        });
    });

    describe('getPayerPipeline', () => {
        it('generates the correct pipeline for valid input', () => {
            const payerQueryMatchPart = { orgId: 'org123', createdAt: { $lt: 1695873599999 } };
            const reportDateStamp = 1695873599999;

            const result = BillingReportAgingService.getPayerPipeline(payerQueryMatchPart, reportDateStamp);

            expect(result).toBeInstanceOf(Array);
            expect(result.length).toBeGreaterThan(0);
            expect(result[0]).toEqual({ "$match": payerQueryMatchPart });

            const facetStage = result.find(stage => stage["$facet"]);
            expect(facetStage).toBeDefined();
            expect(facetStage["$facet"]).toHaveProperty("payerAmounts");
            expect(facetStage["$facet"]).toHaveProperty("payerCredits");
            expect(facetStage["$facet"]).toHaveProperty("payerAllocations");

            const matchStage = result.find(stage => stage["$match"] && stage["$match"].amount);
            expect(matchStage["$match"].amount).toEqual({ "$gte": 0.01 });
        });

        it('handles empty payerQueryMatchPart gracefully', () => {
            const payerQueryMatchPart = {};
            const reportDateStamp = 1695873599999;

            const result = BillingReportAgingService.getPayerPipeline(payerQueryMatchPart, reportDateStamp);

            expect(result[0]["$match"]).toEqual(payerQueryMatchPart);
            expect(result).toBeInstanceOf(Array);
        });

        it('ensures the pipeline stages contain the correct keys', () => {
            const payerQueryMatchPart = { orgId: 'org123', createdAt: { $lt: 1695873599999 } };
            const reportDateStamp = 1695873599999;

            const result = BillingReportAgingService.getPayerPipeline(payerQueryMatchPart, reportDateStamp);

            const lookupStage = result.find(stage => stage["$lookup"]);
            expect(lookupStage).toBeDefined();
            expect(lookupStage["$lookup"]).toHaveProperty("from", "invoices");
            expect(lookupStage["$lookup"]).toHaveProperty("localField", "_id.invoiceId");
            expect(lookupStage["$lookup"]).toHaveProperty("foreignField", "_id");
        });
    });

    describe('getIsInvoiceValidForCounting', () => {
        const reportDateStamp = 1695873599999;

        it('returns true if invoice is not voided and has no minPeriodDate', () => {
            const invoice = {
                voidedAt: null,
                minPeriodDate: null,
            };

            const result = BillingReportAgingService.getIsInvoiceValidForCounting(invoice, reportDateStamp);
            expect(result).toBe(true);
        });

        it('returns true if invoice is voided after the report date and has no minPeriodDate', () => {
            const invoice = {
                voidedAt: reportDateStamp + 1000,
                minPeriodDate: null,
            };

            const result = BillingReportAgingService.getIsInvoiceValidForCounting(invoice, reportDateStamp);
            expect(result).toBe(true);
        });

        it('returns true if minPeriodDate is after the report date and invoice is not voided', () => {
            const invoice = {
                voidedAt: null,
                minPeriodDate: reportDateStamp + 1000,
            };

            const result = BillingReportAgingService.getIsInvoiceValidForCounting(invoice, reportDateStamp);
            expect(result).toBe(true);
        });

        it('returns false if invoice is voided before or on the report date', () => {
            const invoice = {
                voidedAt: reportDateStamp,
                minPeriodDate: null,
            };

            const result = BillingReportAgingService.getIsInvoiceValidForCounting(invoice, reportDateStamp);
            expect(result).toBe(false);
        });

        it('returns false if minPeriodDate is before or on the report date', () => {
            const invoice = {
                voidedAt: null,
                minPeriodDate: reportDateStamp - 1000,
            };

            const result = BillingReportAgingService.getIsInvoiceValidForCounting(invoice, reportDateStamp);
            expect(result).toBe(false);
        });

        it('returns false if both conditions are invalid (voided and minPeriodDate are before or on the report date)', () => {
            const invoice = {
                voidedAt: reportDateStamp - 1000,
                minPeriodDate: reportDateStamp - 1000,
            };

            const result = BillingReportAgingService.getIsInvoiceValidForCounting(invoice, reportDateStamp);
            expect(result).toBe(false);
        });
    });

    describe('filterInvoiceCreditsByReportDate', () => {
        beforeAll(() => {
            jest.clearAllMocks();
        });
        const reportDateStamp = 1695873599999;

        it('returns an empty array if there are no credits', () => {
            const invoice = { credits: [] };

            const result = BillingReportAgingService.filterInvoiceCreditsByReportDate(invoice, reportDateStamp);
            expect(result).toEqual([]);
        });

        it('filters credits with no creditPayerSource', () => {
            const invoice = {
                credits: [
                    { creditPayerSource: null, createdAt: reportDateStamp - 1000 },
                    { creditPayerSource: "payer1", createdAt: reportDateStamp - 2000 },
                ],
            };

            const result = BillingReportAgingService.filterInvoiceCreditsByReportDate(invoice, reportDateStamp);
            expect(result).toEqual([invoice.credits[0]]);
        });

        it('filters credits voided before the report date', () => {
            const invoice = {
                credits: [
                    { creditPayerSource: null, voidedAt: reportDateStamp + 1000, createdAt: reportDateStamp - 1000 },
                    { creditPayerSource: null, voidedAt: reportDateStamp - 2000, createdAt: reportDateStamp - 3000 },
                ],
            };

            const result = BillingReportAgingService.filterInvoiceCreditsByReportDate(invoice, reportDateStamp);
            expect(result).toEqual([invoice.credits[0]]);
        });

        it('filters credits created after the report date', () => {
            const invoice = {
                credits: [
                    { creditPayerSource: null, createdAt: reportDateStamp - 1000 },
                    { creditPayerSource: null, createdAt: reportDateStamp + 1000 },
                ],
            };

            const result = BillingReportAgingService.filterInvoiceCreditsByReportDate(invoice, reportDateStamp);
            expect(result).toEqual([invoice.credits[0]]);
        });

        it('returns credits that meet all criteria', () => {
            const invoice = {
                credits: [
                    { creditPayerSource: null, voidedAt: null, createdAt: reportDateStamp - 1000 },
                    { creditPayerSource: null, voidedAt: reportDateStamp + 1000, createdAt: reportDateStamp - 2000 },
                ],
            };

            const result = BillingReportAgingService.filterInvoiceCreditsByReportDate(invoice, reportDateStamp);
            expect(result).toEqual(invoice.credits);
        });
    });

    describe('getCreditAmountsFromInvoice', () => {
        const reportDateStamp = 1695873599999;

        it('returns 0 if the invoice has no credits', () => {
            const invoice = { credits: [] };

            const result = BillingReportAgingService.getCreditAmountsFromInvoice(invoice, reportDateStamp);
            expect(result).toBe(0);
        });

        it('calculates total for credits with no voidedAt', () => {
            const invoice = {
                credits: [
                    { type: "payment", amount: 100, createdAt: reportDateStamp - 1000 },
                    { type: "refund", amount: 50, createdAt: reportDateStamp - 2000 },
                ],
            };

            jest.spyOn(BillingReportAgingService, 'filterInvoiceCreditsByReportDate').mockReturnValue(invoice.credits);

            const result = BillingReportAgingService.getCreditAmountsFromInvoice(invoice, reportDateStamp);
            expect(result).toBe(50); // 100 - 50 (refund multiplier)
        });

        it('uses voidedAmount if voidedAt is present', () => {
            const invoice = {
                credits: [
                    { type: "payment", amount: 100, voidedAt: reportDateStamp + 1000, voidedAmount: 80, createdAt: reportDateStamp - 1000 },
                    { type: "chargeback", amount: 50, voidedAt: reportDateStamp - 2000, voidedAmount: 30, createdAt: reportDateStamp - 2000 },
                ],
            };

            jest.spyOn(BillingReportAgingService, 'filterInvoiceCreditsByReportDate').mockReturnValue(invoice.credits);

            const result = BillingReportAgingService.getCreditAmountsFromInvoice(invoice, reportDateStamp);
            expect(result).toBe(50); // 80 (voidedAmount) - 30 (chargeback voidedAmount, multiplier)
        });

        it('applies correct multipliers for credit types', () => {
            const invoice = {
                credits: [
                    { type: "refund", amount: 40, createdAt: reportDateStamp - 1000 },
                    { type: "chargeback", amount: 30, createdAt: reportDateStamp - 2000 },
                    { type: "payment", amount: 100, createdAt: reportDateStamp - 3000 },
                ],
            };

            jest.spyOn(BillingReportAgingService, 'filterInvoiceCreditsByReportDate').mockReturnValue(invoice.credits);

            const result = BillingReportAgingService.getCreditAmountsFromInvoice(invoice, reportDateStamp);
            expect(result).toBe(30); // -40 (refund) -30 (chargeback) +100
        });

        it('only includes credits filtered by report date', () => {
            const invoice = {
                credits: [
                    { type: "payment", amount: 100, createdAt: reportDateStamp - 1000 },
                    { type: "refund", amount: 50, createdAt: reportDateStamp - 2000 },
                    { type: "chargeback", amount: 30, createdAt: reportDateStamp + 1000 },
                ],
            };

            jest.spyOn(BillingReportAgingService, 'filterInvoiceCreditsByReportDate').mockReturnValue(invoice.credits.slice(0, 2));

            const result = BillingReportAgingService.getCreditAmountsFromInvoice(invoice, reportDateStamp);
            expect(result).toBe(50); // 100 - 50 (last credit excluded)
        });
    });

    describe('getFamilyBuckets', () => {
        it('returns "current" for duration <= 0', () => {
            expect(BillingReportAgingService.getFamilyBuckets(0)).toBe("current");
            expect(BillingReportAgingService.getFamilyBuckets(-5)).toBe("current");
        });

        it('returns "0-6" for duration > 0 and < 7', () => {
            expect(BillingReportAgingService.getFamilyBuckets(1)).toBe("0-6");
            expect(BillingReportAgingService.getFamilyBuckets(6)).toBe("0-6");
        });

        it('returns "7-13" for duration >= 7 and < 14', () => {
            expect(BillingReportAgingService.getFamilyBuckets(7)).toBe("7-13");
            expect(BillingReportAgingService.getFamilyBuckets(13)).toBe("7-13");
        });

        it('returns "14-20" for duration >= 14 and < 21', () => {
            expect(BillingReportAgingService.getFamilyBuckets(14)).toBe("14-20");
            expect(BillingReportAgingService.getFamilyBuckets(20)).toBe("14-20");
        });

        it('returns "21-27" for duration >= 21 and < 28', () => {
            expect(BillingReportAgingService.getFamilyBuckets(21)).toBe("21-27");
            expect(BillingReportAgingService.getFamilyBuckets(27)).toBe("21-27");
        });

        it('returns "over28" for duration >= 28', () => {
            expect(BillingReportAgingService.getFamilyBuckets(28)).toBe("over28");
            expect(BillingReportAgingService.getFamilyBuckets(100)).toBe("over28");
        });
    });

    describe('getPayerBuckets', () => {
        it('returns "current" for duration <= 0', () => {
            expect(BillingReportAgingService.getPayerBuckets(0)).toBe("current");
            expect(BillingReportAgingService.getPayerBuckets(-5)).toBe("current");
        });

        it('returns "1-30" for duration > 0 and < 31', () => {
            expect(BillingReportAgingService.getPayerBuckets(1)).toBe("1-30");
            expect(BillingReportAgingService.getPayerBuckets(30)).toBe("1-30");
        });

        it('returns "31-60" for duration >= 31 and < 61', () => {
            expect(BillingReportAgingService.getPayerBuckets(31)).toBe("31-60");
            expect(BillingReportAgingService.getPayerBuckets(60)).toBe("31-60");
        });

        it('returns "61-90" for duration >= 61 and < 91', () => {
            expect(BillingReportAgingService.getPayerBuckets(61)).toBe("61-90");
            expect(BillingReportAgingService.getPayerBuckets(90)).toBe("61-90");
        });

        it('returns "91-120" for duration >= 91 and < 121', () => {
            expect(BillingReportAgingService.getPayerBuckets(91)).toBe("91-120");
            expect(BillingReportAgingService.getPayerBuckets(120)).toBe("91-120");
        });

        it('returns "over121" for duration >= 121', () => {
            expect(BillingReportAgingService.getPayerBuckets(121)).toBe("over121");
            expect(BillingReportAgingService.getPayerBuckets(200)).toBe("over121");
        });
    });

    describe('getAmountBucketByDurationAndFilter', () => {
        beforeEach(() => {
            jest.spyOn(BillingReportAgingService, 'getFamilyBuckets');
            jest.spyOn(BillingReportAgingService, 'getPayerBuckets');
        });

        afterEach(() => {
            jest.restoreAllMocks();
        });

        it('calls getFamilyBuckets for "family" filter', () => {
            const duration = 15;
            const result = BillingReportAgingService.getAmountBucketByDurationAndFilter(duration, 'family');
            expect(BillingReportAgingService.getFamilyBuckets).toHaveBeenCalledWith(duration);
            expect(result).toBe("14-20"); // Assuming 15 falls into "14-20"
        });

        it('calls getPayerBuckets for "payer" filter', () => {
            const duration = 45;
            const result = BillingReportAgingService.getAmountBucketByDurationAndFilter(duration, 'payer');
            expect(BillingReportAgingService.getPayerBuckets).toHaveBeenCalledWith(duration);
            expect(result).toBe("31-60"); // Assuming 45 falls into "31-60"
        });
    });

    describe('generateCreditMemoAggregationQuery', () => {
        it('generates a valid aggregation pipeline for credit memo processing', () => {
            const orgIds = ['org1', 'org2'];
            const creditMemoIds = ['memo1', 'memo2'];
            const reportDateStamp = 1695873600000; // Example report date

            const pipeline = BillingReportAgingService.generateCreditMemoAggregationQuery(orgIds, creditMemoIds, reportDateStamp);

            expect(pipeline).toEqual([
                {
                    $match: {
                        orgId: { $in: orgIds },
                        "credits.creditMemoId": { $in: creditMemoIds }
                    }
                },
                { $unwind: "$credits" },
                {
                    $match: {
                        $and: [
                            { "credits.creditMemoId": { $in: creditMemoIds } },
                            { "credits.createdAt": { $lte: reportDateStamp } },
                            {
                                $or: [
                                    { "credits.voidedAt": { $exists: false } },
                                    { "credits.voidedAt": { $gt: reportDateStamp } }
                                ]
                            }
                        ]
                    }
                },
                {
                    $unwind: {
                        path: "$credits.adjustments",
                        preserveNullAndEmptyArrays: true
                    }
                },
                {
                    $match: {
                        $or: [
                            { "credits.adjustments.adjustedAt": { $lte: reportDateStamp } },
                            { "credits.adjustments": { $exists: false } }
                        ]
                    }
                },
                {
                    $group: {
                        _id: "$credits.creditMemoId",
                        totalAmount: {
                            $sum: {
                                $cond: [
                                    {
                                        $and: [
                                            { $gt: ["$credits.voidedAt", reportDateStamp] },
                                            { $ifNull: ["$credits.voidedAmount", false] }
                                        ]
                                    },
                                    "$credits.voidedAmount",
                                    "$credits.amount"
                                ]
                            }
                        },
                        totalAdjustedAmount: {
                            $sum: {
                                $ifNull: ["$credits.adjustments.adjustedAmount", 0]
                            }
                        }
                    }
                },
                {
                    $project: {
                        creditMemoId: "$_id",
                        totalReducedAmount: { $add: ["$totalAmount", "$totalAdjustedAmount"] }
                    }
                }
            ]);
        });
    });

    describe('groupCreditMemosAndReduceCreditAmount', () => {
        it('returns reduced credit amounts when valid inputs are provided', async () => {
            const peopleWithOpenCredits = [
                {
                    orgId: 'org1',
                    billing: {
                        creditMemos: [{ _id: 'memo1' }, { _id: 'memo2' }]
                    }
                },
                {
                    orgId: 'org2',
                    billing: {
                        creditMemos: [{ _id: 'memo3' }]
                    }
                }
            ];

            const reportDateStamp = 1695873600000;

            const mockPipelineResult = [
                { creditMemoId: 'memo1', totalReducedAmount: 100 },
                { creditMemoId: 'memo2', totalReducedAmount: 200 },
                { creditMemoId: 'memo3', totalReducedAmount: 300 }
            ];

            const aggregationMock = Invoices.aggregate
            aggregationMock.mockImplementation(() => ({
                toArray: jest.fn().mockResolvedValue(mockPipelineResult),
            }));

            const result = await BillingReportAgingService.groupCreditMemosAndReduceCreditAmount(peopleWithOpenCredits, reportDateStamp);

            expect(result).toEqual(mockPipelineResult);
        });

        it('returns an empty array if no credit memos are found', async () => {
            const peopleWithOpenCredits = [];
            const reportDateStamp = 1695873600000;

            const aggregationMock = Invoices.aggregate
            aggregationMock.mockImplementation(() => ({
                toArray: jest.fn().mockResolvedValue([]),
            }));

            const result = await BillingReportAgingService.groupCreditMemosAndReduceCreditAmount(peopleWithOpenCredits, reportDateStamp);

            expect(result).toEqual([]);
            expect(aggregationMock).toHaveBeenCalled();
        });

        it('handles missing billing or credit memos gracefully', async () => {
            const peopleWithOpenCredits = [
                { orgId: 'org1', billing: null },
                { orgId: 'org2', billing: { creditMemos: null } }
            ];
            const reportDateStamp = 1695873600000;

            const aggregationMock = Invoices.aggregate
            aggregationMock.mockImplementation(() => ({
                toArray: jest.fn().mockResolvedValue([]),
            }));

            const result = await BillingReportAgingService.groupCreditMemosAndReduceCreditAmount(peopleWithOpenCredits, reportDateStamp);

            expect(result).toEqual([]);
            expect(aggregationMock).toHaveBeenCalledWith(expect.any(Array));
        });
    });

    describe('getCreditMemoBalancePointInTime', () => {
        it('returns openAmount if report date equals todayStamp', () => {
            const creditMemo = { openAmount: 100 };
            const reportDateStamp = 1695873600000;
            const todayStamp = 1695873600000;

            const result = BillingReportAgingService.getCreditMemoBalancePointInTime(creditMemo, reportDateStamp, todayStamp, null);

            expect(result).toBe(100);
        });

        it('calculates balance correctly when report date is before today', () => {
            const creditMemo = { originalAmount: 200 };
            const reportDateStamp = 1695787200000; // 1 day before today
            const todayStamp = 1695873600000;
            const foundInvoiceCreditReduction = { totalReducedAmount: 50 };

            const result = BillingReportAgingService.getCreditMemoBalancePointInTime(creditMemo, reportDateStamp, todayStamp, foundInvoiceCreditReduction);

            expect(result).toBe(150); // originalAmount - totalReducedAmount
        });

        it('applies refundedAmount when refundedAt is before or on report date', () => {
            const creditMemo = {
                originalAmount: 200,
                refundedAt: 1695787200000, // 1 day before report date
                refundedAmount: 50
            };
            const reportDateStamp = 1695873600000;
            const todayStamp = 1695959999999;
            const foundInvoiceCreditReduction = { totalReducedAmount: 100 };

            const result = BillingReportAgingService.getCreditMemoBalancePointInTime(creditMemo, reportDateStamp, todayStamp, foundInvoiceCreditReduction);

            expect(result).toBe(50); // originalAmount - totalReducedAmount - refundedAmount
        });

        it('sets balance to 0 when voidedAt is on or before report date', () => {
            const creditMemo = {
                originalAmount: 300,
                voidedAt: 1695787200000 // 1 day before report date
            };
            const reportDateStamp = 1695873600000;
            const todayStamp = 1695959999999;

            const result = BillingReportAgingService.getCreditMemoBalancePointInTime(creditMemo, reportDateStamp, todayStamp, null);

            expect(result).toBe(0);
        });

        it('returns adjusted balance correctly with no reductions or refunds', () => {
            const creditMemo = { originalAmount: 150 };
            const reportDateStamp = 1695787200000;
            const todayStamp = 1695873600000;

            const result = BillingReportAgingService.getCreditMemoBalancePointInTime(creditMemo, reportDateStamp, todayStamp, null);

            expect(result).toBe(150); // originalAmount with no reductions or refunds
        });
    });

    describe('getAllRelationshipsFromIntersectedPeopleAndMapByTargetId', () => {
        beforeEach(() => {
            jest.clearAllMocks();
        });

        it('returns an empty map and array if no relationships are found', async () => {
            const mockCursor = Relationships.find;
            mockCursor.mockReturnValue({
                fetchAsync:jest.fn().mockImplementation(()=>[])
            });

            const result = await BillingReportAgingService.getAllRelationshipsFromIntersectedPeopleAndMapByTargetId(['123', '456']);

            expect(result).toEqual({
                relationshipsMap: {},
                relationships: []
            });
        });

        it('returns relationships and maps them by targetId', async () => {
            const mockRelationships = [
                { targetId: '123', personId: '456', relationshipType: 'family' },
                { targetId: '123', personId: '789', relationshipType: 'family' },
                { targetId: '456', personId: '101', relationshipType: 'family' },
            ];

            const mockCursor = Relationships.find;
            mockCursor.mockReturnValue({
                fetchAsync:jest.fn().mockImplementation(()=>mockRelationships)
            });

            const result = await BillingReportAgingService.getAllRelationshipsFromIntersectedPeopleAndMapByTargetId(['123', '456']);

            expect(result.relationships).toEqual(mockRelationships);
            expect(result.relationshipsMap).toEqual({
                '123': [
                    { targetId: '123', personId: '456', relationshipType: 'family' },
                    { targetId: '123', personId: '789', relationshipType: 'family' }
                ],
                '456': [
                    { targetId: '456', personId: '101', relationshipType: 'family' }
                ]
            });
        });
    });

    describe('getAllParentsFromRelationships', () => {
        beforeEach(() => {
            jest.clearAllMocks();
        });

        it('returns an empty map if no people are found', async () => {
            const mockCursor = People.find;
            mockCursor.mockReturnValue({
                fetchAsync:jest.fn().mockImplementation(()=>[])
            });

            const result = await BillingReportAgingService.getAllParentsFromRelationships([]);

            expect(result).toEqual({});
        });

        it('returns a map of people keyed by their personId', async () => {
            const mockRelationships = [
                { personId: '123' },
                { personId: '456' },
                { personId: '789' },
            ];

            const mockPeople = [
                { _id: '123', firstName: 'John', lastName: 'Doe' },
                { _id: '456', firstName: 'Jane', lastName: 'Smith' },
                { _id: '789', firstName: 'Bob', lastName: 'Brown' },
            ];

            const mockCursor = People.find;
            mockCursor.mockReturnValue({
                fetchAsync:jest.fn().mockImplementation(()=>mockPeople)
            });

            const result = await BillingReportAgingService.getAllParentsFromRelationships(mockRelationships);

            expect(result).toEqual({
                '123': { _id: '123', firstName: 'John', lastName: 'Doe' },
                '456': { _id: '456', firstName: 'Jane', lastName: 'Smith' },
                '789': { _id: '789', firstName: 'Bob', lastName: 'Brown' },
            });
        });

        it('skips duplicate personIds in the relationships array', async () => {
            const mockRelationships = [
                { personId: '123' },
                { personId: '123' },
                { personId: '456' },
            ];

            const mockPeople = [
                { _id: '123', firstName: 'John', lastName: 'Doe' },
                { _id: '456', firstName: 'Jane', lastName: 'Smith' },
            ];

            const mockCursor = People.find;
            mockCursor.mockReturnValue({
                fetchAsync:jest.fn().mockImplementation(()=>mockPeople)
            });

            const result = await BillingReportAgingService.getAllParentsFromRelationships(mockRelationships);

            expect(result).toEqual({
                '123': { _id: '123', firstName: 'John', lastName: 'Doe' },
                '456': { _id: '456', firstName: 'Jane', lastName: 'Smith' },
            });
        });
    });

    describe('updateCenterTotals', () => {
        const mockCenterHelper = jest.fn();
        const scopedOrgs = [
            { _id: 'org1', name: 'Organization 1' },
            { _id: 'org2', name: 'Organization 2' },
        ];
        const results = [
            {
                familyMembers: [{ orgId: 'org1' }],
                groupedInvoices: [{ orgId: 'org1' }],
                familyCredits: [],
            },
            {
                familyMembers: [{ orgId: 'org2' }],
                groupedInvoices: [],
                familyCredits: [{ orgId: 'org2' }],
            },
        ];
        const orgsMap = {
            org1: ['parentOrg1'],
            org2: ['parentOrg2'],
        };
        const centerTotals = {
            parentOrg1: {},
            parentOrg2: {},
            'Organization 1': {},
            'Organization 2': {},
        };

        beforeAll(() => {
            jest.spyOn(BillingReportAgingService, 'centerHelper').mockImplementation(mockCenterHelper);
        });

        afterEach(() => {
            jest.clearAllMocks();
        });

        it('updates center totals correctly for organizations and parent organizations', () => {
            BillingReportAgingService.updateCenterTotals(results, orgsMap, centerTotals, 'family', scopedOrgs);

            expect(mockCenterHelper).toHaveBeenCalledTimes(4);

            // Verify calls for parent organizations
            expect(mockCenterHelper).toHaveBeenCalledWith(centerTotals['parentOrg1'], results[0], true, 'family');
            expect(mockCenterHelper).toHaveBeenCalledWith(centerTotals['parentOrg2'], results[1], true, 'family');

            // Verify calls for direct organizations
            expect(mockCenterHelper).toHaveBeenCalledWith(centerTotals['Organization 1'], results[0], false, 'family');
            expect(mockCenterHelper).toHaveBeenCalledWith(centerTotals['Organization 2'], results[1], false, 'family');
        });

        it('does not update totals if orgName is missing', () => {
            const scopedOrgsWithoutNames = [{ _id: 'org1' }, { _id: 'org2' }];

            BillingReportAgingService.updateCenterTotals(results, orgsMap, centerTotals, 'family', scopedOrgsWithoutNames);

            expect(mockCenterHelper).toHaveBeenCalledTimes(2); // Only parent orgs updated
        });

        it('handles cases with missing orgId gracefully', () => {
            const resultsWithMissingOrg = [
                { familyMembers: [], groupedInvoices: [], familyCredits: [] },
            ];

            BillingReportAgingService.updateCenterTotals(resultsWithMissingOrg, orgsMap, centerTotals, 'family', scopedOrgs);

            expect(mockCenterHelper).not.toHaveBeenCalled();
        });
    });

    describe('aggregateTotals', () => {
        const mockGroupedInvoices = [
            {
                totalOriginalAmount: 100,
                totalOpenAmount: 50,
                totalCurrentAmount: 25,
                totalZeroToSix: 10,
                totalSevenToThirteen: 5,
            },
            {
                totalOriginalAmount: 200,
                totalOpenAmount: 100,
                totalCurrentAmount: 50,
                totalZeroToSix: 20,
                totalSevenToThirteen: 10,
            },
        ];

        const mockFamilyCredits = [
            {
                balancePointInTime: 40,
                originalAmount: 20,
                amountBucket: '0-6',
            },
            {
                balancePointInTime: 30,
                originalAmount: 15,
                amountBucket: '7-13',
            },
        ];

        it('aggregates totals correctly for "family" filterBuckets', () => {
            const result = BillingReportAgingService.aggregateTotals(mockGroupedInvoices, mockFamilyCredits, 'family');
            expect(result).toEqual({
                totalOriginalAmount: 300,
                totalOpenAmount: 150,
                totalCurrentAmount: 75,
                totalZeroToSix: 30,
                totalSevenToThirteen: 15,
                totalFourteenToTwenty: 0,
                totalTwentyOneToTwentySeven: 0,
                totalOverTwentyEight: 0,
                totalCreditOriginalAmount: 35,
                totalCreditOpenAmount: 70,
                totalCreditCurrentAmount: 0,
                totalCreditZeroToSix: 40,
                totalCreditSevenToThirteen: 30,
                totalCreditFourteenToTwenty: 0,
                totalCreditTwentyOneToTwentySeven: 0,
                totalCreditOverTwentyEight: 0,
                totalNetOriginalAmount: 335,
                totalNetOpenAmount: 220,
                totalNetCurrentAmount: 75,
                totalNetZeroToSix: 70,
                totalNetSevenToThirteen: 45,
                totalNetFourteenToTwenty: 0,
                totalNetTwentyOneToTwentySeven: 0,
                totalNetOverTwentyEight: 0,
            });
        });

        it('aggregates totals correctly for "payer" filterBuckets', () => {
            const mockGroupedInvoicesPayer = [
                {
                    totalOriginalAmount: 100,
                    totalOpenAmount: 50,
                    totalCurrentAmount: 25,
                    totalOneToThirty: 10,
                    totalThirtyOneToSixty: 5,
                },
            ];

            const mockFamilyCreditsPayer = [
                {
                    balancePointInTime: 30,
                    originalAmount: 15,
                    amountBucket: '1-30',
                },
            ];

            const result = BillingReportAgingService.aggregateTotals(mockGroupedInvoicesPayer, mockFamilyCreditsPayer, 'payer');

            expect(result).toEqual({
                totalOriginalAmount: 100,
                totalOpenAmount: 50,
                totalCurrentAmount: 25,
                totalOneToThirty: 10,
                totalThirtyOneToSixty: 5,
                totalSixtyOneToNinety: 0,
                totalNinetyOneToOneTwenty: 0,
                totalOverOneTwenty: 0,
                totalCreditOriginalAmount: 15,
                totalCreditOpenAmount: 30,
                totalCreditCurrentAmount: 0,
                totalCreditOneToThirty: 30,
                totalCreditThirtyOneToSixty: 0,
                totalCreditSixtyOneToNinety: 0,
                totalCreditNinetyOneToOneTwenty: 0,
                totalCreditOverOneTwenty: 0,
                totalNetOriginalAmount: 115,
                totalNetOpenAmount: 80,
                totalNetCurrentAmount: 25,
                totalNetOneToThirty: 40,
                totalNetThirtyOneToSixty: 5,
                totalNetSixtyOneToNinety: 0,
                totalNetNinetyOneToOneTwenty: 0,
                totalNetOverOneTwenty: 0,
            });
        });

        it('returns default totals when no groupedInvoices or familyCredits are provided', () => {
            const result = BillingReportAgingService.aggregateTotals([], [], 'family');

            expect(result).toEqual({
                totalOriginalAmount: 0,
                totalOpenAmount: 0,
                totalCurrentAmount: 0,
                totalZeroToSix: 0,
                totalSevenToThirteen: 0,
                totalFourteenToTwenty: 0,
                totalTwentyOneToTwentySeven: 0,
                totalOverTwentyEight: 0,
                totalCreditOriginalAmount: 0,
                totalCreditOpenAmount: 0,
                totalCreditCurrentAmount: 0,
                totalCreditZeroToSix: 0,
                totalCreditSevenToThirteen: 0,
                totalCreditFourteenToTwenty: 0,
                totalCreditTwentyOneToTwentySeven: 0,
                totalCreditOverTwentyEight: 0,
                totalNetOriginalAmount: 0,
                totalNetOpenAmount: 0,
                totalNetCurrentAmount: 0,
                totalNetZeroToSix: 0,
                totalNetSevenToThirteen: 0,
                totalNetFourteenToTwenty: 0,
                totalNetTwentyOneToTwentySeven: 0,
                totalNetOverTwentyEight: 0,
            });
        });
    });

    describe('initializeTotals', () => {
        it('initializes totals for family buckets without credits', () => {
            const result = BillingReportAgingService.initializeTotals("family");

            expect(result).toEqual({
                totalOriginalAmount: 0,
                totalOpenAmount: 0,
                totalCurrentAmount: 0,
                totalZeroToSix: 0,
                totalSevenToThirteen: 0,
                totalFourteenToTwenty: 0,
                totalTwentyOneToTwentySeven: 0,
                totalOverTwentyEight: 0,
            });
        });

        it('initializes totals for family buckets with credits', () => {
            const result = BillingReportAgingService.initializeTotals("family", true);

            expect(result).toEqual({
                totalCreditOriginalAmount: 0,
                totalCreditOpenAmount: 0,
                totalCreditCurrentAmount: 0,
                totalCreditZeroToSix: 0,
                totalCreditSevenToThirteen: 0,
                totalCreditFourteenToTwenty: 0,
                totalCreditTwentyOneToTwentySeven: 0,
                totalCreditOverTwentyEight: 0,
            });
        });

        it('initializes totals for payer buckets without credits', () => {
            const result = BillingReportAgingService.initializeTotals("payer");

            expect(result).toEqual({
                totalOriginalAmount: 0,
                totalOpenAmount: 0,
                totalCurrentAmount: 0,
                totalOneToThirty: 0,
                totalThirtyOneToSixty: 0,
                totalSixtyOneToNinety: 0,
                totalNinetyOneToOneTwenty: 0,
                totalOverOneTwenty: 0,
            });
        });

        it('initializes totals for payer buckets with credits', () => {
            const result = BillingReportAgingService.initializeTotals("payer", true);

            expect(result).toEqual({
                totalCreditOriginalAmount: 0,
                totalCreditOpenAmount: 0,
                totalCreditCurrentAmount: 0,
                totalCreditOneToThirty: 0,
                totalCreditThirtyOneToSixty: 0,
                totalCreditSixtyOneToNinety: 0,
                totalCreditNinetyOneToOneTwenty: 0,
                totalCreditOverOneTwenty: 0,
            });
        });
    });

    describe('generateAgingReport', () => {
        describe('calculates credit memos correctly', () => {
            const reportOptions = {
                searchQuery: "",
                filterGrouping: "family",
                filterBalance: "",
                filterBalanceType: "family",
                filterDate: "04/22/2025",
                orgIds: [],
                usePeriodDate: false,
                includeWaitList: true,
                currentPerson: personFixture,
                org: orgFixture
            };
            let generateAgeBucketsSpy;

            // Restore the original implementation after each test
            afterEach(async () => {
                generateAgeBucketsSpy.mockRestore();
                jest.clearAllMocks();
            });

            beforeEach(async () => {
                generateAgeBucketsSpy = jest.spyOn(BillingReportAgingService, 'generateAgeBucketsByCreditCreatedDate');
            });

            it('passes valid peopleWithOpenCredits to generateAgeBucketsByCreditCreatedDate', async () => {
                const find = People.find;
                find.mockImplementation(() => ({
                    fetchAsync: jest.fn().mockResolvedValue(singleOrgOpenCreditPeople)
                }));

                const orgCursorMock = Orgs.find;
                // for the scoped orgs call for single org
                orgCursorMock.mockImplementationOnce(() => {
                    return ({
                        fetchAsync: jest.fn().mockImplementation(() => currentOrg)
                    })
                });
                // for the all orgs call as well as the calls for multiple ids (calls 3 and 4)
                orgCursorMock.mockImplementation(() => {
                    return ({
                        fetchAsync: jest.fn().mockImplementation(() => allOrgs)
                    })
                });

                Invoices.aggregate.mockReturnValue({
                    toArray: jest.fn().mockResolvedValue([]),
                });
                Invoices.find.mockImplementation(() => {
                    return ({
                        fetchAsync: jest.fn().mockImplementation(() => [])
                    })
                });

                Relationships.find.mockImplementation(() => {
                    return ({
                        fetchAsync: jest.fn().mockImplementation(() => [])
                    })
                });

                await BillingReportAgingService.generateAgingReport(reportOptions);

                expect(generateAgeBucketsSpy).toHaveBeenCalled();
                const callArgs = generateAgeBucketsSpy.mock.calls[0][0];
                expect(callArgs.peopleWithOpenCredits).toEqual(singleOrgOpenCreditPeople);
            });
        });
    });
});









