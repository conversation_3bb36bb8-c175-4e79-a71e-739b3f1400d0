import { LedgerDetailService } from '../../../../server/ledgerDetailService';
import { MiscUtils } from '../../../../lib/util/miscUtils';
import { LedgerDetailServiceUtils } from '../../../../lib/util/ledgerDetailServiceUtils';
import moment from 'moment-timezone';
import { Orgs } from '../../../../lib/collections/orgs';
import { People } from '../../../../lib/collections/people';
import { BillingServerUtils } from '../../../../server/billingServerUtils';
import { jest } from '@jest/globals';
import { expect } from 'expect';
import { Deposits } from '../../../../lib/collections/deposits';
import { Invoices } from '../../../../lib/collections/invoices';
import { ledgerAccountTypes } from '../../../../lib/constants/billingConstants';
import { AdyenProvider } from '../../../../server/card_providers/adyenProvider';

describe('ledgerDetailService', () => {
    describe('generateInvoiceQuery', () => {

        const validOrgIds = ['org1', 'org2', 'org3'];
        const options = {
            startDate: *************,
            endDate: *************
        };

        it('should generate a query with basic conditions when usePeriodDate is false', () => {
            const query = LedgerDetailService.generateInvoiceQuery(options, false, validOrgIds);

            expect(query).toHaveProperty('orgId', { $in: validOrgIds });
            expect(query.$or).toBeInstanceOf(Array);

            const expectedConditions = [
                { createdAt: { $gte: options.startDate, $lt: options.endDate } },
                { 'credits.createdAt': { $gte: options.startDate, $lt: options.endDate } },
                { 'credits.voidedAt': { $gte: options.startDate, $lt: options.endDate } },
                { 'credits.refundedAt': { $gte: options.startDate, $lt: options.endDate } },
                {
                    allocationEntries: {
                        $elemMatch: {
                            createdAt: { $gte: options.startDate, $lt: options.endDate },
                            destination: { $in: ['bad-debt', 'agency', 'collections'] }
                        }
                    }
                },
                { 'lineItems.appliedDiscounts.createdAt': { $gte: options.startDate, $lt: options.endDate } },
                {
                    'lineItems.appliedDiscounts.modificationHistory': {
                        $elemMatch: { modifiedAt: { $gte: options.startDate, $lt: options.endDate } }
                    }
                },
                {
                    "credits.modificationHistory": {
                        $elemMatch: {
                            modifiedAt: { $gte: options.startDate, $lt: options.endDate }
                        }
                    }
                },
                {
                    "credits.adjustments": {
                        $elemMatch: {
                            adjustedAt: { $gte: options.startDate, $lt: options.endDate }
                        }
                    }
                }
            ];

            expectedConditions.forEach(condition => expect(query.$or).toContainEqual(condition));
        });

        it('should include periodStartDate condition when usePeriodDate is true', () => {
            const query = LedgerDetailService.generateInvoiceQuery(options, true, validOrgIds);

            expect(query).toHaveProperty('orgId', { $in: validOrgIds });
            expect(query.$or).toBeInstanceOf(Array);

            const periodStartDateCondition = {
                'lineItems.periodStartDate': { $gte: options.startDate, $lt: options.endDate }
            };
            expect(query.$or).toContainEqual(periodStartDateCondition);
        });

        it('should handle an empty array of validOrgIds', () => {
            const emptyOrgIds = [];
            const query = LedgerDetailService.generateInvoiceQuery(options, false, emptyOrgIds);

            expect(query).toHaveProperty('orgId', { $in: emptyOrgIds });
            expect(query.$or).toBeInstanceOf(Array);
        });

        it('should throw an error if options are missing', () => {
            expect(() => LedgerDetailService.generateInvoiceQuery(null, false, validOrgIds)).toThrow();
        });

        it('should handle a case where no $or conditions match', () => {
            const invalidOptions = {
                startDate: null,
                endDate: null
            };
            const query = LedgerDetailService.generateInvoiceQuery(invalidOptions, false, validOrgIds);

            expect(query).toHaveProperty('orgId', { $in: validOrgIds });
            expect(query.$or).toBeInstanceOf(Array);
        });
    });
    describe('getOrgsMapAndMeta', () => {
        let orgsFindMock;
        const mockOptions = { orgIds: ['org1', 'org2', 'org3'] };
        const mockOrg = { _id: 'org1' };
        const mockCurrentUser = {
            fetchPerson: jest.fn().mockReturnValue({
                findScopedOrgs: jest.fn().mockReturnValue([{ _id: 'org1' }, { _id: 'org2' }])
            })
        };
        const mockOrgIdsToNameMap = new Map();

        beforeEach(() => {
            jest.clearAllMocks();

            orgsFindMock = Orgs.find;
        });

        it('should return a single org context when no orgIds are provided', async () => {
            const options = {};
            const result = await LedgerDetailService.getOrgsMapAndMeta(options, mockOrg, null, mockOrgIdsToNameMap);

            expect(result.isMultiOrgs).toBe(false);
            expect(result.orgsMap).toEqual([]);
            expect(result.orgsMeta).toEqual([]);
            expect(result.validOrgIds).toEqual([mockOrg._id]);
        });

        it('should process multiple organizations when valid orgIds are provided', async () => {
            const mockFullOrgs = [
                { _id: 'org1', parentOrgId: null, name: 'Org 1' },
                { _id: 'org2', parentOrgId: 'org1', name: 'Org 2' },
                { _id: 'org3', parentOrgId: 'org2', name: 'Org 3' }
            ];
            const mockValidOrgs = [
                { _id: 'org1', parentOrgId: null, name: 'Org 1' },
                { _id: 'org2', parentOrgId: 'org1', name: 'Org 2' }
            ];

            orgsFindMock.mockReturnValueOnce(({
                fetchAsync:jest.fn().mockImplementation(()=>mockFullOrgs)
            }));
            orgsFindMock.mockReturnValueOnce(({
                fetchAsync:jest.fn().mockImplementation(()=>mockValidOrgs)
            }));

            const result = await LedgerDetailService.getOrgsMapAndMeta(mockOptions, mockOrg, mockCurrentUser, mockOrgIdsToNameMap);

            expect(result.isMultiOrgs).toBe(true);
            expect(result.orgsMap).toEqual({
                org1: [],
                org2: ['Org 1']
            });
            expect(result.orgsMeta).toEqual([
                { id: 'org1', name: 'Org 1', depth: 0, parent: null, childrenCount: 2 }, // Includes org2 and org3
                { id: 'org2', name: 'Org 2', depth: 1, parent: 'org1', childrenCount: 1 } // Includes org3
            ]);
            expect(result.validOrgIds).toEqual(['org1', 'org2']);
            expect(mockOrgIdsToNameMap.get('org1')).toBe('Org 1');
            expect(mockOrgIdsToNameMap.get('org2')).toBe('Org 2');
        });
    });
    describe('mapPostingDateToInvoiceLineItems', () => {
        const invoices = [
            {
                createdAt: 1733032800000,
                lineItems: [
                    { periodStartDate: 1733032800000, appliedDiscounts: [] },
                    { appliedDiscounts: [] }
                ]
            }
        ];
        const org = { getTimezone: jest.fn(() => 'America/New_York') };

        it('should map posting dates to line items and discounts when usePeriodDate is true', () => {
            const result = LedgerDetailService.mapPostingDateToInvoiceLineItems(true, invoices, org);

            expect(result[0].lineItems[0].postingDate).toBe(1733032800000); // From periodStartDate
            expect(result[0].lineItems[1].postingDate).toBe(1733032800000); // From createdAt
        });

        it('should map posting dates to line items and discounts when usePeriodDate is false', () => {
            const result = LedgerDetailService.mapPostingDateToInvoiceLineItems(false, invoices, org);

            expect(result[0].lineItems[0].postingDate).toBe(1733032800000); // From createdAt
            expect(result[0].lineItems[1].postingDate).toBe(1733032800000); // From createdAt
        });
    });
    describe('filterInvoicesByPeriodDate', () => {
        const invoices = [
            {
                lineItems: [
                    { periodStartDate: 1733032800000 },
                    { periodStartDate: 1733032900000 }
                ]
            },
            {
                lineItems: [
                    { periodStartDate: 1733033000000 },
                    { periodStartDate: 1733033100000 }
                ]
            }
        ];
        const options = { periodStartDate: 1733032800000, periodStartDateEnd: 1733033000000 };

        it('should filter invoices where any line item falls within the period date range', () => {
            const result = LedgerDetailService.filterInvoicesByPeriodDate(invoices, options);

            expect(result).toHaveLength(2); // Both invoices have at least one lineItem within the range
            expect(result[0].lineItems[0].periodStartDate).toBe(1733032800000);
            expect(result[1].lineItems[0].periodStartDate).toBe(1733033000000);
        });

        it('should return an empty array when no invoices are within the period date range', () => {
            const result = LedgerDetailService.filterInvoicesByPeriodDate(invoices, { periodStartDate: 1733033200000 });

            expect(result).toHaveLength(0);
        });

        it('should return an empty array when no invoices are provided', () => {
            const result = LedgerDetailService.filterInvoicesByPeriodDate([], options);

            expect(result).toHaveLength(0);
        });

        it('should return all invoices if no periodStartDate is provided', () => {
            const result = LedgerDetailService.filterInvoicesByPeriodDate(invoices, {});

            expect(result).toHaveLength(2);
        });
    });
    describe('mapPostingDateToLineItemDiscounts', () => {
        const timezone = 'America/New_York';
        const today = moment.tz(timezone);
        const org = {
            getTimezone: jest.fn(() => timezone),
        };

        const lineItem = {
            appliedDiscounts: [
                {
                    createdAt: today.startOf('day').valueOf(),
                    modificationHistory: [
                        { modifiedAt: today.clone().add(1, 'days').startOf('day').valueOf() },
                    ],
                },
                {
                    createdAt: today.clone().add(2, 'days').startOf('day').valueOf(),
                    voidedAt: today.clone().add(3, 'days').startOf('day').valueOf(),
                },
            ],
            periodStartDate: today.clone().add(1, 'weeks').startOf('day').valueOf(),
        };

        it('should return undefined if org or lineItem.appliedDiscounts is missing', () => {
            const resultNoOrg = LedgerDetailService.mapPostingDateToLineItemDiscounts(true, lineItem, null);
            const resultNoDiscounts = LedgerDetailService.mapPostingDateToLineItemDiscounts(true, { appliedDiscounts: null }, org);

            expect(resultNoOrg).toBeUndefined();
            expect(resultNoDiscounts).toBeUndefined();
        });

        it('should work if lineItem.appliedDiscounts is an object', () => {
            const result = LedgerDetailService.mapPostingDateToLineItemDiscounts(
                true,
                {
                    appliedDiscounts: {
                        "0": {
                            "amount": 0,
                            "updatedAt": 1729719461805,
                            "updatedByPersonId": "aaaaaaaaaaaaaaaa",
                            "voidedAt": 1729719461805,
                            "voidedByPersonId": "aaaaaaaaaaaaaaaa"
                        }
                    }
                },
                org
            );

            expect(result).toEqual([
                {
                    "amount": 0,
                    "updatedAt": 1729719461805,
                    "updatedByPersonId": "aaaaaaaaaaaaaaaa",
                    "voidedAt": 1729719461805,
                    "voidedByPersonId": "aaaaaaaaaaaaaaaa"
                }
            ]);
        });

        it('should map posting dates to discounts and their modification history if usePeriodDate is true and periodStart is in the future', () => {
            const usePeriodDate = true;

            const result = LedgerDetailService.mapPostingDateToLineItemDiscounts(usePeriodDate, lineItem, org);

            expect(result).toHaveLength(2);

            expect(result[0].postingDate).toBe(lineItem.periodStartDate);
            expect(result[0].modificationHistory).toHaveLength(1);
            expect(result[0].modificationHistory[0].postingDate).toBe(lineItem.periodStartDate);

            expect(result[1].postingDate).toBe(lineItem.periodStartDate);
            expect(result[1].modificationHistory).toBeUndefined();
        });

        it('should map posting dates to discounts and their modification history if usePeriodDate is true and periodStart is in the past', () => {
            const usePeriodDate = true;
            lineItem.periodStartDate = today.clone().subtract(1, 'weeks').startOf('day').valueOf();

            const result = LedgerDetailService.mapPostingDateToLineItemDiscounts(usePeriodDate, lineItem, org);

            expect(result).toHaveLength(2);

            expect(result[0].postingDate).toBe(lineItem.appliedDiscounts[0].createdAt);
            expect(result[0].modificationHistory).toHaveLength(1);
            expect(result[0].modificationHistory[0].postingDate).toBe(lineItem.appliedDiscounts[0].modificationHistory[0].modifiedAt);

            expect(result[1].postingDate).toBe(lineItem.appliedDiscounts[1].voidedAt);
            expect(result[1].modificationHistory).toBeUndefined();
        });

        it('should handle discounts with no modification history gracefully', () => {
            const usePeriodDate = false;
            const modifiedLineItem = {
                appliedDiscounts: [
                    {
                        createdAt: today.valueOf(),
                        voidedAt: today.clone().add(1, 'days').startOf('day').valueOf(),
                        modificationHistory: [],
                    },
                ],
            };

            const result = LedgerDetailService.mapPostingDateToLineItemDiscounts(usePeriodDate, modifiedLineItem, org);

            expect(result).toHaveLength(1);
            expect(result[0].postingDate).toBe(modifiedLineItem.appliedDiscounts[0].voidedAt);
            expect(result[0].modificationHistory).toHaveLength(0);
        });
    });
    describe('getDiscountPostingDate', () => {
        const timezone = 'America/New_York';
        const today = moment.tz(timezone).startOf('day');

        it('should return the voidedAt date if available', () => {
            const discount = { voidedAt: today.valueOf(), createdAt: today.clone().add(1, 'days').valueOf() };
            const lineItem = { periodStartDate: today.clone().add(1, 'weeks').valueOf() };

            const result = LedgerDetailService.getDiscountPostingDate(discount, false, lineItem, timezone);

            expect(result).toBe(today.valueOf()); // From voidedAt
        });

        it('should return the period start date when usePeriodDate is true and today is before period start', () => {
            const discount = { createdAt:  today.valueOf()};
            const lineItem = { periodStartDate: today.clone().add(1, 'weeks').valueOf() };


            const result = LedgerDetailService.getDiscountPostingDate(discount, true, lineItem, timezone);

            expect(result).toBe(today.clone().add(1, 'weeks').valueOf()); // From periodStartDate
        });

        it('should return the createdAt date when usePeriodDate is true and today is after period start', () => {
            const discount = { createdAt: today.valueOf() };
            const lineItem = { periodStartDate: today.clone().subtract(1, 'weeks').valueOf() };

            const result = LedgerDetailService.getDiscountPostingDate(discount, true, lineItem, timezone);

            expect(result).toBe(today.valueOf()); // From createdAt
        });

        it('should return the createdAt date when usePeriodDate is false', () => {
            const discount = { createdAt: today.valueOf() };
            const lineItem = { periodStartDate: today.clone().add(1, 'days').valueOf() };

            const result = LedgerDetailService.getDiscountPostingDate(discount, false, lineItem, timezone);

            expect(result).toBe(today.valueOf()); // From createdAt
        });
    });
    describe('returnPeriodStartOrActionDateBasedOnToday', () => {

        it('should return the action date when usePeriodDate is false', () => {
            const usePeriodDate = false;
            const actionDate = moment('2024-12-01');
            const periodStartDate = moment('2024-12-10');
            const today = moment('2024-12-05');

            const result = LedgerDetailService.returnPeriodStartOrActionDateBasedOnToday(usePeriodDate, actionDate, periodStartDate, today);

            expect(result.isSame(actionDate)).toBe(true);
        });

        it('should return the period start date when usePeriodDate is true and today is before the period start date', () => {
            const usePeriodDate = true;
            const actionDate = moment('2024-12-01');
            const periodStartDate = moment('2024-12-10');
            const today = moment('2024-12-05');

            const result = LedgerDetailService.returnPeriodStartOrActionDateBasedOnToday(usePeriodDate, actionDate, periodStartDate, today);

            expect(result.isSame(periodStartDate)).toBe(true);
        });

        it('should return the action date when usePeriodDate is true and today is on or after the period start date', () => {
            const usePeriodDate = true;
            const actionDate = moment('2024-12-15');
            const periodStartDate = moment('2024-12-10');
            const today = moment('2024-12-10');

            const result = LedgerDetailService.returnPeriodStartOrActionDateBasedOnToday(usePeriodDate, actionDate, periodStartDate, today);

            expect(result.isSame(actionDate)).toBe(true);
        });

        it('should return the action date when all inputs are valid and today is equal to period start date', () => {
            const usePeriodDate = true;
            const actionDate = moment('2024-12-15');
            const periodStartDate = moment('2024-12-10');
            const today = moment('2024-12-10');

            const result = LedgerDetailService.returnPeriodStartOrActionDateBasedOnToday(usePeriodDate, actionDate, periodStartDate, today);

            expect(result.isSame(actionDate)).toBe(true);
        });
    });
    describe('getDiscountModificationPostingDate', () => {
        const timezone = 'America/New_York';
        const lineItem = { periodStartDate: '2024-12-10', postingDate: ************* };

        it('should return the line item posting date if history.modifiedAt is not provided', () => {
            const history = {};
            const usePeriodDate = true;

            const result = LedgerDetailService.getDiscountModificationPostingDate(history, usePeriodDate, lineItem, timezone);

            expect(result).toBe(lineItem.postingDate);
        });

        it('should return the period start date if usePeriodDate is true and today is before period start date', () => {
            const history = { modifiedAt: '2024-12-05T00:00:00Z' };
            const usePeriodDate = true;

            jest.spyOn(LedgerDetailService, 'returnPeriodStartOrActionDateBasedOnToday').mockImplementation(() => moment.tz(lineItem.periodStartDate, timezone));

            const result = LedgerDetailService.getDiscountModificationPostingDate(history, usePeriodDate, lineItem, timezone);

            expect(result).toBe(moment.tz(lineItem.periodStartDate, timezone).valueOf());
        });

        it('should return the action date if usePeriodDate is false', () => {
            const history = { modifiedAt: '2024-12-05T00:00:00Z' };
            const usePeriodDate = false;

            jest.spyOn(LedgerDetailService, 'returnPeriodStartOrActionDateBasedOnToday').mockImplementation(() => moment.tz(history.modifiedAt, timezone));

            const result = LedgerDetailService.getDiscountModificationPostingDate(history, usePeriodDate, lineItem, timezone);

            expect(result).toBe(moment.tz(history.modifiedAt, timezone).startOf('day').valueOf());
        });

        it('should return the action date if usePeriodDate is true and today is after or on the period start date', () => {
            const history = { modifiedAt: '2024-12-15T00:00:00Z' };
            const usePeriodDate = true;

            jest.spyOn(LedgerDetailService, 'returnPeriodStartOrActionDateBasedOnToday').mockImplementation(() => moment.tz(history.modifiedAt, timezone));

            const result = LedgerDetailService.getDiscountModificationPostingDate(history, usePeriodDate, lineItem, timezone);

            expect(result).toBe(moment.tz(history.modifiedAt, timezone).startOf('day').valueOf());
        });

        afterEach(() => {
            jest.restoreAllMocks();
        });
    });
    describe('processLineItemAppliedDiscounts', () => {
        const mockApplyItemToGroup = jest.fn();
        const processOptions = {
            lineItem: { type: 'plan' },
            options: { startDate: '2024-12-01', endDate: '2024-12-31' },
            invoice: { invoiceNumber: '123' },
            org: { getTimezone: jest.fn(() => 'America/New_York') },
            invoiceNameDescriptor: 'Test Descriptor',
            personDefaultGroupName: 'Default Group',
            applyItemToGroup: mockApplyItemToGroup,
        };

        const lineItemAppliedDiscountsPlan = [
            {
                originalAllocation: { amountType: 'percent' },
                type: 'discount',
                voidedAt: null,
                amount: 100,
                modificationHistory: [],
            },
        ];

        const lineItemAppliedDiscountsItem = [
            {
                originalAllocation: { amountType: 'fixed' },
                type: 'discount',
                voidedAt: null,
                amount: 50,
            },
        ];

        beforeEach(() => {
            jest.clearAllMocks();
            jest.spyOn(MiscUtils, 'convertNumericKeyedObjectToArray').mockImplementation(discounts => discounts);
            jest.spyOn(LedgerDetailServiceUtils, 'planChargeFromAppliedDiscounts').mockImplementation(() => [
                { charge: 'Plan Charge 1' },
                { charge: 'Plan Charge 2' },
            ]);
            jest.spyOn(LedgerDetailServiceUtils, 'itemDiscountObject').mockImplementation(() => ({
                discount: 'Item Discount 1',
            }));
        });

        afterEach(() => {
            jest.restoreAllMocks();
        });

        it('should process discounts for line items of type "plan"', () => {
            const processOptionsPlan = { ...processOptions, lineItem: { type: 'plan' } };

            LedgerDetailService.processLineItemAppliedDiscounts(lineItemAppliedDiscountsPlan, processOptionsPlan);

            expect(MiscUtils.convertNumericKeyedObjectToArray).toHaveBeenCalledWith(lineItemAppliedDiscountsPlan);
            expect(LedgerDetailServiceUtils.planChargeFromAppliedDiscounts).toHaveBeenCalledWith(
                processOptionsPlan.lineItem,
                lineItemAppliedDiscountsPlan[0],
                processOptionsPlan.org,
                processOptionsPlan.invoice,
                processOptionsPlan.invoiceNameDescriptor,
                processOptionsPlan.personDefaultGroupName,
                processOptionsPlan.options,
                {}
            );
            expect(mockApplyItemToGroup).toHaveBeenCalledTimes(2);
        });

        it('should process discounts for line items of other types', () => {
            const processOptionsItem = { ...processOptions, lineItem: { type: 'item' } };

            LedgerDetailService.processLineItemAppliedDiscounts(lineItemAppliedDiscountsItem, processOptionsItem);

            expect(LedgerDetailServiceUtils.itemDiscountObject).toHaveBeenCalledWith(
                processOptionsItem.invoice,
                lineItemAppliedDiscountsItem[0],
                processOptionsItem.org,
                processOptionsItem.personDefaultGroupName,
                processOptionsItem.invoiceNameDescriptor,
                'Item Discounts',
                `Test DescriptorItem discount for invoice 123`,
                'Item discount - ',
                {}
            );
            expect(mockApplyItemToGroup).toHaveBeenCalledTimes(1);
            expect(mockApplyItemToGroup).toHaveBeenCalledWith({ discount: 'Item Discount 1' });
        });
    });
    describe('processNonVoidedLineItem', () => {
        const mockApplyItemToGroup = jest.fn();
        const mockOptions = { startDate: '2024-12-01', endDate: '2024-12-31' };
        const mockInvoice = { invoiceNumber: '123', voidedAt: null, invoiceDate: '2024-12-01', _id: 'inv123', orgId: 'org123' };
        const mockOrg = { getTimezone: jest.fn(() => 'America/New_York') };
        const mockLineItemPlan = { type: 'plan', postingDate: '2024-12-05', frequency: 'monthly', amount: 100 };
        const mockLineItemItem = { type: 'item', postingDate: '2024-12-05', amount: 50, originalItem: { description: 'Test Item' }, quantity: '2' };

        beforeEach(() => {
            jest.clearAllMocks();

            jest.spyOn(LedgerDetailServiceUtils, 'activePostingDateNotVoided').mockReturnValue(true);
            jest.spyOn(LedgerDetailServiceUtils, 'planChargeFromDailyChargedMonthly').mockImplementation(() => [
                { charge: 'Plan Charge 1' },
                { charge: 'Plan Charge 2' },
            ]);
            jest.spyOn(LedgerDetailServiceUtils, 'itemChargeObject').mockImplementation(() => ({
                charge: 'Item Charge',
            }));
        });

        afterEach(() => {
            jest.restoreAllMocks();
        });

        it('should process line items of type "plan" and apply plan charges', () => {
            const processOptions = {
                lineItem: mockLineItemPlan,
                options: mockOptions,
                invoice: mockInvoice,
                org: mockOrg,
                invoiceNameDescriptor: 'Test Descriptor',
                personDefaultGroupName: 'Default Group',
                applyItemToGroup: mockApplyItemToGroup,
            };

            LedgerDetailService.processNonVoidedLineItem(processOptions);

            expect(LedgerDetailServiceUtils.activePostingDateNotVoided).toHaveBeenCalledWith(mockLineItemPlan, mockOptions, mockInvoice);
            expect(LedgerDetailServiceUtils.planChargeFromDailyChargedMonthly).toHaveBeenCalledWith(
                mockLineItemPlan,
                mockOrg,
                'Test Descriptor',
                mockInvoice,
                'Default Group',
                mockOptions,
                {}
            );
            expect(mockApplyItemToGroup).toHaveBeenCalledTimes(2);
            expect(mockApplyItemToGroup).toHaveBeenCalledWith({ charge: 'Plan Charge 1' });
            expect(mockApplyItemToGroup).toHaveBeenCalledWith({ charge: 'Plan Charge 2' });
        });

        it('should process line items of other types and apply item charges', () => {
            const processOptions = {
                lineItem: mockLineItemItem,
                options: mockOptions,
                invoice: mockInvoice,
                org: mockOrg,
                invoiceNameDescriptor: 'Test Descriptor',
                personDefaultGroupName: 'Default Group',
                applyItemToGroup: mockApplyItemToGroup,
            };

            LedgerDetailService.processNonVoidedLineItem(processOptions);

            expect(LedgerDetailServiceUtils.activePostingDateNotVoided).toHaveBeenCalledWith(mockLineItemItem, mockOptions, mockInvoice);
            expect(LedgerDetailServiceUtils.itemChargeObject).toHaveBeenCalledWith(
                mockLineItemItem,
                mockInvoice,
                'Default Group',
                'Test DescriptorItem charge for invoice 123',
                'Item Charges',
                {}
            );
            expect(mockApplyItemToGroup).toHaveBeenCalledTimes(1);
            expect(mockApplyItemToGroup).toHaveBeenCalledWith({ charge: 'Item Charge' });
        });

        it('should not process the line item if activePostingDateNotVoided returns false', () => {
            jest.spyOn(LedgerDetailServiceUtils, 'activePostingDateNotVoided').mockReturnValue(false);

            const processOptions = {
                lineItem: mockLineItemPlan,
                options: mockOptions,
                invoice: mockInvoice,
                org: mockOrg,
                invoiceNameDescriptor: 'Test Descriptor',
                personDefaultGroupName: 'Default Group',
                applyItemToGroup: mockApplyItemToGroup,
            };

            LedgerDetailService.processNonVoidedLineItem(processOptions);

            expect(LedgerDetailServiceUtils.activePostingDateNotVoided).toHaveBeenCalledWith(mockLineItemPlan, mockOptions, mockInvoice);
            expect(mockApplyItemToGroup).not.toHaveBeenCalled();
        });
    });
    describe('processModifiedCredit', () => {
        let mockCredit, mockItemGroups, mockInvoice;

        beforeEach(() => {
            mockCredit = {
                modificationHistory: [
                    { modifiedAt: new Date('2024-01-15'), amountModified: 50 }
                ],
                creditReason: 'adjustment'
            };

            mockItemGroups = { "Modified Credits": { total: 0, count: 0, amountIncreased: 0, amountDecreased: 0 } };

            mockInvoice = { orgId: 'org123', invoiceNumber: 'INV-001' };

            jest.spyOn(LedgerDetailServiceUtils, 'modifiedCreditIsWithinReportRange').mockReturnValue(true);
            jest.spyOn(LedgerDetailServiceUtils, 'processModifiedCreditLinkedDetail').mockReturnValue({ linked: 'detail' });
        });

        afterEach(() => {
            jest.restoreAllMocks();
        });

        it('should process a modified credit within the report range', () => {
            const result = LedgerDetailService.processModifiedCredit(mockCredit, mockItemGroups, mockInvoice, 'Test Descriptor', 'Default Group', new Date('2024-01-01'), new Date('2024-02-01'));

            expect(result).toHaveLength(1);
            expect(mockItemGroups["Modified Credits"].total).toBe(50);
            expect(mockItemGroups["Modified Credits"].count).toBe(1);
            expect(mockItemGroups["Modified Credits"].amountIncreased).toBe(50);
        });

        it('should skip modification history outside of the report range', () => {
            jest.spyOn(LedgerDetailServiceUtils, 'modifiedCreditIsWithinReportRange').mockReturnValue(false);
            const result = LedgerDetailService.processModifiedCredit(mockCredit, mockItemGroups, mockInvoice, 'Test Descriptor', 'Default Group', new Date('2024-02-01'), new Date('2024-03-01'));

            expect(result).toHaveLength(0);
        });
    });
    describe('handling deposit changes', () => {
        const mockDeposit = {
            _id: 'deposit123',
            orgId: 'org1',
            depositDate: '2024-01-15',
            depositTotal: 800,
            originalDepositTotal: 1500,
            memo: 'Test deposit',
            updatedAt: *************,
            history: [
                {
                    depositTotal: 1500,
                    date: *************,
                    personId: 'user1'
                },
                {
                    depositTotal: 1200,
                    date: *************,
                    personId: 'user2'
                }
            ]
        };

        const mockCashPayerDeposit = {
            payerName: 'Test Payer',
            cashAccountName: 'Test Account'
        };

        beforeEach(() => {
            jest.spyOn(BillingServerUtils, 'getCashPayerDepositAccount').mockResolvedValue(mockCashPayerDeposit);
            Deposits.find.mockReturnValue({
                fetchAsync: jest.fn().mockResolvedValue([mockDeposit])
            });
            People.find.mockReturnValue({
                fetchAsync: jest.fn().mockResolvedValue([])
            });
            Invoices.find.mockReturnValue({
                fetchAsync: jest.fn().mockResolvedValue([])
            });
            Orgs.findOneAsync.mockResolvedValue({
                getTimezone: () => 'America/New_York',
                hasCustomization: () => false,
                getAccountLedgerCodes: () => ([
                    {
                        'description': '4783 stuff',
                        'accountName': 'Stuff',
                        'accountNumber': '4783'
                    }
                ]),
                availableCreditMemoTypes: () => [],
                billingCardProviderName: () => 'adyen',
                getMappedLedgerAccount: () => ({
                    glImportIgnore: true,
                    accountName: '4783',
                    type: ledgerAccountTypes.MANUAL_DEPOSITS,
                    offsetAccountName: null
                })
            });
        });

        afterEach(() => {
            jest.restoreAllMocks();
        });

        it('should process deposit with history changes', async () => {
            const options = {
                startDate: *************,
                endDate: *************,
                includeBankDeposits: true,
                includeLinkedDetails: true,
                orgId: 'org1'
            };

            const itemGroups = await LedgerDetailService.LedgerEntriesForRange(options);
            expect(itemGroups["Manual Deposits"].total).toBe(800); // Final total after all changes
            expect(itemGroups["Manual Deposits"].count).toBe(3);  // Initial deposit + 2 changes

            const depositEntries = itemGroups["Manual Deposits"].itemsGroup["Test Payer Deposits"].linkedDetails;
            expect(depositEntries).toHaveLength(3);

            // Initial deposit
            expect(depositEntries[0]).toMatchObject({
                description: expect.stringContaining(mockDeposit.depositDate),
                date: "1/15/2024",
                amount: 1500,
                targetType: "deposit",
                target: mockDeposit._id
            });

            // First change
            expect(depositEntries[1]).toMatchObject({
                description: expect.stringContaining(mockDeposit.depositDate),
                date: "1/16/2024",
                amount: -300,
                targetType: "deposit",
                target: mockDeposit._id
            });

            // Second change
            expect(depositEntries[2]).toMatchObject({
                description: expect.stringContaining(mockDeposit.depositDate),
                date: "1/17/2024",
                amount: -400,
                targetType: "deposit",
                target: mockDeposit._id
            });
        });

        it('should handle deposit without cash payer information', async () => {
            jest.spyOn(BillingServerUtils, 'getCashPayerDepositAccount').mockResolvedValue(null);

            const options = {
                startDate: *************,
                endDate: *************,
                includeBankDeposits: true,
                includeLinkedDetails: true,
                orgId: 'org1'
            };

            const itemGroups = await LedgerDetailService.LedgerEntriesForRange(options);

            expect(itemGroups["Manual Deposits"].itemsGroup["Manual Deposits"]).toBeDefined();
            expect(itemGroups["Manual Deposits"].total).toBe(800);
            expect(itemGroups["Manual Deposits"].count).toBe(3);
        });
    });

    describe('LedgerDetailService - Adyen settlements processing', () => {
        let mockOrg, mockOptions;

        beforeEach(() => {
            // Mock organization with Adyen provider
            mockOrg = {
                _id: 'org123',
                getTimezone: jest.fn().mockReturnValue('America/New_York'),
                billingCardProviderName: jest.fn().mockReturnValue('adyen'),
                hasCustomization: jest.fn().mockReturnValue(false),
                getAccountLedgerCodes: jest.fn().mockReturnValue([]),
                availableCreditMemoTypes: jest.fn().mockReturnValue([]),
                getMappedLedgerAccount: jest.fn().mockReturnValue({
                    glImportIgnore: true,
                    accountName: '4783',
                    type: 'manualDeposits',
                    offsetAccountName: null
                })
            };

            // Mock options for ledger entries
            mockOptions = {
                startDate: *************, // 2023-01-01
                endDate: *************,   // 2023-02-01
                orgId: 'org123',
                includeBankDeposits: true,
                includeLinkedDetails: true
            };

            // Mock Orgs.findOneAsync to return our mockOrg
            Orgs.findOneAsync = jest.fn().mockResolvedValue(mockOrg);

            // Mock other collections that might be used
            Invoices.find = jest.fn().mockReturnValue({
                fetchAsync: jest.fn().mockResolvedValue([])
            });

            People.find = jest.fn().mockReturnValue({
                fetchAsync: jest.fn().mockResolvedValue([])
            });

            Deposits.find = jest.fn().mockReturnValue({
                fetchAsync: jest.fn().mockResolvedValue([])
            });

            // Mock AdyenProvider methods
            jest.spyOn(AdyenProvider, 'billingPayoutsReport').mockResolvedValue({
                data: [
                    {
                        arrival_date: *************, // 2023-01-10
                        amount: 100.00,
                        id: 'payout123',
                        ref: 'ref456',
                        org: 'Test Org',
                        orgId: 'org123'
                    }
                ]
            });

            jest.spyOn(AdyenProvider, 'billingPayoutDetailReport').mockResolvedValue({
                transferData: {
                    available_on: '2023-01-10',
                    net: 95.50,
                    fee: 4.50,
                    chargebackFee: 15.00
                }
            });

            // Mock MiscUtils.roundToTwo
            jest.spyOn(MiscUtils, 'roundToTwo').mockImplementation(val => Math.round(val * 100) / 100);

            // Mock moment.tz and format
            global.moment = {
                tz: jest.fn().mockReturnValue({
                    format: jest.fn().mockImplementation(format => {
                        if (format === "MM/DD/YYYY") return "01/01/2023";
                        return "1/10/2023";
                    })
                }),
                mockImplementation: jest.fn().mockReturnValue({
                    format: jest.fn().mockReturnValue("1/10/2023")
                })
            };
            global.moment.mockImplementation = jest.fn().mockReturnValue({
                format: jest.fn().mockReturnValue("1/10/2023")
            });

            // Mock lodash chain
            global._ = {
                chain: jest.fn().mockReturnValue({
                    map: jest.fn().mockReturnValue({
                        uniq: jest.fn().mockReturnValue([
                            { payoutId: 'payout123', payoutReference: 'ref456' }
                        ])
                    })
                }),
                each: jest.fn()
            };
        });

        afterEach(() => {
            jest.restoreAllMocks();
        });

        it('should process Adyen settlements and fees for adyen provider', async () => {
            // Call the actual method from LedgerDetailService
            const itemGroups = await LedgerDetailService.LedgerEntriesForRange(mockOptions);

            // Verify AdyenProvider.billingPayoutsReport was called with correct parameters
            expect(AdyenProvider.billingPayoutsReport).toHaveBeenCalledWith({
                orgIds: [mockOrg._id],
                startDate: expect.any(String),
                endDate: expect.any(String),
                excludeEndDate: true
            });

            // Verify AdyenProvider.billingPayoutDetailReport was called for each payout
            expect(AdyenProvider.billingPayoutDetailReport).toHaveBeenCalledWith({
                org: mockOrg,
                payoutId: 'payout123',
                payoutReference: 'ref456'
            });

            // Check that the itemGroups contains the expected settlement data
            expect(itemGroups).toHaveProperty('Settlements');
            expect(itemGroups.Settlements.total).toBe(95.50);
            expect(itemGroups.Settlements.count).toBe(1);

            // Check that the itemGroups contains the expected settlement fees data
            expect(itemGroups).toHaveProperty('Settlement Fees');
            expect(itemGroups['Settlement Fees'].total).toBe(4.50);
            expect(itemGroups['Settlement Fees'].count).toBe(1);

            // Check that the itemGroups contains the expected chargeback fees data
            expect(itemGroups).toHaveProperty('Chargeback Fees');
            expect(itemGroups['Chargeback Fees'].total).toBe(15.00);
            expect(itemGroups['Chargeback Fees'].count).toBe(1);
        });

        it('should process Adyen settlements for adyen_balance provider', async () => {
            // Change provider to adyen_balance
            mockOrg.billingCardProviderName.mockReturnValue('adyen_balance');

            // Call the actual method
            const itemGroups = await LedgerDetailService.LedgerEntriesForRange(mockOptions);

            // Verify AdyenProvider.billingPayoutsReport was still called
            expect(AdyenProvider.billingPayoutsReport).toHaveBeenCalled();

            // Verify AdyenProvider.billingPayoutDetailReport was called
            expect(AdyenProvider.billingPayoutDetailReport).toHaveBeenCalled();

            // Check that the itemGroups contains the expected settlement data
            expect(itemGroups).toHaveProperty('Settlements');
            expect(itemGroups.Settlements.total).toBe(95.50);
        });

        it('should not process chargeback fees for settlements before 2022-10-01', async () => {
            // Mock a settlement date before the cutoff
            jest.spyOn(AdyenProvider, 'billingPayoutDetailReport').mockResolvedValue({
                transferData: {
                    available_on: '2022-09-30', // Before the cutoff date
                    net: 95.50,
                    fee: 4.50,
                    chargebackFee: 15.00
                }
            });

            // Call the actual method
            const itemGroups = await LedgerDetailService.LedgerEntriesForRange(mockOptions);

            // Check that the itemGroups contains the expected settlement data
            expect(itemGroups).toHaveProperty('Settlements');
            expect(itemGroups.Settlements.total).toBe(95.50);

            // Check that the itemGroups contains the expected settlement fees data
            expect(itemGroups).toHaveProperty('Settlement Fees');
            expect(itemGroups['Settlement Fees'].total).toBe(4.50);

            // Check that the itemGroups does NOT contain chargeback fees or has zero total
            if (itemGroups['Chargeback Fees']) {
                expect(itemGroups['Chargeback Fees'].total).toBe(0);
                expect(itemGroups['Chargeback Fees'].count).toBe(0);
            }
        });

        it('should handle empty payout details gracefully', async () => {
            // Mock empty payout details
            jest.spyOn(AdyenProvider, 'billingPayoutDetailReport').mockResolvedValue({});

            // Call the actual method
            const itemGroups = await LedgerDetailService.LedgerEntriesForRange(mockOptions);

            // Verify AdyenProvider.billingPayoutsReport was called
            expect(AdyenProvider.billingPayoutsReport).toHaveBeenCalled();

            // Verify AdyenProvider.billingPayoutDetailReport was called
            expect(AdyenProvider.billingPayoutDetailReport).toHaveBeenCalled();

            // Check that the settlement-related itemGroups have zero totals
            if (itemGroups['Settlements']) {
                expect(itemGroups['Settlements'].total).toBe(0);
            }
            if (itemGroups['Settlement Fees']) {
                expect(itemGroups['Settlement Fees'].total).toBe(0);
            }
            if (itemGroups['Chargeback Fees']) {
                expect(itemGroups['Chargeback Fees'].total).toBe(0);
            }
        });

        it('should skip processing for non-Adyen providers', async () => {
            // Change provider to something other than adyen or adyen_balance
            mockOrg.billingCardProviderName.mockReturnValue('stripe');

            // Call the actual method
            const itemGroups = await LedgerDetailService.LedgerEntriesForRange(mockOptions);

            // Verify AdyenProvider methods were not called
            expect(AdyenProvider.billingPayoutsReport).not.toHaveBeenCalled();
            expect(AdyenProvider.billingPayoutDetailReport).not.toHaveBeenCalled();
        });
    });
});