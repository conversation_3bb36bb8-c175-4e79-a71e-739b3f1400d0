import { Meteor } from 'meteor/meteor';
import {
    ADYEN_WEBHOOK_STATUS_CODES,
    ADYEN_WEBHOOKS,
    AdyenProvider,
    getMomentTzByTz,
    clearCache
} from "../../../../server/card_providers/adyenProvider";
import { afterAll, beforeAll, beforeEach, jest } from '@jest/globals';
import expect from 'expect';
import { ConfigurationSettingsService, ServerConfigurationConstants } from '../../../../server/config/configurationSettingsService';
import { mockMeteor } from '../../../helpers/meteorMock';
import { HistoryAuditService } from '../../../../server/historyAuditService';
import { CheckoutAPI } from '@adyen/api-library';
import Papa from 'papaparse';
import { EmailService } from '../../../../server/emails/emailService';
import { AdyenNotifications } from "../../../../server/collections/adyenNotifications";
import { CustomerChargebacksInvoices } from "../../../../lib/collections/customerChargebacksInvoices";
import { AdyenTransactions } from "../../../../lib/collections/adyenTransactions";
import { Orgs } from "../../../../lib/collections/orgs";
import { AdyenReports } from "../../../../lib/collections/adyenReports";
import { Invoices } from "../../../../lib/collections/invoices";
import moment from 'moment';
import { AdyenReportsService } from '../../../../server/adyen/adyenReportsService'; // Assume this is the correct import
import { AdyenBalancePaymentProvider } from '../../../../server/card_providers/adyenBalancePaymentProvider';

let invoice = {};
let org = null;
const person = { _id: '17aad213' };
Object.defineProperty(global, 'fetch', {
    writable: true,
    value: jest.fn(),
});
global.fetch = jest.fn();
jest.mock('../../../../server/adyen/adyenReportsService');

jest.mock('meteor/http');
jest.mock('papaparse');

const adyenNotificationsMock = AdyenNotifications;
const chargebackInvoicesMock = CustomerChargebacksInvoices;
const findOneAsyncMock = AdyenTransactions.findOneAsync;
const adyenReportsAggMock = AdyenReports.aggregate;

const getTimestampFromMockData = (dateStr, dateTmz) => {
    return moment.tz(dateStr, "YYYY-MM-DD[T]HH:mm:ss", getMomentTzByTz(dateTmz)).valueOf();
}

Meteor.settings = {
    adyen: {
        reportingUsername: 'testUser',
        reportingPassword: 'testPassword',
    },
};

const mockOrg = {
    _id: 'nTvbx24M2dbM9w6tu',
    name: '321-Mariposa Local',
    billing: {
        adyenInfo: {
            accountCode: '****************'
        }
    },
    getTimezone: () => 'America/Chicago'
};

const mockPayoutsDetail = [
    {
        _id: 'm3RSAL9x3vjHDzF8S',
        date: '2022-05-01',
        cols: [
            '', '', '****************', 'Payout', '****************',
            '', '', '', '', '', '', '2022-05-01T16:34:30.000Z', 'EDT', '', '19.01'
        ]
    },
    {
        _id: 'Eogbx2XzNfLNtfqd7',
        date: '2022-05-04',
        cols: [
            '', '', '****************', 'Payout', '****************',
            '', '', '', '', '', '', '2022-05-04T16:34:30.000Z', 'EDT', '', '19.01'
        ]
    },
    {
        _id: 'JSL2HjJ9tYiZYXQwd',
        date: '2022-09-15',
        cols: [
            '', '', '****************', 'Payout', '****************',
            '', '', '', '', '', '', '2022-09-15T16:30:47.000Z', 'EDT', '', '1129.70'
        ]
    }
];

const mockTopUpTransaction = {
    _id: { _str: '670ec3b8de5a3b1102892b68' },
    datetime: '2024-10-15T16:34:16.057Z',
    amount: 1500,
    accountCode: '****************',
    type: 'TOP_UP',
    response: {
        pspReference: 'pspReference123'
    }
};

// Mock Mongo.ObjectID for Jest
global.Mongo = {
    ObjectID: jest.fn().mockImplementation((id) => id)
};

describe('AdyenProvider class tests', () => {
    afterAll(async () => {
        jest.clearAllMocks();
    });
    describe('getPersonPaymentAmountForInvoice tests with family splits', () => {
        beforeAll(() => {
            invoice = {
                openAmount: 100,
                amountDueForFamilyMember: jest.fn().mockReturnValue(50),
                getFamilySplits: jest.fn().mockReturnValue([{ [person._id]: 50, 'aaaaa': 50 }])
            }
        });
        it('should throw an error for overpayments when not allowed dynamically', () => {
            org = {
                hasCustomization: jest.fn().mockReturnValue(false),
            }
            // Make an overpayment of 25
            // That should throw an error since we don't allow overpayments in this test
            const result = () => AdyenProvider.getPersonPaymentAmountForInvoice(invoice, person, org, 75, false);
            expect(result).toThrow(Error);
            expect(result).toThrow('Family Invoice Overpayment');
        });
        it('should throw an error for overpayments when not allowed by customization', () => {
            org = {
                hasCustomization: jest.fn().mockReturnValue(true),
            }
            // Make an overpayment of 25
            // That should throw an error since we don't allow overpayments in this test via a customization
            const result = () => AdyenProvider.getPersonPaymentAmountForInvoice(invoice, person, org, 75, true);
            expect(result).toThrow(Error);
            expect(result).toThrow('Family Invoice Overpayment');
        });
        it('should work correctly for overpayments with family splits', () => {
            org = {
                hasCustomization: jest.fn().mockReturnValue(false),
            }
            // Make an overpayment of 25
            // This should work since it is allowed through the call and there is no customization blocking it
            expect(AdyenProvider.getPersonPaymentAmountForInvoice(invoice, person, org, 75, true))
                .toStrictEqual({ appliedAmount: 50, overpaymentAmount: 25 });
        });
        it('should work correctly for no overpayments with family splits', () => {
            org = {
                hasCustomization: jest.fn().mockReturnValue(false),
            }
            // Make an overpayment of 25
            // This should work since it is allowed through the call and there is no customization blocking it
            expect(AdyenProvider.getPersonPaymentAmountForInvoice(invoice, person, org, 50, true))
                .toStrictEqual({ appliedAmount: 50, overpaymentAmount: 0 });
        });
    });

    describe('handleNotification', () => {
        let orgsFindOneMock;
        beforeEach(() => {
            orgsFindOneMock = Orgs.findOneAsync;
            jest.clearAllMocks();
        });
        it('email adyen account holder when top up occurs', async () => {
            const item = {
                "eventDate": "2021-02-01T14:19:14-08:00",
                "eventType": "DIRECT_DEBIT_INITIATED",
                "live": "false",
                "pspReference": "****************",
                "content": {
                    "accountCode": "****************",
                    "amount": {
                        "currency": "USD",
                        "value": 6200
                    },
                    "debitInitiationDate": "2021-02-01",
                    "splits": [
                        {
                            "account": "****************",
                            "amount": {
                                "currency": "EUR",
                                "value": 6000
                            },
                            "reference": "YOUR_SPLIT_REFERENCE_1",
                            "type": "MarketPlace",
                        },
                        {
                            "amount": {
                                "currency": "EUR",
                                "value": 200
                            },
                            "reference": "YOUR_SPLIT_REFERENCE_2",
                            "type": "Commission",
                        }
                    ],
                    "status": {
                        "statusCode": "Initiated"
                    }
                }
            };

            const sendEmailSpy = jest.spyOn(EmailService, 'sendEmail')
                .mockImplementation((...args) => args);

            const orgs = [
                {
                    _id: '1',
                    name: 'test org',
                    billing: {
                        adyenInfo: {
                            accountCode: '****************',
                            email: '<EMAIL>'
                        }
                    },
                    valueOverrides: {
                        chargebackInvoiceEmail: '<EMAIL>'
                    }
                },
                {
                    _id: '2',
                    name: 'other org',
                    billing: {
                        adyenInfo: {
                            accountCode: 'different-account-code',
                            email: '<EMAIL>'
                        }
                    }
                }
            ];

            orgsFindOneMock.mockImplementation((query) => {
                return orgs.find(org =>
                    org.billing.adyenInfo.accountCode === query["billing.adyenInfo.accountCode"]
                );
            });

            await AdyenProvider.handleNotification(item);

            expect(sendEmailSpy).toHaveBeenCalledWith(
                '1',
                'directDebitInitiatedEmail',
                'email_templates/direct_debit_initiated_email.html',
                'test org Registrations <<EMAIL>>',
                '<EMAIL>',
                `Balance Adjustment for test org`,
                { amount: 6200 }
            );
        });
        it('email adyen account holder when top up FAILED', async () => {
            const item = {
                content: {
                    status: {
                        statusCode: "Failed"
                    }
                }
            };

            await AdyenProvider.handleNotification(item);
            expect(orgsFindOneMock).not.toHaveBeenCalled();
        });
        it('should log that an event came in for every request', async () => {
            const item = {
                eventType: 'TestEventType'
            };
            await AdyenProvider.handleNotification(item);
            expect(adyenNotificationsMock.insertAsync).toHaveBeenCalledTimes(1);
            expect(adyenNotificationsMock.insertAsync.mock.calls[0]).toStrictEqual([
                {
                    timestamp: expect.any(Number),
                    rawData: JSON.stringify(item),
                    processed: false,
                    eventType: 'TestEventType'
                }]
            );
        });
        it('should log successful transfer funds responses', async () => {
            const item = {
                eventType: ADYEN_WEBHOOKS.TRANSFER_FUNDS,
                content: {
                    status: {
                        statusCode: ADYEN_WEBHOOK_STATUS_CODES.SUCCESS
                    }
                },
                pspReference: 'pspReference123'
            };

            await AdyenProvider.handleNotification(item);
            expect(adyenNotificationsMock.insertAsync).toHaveBeenCalledTimes(1);
            expect(adyenNotificationsMock.insertAsync.mock.calls[0]).toStrictEqual([
                {
                    timestamp: expect.any(Number),
                    rawData: JSON.stringify(item),
                    processed: false,
                    eventType: ADYEN_WEBHOOKS.TRANSFER_FUNDS
                }
            ]);
            expect(chargebackInvoicesMock.updateAsync).toHaveBeenCalledTimes(1);
            expect(chargebackInvoicesMock.updateAsync.mock.calls[0]).toStrictEqual([
                {
                    $or: [
                        { "confirmationResponse.pspReference": 'pspReference123' },
                        { "confirmationResponse.data.pspReference": 'pspReference123' }
                    ]
                },
                {
                    $set: {
                        "confirmedAt": expect.any(Number),
                        "confirmedMessage": item
                    }
                }
            ]);
        });
        it('should log failed transfer funds responses', async () => {
            const item = {
                eventType: ADYEN_WEBHOOKS.TRANSFER_FUNDS,
                content: {
                    status: {
                        statusCode: ADYEN_WEBHOOK_STATUS_CODES.FAILED
                    }
                },
                pspReference: 'pspReference123'
            };

            await AdyenProvider.handleNotification(item);
            expect(adyenNotificationsMock.insertAsync).toHaveBeenCalledTimes(1);
            expect(adyenNotificationsMock.insertAsync.mock.calls[0]).toStrictEqual([
                {
                    timestamp: expect.any(Number),
                    rawData: JSON.stringify(item),
                    processed: false,
                    eventType: ADYEN_WEBHOOKS.TRANSFER_FUNDS
                }
            ]);
            expect(chargebackInvoicesMock.updateAsync).toHaveBeenCalledTimes(1);
            expect(chargebackInvoicesMock.updateAsync.mock.calls[0]).toStrictEqual([
                {
                    $or: [
                        { "confirmationResponse.pspReference": 'pspReference123' },
                        { "confirmationResponse.data.pspReference": 'pspReference123' }
                    ]
                },
                {
                    $set: {
                        "failedAt": expect.any(Number),
                        "failedMessage": item
                    }
                }
            ]);
        });
        it('should pull adyenReports when notified', async () => {
            const item = {
                eventType: 'REPORT_AVAILABLE',
                pspReference: 'marketplace_payments_accounting_report_2023_01_01.csv',
                content: { remoteAccessUrl: 'https://example.com/report.csv' },
            };

            const mockResponse = {
                ok: true,
                text: jest.fn().mockResolvedValue('report data'),
            };
            global.fetch.mockResolvedValue(mockResponse);

            AdyenReportsService.saveReport.mockResolvedValue(['reportId']);

            await AdyenProvider.handleNotification(item);

            const authString = `${Meteor.settings.adyen.reportingUsername}:${Meteor.settings.adyen.reportingPassword}`;
            const base64Auth = Buffer.from(authString).toString('base64');

            // Assert
            expect(global.fetch).toHaveBeenCalledWith('https://example.com/report.csv', {
                method: 'GET',
                headers: { Authorization: `Basic ${base64Auth}` },
            });

            expect(AdyenReportsService.saveReport).toHaveBeenCalledWith(
                'marketplace_payments_accounting_report',
                expect.any(Number),
                '2023-01-01',
                'report data'
            );

            expect(AdyenReportsService.parseReports).toHaveBeenCalledWith(['reportId']);
        });
        it('should log invalid transfer funds responses', async () => {
            const item = {
                eventType: ADYEN_WEBHOOKS.TRANSFER_FUNDS,
                pspReference: 'pspReference123'
            };

            await AdyenProvider.handleNotification(item);
            expect(adyenNotificationsMock.insertAsync).toHaveBeenCalledTimes(1);
            expect(adyenNotificationsMock.insertAsync.mock.calls[0]).toStrictEqual([
                {
                    timestamp: expect.any(Number),
                    rawData: JSON.stringify(item),
                    processed: false,
                    eventType: ADYEN_WEBHOOKS.TRANSFER_FUNDS
                }
            ]);
            expect(chargebackInvoicesMock.updateAsync).toHaveBeenCalledTimes(1);
            expect(chargebackInvoicesMock.updateAsync.mock.calls[0]).toStrictEqual([
                {
                    $or: [
                        { "confirmationResponse.pspReference": 'pspReference123' },
                        { "confirmationResponse.data.pspReference": 'pspReference123' }
                    ]
                },
                {
                    $set: {
                        "failedAt": expect.any(Number),
                        "failedMessage": item
                    }
                }
            ]);
        });
    });

    describe('getPersonPaymentAmountForInvoice tests with no family splits', () => {
        beforeAll(() => {
            invoice = {
                openAmount: 100,
                amountDueForFamilyMember: jest.fn().mockReturnValue(50),
                getFamilySplits: jest.fn().mockReturnValue(null)
            }
        });
        it('should throw an error for overpayments when not allowed dynamically with no family splits', () => {
            org = {
                hasCustomization: jest.fn().mockReturnValue(false),
            }
            // Make an overpayment of 25
            // That should throw an error since we don't allow overpayments in this test
            const result = () => AdyenProvider.getPersonPaymentAmountForInvoice(invoice, person, org, 125, false);
            expect(result).toThrow(Error);
            expect(result).toThrow('Invoice Overpayment');
        });
        it('should throw an error for overpayments when not allowed by customization with no family splits', () => {
            org = {
                hasCustomization: jest.fn().mockReturnValue(true),
            }// Make an overpayment of 25
            // That should throw an error since we don't allow overpayments in this test via a customization
            const result = () => AdyenProvider.getPersonPaymentAmountForInvoice(invoice, person, org, 125, true);
            expect(result).toThrow(Error);
            expect(result).toThrow('Invoice Overpayment');
        });
        it('should work correctly for overpayments with no family splits', () => {
            org = {
                hasCustomization: jest.fn().mockReturnValue(false),
            }
            // Make an overpayment of 25
            // This should work since it is allowed through the call and there is no customization blocking it
            expect(AdyenProvider.getPersonPaymentAmountForInvoice(invoice, person, org, 125, true))
                .toStrictEqual({ appliedAmount: 100, overpaymentAmount: 25 });
        });
        it('should work correctly for no overpayments with no family splits', () => {
            org = {
                hasCustomization: jest.fn().mockReturnValue(false),
            }
            // Make an overpayment of 25
            // This should work since it is allowed through the call and there is no customization blocking it
            expect(AdyenProvider.getPersonPaymentAmountForInvoice(invoice, person, org, 100, true))
                .toStrictEqual({ appliedAmount: 100, overpaymentAmount: 0 });
        });
    });

    describe('assessChargebackFee', () => {
        const mockOrg = {
            _id: 'org123',
            billing: {
                adyenInfo: {
                    accountCode: 'orgAccountCode123'
                }
            }
        };
        let orgsMock = null;
        const options = {
            orgId: 'org123',
            amount: 100,
            merchantReference: 'merchantRef123',
            chargebackId: 'chargebackId123'
        };
        const mockMpAccountCode = 'mpAccountCode123';
        const mockApiPassword = 'mockApiPassword123';
        const mockApiUser = 'mockApiUser123';
        const mockAdyenTransferFundsUrl = 'https://adyen-transfer-funds-url.com';

        beforeEach(() => {
            jest.clearAllMocks();
            mockMeteor();
            const mockConfig = jest.mock();
            const configSpy = mockConfig.spyOn(ConfigurationSettingsService, 'getServerConfigurationSetting').mockImplementation((key) => {
                if (key === ServerConfigurationConstants.ADYEN_DEFAULT_ACCOUNT_CODE) {
                    return mockMpAccountCode;
                }
                if (key === ServerConfigurationConstants.ADYEN_TRANSFER_FUNDS_URL) {
                    return mockAdyenTransferFundsUrl;
                }
                if (key === ServerConfigurationConstants.ADYEN_API_PASSWORD) {
                    return mockApiPassword;
                }
                if (key === ServerConfigurationConstants.ADYEN_API_USER) {
                    return mockApiUser;
                }
                return null;
            });
            orgsMock = Orgs.findOneAsync;
            orgsMock.mockReturnValue(mockOrg);
        });

        it('should return early if mpAccountCode is not configured', async () => {
            jest.spyOn(ConfigurationSettingsService, 'getServerConfigurationSetting').mockReturnValueOnce(null);

            await AdyenProvider.assessChargebackFee(options);

            expect(orgsMock.mock.calls.length).toBe(0);
            expect(global.fetch).not.toHaveBeenCalled();
        });

        it('should throw an error if org is not found', async () => {
            orgsMock.mockReturnValueOnce(null);

            await expect(AdyenProvider.assessChargebackFee(options)).rejects.toThrow('Invalid for chargeback fee processing:' + options);

            expect(global.fetch).not.toHaveBeenCalled();
        });

        it('should make a fetch call with correct parameters and return the response', async () => {
            const mockResponse = { success: true };
            global.fetch = jest.fn().mockResolvedValue({
                ok: true,
                json: async () => mockResponse
            });
        

        
        
        
        
        
            const response = await AdyenProvider.assessChargebackFee(options);

            expect(fetch).toHaveBeenCalledWith(
                mockAdyenTransferFundsUrl,
                {
                    method: 'POST',
                    body: JSON.stringify({
                        sourceAccountCode: mockOrg.billing.adyenInfo.accountCode,
                        destinationAccountCode: mockMpAccountCode,
                        amount: {
                            "value": options.amount * 100,
                            "currency": "USD"
                        },
                        merchantReference: options.merchantReference,
                        transferCode: "DEBIT_CHARGEBACK_FEE"
                    }),
                    headers: {
                        'Authorization': 'Basic ' + btoa(mockApiUser + ":" + mockApiPassword),
                        'Content-Type': 'application/json',
                    }
                }
            );
            expect(response).toEqual(mockResponse.data);
        });

        it('should log and throw an error if fetch call fails', async () => {
            global.fetch.mockImplementation((method, url, params, callback) => {
                callback(new Error('Http call error'), null);
            });
            await expect(AdyenProvider.assessChargebackFee(options)).rejects.toThrow('There was an error processing the chargeback.');
        });
    });

    describe('refundCharge', () => {
        let orgMock, invoiceMock, logRefundMock, currentUserMock, getApiClientMock, refundMock;

        beforeEach(() => {
            jest.clearAllMocks();

            // Mock getApiClient to completely bypass any real configuration or `apiKey` access
            getApiClientMock = jest.spyOn(AdyenProvider, 'getApiClient');
            getApiClientMock.mockResolvedValue({});

            // Mock the Modification class and its refund method
            refundMock = jest.fn().mockResolvedValue({ status: 'received' });

            // Mock Modification class with refund method
            jest.spyOn(CheckoutAPI.prototype, 'ModificationsApi', 'get').mockImplementation(() => ({
                refundCapturedPayment: refundMock
            }));

            // Mock HistoryAuditService.logRefund to just check if it's called with correct data
            logRefundMock = jest.spyOn(HistoryAuditService, 'logRefund').mockImplementation(() => {});

            // Mocking the Orgs.findOne to return a test organization
            orgMock = Orgs.findOneAsync;
            orgMock.mockReturnValue({
                _id: 'org123',
                billing: { adyenInfo: { accountCode: 'orgAccountCode123' } }
            });

            // Mocking Invoices.findOne to return a test invoice
            invoiceMock = Invoices.findOneAsync;
            invoiceMock.mockReturnValue({
                _id: 'invoice123',
                credits: [
                    { type: 'payment', adyenInfo: { pspReference: 'charge123' }, paidBy: 'parentId123' }
                ],
                invoiceNumber: 'INV123',
                personId: 'childId123'
            });

            // Manually mock the `updateByIdWithJournalEntry` method for the `Invoices` object
            Invoices.updateByIdWithJournalEntry = jest.fn();

            // Mocking current user
            currentUserMock = { _id: 'user123', personId: 'person123' };
        });

        it('should process the refund successfully', async () => {
            const options = {
                currentUser: currentUserMock,
                orgId: 'org123',
                refund_amount: 100,
                charge_id: 'charge123',
                invoiceId: 'invoice123',
                creditedByPersonId: 'person456',
                refund_reason: 'Test Refund',
                refund_note: 'Test Note',
                isRetry: false
            };

            await AdyenProvider.refundCharge(options);

            expect(getApiClientMock).toHaveBeenCalled();
            expect(refundMock).toHaveBeenCalledWith(expect.stringMatching(options.charge_id), expect.objectContaining({
                merchantAccount: 'TendlyLLCDbaMomentPathMP',
                amount: {
                    value: options.refund_amount * 100,
                    currency: 'USD'
                },
                reference: options.invoiceId,
                splits: expect.any(Array)
            }));
            expect(logRefundMock).toHaveBeenCalledWith(expect.objectContaining({
                amount: parseFloat(options.refund_amount),
                invoiceNumber: 'INV123',
                invoiceId: 'invoice123',
                parentId: 'parentId123',
                childId: 'childId123',
                performedByUser: currentUserMock
            }));
        });

        it('should throw an error when refund fails', async () => {
            refundMock.mockRejectedValueOnce(new Error('Refund Error'));

            const options = {
                currentUser: currentUserMock,
                orgId: 'org123',
                refund_amount: 100,
                charge_id: 'charge123',
                invoiceId: 'invoice123',
                creditedByPersonId: 'person456',
                refund_reason: 'Test Refund',
                refund_note: 'Test Note',
                isRetry: false
            };

            await expect(AdyenProvider.refundCharge(options)).rejects.toThrow(new Meteor.Error(500, "There was an error processing the refund."));
            expect(logRefundMock).not.toHaveBeenCalled();
        });

        it('should process the refund successfully when retrying', async () => {
            const options = {
                currentUser: currentUserMock,
                orgId: 'org123',
                refund_amount: 100,
                charge_id: 'charge123',
                invoiceId: 'invoice123',
                creditedByPersonId: 'person456',
                refund_reason: 'Test Refund',
                refund_note: 'Test Note',
                isRetry: true,
                retryCreditIndex: 0
            };

            await AdyenProvider.refundCharge(options);

            expect(getApiClientMock).toHaveBeenCalled();
            expect(refundMock).toHaveBeenCalledWith(expect.stringMatching(options.charge_id), expect.objectContaining({
                merchantAccount: 'TendlyLLCDbaMomentPathMP',
                amount: {
                    value: options.refund_amount * 100,
                    currency: 'USD'
                },
                reference: options.invoiceId,
                splits: expect.any(Array)
            }));
            expect(Invoices.updateByIdWithJournalEntry).toHaveBeenCalled();
        });

        it('should process the refund without currentUser', async () => {
            const options = {
                currentUser: null,
                orgId: 'org123',
                refund_amount: 100,
                charge_id: 'charge123',
                invoiceId: 'invoice123',
                creditedByPersonId: 'person456',
                refund_reason: 'Test Refund',
                refund_note: 'Test Note',
                isRetry: false
            };

            await AdyenProvider.refundCharge(options);

            expect(getApiClientMock).toHaveBeenCalled();
            expect(refundMock).toHaveBeenCalledWith(expect.stringMatching(options.charge_id), expect.objectContaining({
                merchantAccount: 'TendlyLLCDbaMomentPathMP',
                amount: {
                    value: options.refund_amount * 100,
                    currency: 'USD'
                },
                reference: options.invoiceId,
                splits: expect.any(Array)
            }));
            expect(logRefundMock).toHaveBeenCalledWith(expect.objectContaining({
                amount: parseFloat(options.refund_amount),
                invoiceNumber: 'INV123',
                invoiceId: 'invoice123',
                parentId: 'parentId123',
                childId: 'childId123',
                performedByUser: null
            }));
        });
    });

    describe('billingPayoutDetailTopUp', () => {
        const mockAdyenTransaction = {
            _id: 'mock-object-id',
            amount: 1000,
            datetime: '2024-01-01T00:00:00Z',
            response: {
                pspReference: 'pspReference123'
            }
        };

        // Create a consistent mock instance for ObjectID
        const mockObjectIDInstance = { toString: () => 'mock-object-id' };
        const MockObjectID = jest.fn(() => mockObjectIDInstance);

        beforeEach(() => {
            jest.clearAllMocks();
            findOneAsyncMock.mockImplementation(() => mockAdyenTransaction);
            global.Mongo = { ObjectID: MockObjectID };
        });

        it('should handle successful transaction with positive amount', async () => {
            const options = {
                payoutId: 'mock-object-id'
            };

            const result = await AdyenProvider.billingPayoutDetailTopUp(options);

            expect(findOneAsyncMock).toHaveBeenCalledWith({
                _id: { $in: ['mock-object-id', mockObjectIDInstance] }
            });

            expect(result).toEqual({
                data: [{
                    type: 'balance_adjustment',
                    arrival_date: mockAdyenTransaction.datetime,
                    amount: -10, // Should be negative
                    fee: 0,
                    net: -10,
                    description: 'Balance Adjustment',
                    subDescription: 'Transaction resolves negative balance in payment account due to refunds, chargebacks, and other payment errors',
                    source: {
                        id: mockAdyenTransaction.response.pspReference,
                    },
                    id: '',
                    destinationType: null
                }],
                transferData: {
                    available_on: mockAdyenTransaction.datetime,
                    net: -10,
                    fee: 0,
                    chargebackFee: 0,
                }
            });
        });

        it('should handle transaction with negative amount', async () => {
            const negativeTransaction = {
                ...mockAdyenTransaction,
                amount: -500
            };
            findOneAsyncMock.mockImplementation(() => negativeTransaction);

            const result = await AdyenProvider.billingPayoutDetailTopUp({
                payoutId: 'mock-object-id'
            });

            expect(result.data[0].amount).toBe(-5);
            expect(result.transferData.net).toBe(-5);
        });

        it('should handle transaction with zero amount', async () => {
            const zeroTransaction = {
                ...mockAdyenTransaction,
                amount: 0
            };
            findOneAsyncMock.mockImplementation(() => zeroTransaction);

            const result = await AdyenProvider.billingPayoutDetailTopUp({
                payoutId: 'mock-object-id'
            });

            expect(result.data[0].amount).toBe(0);
            expect(result.transferData.net).toBe(0);
        });

        it('should throw error when no transaction is found', async () => {
            findOneAsyncMock.mockImplementation(() => null);

            await expect(AdyenProvider.billingPayoutDetailTopUp({
                payoutId: 'NONEXISTENT'
            })).rejects.toThrow('Transaction not found');
        });

        it('should maintain consistent data structure across all properties', async () => {
            const result = await AdyenProvider.billingPayoutDetailTopUp({
                payoutId: 'mock-object-id'
            });

            // Check data structure
            expect(result).toHaveProperty('data');
            expect(result).toHaveProperty('transferData');
            expect(Array.isArray(result.data)).toBe(true);
            expect(result.data).toHaveLength(1);

            // Check required properties in data object
            const dataItem = result.data[0];
            expect(dataItem).toHaveProperty('type', 'balance_adjustment');
            expect(dataItem).toHaveProperty('arrival_date');
            expect(dataItem).toHaveProperty('amount');
            expect(dataItem).toHaveProperty('fee');
            expect(dataItem).toHaveProperty('net');
            expect(dataItem).toHaveProperty('description');
            expect(dataItem).toHaveProperty('subDescription');
            expect(dataItem).toHaveProperty('source');
            expect(dataItem).toHaveProperty('id');
            expect(dataItem).toHaveProperty('destinationType');

            // Check transferData properties
            expect(result.transferData).toHaveProperty('available_on');
            expect(result.transferData).toHaveProperty('net');
            expect(result.transferData).toHaveProperty('fee');
            expect(result.transferData).toHaveProperty('chargebackFee');
        });

        it('should handle transaction with decimal amounts', async () => {
            const decimalTransaction = {
                ...mockAdyenTransaction,
                amount: 100.50
            };
            findOneAsyncMock.mockImplementation(() => decimalTransaction);

            const result = await AdyenProvider.billingPayoutDetailTopUp({
                payoutId: 'mock-object-id'
            });

            expect(result.data[0].amount).toBe(-1.0050);
            expect(result.transferData.net).toBe(-1.0050);
        });

        it('should verify net amount equals amount minus fees', async () => {
            const result = await AdyenProvider.billingPayoutDetailTopUp({
                payoutId: 'mock-object-id'
            });

            const dataItem = result.data[0];
            expect(dataItem.net).toBe(dataItem.amount - dataItem.fee);
        });
    });

    describe('billingPayoutsReport', () => {
        beforeAll(() => {
            let orgsMock = null;
            orgsMock = Orgs.findOneAsync;
            orgsMock.mockReturnValue(mockOrg);

            adyenReportsAggMock.mockReturnValue({
                toArray: jest.fn().mockResolvedValue(mockPayoutsDetail)
            });

            let adyenTransactionsMock = AdyenTransactions.find;
            adyenTransactionsMock.mockReturnValue({
                fetchAsync: () => [mockTopUpTransaction]
            });

            // Add this: Mock moment timezone to always return UTC
            jest.spyOn(moment.tz, 'guess').mockReturnValue('UTC');
        });

        beforeEach(() => {
            jest.clearAllMocks();
        });

        it('should generate correct report with both payouts and top-ups', async () => {
            const options = {
                startDate: '10/23/2021',
                endDate: '10/23/2024',
                stripeLegacy: false,
                orgIds: [mockOrg._id]
            };

            const expectedOutput = [
                {
                    arrival_date: getTimestampFromMockData(mockPayoutsDetail[0].cols[11], mockPayoutsDetail[0].cols[12]),
                    amount: -19.01,
                    id: 'm3RSAL9x3vjHDzF8S',
                    ref: '****************',
                    org: '321-Mariposa Local',
                    orgId: 'nTvbx24M2dbM9w6tu'
                },
                {
                    arrival_date: getTimestampFromMockData(mockPayoutsDetail[1].cols[11], mockPayoutsDetail[1].cols[12]),
                    amount: -19.01,
                    id: 'Eogbx2XzNfLNtfqd7',
                    ref: '****************',
                    org: '321-Mariposa Local',
                    orgId: 'nTvbx24M2dbM9w6tu'
                },
                {
                    arrival_date: getTimestampFromMockData(mockPayoutsDetail[2].cols[11], mockPayoutsDetail[2].cols[12]),
                    amount: -1129.7,
                    id: 'JSL2HjJ9tYiZYXQwd',
                    ref: '****************',
                    org: '321-Mariposa Local',
                    orgId: 'nTvbx24M2dbM9w6tu'
                },
                {
                    arrival_date: moment(mockTopUpTransaction.datetime).valueOf(),
                    amount: -15,
                    id: '670ec3b8de5a3b1102892b68',
                    ref: '****************',
                    org: '321-Mariposa Local',
                    orgId: 'nTvbx24M2dbM9w6tu',
                    type: 'TOP_UP'
                }
            ];

            const result = await AdyenProvider.billingPayoutsReport(options);
            expect(result.data).toEqual(expectedOutput);
        });

        it('should handle missing org data gracefully', async () => {
            let orgsMock = Orgs.findOneAsync;
            orgsMock.mockReturnValue(null);

            const options = {
                startDate: '10/23/2021',
                endDate: '10/23/2024',
                orgIds: ['nonexistent-org']
            };

            const result = await AdyenProvider.billingPayoutsReport(options);
            expect(result.data).toEqual([]);
        });

        it('should handle org without billing info gracefully', async () => {
            let orgsMock = Orgs.findOneAsync;
            orgsMock.mockReturnValue({ ...mockOrg, billing: null });

            const options = {
                startDate: '10/23/2021',
                endDate: '10/23/2024',
                orgIds: [mockOrg._id]
            };

            const result = await AdyenProvider.billingPayoutsReport(options);
            expect(result.data).toEqual([]);
        });

        it('should handle empty payout details correctly', async () => {
            // Reset and setup mocks specifically for this test
            adyenReportsAggMock.mockReturnValue({
                toArray: jest.fn().mockResolvedValue([])
            });

            let adyenTransactionsMock = AdyenTransactions.find;
            adyenTransactionsMock.mockReturnValue({
                fetchAsync: () => [{
                    _id: { _str: '670ec3b8de5a3b1102892b68' },
                    datetime: '2024-10-15T16:34:16.057Z',
                    amount: 1500,
                    accountCode: '****************',
                    type: 'TOP_UP'
                }]
            });

            let orgsMock = Orgs.findOneAsync;
            orgsMock.mockReturnValue(mockOrg);

            const options = {
                startDate: '10/23/2021',
                endDate: '10/23/2024',
                orgIds: [mockOrg._id]
            };

            const expectedOutput = [{
                arrival_date: *************,
                amount: -15,
                id: '670ec3b8de5a3b1102892b68',
                ref: '****************',
                org: '321-Mariposa Local',
                orgId: 'nTvbx24M2dbM9w6tu',
                type: 'TOP_UP'
            }];

            const result = await AdyenProvider.billingPayoutsReport(options);
            expect(result.data).toHaveLength(1);
            expect(result.data[0].type).toBe('TOP_UP');
            expect(result.data).toEqual(expectedOutput);

            // Verify the mocks were called correctly
            expect(adyenReportsAggMock).toHaveBeenCalled();
            expect(adyenTransactionsMock).toHaveBeenCalled();
        });

        it('should handle empty top-up transactions correctly', async () => {
            const mockPayoutsData = [
                {
                    _id: 'm3RSAL9x3vjHDzF8S',
                    date: '2022-05-01',
                    cols: [
                        '', '', '****************', 'Payout', '****************',
                        '', '', '', '', '', '', '2022-05-01T16:34:30Z', '', '', '19.01'
                    ]
                },
                {
                    _id: 'Eogbx2XzNfLNtfqd7',
                    date: '2022-05-04',
                    cols: [
                        '', '', '****************', 'Payout', '****************',
                        '', '', '', '', '', '', '2022-05-04T16:34:30Z', '', '', '19.01'
                    ]
                },
                {
                    _id: 'JSL2HjJ9tYiZYXQwd',
                    date: '2022-09-15',
                    cols: [
                        '', '', '****************', 'Payout', '****************',
                        '', '', '', '', '', '', '2022-09-15T16:30:47Z', '', '', '1129.70'
                    ]
                }
            ];

            // Setup empty top-up transactions
            let adyenTransactionsMock = AdyenTransactions.find;
            adyenTransactionsMock.mockReturnValue({
                fetchAsync: () => []
            });

            // Setup payout details
            adyenReportsAggMock.mockReturnValue({
                toArray: jest.fn().mockResolvedValue(mockPayoutsData)
            });

            // Setup org mock
            let orgsMock = Orgs.findOneAsync;
            orgsMock.mockReturnValue(mockOrg);

            const options = {
                startDate: '10/23/2021',
                endDate: '10/23/2024',
                orgIds: [mockOrg._id]
            };

            const expectedOutput = [
                {
                    arrival_date: getTimestampFromMockData(mockPayoutsData[0].cols[11]),
                    amount: -19.01,
                    id: 'm3RSAL9x3vjHDzF8S',
                    ref: '****************',
                    org: '321-Mariposa Local',
                    orgId: 'nTvbx24M2dbM9w6tu'
                },
                {
                    arrival_date: getTimestampFromMockData(mockPayoutsData[1].cols[11]),
                    amount: -19.01,
                    id: 'Eogbx2XzNfLNtfqd7',
                    ref: '****************',
                    org: '321-Mariposa Local',
                    orgId: 'nTvbx24M2dbM9w6tu'
                },
                {
                    arrival_date: getTimestampFromMockData(mockPayoutsData[2].cols[11]),
                    amount: -1129.7,
                    id: 'JSL2HjJ9tYiZYXQwd',
                    ref: '****************',
                    org: '321-Mariposa Local',
                    orgId: 'nTvbx24M2dbM9w6tu'
                }
            ];

            const result = await AdyenProvider.billingPayoutsReport(options);

            expect(result.data).toHaveLength(3); // Only payout transactions
            expect(result.data).toEqual(expectedOutput);

            // Verify no entries have a type field
            result.data.forEach(item => {
                expect(item.type).toBeUndefined();
            });

            // Verify mocks were called correctly
            expect(adyenReportsAggMock).toHaveBeenCalled();
            expect(adyenTransactionsMock).toHaveBeenCalled();
        });

        it('should handle date range with no data', async () => {
            adyenReportsAggMock.mockReturnValue({
                toArray: jest.fn().mockResolvedValue([])
            });
            let adyenTransactionsMock = AdyenTransactions.find;
            adyenTransactionsMock.mockReturnValue({
                fetchAsync: () => []
            });

            const options = {
                startDate: '01/01/2020',
                endDate: '01/31/2020',
                orgIds: [mockOrg._id]
            };

            const result = await AdyenProvider.billingPayoutsReport(options);
            expect(result.data).toEqual([]);
        });

        it('should handle multiple orgIds correctly', async () => {
            // Set up second mock org
            const secondMockOrg = {
                _id: 'second-org-id',
                name: 'Second Org',
                billing: {
                    adyenInfo: {
                        accountCode: 'SECOND_ORG_CODE'
                    }
                },
                getTimezone: () => 'America/Chicago'
            };

            // Store the mock data that we'll use for both organizations
            const firstOrgPayoutData = [
                {
                    _id: 'm3RSAL9x3vjHDzF8S',
                    date: '2022-05-01',
                    cols: [
                        '', '', mockOrg.billing.adyenInfo.accountCode, 'Payout', '****************',
                        '', '', '', '', '', '', '2022-05-01T16:34:30Z', '', '', '19.01'
                    ]
                }
            ];

            const secondOrgPayoutData = [
                {
                    _id: 'secondOrgPayout',
                    date: '2022-05-01',
                    cols: [
                        '', '', secondMockOrg.billing.adyenInfo.accountCode, 'Payout', '*************',
                        '', '', '', '', '', '', '2022-05-01T16:34:30Z', '', '', '25.50'
                    ]
                }
            ];

            const firstOrgTopup = {
                _id: { _str: '670ec3b8de5a3b1102892b68' },
                datetime: '2024-10-15T16:34:16.057Z',
                amount: 1500,
                accountCode: mockOrg.billing.adyenInfo.accountCode,
                type: 'TOP_UP'
            };

            const secondOrgTopup = {
                _id: { _str: 'second-org-topup' },
                datetime: '2024-10-15T16:34:16.057Z',
                amount: 2000,
                accountCode: secondMockOrg.billing.adyenInfo.accountCode,
                type: 'TOP_UP'
            };

            // Mock org lookup
            let orgsMock = Orgs.findOneAsync;
            orgsMock.mockImplementation((id) => {
                if (id === mockOrg._id) return mockOrg;
                if (id === secondMockOrg._id) return secondMockOrg;
                return null;
            });

            // Mock payout details for both orgs
            adyenReportsAggMock.mockImplementation((pipeline) => {
                const accountCodeMatch = pipeline.find(stage => stage.$match?.['cols.2']);
                let payoutData = accountCodeMatch.$match['cols.2'] === mockOrg.billing.adyenInfo.accountCode ?
                    firstOrgPayoutData : secondOrgPayoutData;
                return {
                    toArray: jest.fn().mockResolvedValue(payoutData)
                };
            });

            // Mock top-up transactions for both orgs
            let adyenTransactionsMock = AdyenTransactions.find;
            adyenTransactionsMock.mockImplementation((query) => {
                let transactions = query.accountCode === mockOrg.billing.adyenInfo.accountCode ?
                    [firstOrgTopup] : [secondOrgTopup];
                return {
                    fetchAsync: () => transactions
                };
            });

            const options = {
                startDate: '10/23/2021',
                endDate: '10/23/2024',
                orgIds: [mockOrg._id, secondMockOrg._id]
            };

            const expectedOutput = [
                {
                    arrival_date: getTimestampFromMockData(firstOrgPayoutData[0].cols[11]),
                    amount: -19.01,
                    id: 'm3RSAL9x3vjHDzF8S',
                    ref: '****************',
                    org: '321-Mariposa Local',
                    orgId: mockOrg._id
                },
                {
                    arrival_date: moment(firstOrgTopup.datetime).valueOf(),
                    amount: -15,
                    id: '670ec3b8de5a3b1102892b68',
                    ref: mockOrg.billing.adyenInfo.accountCode,
                    org: '321-Mariposa Local',
                    orgId: mockOrg._id,
                    type: 'TOP_UP'
                },
                {
                    arrival_date: getTimestampFromMockData(secondOrgPayoutData[0].cols[11]),
                    amount: -25.50,
                    id: 'secondOrgPayout',
                    ref: '*************',
                    org: 'Second Org',
                    orgId: secondMockOrg._id
                },
                {
                    arrival_date: moment(secondOrgTopup.datetime).valueOf(),
                    amount: -20,
                    id: 'second-org-topup',
                    ref: secondMockOrg.billing.adyenInfo.accountCode,
                    org: 'Second Org',
                    orgId: secondMockOrg._id,
                    type: 'TOP_UP'
                }
            ];

            const result = await AdyenProvider.billingPayoutsReport(options);

            expect(result.data).toHaveLength(4);
            expect(result.data).toEqual(expectedOutput);

            const firstOrgData = result.data.filter(item => item.orgId === mockOrg._id);
            const secondOrgData = result.data.filter(item => item.orgId === secondMockOrg._id);
            expect(firstOrgData).toHaveLength(2);
            expect(secondOrgData).toHaveLength(2);

            expect(orgsMock).toHaveBeenCalledTimes(2);
            expect(adyenReportsAggMock).toHaveBeenCalledTimes(2);
            expect(adyenTransactionsMock).toHaveBeenCalledTimes(2);

            [firstOrgData, secondOrgData].forEach(orgData => {
                expect(orgData.some(item => item.type === 'TOP_UP')).toBeTruthy();
                expect(orgData.some(item => !item.type)).toBeTruthy();
            });
        });

        it('should handle excludeEndDate option correctly', async () => {
            const options = {
                startDate: '10/23/2021',
                endDate: '10/23/2024',
                excludeEndDate: true,
                orgIds: [mockOrg._id]
            };

            await AdyenProvider.billingPayoutsReport(options);

            // Verify that the aggregate query used the correct date range
            expect(adyenReportsAggMock).toHaveBeenCalledWith(
                expect.arrayContaining([
                    expect.objectContaining({
                        $match: expect.objectContaining({
                            date: expect.objectContaining({
                                $lte: expect.any(String)
                            })
                        })
                    })
                ]),
                expect.any(Object)
            );
        });

        it('should handle organizations with balanceAccountId', async () => {
            // Mock org with both balanceAccountId and accountCode
            const orgWithBoth = {
                _id: 'org-with-both',
                name: 'Org With Both',
                billing: {
                    adyenInfo: {
                        accountCode: '****************',
                        balanceAccountId: 'balance-account-123'
                    }
                },
                getTimezone: () => 'UTC'
            };

            // Mock AdyenBalancePaymentProvider.billingPayoutsReport
            const balancePayoutsMock = jest.spyOn(AdyenBalancePaymentProvider, 'billingPayoutsReport')
                .mockResolvedValue({
                    data: [{
                        arrival_date: *************,
                        amount: -25,
                        id: 'balance-payout-id',
                        ref: 'balance-ref',
                        org: 'Org With Both',
                        orgId: 'org-with-both',
                        type: 'BALANCE_PAYOUT'
                    }]
                });

            // Mock org lookup
            let orgsMock = Orgs.findOneAsync;
            orgsMock.mockReturnValue(orgWithBoth);

            // Mock payout details
            adyenReportsAggMock.mockReturnValue({
                toArray: jest.fn().mockResolvedValue([{
                    _id: 'classic-payout-id',
                    date: '2023-01-01',
                    cols: [
                        '', '', orgWithBoth.billing.adyenInfo.accountCode, 'Payout', 'classic-ref',
                        '', '', '', '', '', '', '2023-01-01T12:00:00Z', '', '', '15.00'
                    ]
                }])
            });

            // Mock AdyenTransactions.find
            let adyenTransactionsMock = AdyenTransactions.find;
            adyenTransactionsMock.mockReturnValue({
                fetchAsync: () => []
            });

            const options = {
                startDate: '01/01/2023',
                endDate: '01/31/2023',
                orgIds: ['org-with-both']
            };

            const result = await AdyenProvider.billingPayoutsReport(options);

            // Verify AdyenBalancePaymentProvider was called with correct options
            expect(balancePayoutsMock).toHaveBeenCalledWith(expect.objectContaining({
                startDate: options.startDate,
                endDate: options.endDate,
                orgIds: [orgWithBoth._id]
            }));

            // Verify result contains both balance and classic payouts
            expect(result.data).toContainEqual(expect.objectContaining({
                id: 'balance-payout-id',
                type: 'BALANCE_PAYOUT'
            }));
            expect(result.data).toContainEqual(expect.objectContaining({
                id: 'classic-payout-id'
            }));
        });

        it('should skip organizations without accountCode', async () => {
            // Mock org with only balanceAccountId but no accountCode
            const orgWithoutAccountCode = {
                _id: 'org-without-account',
                name: 'Org Without Account Code',
                billing: {
                    adyenInfo: {
                        balanceAccountId: 'balance-account-123'
                    }
                },
                getTimezone: () => 'UTC'
            };

            // Mock AdyenBalancePaymentProvider.billingPayoutsReport
            const balancePayoutsMock = jest.spyOn(AdyenBalancePaymentProvider, 'billingPayoutsReport')
                .mockResolvedValue({
                    data: [{
                        arrival_date: *************,
                        amount: -25,
                        id: 'balance-payout-id',
                        ref: 'balance-ref',
                        org: 'Org Without Account Code',
                        orgId: 'org-without-account',
                        type: 'BALANCE_PAYOUT'
                    }]
                });

            // Mock org lookup
            let orgsMock = Orgs.findOneAsync;
            orgsMock.mockReturnValue(orgWithoutAccountCode);

            const options = {
                startDate: '01/01/2023',
                endDate: '01/31/2023',
                orgIds: ['org-without-account']
            };

            const result = await AdyenProvider.billingPayoutsReport(options);

            // Verify result only contains balance payouts
            expect(result.data).toHaveLength(1);
            expect(result.data[0]).toEqual(expect.objectContaining({
                id: 'balance-payout-id',
                type: 'BALANCE_PAYOUT'
            }));

            // Verify AdyenReports.aggregate was not called
            expect(adyenReportsAggMock).not.toHaveBeenCalled();
        });
    });

    describe('billingPayoutDetailReport', () => {
        beforeEach(() => {
            // Reset mocks
            jest.clearAllMocks();

            AdyenReports.find.mockReturnValue({ fetchAsync: jest.fn().mockResolvedValue([]) });
            // jest.spyOn(adyenProvider, 'pullAdyenReportsFromCache').mockResolvedValue([]);
        });

        afterEach(() => {
            // Restore the original implementation
            jest.restoreAllMocks();
            clearCache();
        });

        it('should delegate to AdyenBalancePaymentProvider for orgs with balanceAccountId', async () => {
            // Mock org with balanceAccountId
            const orgWithBalance = {
                _id: 'org-with-balance',
                billing: {
                    adyenInfo: {
                        balanceAccountId: 'balance-account-123'
                    }
                }
            };

            // Mock Orgs.findOneAsync
            Orgs.findOneAsync.mockResolvedValue(orgWithBalance);

            // Mock AdyenBalancePaymentProvider.billingPayoutDetailReport
            const mockBalanceReport = {
                data: [
                    {
                        type: 'payment',
                        arrival_date: *************,
                        amount: 100,
                        fee: 2.5,
                        net: 97.5,
                        description: 'Test payment'
                    }
                ],
                transferData: {
                    available_on: '2023-01-15',
                    net: 97.5,
                    fee: 2.5
                }
            };

            const balanceDetailReportMock = jest.spyOn(AdyenBalancePaymentProvider, 'billingPayoutDetailReport')
                .mockResolvedValue(mockBalanceReport);

            // Call the method
            const options = {
                orgId: 'org-with-balance',
                payoutId: 'payout-123'
            };

            const result = await AdyenProvider.billingPayoutDetailReport(options);

            // Verify AdyenBalancePaymentProvider was called with correct options
            expect(balanceDetailReportMock).toHaveBeenCalledWith(options);

            // Verify the result is from the balance provider
            expect(result).toEqual(mockBalanceReport);
        });

        it('should process classic payouts when org has no balanceAccountId', async () => {
            // Mock org without balanceAccountId
            const classicOrg = {
                _id: 'classic-org',
                billing: {
                    adyenInfo: {
                        accountCode: 'account-code-123'
                    }
                }
            };

            // Mock Orgs.findOneAsync
            Orgs.findOneAsync.mockResolvedValue(classicOrg);

            // Mock pullAdyenReportsFromCache to return payout data
            const mockPayoutData = [{
                content: 'TendlyLLCDbaMomentPath,Company,account-code-123,Payout,psp-ref-123,,,,,,,2023-01-15T12:00:00,,,100.00',
                date: '2023-01-15'
            }];

            // global.pullAdyenReportsFromCache.mockResolvedValue(mockPayoutData);
            AdyenReports.find.mockReturnValue({ fetchAsync: jest.fn().mockResolvedValue(mockPayoutData) });

            // Mock Papa.parse
            global.Papa = {
                parse: jest.fn().mockReturnValue({
                    data: [
                        ['TendlyLLCDbaMomentPath', 'Company', 'account-code-123', 'Payout', 'psp-ref-123', '', '', '', '', '', '', '2023-01-15T12:00:00', '', '', '100.00']
                    ]
                })
            };
            const balanceDetailReportMock = jest.spyOn(AdyenBalancePaymentProvider, 'billingPayoutDetailReport');

            // Call the method
            const options = {
                orgId: 'classic-org',
                payoutId: 'payout-123',
                payoutReference: 'psp-ref-123'
            };

            const result = await AdyenProvider.billingPayoutDetailReport(options);
            expect(balanceDetailReportMock).not.toHaveBeenCalled();
        });

        it('should return empty object when no payout data is found', async () => {
            // Mock org without balanceAccountId
            const classicOrg = {
                _id: 'classic-org',
                billing: {
                    adyenInfo: {
                        accountCode: 'account-code-123'
                    }
                }
            };

            // Mock Orgs.findOneAsync
            Orgs.findOneAsync.mockResolvedValue(classicOrg);

            // Call the method
            const options = {
                orgId: 'classic-org',
                payoutId: 'payout-123'
            };

            const result = await AdyenProvider.billingPayoutDetailReport(options);

            // Verify result is empty object
            expect(result).toEqual({});
        });
    });
});

describe('findPayoutForPayment', () => {
    let invoiceMock, orgMock, adyenReportsMock;

    beforeEach(() => {
        invoiceMock = Invoices;
        orgMock = Orgs;
        adyenReportsMock = AdyenReports;
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    it('returns undefined if invoice or payment is missing', async () => {
        const result = await AdyenProvider.findPayoutForPayment({
            invoiceId: 'test-invoice-id',
            payment: null,
            dataCache: {},
        });
        expect(result).toBeUndefined();

        Invoices.findOneAsync.mockReturnValueOnce(null);
        const resultWithNoInvoice = await AdyenProvider.findPayoutForPayment({
            invoiceId: 'test-invoice-id',
            payment: { adyenInfo: { pspReference: 'test-payment-id' } },
            dataCache: {},
        });
        expect(resultWithNoInvoice).toBeUndefined();
    });

    it('searches payout data for 7 days and finds matching payout', async () => {
        const payment = {
            createdAt: new Date(),
            adyenInfo: { pspReference: 'test-payment-id' },
        };

        invoiceMock.findOneAsync.mockReturnValue({
            _id: 'test-invoice-id',
            orgId: 'test-org-id',
        });

        orgMock.findOneAsync.mockReturnValue({
            _id: 'test-org-id',
            billing: { adyenInfo: { accountCode: 'test-account-code' } },
        });

        const payoutData = [
            { content: 'Some payout content\n' },
        ];
        adyenReportsMock.find.mockReturnValueOnce(({
            fetchAsync:jest.fn().mockImplementation(()=>payoutData)
        }));

        Papa.parse.mockReturnValueOnce({
            data: [
                ['TendlyLLCDbaMomentPath', '', 'test-account-code', '', 'test-ref-id', 'test-payment-id'],
                ['TendlyLLCDbaMomentPath', '', 'test-account-code', 'Payout', 'test-ref-id', '', '', '', '', '', '', '2024-01-01'],
            ],
        });

        const result = await AdyenProvider.findPayoutForPayment({
            invoiceId: 'test-invoice-id',
            payment,
            dataCache: {},
        });

        expect(result).toEqual({
            payoutId: undefined, // Mock data doesn't have `_id`
            payoutDate: '2024-01-01',
            refId: 'test-ref-id',
        });
    });

    it('searches regex reports if no payout is found in date-based search', async () => {
        const payment = {
            createdAt: new Date(),
            adyenInfo: { pspReference: 'test-payment-id' },
        };

        invoiceMock.findOneAsync.mockReturnValue({
            _id: 'test-invoice-id',
            orgId: 'test-org-id',
        });

        orgMock.findOneAsync.mockReturnValue({
            _id: 'test-org-id',
            billing: { adyenInfo: { accountCode: 'test-account-code' } },
        });

        adyenReportsMock.find.mockReturnValueOnce(({
            fetchAsync:jest.fn().mockImplementation(()=>[])
        }));

        const regexReport = {
            _id: 'regex-report-id',
            content: 'TendlyLLCDbaMomentPath,test-account-code,,Payout,,,test-payment-id,,',
            date: '2024-01-01',
        };
        adyenReportsMock.find.mockReturnValueOnce(({
            fetchAsync:jest.fn().mockImplementation(()=>[regexReport])
        }));

        Papa.parse = jest.fn(() => ({
            data: [
                ['TendlyLLCDbaMomentPath', '', 'test-account-code', '', '', 'test-payment-id'],
                ['TendlyLLCDbaMomentPath', '', 'test-account-code', 'Payout', 'test-ref-id', '', '', '', '', '', '', '2024-01-01'],
            ],
        }));

        const result = await AdyenProvider.findPayoutForPayment({
            invoiceId: 'test-invoice-id',
            payment,
            dataCache: {},
        });

        expect(result).toEqual({
            payoutId: 'regex-report-id',
            payoutDate: '2024-01-01',
            refId: 'test-ref-id',
        });
    });

    it('caches payout data in the dataCache object', async () => {
        const payment = {
            createdAt: new Date(),
            adyenInfo: { pspReference: 'test-payment-id' },
        };

        invoiceMock.findOneAsync.mockReturnValue({
            _id: 'test-invoice-id',
            orgId: 'test-org-id',
        });

        orgMock.findOneAsync.mockReturnValue({
            _id: 'test-org-id',
            billing: { adyenInfo: { accountCode: 'test-account-code' } },
        });

        const dataCache = {};
        const payoutData = [{ content: 'Some payout content\n' }];
        adyenReportsMock.find.mockReturnValueOnce(({
            fetchAsync:jest.fn().mockImplementation(()=>payoutData)
        }));

        Papa.parse = jest.fn(() => ({
            data: [
                ['TendlyLLCDbaMomentPath', '', 'test-account-code', '', '', 'test-payment-id'],
                ['TendlyLLCDbaMomentPath', '', 'test-account-code', 'Payout', '', '', '', '', '', '', '', '2024-01-01', 'test-ref-id'],
            ],
        }));

        await AdyenProvider.findPayoutForPayment({
            invoiceId: 'test-invoice-id',
            payment,
            dataCache,
        });

        const expectedCacheKey = new moment(payment.createdAt).format('YYYY-MM-DD');
        expect(dataCache[expectedCacheKey]).toEqual(payoutData);
    });
});

describe('findPayoutForPayment helper methods', () => {
    let mockDataCache;

    beforeEach(() => {
        mockDataCache = {};
    });

    it('getPayoutDataForDate fetches payout data from cache or database', async () => {
        const cursorMock = AdyenReports.find;
        cursorMock.mockReturnValue(({
            fetchAsync:jest.fn().mockImplementation(()=>[{ content: 'mock-content' }])
        }));

        const result = await AdyenProvider.getPayoutDataForDate('2024-01-01', mockDataCache);
        expect(result).toEqual([{ content: 'mock-content' }]);
        expect(mockDataCache['2024-01-01']).toEqual([{ content: 'mock-content' }]);
    });

    it('parsePayoutData parses payout data content', () => {
        Papa.parse.mockReturnValueOnce({ data: [['row1'], ['row2']] });
        const result = AdyenProvider.parsePayoutData([{ content: 'data1' }, { content: 'data2' }]);
        expect(result).toEqual([['row1'], ['row2']]);
    });

    it('findMatchingPayout locates matching payout and transaction rows', () => {
        const parsedData = [
            ['TendlyLLCDbaMomentPath', '', 'test-account-code', '', '', 'test-payment-id'],
            ['TendlyLLCDbaMomentPath', '', 'test-account-code', 'Payout', 'test-ref-id', '', '', '', '', '', '', '2024-01-01'],
        ];
        const result = AdyenProvider.findMatchingPayout(parsedData, 'test-account-code', 'test-payment-id');
        expect(result).toEqual({ payoutDate: '2024-01-01', refId: 'test-ref-id' });
    });

    it('findRegexReports retrieves reports from cache or database', async () => {
        const cursorMock = AdyenReports.find;
        cursorMock.mockReturnValue(({
            fetchAsync:jest.fn().mockImplementation(()=>[{ content: 'regex-content' }])
        }));

        const result = await AdyenProvider.findRegexReports('test-account-code', 'test-payment-id', mockDataCache);
        expect(result).toEqual([{ content: 'regex-content' }]);
    });

    it('cacheRegexReport adds a regex report to the cache', () => {
        const report = { date: '2024-01-01', content: 'regex-content' };
        AdyenProvider.cacheRegexReport(report, mockDataCache);
        expect(mockDataCache['2024-01-01']).toEqual([report]);
    });
});

