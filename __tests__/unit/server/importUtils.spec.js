import { mockCollection } from "../../helpers/collectionMock";
import { mockMeteor } from "../../helpers/meteorMock";
import { ImportUtils } from "../../../server/importUtils";
import { mockRandom } from "../../helpers/randomMock";
import { expect } from '@jest/globals';
import { HistoryAuditChangeTypes, HistoryAuditRecordTypes } from '../../../lib/constants/historyAuditConstants';
import { People } from "../../../lib/collections/people";
import { Relationships } from "../../../lib/collections/relationships";
import { Reservations } from "../../../lib/collections/reservations";
import { HistoryAudits } from "../../../server/collections/historyAudits";

// Mock moment-timezone
jest.mock('moment-timezone', () => {
    const moment = jest.requireActual('moment-timezone');
    return moment;
});

const moment = require('moment-timezone');
const meteorMock = mockMeteor();
const baseRow = {
    'First Name': 'Child First  ',
    'Last Name': '  Child Last',
    'Default Group (Classroom)': 'Classroom 1',
    'Monday': 'x',
    'Tuesday': 'x',
    'Friday': '',
    'Date of Birth': '1/1/2010',
    'Gender': 'Female',
    'Media Review Required': 'Yes',
    'Status': 'active',
    'Middle Name': 'Mid',
    'Preferred Name': 'Pref',
    'childProfile.cacfpSubsidy': 'Paid',
    'Parent 1 First Name': 'Parent 1 First',
    'Parent 1 Last Name': 'Parent 1 Last',
    'Parent 1 Email': '<EMAIL>',
    'Parent 1 Relationship Description': 'rel1desc',
    'parentProfile1.phonePrimary': '************',
    'Parent 2 First Name': 'Parent 2 First',
    'Parent 2 Last Name': 'Parent 2 Last',
    'Parent 2 Email': '<EMAIL>',
    'Parent 2 Relationship Description': 'rel2desc',
    'parentProfile2.phonePrimary': '************',
    'Relationship 5 First Name': 'Pickup First',
    'Relationship 5 Last Name': 'Pickup Last',
    'Relationship 5 Relationship Description': 'rel5desc',
    'Relationship 5 User Type': 'authorized pickup',
    'Relationship 6 First Name': 'EC First',
    'Relationship 6 Last Name': 'EC Last',
    'Relationship 6 Relationship Description': 'rel6desc',
    'Relationship 6 User Type': 'emergency contact',
}
describe('importUtils', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('properly finds child dupes', () => {
        const dob = new moment.tz('1/1/2010', 'MM/DD/YYYY', 'America/New_York').startOf('day').valueOf();
        expect(ImportUtils.findChildDupes([], 'foo', 'bar', dob, 'America/New_York')).toStrictEqual([]);
        const mock = [
            {
                _id: '1',
                type: 'person',
                firstName: 'FO-O',
                lastName: '  bAR',
                profileData: {
                    birthday: dob - (1000 * 60 * 60 * 24 * 365 * 5)
                }
            },
            {
                _id: '2',
                type: 'person',
                firstName: 'foo ',
                lastName: 'bAR',
                profileData: {
                    birthday: dob + (1000 * 60 * 60 * 2)
                }
            },
        ];
        expect(ImportUtils.findChildDupes(mock, 'foo', 'bar', '1/1/2010', 'America/New_York')).toStrictEqual([mock[1]]);
        const mock2 = [
            {
                _id: '1',
                type: 'person',
                firstName: 'FO-O',
                lastName: '  bAR',
            },
            {
                _id: '2',
                type: 'person',
                firstName: 'foo ',
                lastName: 'bAR',
                profileData: {
                    birthday: dob + (1000 * 60 * 60 * 2)
                }
            },
        ];
        expect(ImportUtils.findChildDupes(mock2, 'foo', 'bar', '1/1/2010', 'America/New_York')).toStrictEqual(mock2);
        const mock3 = [
            {
                _id: '1',
                type: 'person',
                firstName: 'FO-O',
                lastName: '  bAR',
            },
            {
                _id: '2',
                type: 'person',
                firstName: 'foo ',
                lastName: 'bAR',
                profileData: {
                    birthday: dob + (1000 * 60 * 60 * 2)
                }
            },
        ];
        expect(ImportUtils.findChildDupes(mock3, 'foo', 'bar', null, 'America/New_York')).toStrictEqual(mock3);
    });
    it('properly finds family dupes', async () => {
        expect(ImportUtils.findFamilyDupes([], 'foo', 'bar')).toStrictEqual([]);
        const mock2 = [
            {
                _id: '1',
                type: 'family',
                firstName: 'FO-O',
                lastName: '  bAR'
            },
            {
                _id: '2',
                type: 'family',
                firstName: 'foo ',
                lastName: 'bAR'
            },
            {
                _id: '3',
                type: 'family',
                firstName: 'foo',
                lastName: 'baz'
            }
        ];
        expect(ImportUtils.findFamilyDupes(mock2, 'foo', 'bar')).toStrictEqual([
            {
                _id: '1',
                type: 'family',
                firstName: 'FO-O',
                lastName: '  bAR'
            },
            {
                _id: '2',
                type: 'family',
                firstName: 'foo ',
                lastName: 'bAR'
            },
        ]);
        const mock3 = [
            {
                _id: '1',
                type: 'family',
                firstName: 'FO-O',
                lastName: '  bAR',
                profileEmailAddress: '<EMAIL>',
            },
            {
                _id: '2',
                type: 'family',
                firstName: 'foo ',
                lastName: 'bAR',
            },
        ];
        expect(ImportUtils.findFamilyDupes(mock3, 'foo', 'bar', '', '<EMAIL>')).toStrictEqual([mock3[1]]);
        const mock4 = [
            {
                _id: '1',
                type: 'family',
                firstName: 'FO-O',
                lastName: '  bAR',
                profileEmailAddress: '<EMAIL>',
            },
            {
                _id: '2',
                type: 'family',
                firstName: 'foo ',
                lastName: 'bAR',
                profileData: {
                    phonePrimary: '+15555551212'
                }
            },
        ];
        expect(ImportUtils.findFamilyDupes(mock4, 'foo', 'bar', '15554442222', '')).toStrictEqual([mock4[0]]);
        const mock5 = [
                {
                    _id: '1',
                    type: 'family',
                    firstName: 'FO-O',
                    lastName: '  bAR',
                    profileEmailAddress: '<EMAIL>'
                },
                {
                    _id: '2',
                    type: 'family',
                    firstName: 'foo ',
                    lastName: 'bAR',
                    profileData: {
                        phonePrimary: '+15555551212'
                    }
                }
        ];
        expect(ImportUtils.findFamilyDupes(mock5, 'foo', 'bar', '15555551212', ' <EMAIL> ')).toStrictEqual(mock5);
    });
    it('normalizes strings', () => {
        expect(ImportUtils.dupeNormalize('foo')).toBe('foo');
        expect(ImportUtils.dupeNormalize('    %@#$   foo  9')).toBe('@foo9');
        expect(ImportUtils.dupeNormalize('<EMAIL>')).toBe('<EMAIL>');
    });
    describe('the import process', () => {
        beforeEach(() => {
            jest.clearAllMocks();
        });
        it('bad groups creates failed rows', async() => {
            const randomMock = mockRandom();
            const peopleMock = People;
            peopleMock.find.mockReturnValueOnce(({
                    fetchAsync:jest.fn().mockImplementation(()=>[])
                })
            );
            const relationshipsMock = Relationships;
            const reservationsMock = Reservations;
            const historyAuditsMock = HistoryAudits;
            const results = await ImportUtils.processImport(
                [baseRow],
                Object.keys(baseRow),
                '123',
                'America/New_York',
                [],
                'foo',
                { preview: false, isMock: true }
            );
            expect(results).toStrictEqual({ messages: ["1: Cannot find matching group named 'Classroom 1'. "], successCount: 0, failCount: 1 });
        });
        it('processes basic import correctly', async () => {
            const randomMock = mockRandom();
            const peopleMock = People;
            peopleMock.find.mockReturnValueOnce(({
                    fetchAsync:jest.fn().mockImplementation(()=>[])
                })
            );
            const relationshipsMock = Relationships;
            const reservationsMock = Reservations;
            const historyAuditsMock = HistoryAudits;
            const results = await ImportUtils.processImport(
                [baseRow],
                Object.keys(baseRow),
                '123',
                'America/New_York',
                [{ _id: '1', name: 'Classroom 1' }],
                'foo',
                { preview: false, isMock: true }
            );
            expect(results).toStrictEqual({ messages: [], successCount: 1, failCount: 0 });
            expect(peopleMock.insertAsync.mock.calls.length).toBe(5);
            expect(historyAuditsMock.insertAsync.mock.calls.length).toBe(6);
            expect(relationshipsMock.direct.insertAsync.mock.calls.length).toBe(4);
            expect(reservationsMock.insertAsync.mock.calls.length).toBe(1);
            expect(peopleMock.insertAsync.mock.calls).toStrictEqual([
                [{
                    _id: 42,
                    orgId: '123',
                    type: 'person',
                    createdByPersonId: 'foo',
                    createdByUIImport: true,
                    defaultGroupId: '1',
                    firstName: 'Child First',
                    lastName: 'Child Last',
                    middleName: 'Mid',
                    preferredName: 'Pref',
                    inActive: false,
                    createdAt: expect.anything(),
                    profileData: {
                        birthday: moment.tz('1/1/2010', 'MM/DD/YYYY', 'America/New_York').startOf('day').valueOf(),
                        cacfpSubsidy: 'Paid',
                        gender: 'Female',
                        householdInformation: expect.anything(),
                        mediaRequirements: {
                            mediaRelease: undefined,
                            mediaReviewRequired: 'Yes',
                            noMediaAllowed: 'Yes'
                        },
                        notesPrivate: undefined,
                        notesPublic: undefined,
                        restrictedPickup: expect.anything(),
                        standardOutlook: expect.anything(),
                    }
                }],
                [
                    {
                        "_id": 42,
                        "createdAt": expect.anything(),
                        "createdByPersonId": "foo",
                        "createdByUIImport": true,
                        "firstName": "Parent 1 First",
                        "inActive": false,
                        "lastName": "Parent 1 Last",
                        "orgId": "123",
                        "pinCode": undefined,
                        "pinCodeSupplemental": undefined,
                        "profileData": {
                            "householdInformation": {
                                "addressType": undefined,
                                "parentCity": undefined,
                                "parentState": undefined,
                                "parentStreetAddress": undefined,
                                "parentZip": undefined,
                            },
                            "notesPrivate": undefined,
                            "phoneNumberHome": undefined,
                            "phoneNumberWork": undefined,
                            "phonePrimary": "************",
                        },
                        "profileEmailAddress": "<EMAIL>",
                        "type": "family",
                    },
                ],
                [
                    {
                        "_id": 42,
                        "createdAt": expect.anything(),
                        "createdByPersonId": "foo",
                        "createdByUIImport": true,
                        "firstName": "Parent 2 First",
                        "inActive": false,
                        "lastName": "Parent 2 Last",
                        "orgId": "123",
                        "pinCode": undefined,
                        "pinCodeSupplemental": undefined,
                        "profileData": {
                            "householdInformation": {
                                "addressType": undefined,
                                "parentCity": undefined,
                                "parentState": undefined,
                                "parentStreetAddress": undefined,
                                "parentZip": undefined,
                            },
                            "notesPrivate": undefined,
                            "phoneNumberHome": undefined,
                            "phoneNumberWork": undefined,
                            "phonePrimary": "************",
                        },
                        "profileEmailAddress": "<EMAIL>",
                        "type": "family",
                    },
                ],
                [
                    {
                        "_id": 42,
                        "createdAt": expect.anything(),
                        "createdByPersonId": "foo",
                        "createdByUIImport": true,
                        "firstName": "Pickup First",
                        "inActive": false,
                        "lastName": "Pickup Last",
                        "orgId": "123",
                        "profileData": {
                            "phonePrimary": "",
                        },
                        "type": "family",
                    },
                ],
                [
                    {
                        "_id": 42,
                        "createdAt": expect.anything(),
                        "createdByPersonId": "foo",
                        "createdByUIImport": true,
                        "firstName": "EC First",
                        "inActive": false,
                        "lastName": "EC Last",
                        "orgId": "123",
                        "profileData": {
                            "phonePrimary": "",
                        },
                        "type": "family",
                    },
                ]
                ]
            );
            expect(historyAuditsMock.insertAsync.mock.calls).toEqual([
                [{
                    changeType: HistoryAuditChangeTypes.ADD,
                    details: 'Added via System Import',
                    diff: expect.anything(),
                    personId: 42,
                    orgId: '123',
                    recordId: 42,
                    recordType: HistoryAuditRecordTypes.PERSON,
                    performedById: null,
                    performedByName: 'System Import',
                    timestamp: expect.any(Number)
                }],
                [{
                    changeType: HistoryAuditChangeTypes.ADD,
                    details: 'Added via System Import',
                    diff: expect.anything(),
                    personId: 42,
                    orgId: '123',
                    recordId: 42,
                    recordType: HistoryAuditRecordTypes.PERSON,
                    performedById: null,
                    performedByName: 'System Import',
                    timestamp: expect.any(Number)
                }],
                [{
                    changeType: HistoryAuditChangeTypes.ADD,
                    details: 'Added via System Import',
                    diff: expect.anything(),
                    personId: 42,
                    orgId: '123',
                    recordId: 42,
                    recordType: HistoryAuditRecordTypes.PERSON,
                    performedById: null,
                    performedByName: 'System Import',
                    timestamp: expect.any(Number)
                }],
                [{
                    changeType: HistoryAuditChangeTypes.ADD,
                    details: 'Added via System Import',
                    diff: expect.anything(),
                    personId: 42,
                    orgId: '123',
                    recordId: 42,
                    recordType: HistoryAuditRecordTypes.PERSON,
                    performedById: null,
                    performedByName: 'System Import',
                    timestamp: expect.any(Number)
                }],
                [{
                    changeType: HistoryAuditChangeTypes.ADD,
                    details: 'Added via System Import',
                    diff: expect.anything(),
                    personId: 42,
                    orgId: '123',
                    recordId: 42,
                    recordType: HistoryAuditRecordTypes.PERSON,
                    performedById: null,
                    performedByName: 'System Import',
                    timestamp: expect.any(Number)
                }],
                [{
                    changeType: HistoryAuditChangeTypes.ADD,
                    details: 'Schedule added via System Import',
                    diff: expect.anything(),
                    personId: 42,
                    orgId: '123',
                    recordId: 42,
                    recordType: HistoryAuditRecordTypes.SCHEDULE,
                    performedById: null,
                    performedByName: 'System Import',
                    timestamp: expect.any(Number)
                }]
            ]);
            expect(reservationsMock.insertAsync.mock.calls[0][0]).toStrictEqual({
                _id: 42,
                createdAt: expect.anything(),
                createdByPersonId: 'foo',
                createdByUIImport: true,
                orgId: '123',
                recurringDays: ['mon', 'tue'],
                recurringFrequency: 1,
                recurringProfileSchedule: true,
                recurringType: 'weekly',
                reservationType: 'person',
                scheduleType: '',
                scheduledDate: expect.anything(),
                selectedPerson: 42
            });
            expect(relationshipsMock.direct.insertAsync.mock.calls).toStrictEqual([
                [
                    {
                        "createdAt": expect.anything(),
                        "createdByPersonId": "foo",
                        "createdByUIImport": true,
                        "orgId": "123",
                        "personId": 42,
                        "primaryCaregiver": true,
                        "relationshipDescription": 'rel1desc',
                        "relationshipType": "family",
                        "targetId": 42,
                    },
                ],
                [
                    {
                        "createdAt": expect.anything(),
                        "createdByPersonId": "foo",
                        "createdByUIImport": true,
                        "orgId": "123",
                        "personId": 42,
                        "primaryCaregiver": false,
                        "relationshipDescription": 'rel2desc',
                        "relationshipType": "family",
                        "targetId": 42,
                    },
                ],
                [
                    {
                        "createdAt": expect.anything(),
                        "createdByPersonId": "foo",
                        "createdByUIImport": true,
                        "orgId": "123",
                        "personId": 42,
                        "relationshipDescription": "rel5desc",
                        "relationshipType": "authorizedPickup",
                        "targetId": 42,
                    },
                ],
                [
                    {
                        "createdAt": expect.anything(),
                        "createdByPersonId": "foo",
                        "createdByUIImport": true,
                        "orgId": "123",
                        "personId": 42,
                        "relationshipDescription": "rel6desc",
                        "relationshipType": "emergencyContact",
                        "targetId": 42,
                    },
                ],
            ]);
        })
        it('processes duplicate guardians correctly', async () => {
            const randomMock = mockRandom();
            const peopleMock = People;
            People.find.mockImplementation(() => {
                return ({
                    fetchAsync: jest.fn().mockImplementation(() => [{
                        _id: 'existing1',
                        firstName: 'Parent 1 First',
                        lastName: 'Parent 1 Last',
                        type: 'family',
                        profileEmailAddress: '<EMAIL>'
                    }])
                })
            })
            const historyAuditsMock = HistoryAudits;
            const relationshipsMock = Relationships;
            const reservationsMock = Reservations;
            const results = await ImportUtils.processImport(
                [baseRow],
                Object.keys(baseRow),
                '123',
                'America/New_York',
                [{ _id: '1', name: 'Classroom 1' }],
                'foo',
                { preview: false, isMock: true }
            );
            expect(results).toStrictEqual({ messages: [], successCount: 1, failCount: 0 });
            expect(peopleMock.insertAsync.mock.calls.length).toBe(4);
            expect(historyAuditsMock.insertAsync.mock.calls.length).toBe(5);
            expect(relationshipsMock.direct.insertAsync.mock.calls.length).toBe(4);
            expect(reservationsMock.insertAsync.mock.calls.length).toBe(1);
            expect(peopleMock.insertAsync.mock.calls).toStrictEqual([
                    [{
                        _id: 42,
                        orgId: '123',
                        type: 'person',
                        createdByPersonId: 'foo',
                        createdByUIImport: true,
                        defaultGroupId: '1',
                        firstName: 'Child First',
                        lastName: 'Child Last',
                        middleName: 'Mid',
                        preferredName: 'Pref',
                        inActive: false,
                        createdAt: expect.anything(),
                        profileData: {
                            birthday: moment.tz('1/1/2010', 'MM/DD/YYYY', 'America/New_York').startOf('day').valueOf(),
                            cacfpSubsidy: 'Paid',
                            gender: 'Female',
                            householdInformation: expect.anything(),
                            mediaRequirements: {
                                mediaRelease: undefined,
                                mediaReviewRequired: 'Yes',
                                noMediaAllowed: 'Yes'
                            },
                            notesPrivate: undefined,
                            notesPublic: undefined,
                            restrictedPickup: expect.anything(),
                            standardOutlook: expect.anything(),
                        }
                    }],
                    [
                        {
                            "_id": 42,
                            "createdAt": expect.anything(),
                            "createdByPersonId": "foo",
                            "createdByUIImport": true,
                            "firstName": "Parent 2 First",
                            "inActive": false,
                            "lastName": "Parent 2 Last",
                            "orgId": "123",
                            "pinCode": undefined,
                            "pinCodeSupplemental": undefined,
                            "profileData": {
                                "householdInformation": {
                                    "addressType": undefined,
                                    "parentCity": undefined,
                                    "parentState": undefined,
                                    "parentStreetAddress": undefined,
                                    "parentZip": undefined,
                                },
                                "notesPrivate": undefined,
                                "phoneNumberHome": undefined,
                                "phoneNumberWork": undefined,
                                "phonePrimary": "************",
                            },
                            "profileEmailAddress": "<EMAIL>",
                            "type": "family",
                        },
                    ],
                    [
                        {
                            "_id": 42,
                            "createdAt": expect.anything(),
                            "createdByPersonId": "foo",
                            "createdByUIImport": true,
                            "firstName": "Pickup First",
                            "inActive": false,
                            "lastName": "Pickup Last",
                            "orgId": "123",
                            "profileData": {
                                "phonePrimary": "",
                            },
                            "type": "family",
                        },
                    ],
                    [
                        {
                            "_id": 42,
                            "createdAt": expect.anything(),
                            "createdByPersonId": "foo",
                            "createdByUIImport": true,
                            "firstName": "EC First",
                            "inActive": false,
                            "lastName": "EC Last",
                            "orgId": "123",
                            "profileData": {
                                "phonePrimary": "",
                            },
                            "type": "family",
                        },
                    ]
                ]
            );
            expect(reservationsMock.insertAsync.mock.calls[0][0]).toStrictEqual({
                _id: 42,
                createdAt: expect.anything(),
                createdByPersonId: 'foo',
                createdByUIImport: true,
                orgId: '123',
                recurringDays: ['mon', 'tue'],
                recurringFrequency: 1,
                recurringProfileSchedule: true,
                recurringType: 'weekly',
                reservationType: 'person',
                scheduleType: '',
                scheduledDate: expect.anything(),
                selectedPerson: 42
            });
            expect(relationshipsMock.direct.insertAsync.mock.calls).toStrictEqual([
                [
                    {
                        "createdAt": expect.anything(),
                        "createdByPersonId": "foo",
                        "createdByUIImport": true,
                        "orgId": "123",
                        "personId": "existing1",
                        "primaryCaregiver": true,
                        "relationshipDescription": 'rel1desc',
                        "relationshipType": "family",
                        "targetId": 42,
                    },
                ],
                [
                    {
                        "createdAt": expect.anything(),
                        "createdByPersonId": "foo",
                        "createdByUIImport": true,
                        "orgId": "123",
                        "personId": 42,
                        "primaryCaregiver": false,
                        "relationshipDescription": 'rel2desc',
                        "relationshipType": "family",
                        "targetId": 42,
                    },
                ],
                [
                    {
                        "createdAt": expect.anything(),
                        "createdByPersonId": "foo",
                        "createdByUIImport": true,
                        "orgId": "123",
                        "personId": 42,
                        "relationshipDescription": "rel5desc",
                        "relationshipType": "authorizedPickup",
                        "targetId": 42,
                    },
                ],
                [
                    {
                        "createdAt": expect.anything(),
                        "createdByPersonId": "foo",
                        "createdByUIImport": true,
                        "orgId": "123",
                        "personId": 42,
                        "relationshipDescription": "rel6desc",
                        "relationshipType": "emergencyContact",
                        "targetId": 42,
                    },
                ],
            ]);
        })
        it('processes inactive children correctly', async () => {
            const randomMock = mockRandom();
            const peopleMock = People;
            People.find.mockImplementation(() => {
                return ({ fetchAsync:jest.fn().mockImplementation(()=> [])})
            })
            const historyAuditsMock = HistoryAudits;
            const relationshipsMock = Relationships;
            const reservationsMock = Reservations;
            const row1 = { ...baseRow };
            const row2 = { ...baseRow };
            row1.Status = 'inactive';
            row2.Status = 'inactive';
            row2["First Name"] = 'Child First 2';
            row2["Last Name"] = 'Child Last 2';
            const results = await ImportUtils.processImport(
                [row1, row2],
                Object.keys(baseRow),
                '123',
                'America/New_York',
                [{ _id: '1', name: 'Classroom 1' }],
                'foo',
                { preview: false, isMock: true }
            );
            expect(results).toStrictEqual({ messages: [], successCount: 2, failCount: 0 });
            expect(peopleMock.insertAsync.mock.calls.length).toBe(6);
            expect(historyAuditsMock.insertAsync.mock.calls.length).toBe(8);
            expect(relationshipsMock.direct.insertAsync.mock.calls.length).toBe(8);
            expect(reservationsMock.insertAsync.mock.calls.length).toBe(2);
            expect(peopleMock.insertAsync.mock.calls).toStrictEqual([
                    [
                        {
                        _id: 42,
                        orgId: '123',
                        type: 'person',
                        createdByPersonId: 'foo',
                        createdByUIImport: true,
                        defaultGroupId: '1',
                        firstName: 'Child First',
                        lastName: 'Child Last',
                        middleName: 'Mid',
                        preferredName: 'Pref',
                        inActive: true,
                        createdAt: expect.anything(),
                        profileData: {
                            birthday: moment.tz('1/1/2010', 'MM/DD/YYYY', 'America/New_York').startOf('day').valueOf(),
                            cacfpSubsidy: 'Paid',
                            gender: 'Female',
                            householdInformation: expect.anything(),
                            mediaRequirements: {
                                mediaRelease: undefined,
                                mediaReviewRequired: 'Yes',
                                noMediaAllowed: 'Yes'
                            },
                            notesPrivate: undefined,
                            notesPublic: undefined,
                            restrictedPickup: expect.anything(),
                            standardOutlook: expect.anything(),
                        }
                    }
                    ],
                    [
                        {
                            "_id": 42,
                            "createdAt": expect.anything(),
                            "createdByPersonId": "foo",
                            "createdByUIImport": true,
                            "firstName": "Parent 1 First",
                            "inActive": true,
                            "lastName": "Parent 1 Last",
                            "orgId": "123",
                            "pinCode": undefined,
                            "pinCodeSupplemental": undefined,
                            "profileData": {
                                "householdInformation": {
                                    "addressType": undefined,
                                    "parentCity": undefined,
                                    "parentState": undefined,
                                    "parentStreetAddress": undefined,
                                    "parentZip": undefined,
                                },
                                "notesPrivate": undefined,
                                "phoneNumberHome": undefined,
                                "phoneNumberWork": undefined,
                                "phonePrimary": "************",
                            },
                            "profileEmailAddress": "<EMAIL>",
                            "type": "family",
                        },
                    ],
                    [
                        {
                            "_id": 42,
                            "createdAt": expect.anything(),
                            "createdByPersonId": "foo",
                            "createdByUIImport": true,
                            "firstName": "Parent 2 First",
                            "inActive": true,
                            "lastName": "Parent 2 Last",
                            "orgId": "123",
                            "pinCode": undefined,
                            "pinCodeSupplemental": undefined,
                            "profileData": {
                                "householdInformation": {
                                    "addressType": undefined,
                                    "parentCity": undefined,
                                    "parentState": undefined,
                                    "parentStreetAddress": undefined,
                                    "parentZip": undefined,
                                },
                                "notesPrivate": undefined,
                                "phoneNumberHome": undefined,
                                "phoneNumberWork": undefined,
                                "phonePrimary": "************",
                            },
                            "profileEmailAddress": "<EMAIL>",
                            "type": "family",
                        },
                    ],
                    [
                        {
                            "_id": 42,
                            "createdAt": expect.anything(),
                            "createdByPersonId": "foo",
                            "createdByUIImport": true,
                            "firstName": "Pickup First",
                            "inActive": true,
                            "lastName": "Pickup Last",
                            "orgId": "123",
                            "profileData": {
                                "phonePrimary": "",
                            },
                            "type": "family",
                        },
                    ],
                    [
                        {
                            "_id": 42,
                            "createdAt": expect.anything(),
                            "createdByPersonId": "foo",
                            "createdByUIImport": true,
                            "firstName": "EC First",
                            "inActive": true,
                            "lastName": "EC Last",
                            "orgId": "123",
                            "profileData": {
                                "phonePrimary": "",
                            },
                            "type": "family",
                        },
                    ],
                [
                    {
                        _id: 42,
                        orgId: '123',
                        type: 'person',
                        createdByPersonId: 'foo',
                        createdByUIImport: true,
                        defaultGroupId: '1',
                        firstName: 'Child First 2',
                        lastName: 'Child Last 2',
                        middleName: 'Mid',
                        preferredName: 'Pref',
                        inActive: true,
                        createdAt: expect.anything(),
                        profileData: {
                            birthday: moment.tz('1/1/2010', 'MM/DD/YYYY', 'America/New_York').startOf('day').valueOf(),
                            cacfpSubsidy: 'Paid',
                            gender: 'Female',
                            householdInformation: expect.anything(),
                            mediaRequirements: {
                                mediaRelease: undefined,
                                mediaReviewRequired: 'Yes',
                                noMediaAllowed: 'Yes'
                            },
                            notesPrivate: undefined,
                            notesPublic: undefined,
                            restrictedPickup: expect.anything(),
                            standardOutlook: expect.anything(),
                        }
                    }
                ]
                ]
            );
        })
        it('processes active/inactive children correctly', async () => {
            const randomMock = mockRandom();
            const peopleMock = People;
            peopleMock.find.mockImplementation(()=>{
                return ({
                    fetchAsync:jest.fn().mockImplementation(()=>[])
                })
            });
            peopleMock.aggregate.mockReturnValue(
                {
                    toArray: jest.fn().mockResolvedValue([]),
                });
            const historyAuditsMock = HistoryAudits;
            const relationshipsMock = Relationships;
            const reservationsMock = Reservations;
            const row1 = { ...baseRow };
            const row2 = { ...baseRow };
            row1.Status = 'inactive';
            row2.Status = 'active';
            row2["First Name"] = 'Child First 2';
            row2["Last Name"] = 'Child Last 2';
            const results = await ImportUtils.processImport(
                [row1, row2],
                Object.keys(baseRow),
                '123',
                'America/New_York',
                [{ _id: '1', name: 'Classroom 1' }],
                'foo',
                { preview: false, isMock: true }
            );
            expect(results).toStrictEqual({ messages: [], successCount: 2, failCount: 0 });
            expect(peopleMock.insertAsync.mock.calls.length).toBe(6);
            expect(historyAuditsMock.insertAsync.mock.calls.length).toBe(8);
            expect(relationshipsMock.direct.insertAsync.mock.calls.length).toBe(8);
            expect(reservationsMock.insertAsync.mock.calls.length).toBe(2);
            expect(peopleMock.insertAsync.mock.calls).toStrictEqual([
                    [
                        {
                            _id: 42,
                            orgId: '123',
                            type: 'person',
                            createdByPersonId: 'foo',
                            createdByUIImport: true,
                            defaultGroupId: '1',
                            firstName: 'Child First',
                            lastName: 'Child Last',
                            middleName: 'Mid',
                            preferredName: 'Pref',
                            inActive: false,
                            createdAt: expect.anything(),
                            profileData: {
                                birthday: moment.tz('1/1/2010', 'MM/DD/YYYY', 'America/New_York').startOf('day').valueOf(),
                                cacfpSubsidy: 'Paid',
                                gender: 'Female',
                                householdInformation: expect.anything(),
                                mediaRequirements: {
                                    mediaRelease: undefined,
                                    mediaReviewRequired: 'Yes',
                                    noMediaAllowed: 'Yes'
                                },
                                notesPrivate: undefined,
                                notesPublic: undefined,
                                restrictedPickup: expect.anything(),
                                standardOutlook: expect.anything(),
                            }
                        }
                    ],
                    [
                        {
                            "_id": 42,
                            "createdAt": expect.anything(),
                            "createdByPersonId": "foo",
                            "createdByUIImport": true,
                            "firstName": "Parent 1 First",
                            "inActive": false,
                            "lastName": "Parent 1 Last",
                            "orgId": "123",
                            "pinCode": undefined,
                            "pinCodeSupplemental": undefined,
                            "profileData": {
                                "householdInformation": {
                                    "addressType": undefined,
                                    "parentCity": undefined,
                                    "parentState": undefined,
                                    "parentStreetAddress": undefined,
                                    "parentZip": undefined,
                                },
                                "notesPrivate": undefined,
                                "phoneNumberHome": undefined,
                                "phoneNumberWork": undefined,
                                "phonePrimary": "************",
                            },
                            "profileEmailAddress": "<EMAIL>",
                            "type": "family",
                        },
                    ],
                    [
                        {
                            "_id": 42,
                            "createdAt": expect.anything(),
                            "createdByPersonId": "foo",
                            "createdByUIImport": true,
                            "firstName": "Parent 2 First",
                            "inActive": false,
                            "lastName": "Parent 2 Last",
                            "orgId": "123",
                            "pinCode": undefined,
                            "pinCodeSupplemental": undefined,
                            "profileData": {
                                "householdInformation": {
                                    "addressType": undefined,
                                    "parentCity": undefined,
                                    "parentState": undefined,
                                    "parentStreetAddress": undefined,
                                    "parentZip": undefined,
                                },
                                "notesPrivate": undefined,
                                "phoneNumberHome": undefined,
                                "phoneNumberWork": undefined,
                                "phonePrimary": "************",
                            },
                            "profileEmailAddress": "<EMAIL>",
                            "type": "family",
                        },
                    ],
                    [
                        {
                            "_id": 42,
                            "createdAt": expect.anything(),
                            "createdByPersonId": "foo",
                            "createdByUIImport": true,
                            "firstName": "Pickup First",
                            "inActive": false,
                            "lastName": "Pickup Last",
                            "orgId": "123",
                            "profileData": {
                                "phonePrimary": "",
                            },
                            "type": "family",
                        },
                    ],
                    [
                        {
                            "_id": 42,
                            "createdAt": expect.anything(),
                            "createdByPersonId": "foo",
                            "createdByUIImport": true,
                            "firstName": "EC First",
                            "inActive": false,
                            "lastName": "EC Last",
                            "orgId": "123",
                            "profileData": {
                                "phonePrimary": "",
                            },
                            "type": "family",
                        },
                    ],
                    [
                        {
                            _id: 42,
                            orgId: '123',
                            type: 'person',
                            createdByPersonId: 'foo',
                            createdByUIImport: true,
                            defaultGroupId: '1',
                            firstName: 'Child First 2',
                            lastName: 'Child Last 2',
                            middleName: 'Mid',
                            preferredName: 'Pref',
                            inActive: false,
                            createdAt: expect.anything(),
                            profileData: {
                                birthday: moment.tz('1/1/2010', 'MM/DD/YYYY', 'America/New_York').startOf('day').valueOf(),
                                cacfpSubsidy: 'Paid',
                                gender: 'Female',
                                householdInformation: expect.anything(),
                                mediaRequirements: {
                                    mediaRelease: undefined,
                                    mediaReviewRequired: 'Yes',
                                    noMediaAllowed: 'Yes'
                                },
                                notesPrivate: undefined,
                                notesPublic: undefined,
                                restrictedPickup: expect.anything(),
                                standardOutlook: expect.anything(),
                            }
                        }
                    ]
                ]
            );
        })
    });
});