import {mockMeteor} from "../../../helpers/meteorMock";
import {ChildcareCrmFamilyService} from "../../../../server/childcareCrmFamilyService";
import { mockCollection } from "../../../helpers/collectionMock";
import fetchMock from "jest-fetch-mock";
import { People } from "../../../../lib/collections/people";
import { Orgs } from "../../../../lib/collections/orgs";
import { ChildcareCrmAccounts } from "../../../../lib/collections/childcareCrmAccounts";
import { Relationships } from "../../../../lib/collections/relationships";
import { Reservations } from "../../../../lib/collections/reservations";

fetchMock.enableMocks();

const meteorMock = mockMeteor();
const peopleMock = People;
const orgsMock = Orgs;
const childcareCrmAccountsMock = ChildcareCrmAccounts;
const relationshipsMock = Relationships;
const reservationsMock = Reservations;

describe('childcareCrmFamilyService', () => {
    beforeEach(() => {
        jest.clearAllMocks();
        fetch.resetMocks();
    });
    it('should send a family to crm', async () => {
        // child
        peopleMock.findOneAsync.mockImplementationOnce(() => ({
            'orgId': 'org1',
            firstName: 'chf',
            lastName: 'chl',
            type: 'person',
            findOwnedRelationships: () => [
                {
                    personId: 'zxy',
                    relationshipType: 'family',
                },
                {
                    personId: 'yyy',
                    relationshipType: 'family'
                }
            ]
        }));
        // account
        childcareCrmAccountsMock.findOneAsync.mockImplementationOnce(() => ({
            centers: [{
                orgId: 'org1',
                id: 1
            }]
        }));
        // org
        orgsMock.findOneAsync.mockImplementationOnce(() => ({
            _id: 'org1',
            profileDataPrefix: () => ('')
        }));
        // login
        fetch.mockResponseOnce(JSON.stringify({
            token: 'tokenstring'
        }));
        // integrations
        fetch.mockResponseOnce(JSON.stringify([
            {
                name: 'Manage',
                id: 32
            }
        ]));
        // all children relationships
        relationshipsMock.find.mockImplementationOnce(() => {
            return ({
                fetchAsync:jest.fn().mockImplementation(()=> [
                    {
                        personId: 'zxy',
                        targetId: '123'
                    }
                ])
            })
        });
        relationshipsMock.find.mockImplementationOnce(() => {
            return ({
                fetchAsync:jest.fn().mockImplementation(()=> [
                    {
                        personId: 'yyy',
                        targetId: '123'
                    }
                ])
            })
        });
        // all children
        peopleMock.find.mockImplementationOnce(() => {
            return ({
                fetchAsync:jest.fn().mockImplementation(()=> [
                    {
                        _id: '123',
                        firstName: 'chf',
                        lastName: 'chl'
                    }
                ])
            })
        });
        relationshipsMock.find.mockImplementationOnce(() => {
            return ({
                fetchAsync:jest.fn().mockImplementation(()=> [])
            })
        });
        // all contacts
        peopleMock.find.mockImplementationOnce(() => {
            return ({
                fetchAsync:jest.fn().mockImplementation(()=> [
                    {
                        _id: 'zxy',
                        firstName: 'guardianf',
                        lastName: 'guardianl',
                        createdAt: 111,
                    },
                    {
                        _id: 'yyy',
                        firstName: 'secondf',
                        lastName: 'secondl',
                        createdAt: 222
                    }
                ])
            })
        });
        // primary guardian
        relationshipsMock.find.mockImplementationOnce(() => {
            return ({
                fetchAsync:jest.fn().mockImplementation(()=> [{
                    relationshipType: 'family',
                    isPrimary: true
                }])
            })
        });
        // secondary guardian
        relationshipsMock.find.mockImplementationOnce(() => {
            return ({
                fetchAsync:jest.fn().mockImplementation(()=> [{
                    relationshipType: 'family',
                    isPrimary: false
                }])
            })
        });
        // dupe check response
        fetch.mockResponseOnce(JSON.stringify([]));
        // inquiry type
        fetch.mockResponseOnce(JSON.stringify([
            {
                id: 1,
                identifier: 'inquirySourceUnifiedIntegration'
            }
        ]));
        // lead source type
        fetch.mockResponseOnce(JSON.stringify([
            {
                id: 2,
                identifier: 'leadSourceUnifiedIntegration'
            }
        ]));
        // add family response
        fetch.mockResponseOnce(JSON.stringify(
            {
                id: 987,
                primary_guardian: {
                    id: 345
                }
            }
        ), { status: 201 });
        // secondary guardian response
        fetch.mockResponseOnce(JSON.stringify([
            {
                id: 444,
            }
        ]), { status: 201 });
        // add child response
        fetch.mockResponseOnce(JSON.stringify([
            {
                id: 876,
            }
        ]), { status: 201 });
        reservationsMock.find.mockImplementationOnce(() => {
            return ({
                fetchAsync:jest.fn().mockImplementation(()=>[])
            })
        });
    
        const result = await ChildcareCrmFamilyService.sendUpdateAfterAddingRelationship('123');
    
        expect(peopleMock.direct.updateAsync.mock.calls.length).toBe(3);
        expect(peopleMock.direct.updateAsync.mock.calls[0]).toStrictEqual([{ _id: 'zxy' }, { $set: { childcareCrm: { centerId: 1, familyId: 987, guardianId: 345 } } }]);
        expect(peopleMock.direct.updateAsync.mock.calls[1]).toStrictEqual([{ _id: 'yyy' }, { $set: { childcareCrm: { familyId: 987, guardianId: 444 } } }]);
        expect(peopleMock.direct.updateAsync.mock.calls[2]).toStrictEqual([{ _id: '123' }, { $set: { childcareCrm: { centerId: 1, familyId: 987, childId: 876 } } }]);
    });
    it('should a family in crm', async () => {
        // child
        peopleMock.findOneAsync.mockImplementationOnce(() => ({
            'orgId': 'org1',
            firstName: 'chf',
            lastName: 'chl',
            type: 'person',
            findOwnedRelationships: () => [
                {
                    personId: 'zxy',
                    relationshipType: 'family',
                },
                {
                    personId: 'yyy',
                    relationshipType: 'family'
                }
            ]
        }));
        // account
        childcareCrmAccountsMock.findOneAsync.mockImplementationOnce(() => ({
            centers: [{
                orgId: 'org1',
                id: 1
            }]
        }));
        // org
        orgsMock.findOneAsync.mockImplementationOnce(() => ({
            _id: 'org1',
            profileDataPrefix: () => ('')
        }));
        // login
        fetch.mockResponseOnce(JSON.stringify({
            token: 'tokenstring'
        }));
        // integrations
        fetch.mockResponseOnce(JSON.stringify([
            {
                name: 'Manage',
                id: 32
            }
        ]));
        // all children relationships
        relationshipsMock.find.mockImplementationOnce(() => {
            return ({
                fetchAsync:jest.fn().mockImplementation(()=>[
                    {
                        personId: 'zxy',
                        targetId: '123'
                    }
                ])
            })
        });
        relationshipsMock.find.mockImplementationOnce(() => {
            return ({
                fetchAsync:jest.fn().mockImplementation(()=>[
                    {
                        personId: 'yyy',
                        targetId: '123'
                    }
                ])
            })
        });
        // all children
        peopleMock.find.mockImplementationOnce(() => {
            return ({
                fetchAsync:jest.fn().mockImplementation(()=>[
                    {
                        _id: '123',
                        firstName: 'chf',
                        lastName: 'chl'
                    }
                ])
            })
        });
        relationshipsMock.find.mockImplementationOnce(() => {
            return ({
                fetchAsync:jest.fn().mockImplementation(()=>[])
            })
        });
        // all contacts
        peopleMock.find.mockImplementationOnce(() => {
            return ({
                fetchAsync:jest.fn().mockImplementation(()=>[
                    {
                        _id: 'zxy',
                        firstName: 'guardianf',
                        lastName: 'guardianl',
                        createdAt: 111,
                    },
                    {
                        _id: 'yyy',
                        firstName: 'secondf',
                        lastName: 'secondl',
                        createdAt: 222,
                        childcareCrm: {
                            familyId: 123,
                            guardianId: 345
                        }
                    }
                ])
            })
        });
        // primary guardian
        relationshipsMock.find.mockImplementationOnce(() => {
            return ({
                fetchAsync:jest.fn().mockImplementation(()=>[{
                    relationshipType: 'family',
                    isPrimary: true
                }])
            })
        });
        // secondary guardian
        relationshipsMock.find.mockImplementationOnce(() => {
            return ({
                fetchAsync:jest.fn().mockImplementation(()=>[{
                    relationshipType: 'family',
                    isPrimary: false
                }])
            })
        });
        // existing family response
        fetchMock.mockResponseOnce(JSON.stringify(
            {
                id: 987,
                primary_guardian: {
                    id: 345
                }
            }
        ), { status: 200 });
        // update primrary guardian response
        fetchMock.mockResponseOnce(JSON.stringify([
            {
                id: 345,
            }
        ]), { status: 201 });
        // secondary guardian response
        fetchMock.mockResponseOnce(JSON.stringify([
            {
                id: 444,
            }
        ]), { status: 201 });
        // add child response
        fetchMock.mockResponseOnce(JSON.stringify([
            {
                id: 876,
            }
        ]), { status: 201 });
        reservationsMock.find.mockImplementationOnce(() => {
            return ({
                fetchAsync:jest.fn().mockImplementation(()=>[])
            })
        });
        const result = await ChildcareCrmFamilyService.sendUpdateAfterAddingRelationship('123');
        expect(peopleMock.direct.updateAsync.mock.calls.length).toBe(3);
        expect(peopleMock.direct.updateAsync.mock.calls[0]).toStrictEqual([{_id: 'zxy'}, {$set: {childcareCrm: { familyId: 123, guardianId: 345}}}]);
        expect(peopleMock.direct.updateAsync.mock.calls[1]).toStrictEqual([{_id: 'yyy'}, {$set: {childcareCrm: { familyId: 123, guardianId: 444}}}]);
        expect(peopleMock.direct.updateAsync.mock.calls[2]).toStrictEqual([{_id: '123'}, {$set: {childcareCrm: { centerId: 1, familyId: 123, childId: 876}}}]);
    });
    it('update local ids with a duplicate family', async () => {
        // child
        peopleMock.findOneAsync.mockImplementationOnce(() => ({
            'orgId': 'org1',
            firstName: 'chf',
            lastName: 'chl',
            type: 'person',
            findOwnedRelationships: () => [
                {
                    personId: 'zxy',
                    relationshipType: 'family',
                }
            ]
        }));
        // account
        childcareCrmAccountsMock.findOneAsync.mockImplementationOnce(() => ({
            centers: [{
                orgId: 'org1',
                id: 1
            }]
        }));
        // org
        orgsMock.findOneAsync.mockImplementationOnce(() => ({
            _id: 'org1',
            profileDataPrefix: () => ('')
        }));
        // login
        fetch.mockResponseOnce(JSON.stringify({
            token: 'tokenstring'
        }));
        // integrations
        fetch.mockResponseOnce(JSON.stringify([
            {
                name: 'Manage',
                id: 32
            }
        ]));
        // all children relationships
        relationshipsMock.find.mockImplementationOnce(() => {
            return ({
                fetchAsync:jest.fn().mockImplementation(()=>[
                    {
                        personId: 'zxy',
                        targetId: '123'
                    }
                ])
            })
        });
        // all children
        peopleMock.find.mockImplementationOnce(() => {
            return ({
                fetchAsync:jest.fn().mockImplementation(()=>[
                    {
                        _id: '123',
                        firstName: 'chf',
                        lastName: 'chl'
                    }
                ])
            })
        });
        relationshipsMock.find.mockImplementationOnce(() => {
            return ({
                fetchAsync:jest.fn().mockImplementation(()=>[])
            })
        });
        // all contacts
        peopleMock.find.mockImplementationOnce(() => {
            return ({
                fetchAsync:jest.fn().mockImplementation(()=>[
                    {
                        _id: 'zxy',
                        firstName: 'guardianf',
                        lastName: 'guardianl'
                    }
                ])
            })
        });
        // contact relationship
        relationshipsMock.find.mockImplementationOnce(() => {
            return ({
                fetchAsync:jest.fn().mockImplementation(()=>[
                    {
                        isPrimary: true,
                        relationshipType: 'family'
                    }
                ])
            })
        });
        // dupe check response
        fetchMock.mockResponseOnce(JSON.stringify([444]));
        // fetch family response
        fetchMock.mockResponseOnce(JSON.stringify(
            {
                id: 444,
                primary_guardian: {
                    id: 333,
                    integrations: [
                        {
                            integration_partner_id: 32
                        }
                    ]
                }
            }
        ), { status: 200 });
        // update family response
        fetchMock.mockResponseOnce(JSON.stringify(
            {
                id: 444,
                primary_guardian: {
                    id: 333,
                    integrations: [
                        {
                            integration_partner_id: 32
                        }
                    ]
                }
            }
        ), { status: 200 });
        // add child response
        fetchMock.mockResponseOnce(JSON.stringify([
            {
                id: 876,
            }
        ]), { status: 201 });
        reservationsMock.find.mockImplementationOnce(() => {
            return ({
                fetchAsync:jest.fn().mockImplementation(()=>[])
            })
        });
        const result = await ChildcareCrmFamilyService.sendUpdateAfterAddingRelationship('123');
        expect(peopleMock.direct.updateAsync.mock.calls.length).toBe(2);
        expect(peopleMock.direct.updateAsync.mock.calls[0]).toStrictEqual([{_id: 'zxy'}, {$set: {childcareCrm: { centerId: 1, familyId: 444, guardianId: 333}}}]);
        expect(peopleMock.direct.updateAsync.mock.calls[1]).toStrictEqual([{_id: '123'}, {$set: {childcareCrm: { centerId: 1, familyId: 444, childId: 876}}}]);
    });
    it('cms ids family', async () => {
        // child
        peopleMock.findOneAsync.mockImplementationOnce(() => ({
            'orgId': 'org1',
            firstName: 'chf',
            lastName: 'chl',
            type: 'person',
            findOwnedRelationships: () => [
                {
                    personId: 'zxy',
                    relationshipType: 'family',
                }
            ]
        }));
        // account
        childcareCrmAccountsMock.findOneAsync.mockImplementationOnce(() => ({
            centers: [{
                orgId: 'org1',
                id: 1
            }]
        }));
        // org
        orgsMock.findOneAsync.mockImplementationOnce(() => ({
            _id: 'org1',
            profileDataPrefix: () => ('')
        }));
        // login
        fetch.mockResponseOnce(JSON.stringify({
            token: 'tokenstring'
        }));
        // integrations
        fetch.mockResponseOnce(JSON.stringify([
            {
                name: 'Manage',
                id: 32
            }
        ]));
        // all children relationships
        relationshipsMock.find.mockImplementationOnce(() => {
            return ({
                fetchAsync:jest.fn().mockImplementation(()=>[
                    {
                        personId: 'zxy',
                        targetId: '123'
                    }
                ])
            })
        });
        // all children
        peopleMock.find.mockImplementationOnce(() => {
            return ({
                fetchAsync:jest.fn().mockImplementation(()=>[
                    {
                        _id: '123',
                        firstName: 'chf',
                        lastName: 'chl',
                        profileData: {
                            historicalCMSid: 'childCms'
                        },
                    }
                ])
            })
        });
        relationshipsMock.find.mockImplementationOnce(() => {
            return ({
                fetchAsync:jest.fn().mockImplementation(()=>[])
            })
        });
        // all contacts
        peopleMock.find.mockImplementationOnce(() => {
            return ({
                fetchAsync:jest.fn().mockImplementation(()=>[
                    {
                        _id: 'zxy',
                        firstName: 'guardianf',
                        lastName: 'guardianl',
                        profileData: {
                            historicalCMSid: 'leadCms'
                        },
                    }
                ])
            })
        });
        // contact relationship
        relationshipsMock.find.mockImplementationOnce(() => {
            return ({
                fetchAsync:jest.fn().mockImplementation(()=>[
                    {
                        isPrimary: true,
                        relationshipType: 'family'
                    }
                ])
            })
        });
        // dupe check response
        fetchMock.mockResponseOnce(JSON.stringify([444]));
        // fetch family response
        fetchMock.mockResponseOnce(JSON.stringify(
            {
                id: 444,
                primary_guardian: {
                    id: 333,
                    integrations: [
                        {
                            integration_partner_id: 32
                        }
                    ]
                },
                children: [
                    {
                        id: 877,
                        integrations: []
                    }
                ]
            }
        ), { status: 200 });
        // update family response
        fetchMock.mockResponseOnce(JSON.stringify(
            {
                id: 444,
                primary_guardian: {
                    id: 333,
                    integrations: [
                        {
                            integration_partner_id: 32
                        }
                    ]
                },
            }
        ), { status: 200 });
        // child cms check response
        fetchMock.mockResponseOnce(JSON.stringify(
            {
                familyId: 444,
                childId: 877
            }
        ), { status: 200 });
        // retrieve family response
        fetchMock.mockResponseOnce(JSON.stringify(
            {
                id: 444,
            }
        ), { status: 200 });
        // update child response
        fetchMock.mockResponseOnce(JSON.stringify([
            {
                id: 877,
            }
        ]), { status: 200 });
        reservationsMock.find.mockImplementationOnce(() => {
            return ({
                fetchAsync:jest.fn().mockImplementation(()=>[])
            })
        });
        const result = await ChildcareCrmFamilyService.sendUpdateAfterAddingRelationship('123');
        expect(peopleMock.direct.updateAsync.mock.calls.length).toBe(2);
        expect(peopleMock.direct.updateAsync.mock.calls[0]).toStrictEqual([{_id: 'zxy'}, {$set: {childcareCrm: { centerId: 1, familyId: 444, guardianId: 333}}}]);
        expect(peopleMock.direct.updateAsync.mock.calls[1]).toStrictEqual([{_id: '123'}, {$set: {childcareCrm: { centerId: 1, familyId: 444, childId: 877}}}]);
    });
});