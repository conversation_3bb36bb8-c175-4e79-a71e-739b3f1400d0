[{"_id": "b6pmKACr8XHP7dePZ", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "designations": [], "type": "person", "profileData": {"gender": "Male", "studentGrade": "4", "document1": "Yes", "photoAttestation": "Yes"}, "createdBy": "gtkCNvhj8rKa3aGvC", "createdAt": *************, "orgId": "7y5bsDP3Pnr4LvEju", "inActive": false, "billing": {"enrolledPlans": [{"_id": "9m88u4je4P4rQjAqL", "planDetails": {"_id": "9m88u4je4P4rQjAqL", "description": "After School", "type": "plan", "program": "cuPS2a63AEXWr8Rzs", "frequency": "scaledMonthly", "category": "tuition", "amount": 288, "scaledAmounts": [102, 164, 221, 266, 288], "ledgerAccountName": "4012", "details": {"startTime": "2:40 pm", "endTime": "6:00 pm", "regStartDate": *************, "grades": ["K", "1", "2", "3", "4", "5", "6", "7", "8"], "scheduleType": "ivEwGqazmcWoWv6oJ", "dateType": "timePeriod", "timePeriod": "cwfxok5bNERCh84TR"}}, "enrollmentDate": *************, "allocations": [{"allocationType": "discount", "amount": 10, "amountType": "percent", "discountType": "siblingDiscount", "allocationDescription": "Discount: Sibling Discount", "id": "gLx8t7Yy85BvFBQqL", "discountAmount": 28.8}, {"allocationType": "reimbursable", "amount": 10, "amountType": "dollars", "reimbursementType": "ccdf", "allocationDescription": "Reimbursable: CCDF", "payerStartDate": *************, "payerEndDate": *************, "id": "wzjypN2wnofmLT7Tp"}], "createdAt": *************, "reservationId": "4zhoyL48CoJFC2h4u", "enrollmentForecastStartDate": *************, "enrollmentForecastEndDate": null, "expirationDate": null, "overrideRate": null, "updatedAt": *************, "updatedBy": "q4gDPAEwtFamFxrpq"}], "pendingCharges": [], "lastInvoiced": *************, "enrolledPunchCards": [{"_id": "eCbnkQF4iYzutDKHB", "originalItem": {"_id": "PEbJhPZBPoDP3qqqy", "description": "Before/After Care 10-Day Punch Card", "type": "punchcard", "program": "cuPS2a63AEXWr8Rzs", "numberOfDays": "10", "amount": 315, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 315, "quantity": "1", "notes": "", "type": "item"}]}, "waitlistAddedDate": null}, {"_id": "kvBj3TN6c7Z9ekghf", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "sch<PERSON><PERSON><PERSON><PERSON>", "designations": [], "type": "person", "createdBy": "RXpiMx3eeKXTiZPfc", "createdAt": *************, "orgId": "7y5bsDP3Pnr4LvEju", "inActive": false, "billing": {"enrolledPlans": [{"_id": "FznGhGFu7sBRN3xkf", "planDetails": {"_id": "FznGhGFu7sBRN3xkf", "description": "Before School", "type": "plan", "program": "cuPS2a63AEXWr8Rzs", "frequency": "scaledMonthly", "category": "tuition", "amount": 288, "scaledAmounts": [93, 164, 221, 266, 288], "ledgerAccountName": "4013", "details": {"startTime": "6:45 am", "endTime": "8:35 am", "regStartDate": *************, "grades": ["K", "1", "2", "3", "4", "5", "6", "7", "8"], "scheduleType": "Lm2tqkvGeTXaoi7cX", "dateType": "timePeriod", "timePeriod": "cwfxok5bNERCh84TR"}}, "enrollmentDate": *************, "allocations": [{"allocationType": "reimbursable", "amount": 100, "amountType": "percent", "reimbursementType": "ccdf", "allocationDescription": "Reimbursable: CCDF", "payerStartDate": *************, "payerEndDate": *************, "id": "pop5Auzmszq5KCciE"}], "createdAt": *************, "reservationId": "38B9FmA3f7NzEWe9f", "enrollmentForecastStartDate": *************, "enrollmentForecastEndDate": *************, "overrideRate": null, "updatedAt": *************, "updatedBy": "K2zKEfyE3hQEmzHNw", "expirationDate": *************}, {"_id": "9m88u4je4P4rQjAqL", "planDetails": {"_id": "9m88u4je4P4rQjAqL", "description": "After School", "type": "plan", "program": "cuPS2a63AEXWr8Rzs", "frequency": "scaledMonthly", "category": "tuition", "amount": 288, "scaledAmounts": [102, 164, 221, 266, 288], "ledgerAccountName": "4012", "details": {"startTime": "2:40 pm", "endTime": "6:00 pm", "regStartDate": *************, "grades": ["K", "1", "2", "3", "4", "5", "6", "7", "8"], "scheduleType": "ivEwGqazmcWoWv6oJ", "dateType": "timePeriod", "timePeriod": "cwfxok5bNERCh84TR"}}, "enrollmentDate": *************, "allocations": [], "createdAt": *************, "expirationDate": *************, "reservationId": "vQ5xLHroaZcCRyshX", "enrollmentForecastStartDate": *************, "enrollmentForecastEndDate": *************, "overrideRate": null, "updatedAt": *************, "updatedBy": "K2zKEfyE3hQEmzHNw"}, {"_id": "FznGhGFu7sBRN3xkf", "planDetails": {"_id": "FznGhGFu7sBRN3xkf", "description": "Before School", "type": "plan", "program": "cuPS2a63AEXWr8Rzs", "frequency": "scaledMonthly", "category": "tuition", "amount": 288, "scaledAmounts": [93, 164, 221, 266, 288], "ledgerAccountName": "4013", "details": {"startTime": "6:45 am", "endTime": "8:35 am", "regStartDate": *************, "grades": ["K", "1", "2", "3", "4", "5", "6", "7", "8"], "scheduleType": "Lm2tqkvGeTXaoi7cX", "dateType": "timePeriod", "timePeriod": "cwfxok5bNERCh84TR"}}, "enrollmentDate": *************, "allocations": [], "createdAt": *************, "reservationId": "CiidTiLZjmzK5T6ME", "enrollmentForecastStartDate": *************, "enrollmentForecastEndDate": *************, "overrideRate": null, "updatedAt": *************, "updatedBy": "K2zKEfyE3hQEmzHNw", "expirationDate": *************}], "pendingCharges": [], "enrolledPunchCards": [{"_id": "59uzqeQQXbqmByF9W", "originalItem": {"_id": "PEbJhPZBPoDP3qqqy", "description": "Before/After Care 10-Day Punch Card", "type": "punchcard", "program": "cuPS2a63AEXWr8Rzs", "numberOfDays": "10", "amount": 315, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 315, "quantity": "1", "notes": "", "type": "item", "datesUsed": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}], "lastInvoiced": *************, "enrolledItems": [{"_id": "uhsSSF3s8QJZ97oFz", "originalItem": {"_id": "8NzmyNGABdG6aMtEy", "description": "Non School Day: 10/02/2023", "type": "item", "program": "54yvgchpuKgdPKKuy", "amount": 30, "scaledAmounts": [], "ledgerAccountName": "4104", "details": {"startTime": "6:30 am", "endTime": "6:00 pm", "regStartDate": *************, "regEndDate": *************, "grades": ["K", "1", "2", "3", "4", "5"], "scheduleType": "kHgTKbFfMaMYysEjN", "dateType": "individualDates", "individualDates": [*************]}}, "createdAt": *************, "price": 30, "quantity": "1", "notes": "", "type": "item"}]}, "documentItems": {"eEZRX8DTmrGu83M4h": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "BHhEB378qohoR6i6T", "personName": "<PERSON><PERSON>"}}}, "enrollmentDate": *************, "profileData": {"enrollmentDate": *************}, "defaultGroupId": "3g8rDKkWMPrdRT3HF"}, {"_id": "KDfEYaXTQAdQhHunn", "firstName": "Berlin", "lastName": "<PERSON><PERSON><PERSON>", "designations": [], "type": "person", "profileData": {"birthday": *************, "gender": "Female", "studentGrade": "2", "document1": "Yes", "photoAttestation": "No"}, "createdBy": "SYSTEM", "createdAt": *************, "orgId": "iiadQjLkj2XAwEfPc", "inActive": false, "billing": {"enrolledPlans": [], "pendingCharges": [], "lastInvoiced": *************, "enrolledItems": [{"_id": "d6S5ucmoZBCQ8RwHq", "originalItem": {"_id": "aJCcSa7vKvLdRNspz", "description": "Non-School Day", "type": "item", "program": "s4o83QyWj6LofKeR4", "amount": 30, "scaledAmounts": [], "ledgerAccountName": "4104", "details": {"startTime": "6:00 am", "endTime": "6:00 pm", "regStartDate": *************, "regEndDate": *************, "grades": ["K", "1", "2", "3", "4", "5"], "scheduleType": "oMrmtCgdtMadKETBm", "dateType": "individualDates", "individualDates": [*************]}}, "createdAt": *************, "price": 30, "quantity": "1", "notes": "9/1/2023", "type": "item"}], "enrolledPunchCards": [{"_id": "Kdkenp8qSLkJYkLG9", "originalItem": {"_id": "bzDTdwpJyStdmcHbv", "description": "Drop In", "type": "punchcard", "program": "QF32vJw9nMitXRwvq", "numberOfDays": "1", "category": "tuition", "amount": 30, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 30, "quantity": "1", "notes": "", "type": "item", "datesUsed": [0]}]}, "defaultGroupId": "y2s6de6eCxSvjyW8G", "waitlistAddedDate": null}, {"_id": "ukFGAP2o4nugxkTv5", "firstName": "Test", "lastName": "Test", "designations": [], "defaultGroupId": "edarMyu487ryNiLqF", "type": "person", "createdBy": "eCxkeyCoi7B9rAWMi", "createdAt": *************, "orgId": "ZHK6xEPdCpMfXk988", "inActive": false, "billing": {"enrolledPlans": [{"_id": "6pnHjz9XzhbCNSeLP", "planDetails": {"_id": "6pnHjz9XzhbCNSeLP", "description": "After School", "type": "plan", "program": "6tGZSuegaGmw4EWvf", "frequency": "scaledMonthly", "category": "tuition", "amount": 318, "scaledAmounts": [122, 173, 238, 285, 318], "ledgerAccountName": "4012", "details": {"startTime": "2:45 pm", "endTime": "6:30 pm", "regStartDate": *************, "grades": ["1", "2", "3", "4", "5"], "scheduleType": "ivEwGqazmcWoWv6oJ", "dateType": "timePeriod", "timePeriod": "m4oRzL25EjAvenD3K"}}, "enrollmentDate": *************, "allocations": [], "createdAt": *************, "expirationDate": *************, "reservationId": "EJiiSYMCbdKErgtiM", "enrollmentForecastStartDate": *************, "enrollmentForecastEndDate": *************}], "lastInvoiced": *************, "pendingCharges": [], "enrolledPunchCards": [{"_id": "MD2DY3mymx5c6HmG2", "originalItem": {"_id": "yeMFXLYjPWhKfrE7f", "description": "Drop In", "type": "punchcard", "program": "6tGZSuegaGmw4EWvf", "numberOfDays": "1", "category": "tuition", "amount": 35, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 35, "quantity": "3", "notes": "", "type": "item", "datesUsed": [0]}, {"_id": "rtqpRFeRy5gNddJjn", "originalItem": {"_id": "yeMFXLYjPWhKfrE7f", "description": "Drop In", "type": "punchcard", "program": "6tGZSuegaGmw4EWvf", "numberOfDays": "1", "category": "tuition", "amount": 35, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 35, "quantity": "1", "notes": "10/6/2023", "type": "item", "datesUsed": [0]}]}, "invoiceReminders": {"emailsSent": 10, "lastSentTime": *************}, "documentItems": {"zTi9DiCE9E7WHJoGH": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "bjo2faywr4ubHzYC7", "personName": "Test Test"}}, "sdAGj5WnheCXrymho": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "bjo2faywr4ubHzYC7", "personName": "Test Test"}}, "n9iREKrBirx4bBxyK": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "bjo2faywr4ubHzYC7", "personName": "Test Test"}}}}, {"_id": "eCc7FYmpTXqq2SuAS", "firstName": "<PERSON>", "lastName": "<PERSON>", "designations": [], "type": "person", "profileData": {"birthday": *************, "gender": "Female", "studentGrade": "4", "document1": "Yes", "photoAttestation": "Yes"}, "createdBy": "SYSTEM", "createdAt": *************, "orgId": "gcK7duNfNxw7yKRFY", "inActive": false, "billing": {"enrolledPlans": [{"_id": "fBW2hamF4AY3Aiptw", "planDetails": {"_id": "fBW2hamF4AY3Aiptw", "description": "After School", "type": "plan", "program": "wMYSRAsbnMEdDENKu", "frequency": "scaledMonthly", "category": "tuition", "amount": 360, "scaledAmounts": [138, 222, 288, 340, 360], "ledgerAccountName": "4012", "details": {"startTime": "3:00 pm", "endTime": "6:00 pm", "regStartDate": *************, "grades": ["K", "1", "2", "3", "4", "5", "6"], "scheduleType": "ivEwGqazmcWoWv6oJ", "dateType": "timePeriod", "timePeriod": "wjBSXn5prWCix72kb"}}, "enrollmentDate": *************, "allocations": [{"allocationType": "discount", "amount": 25.5, "amountType": "dollars", "discountType": "bundle", "allocationDescription": "Bundle: After School and Before School", "discountAmount": 25.5}], "createdAt": *************, "reservationId": "Agxvvo7rxAxRCT82W", "enrollmentForecastStartDate": *************, "bundlePlanId": "jz8Lcfb6N4xLJESoP", "enrollmentForecastEndDate": *************, "expirationDate": *************}, {"_id": "ENxZovWBzDi5HJzZp", "planDetails": {"_id": "ENxZovWBzDi5HJzZp", "description": "Before School", "type": "plan", "program": "wMYSRAsbnMEdDENKu", "frequency": "scaledMonthly", "category": "tuition", "amount": 256, "scaledAmounts": [98, 158, 205, 242, 256], "ledgerAccountName": "4013", "details": {"startTime": "7:00 am", "endTime": "9:00 am", "regStartDate": *************, "grades": ["K", "1", "2", "3", "4", "5", "6"], "scheduleType": "Lm2tqkvGeTXaoi7cX", "dateType": "timePeriod", "timePeriod": "wjBSXn5prWCix72kb"}}, "enrollmentDate": *************, "allocations": [{"allocationType": "discount", "amount": 25.5, "amountType": "dollars", "discountType": "bundle", "allocationDescription": "Bundle: After School and Before School", "discountAmount": 25.5}], "createdAt": *************, "reservationId": "WvrfBF7tMEkhf5pZ4", "enrollmentForecastStartDate": *************, "bundlePlanId": "jz8Lcfb6N4xLJESoP", "enrollmentForecastEndDate": *************, "expirationDate": *************}], "pendingCharges": [], "lastInvoiced": *************, "enrolledPunchCards": [{"_id": "wvQpXPh3GKq7MY2QZ", "originalItem": {"_id": "hrftdfe7c8EFFLdDz", "description": "Drop In", "type": "punchcard", "program": "wMYSRAsbnMEdDENKu", "numberOfDays": "1", "category": "tuition", "amount": 35, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": ***********31, "price": 35, "quantity": "1", "notes": "", "type": "item", "datesUsed": [0]}, {"_id": "3qDvk7GeE6FpRQ4F3", "originalItem": {"_id": "hrftdfe7c8EFFLdDz", "description": "Drop In", "type": "punchcard", "program": "wMYSRAsbnMEdDENKu", "numberOfDays": "1", "category": "tuition", "amount": 35, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 35, "quantity": "1", "notes": "", "type": "item", "datesUsed": [0]}]}, "defaultGroupId": "xqoNzxD4bNkvQpqkZ", "waitlistAddedDate": null, "documentItems": {"amxcnmGgisBkKR7pk": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "KRqwijRhhmMZHFH2A", "personName": "<PERSON><PERSON>"}}, "tnjrs6ByKiSMMZKfw": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "KRqwijRhhmMZHFH2A", "personName": "<PERSON><PERSON>"}}, "gfogNWADk4PbB2YuX": {"createdAt": *************, "createdByPersonId": "4eykQgAmE5iJpkNRT", "createdByUserId": "XhpZyZbuC6CNRut4o", "repositoryKey": "gcK7duNfNxw7yKRFY/XhpZyZbuC6CNRut4o/c53KaXfOEnPuPhUeyIpr", "token": "c53KaXfOEnPuPhUeyIpr"}, "ERCyhT2GcyzZk72nv": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "4eykQgAmE5iJpkNRT", "personName": "<PERSON>"}}}, "checkInGroupId": null, "checkInGroupName": null, "checkedIn": false, "checkedInById": "4eykQgAmE5iJpkNRT", "checkedInOutTime": 1698929040000, "presenceLastGroupId": "xqoNzxD4bNkvQpqkZ", "previousClassroomGroupId": null, "timestamps": {"moveMomentTimestamp": {"momentId": "YH9pRiyf4TXA48eev", "timestamp": 1698929079659, "groupId": null, "oldGroupId": "xqoNzxD4bNkvQpqkZ"}}, "invoiceReminders": {"emailsSent": 1, "lastSentTime": 1696234229962}}, {"_id": "Z9gKHuahLbFs4ik5f", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "designations": [], "type": "person", "profileData": {"birthday": 1363147200000, "gender": "Female", "studentGrade": "5", "document1": "Yes", "photoAttestation": "Yes", "standardOutlook": {"specialNeeds": "", "allergies": "", "importantNotes": ""}, "agencyIdentifier": "", "cacfpSubsidy": "", "ethnicIdentity": "", "healthInformation": {"primaryDoctor": "", "primaryDoctorPhone": ""}, "homeSchool": "", "mediaRequirements": {"mediaRelease": "", "mediaReviewRequired": "", "noMediaAllowed": ""}, "medicalConditions": "", "nickname": "", "notesPrivate": "", "notesPublic": "", "payerIdentifier": "", "primaryFamily": "", "racialIdentity": "", "residesWith": "", "restrictedPickUp": {"restrictedPickup1": "", "restrictedPickup2": "", "restrictedPickup3": ""}, "schoolPickUp": "", "subsidyCode": "", "subsidyReason": ""}, "createdBy": "SYSTEM", "createdAt": *************, "orgId": "eXrCBS5roD8bh6z6B", "inActive": false, "billing": {"enrolledPlans": [{"_id": "JuQ2N3CjZ4vaHK5Bg", "planDetails": {"_id": "JuQ2N3CjZ4vaHK5Bg", "description": "After School", "type": "plan", "program": "tvRH7NC83zZHm3Ys7", "frequency": "scaledMonthly", "category": "tuition", "amount": 220, "scaledAmounts": [59, 112, 159, 193, 220], "ledgerAccountName": "4012", "details": {"startTime": "3:36 pm", "endTime": "6:00 pm", "regStartDate": *************, "grades": ["K", "1", "2", "3", "4", "5"], "scheduleType": "ivEwGqazmcWoWv6oJ", "dateType": "timePeriod", "timePeriod": "RbdBieKohNsHiae4p"}}, "enrollmentDate": *************, "allocations": [{"allocationType": "discount", "amount": 28.5, "amountType": "dollars", "discountType": "bundle", "allocationDescription": "Bundle: After School and Before School", "discountAmount": 28.5}], "createdAt": *************, "reservationId": "akqnKCDBEfdohwSic", "enrollmentForecastStartDate": *************, "bundlePlanId": "PpynLRnoPjmbG9ucb"}, {"_id": "S8Ff5ATxc4jdXbebo", "planDetails": {"_id": "S8Ff5ATxc4jdXbebo", "description": "Before School", "type": "plan", "program": "tvRH7NC83zZHm3Ys7", "frequency": "scaledMonthly", "category": "tuition", "amount": 163, "scaledAmounts": [44, 83, 118, 143, 163], "ledgerAccountName": "4013", "details": {"startTime": "6:00 am", "endTime": "9:00 am", "regStartDate": *************, "grades": ["K", "1", "2", "3", "4", "5"], "scheduleType": "Lm2tqkvGeTXaoi7cX", "dateType": "timePeriod", "timePeriod": "RbdBieKohNsHiae4p"}}, "enrollmentDate": *************, "allocations": [{"allocationType": "discount", "amount": 28.5, "amountType": "dollars", "discountType": "bundle", "allocationDescription": "Bundle: After School and Before School", "discountAmount": 28.5}], "createdAt": *************, "reservationId": "Z7Zcq9CGX9hWsrkBP", "enrollmentForecastStartDate": *************, "bundlePlanId": "PpynLRnoPjmbG9ucb"}], "pendingCharges": [], "lastInvoiced": *************, "enrolledPunchCards": [{"_id": "Kfd33uCM95iToXh9q", "originalItem": {"_id": "pT6t5C8N9sAa6oFD2", "description": "Drop In", "type": "punchcard", "program": "tvRH7NC83zZHm3Ys7", "numberOfDays": "1", "category": "tuition", "amount": 30, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 30, "quantity": "1", "notes": "9/1 Unregistered Non School Day at Grafton Bethel", "type": "item", "datesUsed": [0]}]}, "defaultGroupId": "trT9a5ZEpyALKSM2v", "waitlistAddedDate": null, "checkInGroupId": null, "checkInGroupName": null, "checkedIn": false, "checkedInById": "SJkLFJSppCzi9dxCR", "checkedInOutTime": *************, "presenceLastGroupId": null, "previousClassroomGroupId": null, "timestamps": {"moveMomentTimestamp": {"momentId": "FTasrG6NyqwX7MHoW", "timestamp": *************, "groupId": "trT9a5ZEpyALKSM2v", "oldGroupId": null}}, "documentItems": {"3rQgPRpgC9povJSv3": {"templateOptionResult": {"action": "ack", "date": ********20775, "personId": "zm97PKru6uMH2z9Sk", "personName": "<PERSON>"}}, "BjBybtBpqPwz3MuAQ": {"templateOptionResult": {"action": "ack", "date": ********27732, "personId": "zm97PKru6uMH2z9Sk", "personName": "<PERSON>"}}, "AbEn6b9b4gNDQCjxe": {"templateOptionResult": {"action": "ack", "date": ********31777, "personId": "zm97PKru6uMH2z9Sk", "personName": "<PERSON>"}}}}, {"_id": "4Pn77CyypYvxnuncH", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "designations": [], "type": "person", "profileData": {"birthday": 1429333200000, "gender": "Male", "studentGrade": "2", "document1": "Yes", "photoAttestation": "Yes", "standardOutlook": {"specialNeeds": "Autism, EBF3 HADDS", "allergies": "", "importantNotes": ""}, "agencyIdentifier": "", "cacfpSubsidy": "", "ethnicIdentity": "", "healthInformation": {"primaryDoctor": "", "primaryDoctorPhone": ""}, "homeSchool": "", "mediaRequirements": {"mediaRelease": "", "mediaReviewRequired": "", "noMediaAllowed": ""}, "medicalConditions": "", "nickname": "", "notesPrivate": "", "notesPublic": "", "payerIdentifier": "", "primaryFamily": "", "racialIdentity": "", "residesWith": "", "restrictedPickUp": {"restrictedPickup1": "", "restrictedPickup2": "", "restrictedPickup3": ""}, "schoolPickUp": "", "subsidyCode": "", "subsidyReason": ""}, "createdBy": "SYSTEM", "createdAt": *************, "orgId": "gJ66DDKJf5ZktvQXP", "inActive": false, "billing": {"enrolledPunchCards": [{"_id": "8MTzkk9EMKcCM2z5r", "originalItem": {"_id": "PEbJhPZBPoDP3qqqy", "description": "Before/After Care 10-Day Punch Card", "type": "punchcard", "program": "L4uwGjJKSCDXwT6Nz", "numberOfDays": "10", "amount": 270, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 270, "quantity": "1", "notes": "", "type": "item"}], "pendingCharges": [], "lastInvoiced": *************}, "defaultGroupId": "jhHyeEMuL3PTtxNQk", "waitlistAddedDate": null, "invoiceReminders": {"emailsSent": 10, "lastSentTime": *************}}, {"_id": "AQNpYWutDD9xkZbXN", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "designations": [], "type": "person", "profileData": {"birthday": *************, "gender": "Male", "studentGrade": "2", "document1": "Yes", "photoAttestation": "Yes", "standardOutlook": {"specialNeeds": "", "allergies": "", "importantNotes": ""}, "agencyIdentifier": "", "cacfpSubsidy": "", "ethnicIdentity": "", "healthInformation": {"primaryDoctor": "", "primaryDoctorPhone": ""}, "homeSchool": "", "mediaRequirements": {"mediaRelease": "", "mediaReviewRequired": "", "noMediaAllowed": ""}, "medicalConditions": "", "nickname": "", "notesPrivate": "", "notesPublic": "", "payerIdentifier": "", "primaryFamily": "", "racialIdentity": "", "residesWith": "", "restrictedPickUp": {"restrictedPickup1": "", "restrictedPickup2": "", "restrictedPickup3": ""}, "schoolPickUp": "", "subsidyCode": "", "subsidyReason": ""}, "createdBy": "SYSTEM", "createdAt": *************, "orgId": "NcQvtvp2KWBpAFopN", "inActive": false, "billing": {"enrolledPlans": [{"_id": "JuQ2N3CjZ4vaHK5Bg", "planDetails": {"_id": "JuQ2N3CjZ4vaHK5Bg", "description": "After School", "type": "plan", "program": "GumirkZjpvRLLsEPX", "frequency": "scaledMonthly", "category": "tuition", "amount": 220, "scaledAmounts": [59, 112, 159, 193, 220], "ledgerAccountName": "4012", "details": {"startTime": "3:36 pm", "endTime": "6:00 pm", "regStartDate": *************, "grades": ["K", "1", "2", "3", "4", "5"], "scheduleType": "ivEwGqazmcWoWv6oJ", "dateType": "timePeriod", "timePeriod": "6AgjCSwzBCx8hYJ5S"}}, "enrollmentDate": *************, "allocations": [{"allocationType": "discount", "amount": 28.5, "amountType": "dollars", "discountType": "bundle", "allocationDescription": "Bundle: After School and Before School", "discountAmount": 28.5}, {"allocationType": "discount", "amount": 25, "amountType": "percent", "discountType": "militaryDiscount", "allocationDescription": "Discount: Military Discount", "id": "m7CqyPX2zw8mXbPSo"}], "createdAt": *************, "reservationId": "cEPbpvdEhKEfx3DfL", "enrollmentForecastStartDate": *************, "bundlePlanId": "PpynLRnoPjmbG9ucb", "enrollmentForecastEndDate": null, "overrideRate": null, "updatedAt": *************, "updatedBy": "zHQZ6EhrLoiGhCNLK"}, {"_id": "S8Ff5ATxc4jdXbebo", "planDetails": {"_id": "S8Ff5ATxc4jdXbebo", "description": "Before School", "type": "plan", "program": "GumirkZjpvRLLsEPX", "frequency": "scaledMonthly", "category": "tuition", "amount": 163, "scaledAmounts": [44, 83, 118, 143, 163], "ledgerAccountName": "4013", "details": {"startTime": "6:00 am", "endTime": "9:00 am", "regStartDate": *************, "grades": ["K", "1", "2", "3", "4", "5"], "scheduleType": "Lm2tqkvGeTXaoi7cX", "dateType": "timePeriod", "timePeriod": "6AgjCSwzBCx8hYJ5S"}}, "enrollmentDate": *************, "allocations": [{"allocationType": "discount", "amount": 28.5, "amountType": "dollars", "discountType": "bundle", "allocationDescription": "Bundle: After School and Before School", "discountAmount": 28.5}, {"allocationType": "discount", "amount": 25, "amountType": "percent", "discountType": "militaryDiscount", "allocationDescription": "Discount: Military Discount", "id": "nCDYmuBZb8nmJTi2x"}], "createdAt": *************, "reservationId": "JxxanXz6gqBMCam4S", "enrollmentForecastStartDate": *************, "bundlePlanId": "PpynLRnoPjmbG9ucb", "enrollmentForecastEndDate": null, "overrideRate": null, "updatedAt": *************, "updatedBy": "zHQZ6EhrLoiGhCNLK"}], "pendingCharges": [], "lastInvoiced": *************, "enrolledPunchCards": [{"_id": "8wpXgTWFqwESpZKmx", "originalItem": {"_id": "vCdA97tx9retrgQiJ", "description": "Drop In", "type": "punchcard", "program": "GumirkZjpvRLLsEPX", "numberOfDays": "1", "category": "tuition", "amount": 30, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 30, "quantity": "1", "notes": "9/1 Unregistered Non School Day at Grafton Bethel", "type": "item", "datesUsed": [0]}]}, "defaultGroupId": "CC3fzCTCbzRMJGys5", "waitlistAddedDate": null, "checkInGroupId": null, "checkInGroupName": null, "checkedIn": false, "checkedInById": "7E2gW34xbZTpKZTz5", "checkedInOutTime": *************, "presenceLastGroupId": "CC3fzCTCbzRMJGys5", "previousClassroomGroupId": null, "timestamps": {"moveMomentTimestamp": {"momentId": "nrsAk8Gh87wupibGi", "timestamp": *************, "groupId": null, "oldGroupId": "CC3fzCTCbzRMJGys5"}}, "avatarPath": "NcQvtvp2KWBpAFopN/dzDCdMtEojuLTb2va/E2YN6ysgw9TLSasTP", "avatarToken": "E2YN6ysgw9TLSasTP", "invoiceReminders": {"emailsSent": 10, "lastSentTime": *************}, "documentItems": {"LP3xSDjEW4Z7W9YfK": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "7E2gW34xbZTpKZTz5", "personName": "<PERSON>"}}, "N8J2nK2u4j3rg3uMK": {"templateOptionResult": {"action": "ack", "date": 1694175846460, "personId": "7E2gW34xbZTpKZTz5", "personName": "<PERSON>"}}, "HbKT2fvwnuoEsA6WC": {"templateOptionResult": {"action": "ack", "date": 1695737432386, "personId": "7E2gW34xbZTpKZTz5", "personName": "<PERSON>"}}, "vZ8wFASBFB3nwp6eK": {"templateOptionResult": {"action": "ack", "date": 1695737465958, "personId": "7E2gW34xbZTpKZTz5", "personName": "<PERSON>"}}}, "lastInteractionDate": 1698405480000, "lastMoment": {"attributionPersonId": null, "momentType": "comment", "comment": "Be careful I heard there are some vampires in Right At School🧛", "time": "7:18 am", "date": "10/27/2023", "sortStamp": 1698405480000, "momentTypePretty": "Comment", "createdAt": 1698405869169, "createdBy": "dzDCdMtEojuLTb2va", "attributionName": "Right at School Yorktown", "createdByPersonId": "jA5v6sv24wGpWxsn6", "orgId": "NcQvtvp2KWBpAFopN", "taggedPeople": ["ecfej5pw5fnB6Fv7e", "AQNpYWutDD9xkZbXN", "DSGJLWTJ3zR6G3pAr", "jA5v6sv24wGpWxsn6"], "mediaFiles": [{"mediaUrl": "https://s3.amazonaws.com/tendlymruploads/NcQvtvp2KWBpAFopN/dzDCdMtEojuLTb2va/RLPaGdYDA7dQdDYN9", "mediaToken": "RLPaGdYDA7dQdDYN9", "mediaFileType": "image", "mediaPath": "NcQvtvp2KWBpAFopN/dzDCdMtEojuLTb2va/RLPaGdYDA7dQdDYN9"}], "_id": "huetvcjsXwPM2daQD", "orgName": "Yorktown Elementary"}, "lastMomentByType": {"comment": {"attributionPersonId": null, "momentType": "comment", "comment": "Be careful I heard there are some vampires in Right At School🧛", "time": "7:18 am", "date": "10/27/2023", "sortStamp": 1698405480000, "momentTypePretty": "Comment", "createdAt": 1698405869169, "createdBy": "dzDCdMtEojuLTb2va", "attributionName": "Right at School Yorktown", "createdByPersonId": "jA5v6sv24wGpWxsn6", "orgId": "NcQvtvp2KWBpAFopN", "taggedPeople": ["ecfej5pw5fnB6Fv7e", "AQNpYWutDD9xkZbXN", "DSGJLWTJ3zR6G3pAr", "jA5v6sv24wGpWxsn6"], "mediaFiles": [{"mediaUrl": "https://s3.amazonaws.com/tendlymruploads/NcQvtvp2KWBpAFopN/dzDCdMtEojuLTb2va/RLPaGdYDA7dQdDYN9", "mediaToken": "RLPaGdYDA7dQdDYN9", "mediaFileType": "image", "mediaPath": "NcQvtvp2KWBpAFopN/dzDCdMtEojuLTb2va/RLPaGdYDA7dQdDYN9"}], "_id": "huetvcjsXwPM2daQD", "orgName": "Yorktown Elementary"}}, "engagements": [{"orgId": "NcQvtvp2KWBpAFopN", "type": "app", "subType": "created_moment", "detail": "comment_with_media", "momentId": "huetvcjsXwPM2daQD", "createdBy": "jA5v6sv24wGpWxsn6", "createdAt": 1698405869227, "targetPersonId": "AQNpYWutDD9xkZbXN", "sourcePersonId": "jA5v6sv24wGpWxsn6", "_id": "bwi3N4Ro8w4QzBkMM"}]}, {"_id": "ecfej5pw5fnB6Fv7e", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "designations": [], "type": "person", "profileData": {"birthday": 1521691200000, "gender": "Female", "studentGrade": "K", "document1": "Yes", "photoAttestation": "Yes", "standardOutlook": {"specialNeeds": "", "allergies": "", "importantNotes": ""}, "agencyIdentifier": "", "cacfpSubsidy": "", "ethnicIdentity": "", "healthInformation": {"primaryDoctor": "", "primaryDoctorPhone": ""}, "homeSchool": "", "mediaRequirements": {"mediaRelease": "", "mediaReviewRequired": "", "noMediaAllowed": ""}, "medicalConditions": "", "nickname": "", "notesPrivate": "", "notesPublic": "", "payerIdentifier": "", "primaryFamily": "", "racialIdentity": "", "residesWith": "", "restrictedPickUp": {"restrictedPickup1": "", "restrictedPickup2": "", "restrictedPickup3": ""}, "schoolPickUp": "", "subsidyCode": "", "subsidyReason": ""}, "createdBy": "SYSTEM", "createdAt": *************, "orgId": "NcQvtvp2KWBpAFopN", "inActive": false, "billing": {"enrolledPlans": [{"_id": "JuQ2N3CjZ4vaHK5Bg", "planDetails": {"_id": "JuQ2N3CjZ4vaHK5Bg", "description": "After School", "type": "plan", "program": "GumirkZjpvRLLsEPX", "frequency": "scaledMonthly", "category": "tuition", "amount": 220, "scaledAmounts": [59, 112, 159, 193, 220], "ledgerAccountName": "4012", "details": {"startTime": "3:36 pm", "endTime": "6:00 pm", "regStartDate": *************, "grades": ["K", "1", "2", "3", "4", "5"], "scheduleType": "ivEwGqazmcWoWv6oJ", "dateType": "timePeriod", "timePeriod": "6AgjCSwzBCx8hYJ5S"}}, "enrollmentDate": *************, "allocations": [{"allocationType": "discount", "amount": 28.5, "amountType": "dollars", "discountType": "bundle", "allocationDescription": "Bundle: After School and Before School", "discountAmount": 28.5}, {"allocationType": "discount", "amount": 25, "amountType": "percent", "discountType": "militaryDiscount", "allocationDescription": "Discount: Military Discount", "id": "vT3saZM4nzgm2uqXY"}], "createdAt": *************, "reservationId": "J58HAYp9AqHp2p5Yc", "enrollmentForecastStartDate": *************, "bundlePlanId": "PpynLRnoPjmbG9ucb", "enrollmentForecastEndDate": null, "overrideRate": null, "updatedAt": *************, "updatedBy": "zHQZ6EhrLoiGhCNLK"}, {"_id": "S8Ff5ATxc4jdXbebo", "planDetails": {"_id": "S8Ff5ATxc4jdXbebo", "description": "Before School", "type": "plan", "program": "GumirkZjpvRLLsEPX", "frequency": "scaledMonthly", "category": "tuition", "amount": 163, "scaledAmounts": [44, 83, 118, 143, 163], "ledgerAccountName": "4013", "details": {"startTime": "6:00 am", "endTime": "9:00 am", "regStartDate": *************, "grades": ["K", "1", "2", "3", "4", "5"], "scheduleType": "Lm2tqkvGeTXaoi7cX", "dateType": "timePeriod", "timePeriod": "6AgjCSwzBCx8hYJ5S"}}, "enrollmentDate": *************, "allocations": [{"allocationType": "discount", "amount": 28.5, "amountType": "dollars", "discountType": "bundle", "allocationDescription": "Bundle: After School and Before School", "discountAmount": 28.5}, {"allocationType": "discount", "amount": 25, "amountType": "percent", "discountType": "militaryDiscount", "allocationDescription": "Discount: Military Discount", "id": "jvsanmgaksZptN3gp"}], "createdAt": *************, "reservationId": "zchY2aQwmqGnsLjbc", "enrollmentForecastStartDate": *************, "bundlePlanId": "PpynLRnoPjmbG9ucb", "enrollmentForecastEndDate": null, "overrideRate": null, "updatedAt": *************, "updatedBy": "zHQZ6EhrLoiGhCNLK"}], "lastInvoiced": *************, "pendingCharges": [], "enrolledPunchCards": [{"_id": "sLWif2NZyzMYQW8br", "originalItem": {"_id": "vCdA97tx9retrgQiJ", "description": "Drop In", "type": "punchcard", "program": "GumirkZjpvRLLsEPX", "numberOfDays": "1", "category": "tuition", "amount": 30, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 30, "quantity": "1", "notes": "9/1 Unregistered Non School Day at Grafton Bethel", "type": "item", "datesUsed": [0]}]}, "defaultGroupId": "CC3fzCTCbzRMJGys5", "waitlistAddedDate": null, "documentItems": {"vZ8wFASBFB3nwp6eK": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "7E2gW34xbZTpKZTz5", "personName": "<PERSON>"}}, "N8J2nK2u4j3rg3uMK": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "7E2gW34xbZTpKZTz5", "personName": "<PERSON>"}}, "LP3xSDjEW4Z7W9YfK": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "7E2gW34xbZTpKZTz5", "personName": "<PERSON>"}}, "HbKT2fvwnuoEsA6WC": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "7E2gW34xbZTpKZTz5", "personName": "<PERSON>"}}, "7JDzx75RraTgM49Xt": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "7E2gW34xbZTpKZTz5", "personName": "<PERSON>"}}}, "checkInGroupId": null, "checkInGroupName": null, "checkedIn": false, "checkedInById": "7E2gW34xbZTpKZTz5", "checkedInOutTime": 1698929173234, "presenceLastGroupId": "CC3fzCTCbzRMJGys5", "previousClassroomGroupId": null, "timestamps": {"moveMomentTimestamp": {"momentId": "FNswnhSgb7qmJejKw", "timestamp": 1698929173273, "groupId": null, "oldGroupId": "CC3fzCTCbzRMJGys5"}}, "invoiceReminders": {"emailsSent": 10, "lastSentTime": 1697184386822}, "lastInteractionDate": 1698405480000, "lastMoment": {"attributionPersonId": null, "momentType": "comment", "comment": "Be careful I heard there are some vampires in Right At School🧛", "time": "7:18 am", "date": "10/27/2023", "sortStamp": 1698405480000, "momentTypePretty": "Comment", "createdAt": 1698405869169, "createdBy": "dzDCdMtEojuLTb2va", "attributionName": "Right at School Yorktown", "createdByPersonId": "jA5v6sv24wGpWxsn6", "orgId": "NcQvtvp2KWBpAFopN", "taggedPeople": ["ecfej5pw5fnB6Fv7e", "AQNpYWutDD9xkZbXN", "DSGJLWTJ3zR6G3pAr", "jA5v6sv24wGpWxsn6"], "mediaFiles": [{"mediaUrl": "https://s3.amazonaws.com/tendlymruploads/NcQvtvp2KWBpAFopN/dzDCdMtEojuLTb2va/RLPaGdYDA7dQdDYN9", "mediaToken": "RLPaGdYDA7dQdDYN9", "mediaFileType": "image", "mediaPath": "NcQvtvp2KWBpAFopN/dzDCdMtEojuLTb2va/RLPaGdYDA7dQdDYN9"}], "_id": "huetvcjsXwPM2daQD", "orgName": "Yorktown Elementary"}, "lastMomentByType": {"comment": {"attributionPersonId": null, "momentType": "comment", "comment": "Be careful I heard there are some vampires in Right At School🧛", "time": "7:18 am", "date": "10/27/2023", "sortStamp": 1698405480000, "momentTypePretty": "Comment", "createdAt": 1698405869169, "createdBy": "dzDCdMtEojuLTb2va", "attributionName": "Right at School Yorktown", "createdByPersonId": "jA5v6sv24wGpWxsn6", "orgId": "NcQvtvp2KWBpAFopN", "taggedPeople": ["ecfej5pw5fnB6Fv7e", "AQNpYWutDD9xkZbXN", "DSGJLWTJ3zR6G3pAr", "jA5v6sv24wGpWxsn6"], "mediaFiles": [{"mediaUrl": "https://s3.amazonaws.com/tendlymruploads/NcQvtvp2KWBpAFopN/dzDCdMtEojuLTb2va/RLPaGdYDA7dQdDYN9", "mediaToken": "RLPaGdYDA7dQdDYN9", "mediaFileType": "image", "mediaPath": "NcQvtvp2KWBpAFopN/dzDCdMtEojuLTb2va/RLPaGdYDA7dQdDYN9"}], "_id": "huetvcjsXwPM2daQD", "orgName": "Yorktown Elementary"}}, "engagements": [{"orgId": "NcQvtvp2KWBpAFopN", "type": "app", "subType": "created_moment", "detail": "comment_with_media", "momentId": "4Y73bfp92kK4cY7e8", "createdBy": "jA5v6sv24wGpWxsn6", "createdAt": 1698143830253, "targetPersonId": "ecfej5pw5fnB6Fv7e", "sourcePersonId": "jA5v6sv24wGpWxsn6", "_id": "h2jpn6c3ur39fsbqG"}, {"orgId": "NcQvtvp2KWBpAFopN", "type": "app", "subType": "created_moment", "detail": "comment_with_media", "momentId": "huetvcjsXwPM2daQD", "createdBy": "jA5v6sv24wGpWxsn6", "createdAt": 1698405869210, "targetPersonId": "ecfej5pw5fnB6Fv7e", "sourcePersonId": "jA5v6sv24wGpWxsn6", "_id": "MkrWD44xB4NQY9NBn"}]}, {"_id": "MJ6by6DDse8KsxCiP", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON> kim<PERSON>", "designations": [], "type": "person", "profileData": {"birthday": 1410235200000, "gender": "Female", "studentGrade": "3", "document1": "Yes", "photoAttestation": "Yes", "standardOutlook": {"allergies": "Non", "specialNeeds": "Non"}, "withdrawDate": *************}, "createdBy": "SYSTEM", "createdAt": *************, "orgId": "CHfXCQKYcrA4QgeGG", "inActive": true, "billing": {"enrolledPunchCards": [{"_id": "aGoWqYbbjmHetA9ob", "originalItem": {"_id": "PEbJhPZBPoDP3qqqy", "description": "After Care 10-Day Punch Card", "type": "punchcard", "program": "5AtG7fWMfsqFQX4rK", "numberOfDays": "10", "amount": 315, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 315, "quantity": "1", "notes": "", "type": "item"}], "pendingCharges": [], "lastInvoiced": *************}, "deactivatedAt": *************, "deactivationReason": "Unknown", "invoiceReminders": {"emailsSent": 10, "lastSentTime": *************}}, {"_id": "B566NreTZsmAPKneD", "firstName": "<PERSON>", "lastName": "<PERSON>", "designations": [], "type": "person", "profileData": {"birthday": *************, "gender": "Male", "studentGrade": "K", "document1": "Yes", "photoAttestation": "Yes", "standardOutlook": {"allergies": "", "specialNeeds": "", "importantNotes": ""}, "agencyIdentifier": "", "cacfpSubsidy": "", "ethnicIdentity": "", "healthInformation": {"primaryDoctor": "", "primaryDoctorPhone": ""}, "homeSchool": "", "mediaRequirements": {"mediaRelease": "", "mediaReviewRequired": "", "noMediaAllowed": ""}, "medicalConditions": "", "nickname": "", "notesPrivate": "", "notesPublic": "", "payerIdentifier": "", "primaryFamily": "", "racialIdentity": "", "residesWith": "", "restrictedPickUp": {"restrictedPickup1": "", "restrictedPickup2": "", "restrictedPickup3": ""}, "schoolPickUp": "", "subsidyCode": "", "subsidyReason": ""}, "createdBy": "SYSTEM", "createdAt": *************, "orgId": "y6XzjKWuRQQuWGycN", "inActive": false, "billing": {"enrolledPunchCards": [{"_id": "udqJnFRcFxkyMFRQL", "originalItem": {"_id": "PEbJhPZBPoDP3qqqy", "description": "After Care 10-Day Punch Card", "type": "punchcard", "program": "CFSm8h4udohZqPWKc", "numberOfDays": "10", "amount": 360, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 360, "quantity": "1", "notes": "", "type": "item"}], "pendingCharges": [], "lastInvoiced": *************}, "defaultGroupId": "c6sCPqGy8rCJb5nRG", "waitlistAddedDate": null, "invoiceReminders": {"emailsSent": 10, "lastSentTime": *************}, "checkInGroupId": null, "checkInGroupName": null, "checkedIn": false, "checkedInOutTime": *************, "presenceLastGroupId": "c6sCPqGy8rCJb5nRG", "previousClassroomGroupId": null, "timestamps": {"moveMomentTimestamp": {"momentId": "vCdtvmi9vhH3bwzxi", "timestamp": *************, "groupId": null, "oldGroupId": "c6sCPqGy8rCJb5nRG"}}}, {"_id": "oHgRHZrD7YqFkcDaN", "firstName": "<PERSON>", "lastName": "<PERSON>", "designations": [], "type": "person", "profileData": {"birthday": *************, "gender": "Female", "studentGrade": "4", "document1": "Yes", "photoAttestation": "Yes", "standardOutlook": {"allergies": "", "specialNeeds": "", "importantNotes": ""}, "agencyIdentifier": "", "cacfpSubsidy": "", "ethnicIdentity": "", "healthInformation": {"primaryDoctor": "", "primaryDoctorPhone": ""}, "homeSchool": "", "mediaRequirements": {"mediaRelease": "", "mediaReviewRequired": "", "noMediaAllowed": ""}, "medicalConditions": "", "nickname": "", "notesPrivate": "", "notesPublic": "", "payerIdentifier": "", "primaryFamily": "", "racialIdentity": "", "residesWith": "", "restrictedPickUp": {"restrictedPickup1": "", "restrictedPickup2": "", "restrictedPickup3": ""}, "schoolPickUp": "", "subsidyCode": "", "subsidyReason": ""}, "createdBy": "SYSTEM", "createdAt": *************, "orgId": "RSyNjABpiJuekeKtS", "inActive": false, "billing": {"enrolledPunchCards": [{"_id": "dv88ifKtx4thWMSud", "originalItem": {"_id": "PEbJhPZBPoDP3qqqy", "description": "Before/After Care 10-Day Punch Card", "type": "punchcard", "program": "guEPTMJLi9iWE3ndM", "numberOfDays": "10", "amount": 270, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 270, "quantity": "1", "notes": "", "type": "item"}], "pendingCharges": [], "lastInvoiced": *************}, "defaultGroupId": "rkigXAyTJoLsy3LfX", "waitlistAddedDate": null, "invoiceReminders": {"emailsSent": 9, "lastSentTime": **********380}}, {"_id": "8FT8PFD7ZngZSnzwX", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON>", "designations": [], "type": "person", "profileData": {"birthday": *************, "gender": "Male", "studentGrade": "K", "document1": "Yes", "homeSchool": "Pleasant Valley Primary", "photoAttestation": "Yes", "standardOutlook": {"allergies": "", "specialNeeds": "", "importantNotes": ""}, "agencyIdentifier": "", "cacfpSubsidy": "", "ethnicIdentity": "", "healthInformation": {"primaryDoctor": "", "primaryDoctorPhone": ""}, "mediaRequirements": {"mediaRelease": "", "mediaReviewRequired": "", "noMediaAllowed": ""}, "medicalConditions": "", "nickname": "", "notesPrivate": "", "notesPublic": "", "payerIdentifier": "", "primaryFamily": "", "racialIdentity": "", "residesWith": "", "restrictedPickUp": {"restrictedPickup1": "", "restrictedPickup2": "", "restrictedPickup3": ""}, "schoolPickUp": "", "subsidyCode": "", "subsidyReason": ""}, "createdBy": "hnEFX5czZQnoPi38M", "createdAt": *************, "orgId": "SNLNnwBvou3QNE9qt", "inActive": false, "billing": {"enrolledPlans": [{"_id": "iWbNeboHZwiqy5Wjt", "planDetails": {"_id": "iWbNeboHZwiqy5Wjt", "description": "Before School", "type": "plan", "program": "W9hQEyypjKZkYbkqc", "frequency": "scaledMonthly", "category": "tuition", "amount": 295, "scaledAmounts": [94, 174, 235, 275, 295], "ledgerAccountName": "4013", "details": {"startTime": "6:30 am", "endTime": "9:10 am", "regStartDate": *************, "regEndDate": *************, "grades": ["K", "1", "2", "3", "4", "5", "6"], "scheduleType": "Lm2tqkvGeTXaoi7cX", "dateType": "timePeriod", "timePeriod": "9cPEQeu9WKhbLCcYr"}}, "enrollmentDate": *************, "allocations": [{"allocationType": "discount", "amount": 50, "amountType": "percent", "discountType": "districtDiscount", "allocationDescription": "District Discount", "id": "r6q7aTmcdAQbdxoA8"}], "createdAt": *************, "reservationId": "xxS5xSToufWCxYBoH", "enrollmentForecastStartDate": *************}], "pendingCharges": [], "lastInvoiced": *************, "enrolledPunchCards": [{"_id": "xdfZahQXGtrCYuAvB", "originalItem": {"_id": "qMThS7Kw2zzbnkLJm", "description": "Drop In", "type": "punchcard", "program": "W9hQEyypjKZkYbkqc", "numberOfDays": "1", "category": "tuition", "amount": 40, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 40, "quantity": "5", "notes": "for dates 10/30, 10/31, 11/1, 11/2, and 11/3 for aftercare ", "type": "item", "datesUsed": [0]}], "billingNotes": "10/2/23 The parent requested to cancel the drop in days for their student as the district discount does not apply to drop-in. I voided the invoice. (<PERSON><PERSON>)"}, "defaultGroupId": "mEyhNjBNXqB9adx7p", "waitlistAddedDate": null, "documentItems": {"XCCFyAvkZPwpR2abe": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "dmrf3xoQtwx7zLphZ", "personName": "<PERSON>"}}, "HaMcbYTH6FpXEqXYa": {"createdAt": *************, "createdByPersonId": "dmrf3xoQtwx7zLphZ", "createdByUserId": "FSXa4czfNFh3wbJQK", "repositoryKey": "SNLNnwBvou3QNE9qt/FSXa4czfNFh3wbJQK/UzGK8P0Nl1Qbo8kFloRF", "token": "UzGK8P0Nl1Qbo8kFloRF"}, "dgdbetM2fC6W4Msvv": {"createdAt": *************, "createdByPersonId": "dmrf3xoQtwx7zLphZ", "createdByUserId": "FSXa4czfNFh3wbJQK", "repositoryKey": "SNLNnwBvou3QNE9qt/FSXa4czfNFh3wbJQK/n0tiudiqhPuivoVCx4C3", "token": "n0tiudiqhPuivoVCx4C3"}, "726zQThAKNsengLuh": {"templateOptionResult": {"action": "ack", "date": 1694581266169, "personId": "dmrf3xoQtwx7zLphZ", "personName": "<PERSON>"}}}, "checkInGroupId": null, "checkInGroupName": null, "checkedIn": false, "checkedInById": "dmrf3xoQtwx7zLphZ", "checkedInOutTime": 1698941291136, "presenceLastGroupId": "mEyhNjBNXqB9adx7p", "previousClassroomGroupId": null, "timestamps": {"moveMomentTimestamp": {"momentId": "EDXWT2DpETSohhY86", "timestamp": 1698941291266, "groupId": null, "oldGroupId": "mEyhNjBNXqB9adx7p"}}, "invoiceReminders": {"emailsSent": 1, "lastSentTime": 1696234390204}}, {"_id": "R7PpnjfjdTAgMfHQD", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON>", "designations": [], "type": "person", "profileData": {"birthday": 1433736000000, "gender": "Female", "studentGrade": "3", "document1": "Yes", "photoAttestation": "Yes", "standardOutlook": {"allergies": "", "specialNeeds": "", "importantNotes": ""}, "agencyIdentifier": "", "cacfpSubsidy": "", "ethnicIdentity": "", "healthInformation": {"primaryDoctor": "", "primaryDoctorPhone": ""}, "homeSchool": "", "mediaRequirements": {"mediaRelease": "", "mediaReviewRequired": "", "noMediaAllowed": ""}, "medicalConditions": "", "nickname": "", "notesPrivate": "", "notesPublic": "", "payerIdentifier": "", "primaryFamily": "", "racialIdentity": "", "residesWith": "", "restrictedPickUp": {"restrictedPickup1": "", "restrictedPickup2": "", "restrictedPickup3": ""}, "schoolPickUp": "", "subsidyCode": "", "subsidyReason": ""}, "createdBy": "SYSTEM", "createdAt": *************, "orgId": "iHZSWasaCNuxKvYcg", "inActive": false, "billing": {"enrolledPunchCards": [{"_id": "7zgHSneRq9ehGwHs4", "originalItem": {"_id": "PEbJhPZBPoDP3qqqy", "description": "After Care 10-Day Punch Card", "type": "punchcard", "program": "oxeGTPQPHG6M4SC94", "numberOfDays": "10", "amount": 360, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 360, "quantity": "1", "notes": "", "type": "item"}], "pendingCharges": [], "lastInvoiced": *************}, "defaultGroupId": "DpMoZbQFKNpFcJYLg", "waitlistAddedDate": null, "invoiceReminders": {"emailsSent": 10, "lastSentTime": *************}, "documentItems": {"fLhkcpywsDBZPbWsT": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "AhqsvyqedSsZQ9A7t", "personName": "<PERSON> "}}, "xfeLmHtKnu53AzxZW": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "AhqsvyqedSsZQ9A7t", "personName": "<PERSON> "}}, "mJgoYSpndRLCfedFv": {"createdAt": *************, "createdByPersonId": "AhqsvyqedSsZQ9A7t", "createdByUserId": "NyYR9tN9syx5nQkfr", "repositoryKey": "iHZSWasaCNuxKvYcg/NyYR9tN9syx5nQkfr/RsArWap8BibKI0yTumZk", "token": "RsArWap8BibKI0yTumZk"}, "SwLyo6SwbRfShfcZz": {"createdAt": *************, "createdByPersonId": "AhqsvyqedSsZQ9A7t", "createdByUserId": "NyYR9tN9syx5nQkfr", "repositoryKey": "iHZSWasaCNuxKvYcg/NyYR9tN9syx5nQkfr/ka0CQ3xXAVz2ANbcS2FZ", "token": "ka0CQ3xXAVz2ANbcS2FZ"}, "czpbraHbtwGc4S7zd": {"createdAt": 1696417179369, "createdByPersonId": "AhqsvyqedSsZQ9A7t", "createdByUserId": "NyYR9tN9syx5nQkfr", "repositoryKey": "iHZSWasaCNuxKvYcg/NyYR9tN9syx5nQkfr/H8PZes5wrgmelhJl7uaF", "token": "H8PZes5wrgmelhJl7uaF"}}, "checkInGroupId": null, "checkInGroupName": null, "checkedIn": false, "checkedInOutTime": 1698872288911, "presenceLastGroupId": "DpMoZbQFKNpFcJYLg", "previousClassroomGroupId": null, "timestamps": {"moveMomentTimestamp": {"momentId": "ZFdHsydSGBCwDvxCf", "timestamp": 1698872288934, "groupId": null, "oldGroupId": "DpMoZbQFKNpFcJYLg"}}}, {"_id": "BFkTYyQGj9hukNi4b", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "designations": [], "type": "person", "profileData": {"birthday": 1410580800000, "gender": "Male", "studentGrade": "3", "document1": "Yes", "photoAttestation": "Yes", "standardOutlook": {"allergies": "", "specialNeeds": "", "importantNotes": ""}, "healthInformation": {"primaryDoctor": "<PERSON><PERSON>", "primaryDoctorPhone": "**********"}, "homeSchool": "", "agencyIdentifier": "", "cacfpSubsidy": "", "ethnicIdentity": "", "mediaRequirements": {"mediaRelease": "", "mediaReviewRequired": "", "noMediaAllowed": ""}, "medicalConditions": "", "nickname": "", "notesPrivate": "", "notesPublic": "", "payerIdentifier": "", "primaryFamily": "", "racialIdentity": "", "residesWith": "", "restrictedPickUp": {"restrictedPickup1": "", "restrictedPickup2": "", "restrictedPickup3": ""}, "schoolPickUp": "", "subsidyCode": "", "subsidyReason": ""}, "createdBy": "SYSTEM", "createdAt": *************, "orgId": "zRmHbZaQSinBvwdRE", "inActive": false, "billing": {"enrolledPunchCards": [{"_id": "2hCyEhqRWBXzb92dR", "originalItem": {"_id": "PEbJhPZBPoDP3qqqy", "description": "Before/After Care 10-Day Punch Card", "type": "punchcard", "program": "huPQWjWjfEGh3rYG6", "numberOfDays": "10", "amount": 315, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 315, "quantity": "1", "notes": "", "type": "item"}], "pendingCharges": [], "lastInvoiced": *************}, "documentItems": {"5un5mvfp5zD4Mq26S": {"createdAt": *************, "createdByPersonId": "LDKdDebB66Yp3qoog", "createdByUserId": "QxbPM3ZqKpGLDfmDz", "repositoryKey": "zRmHbZaQSinBvwdRE/QxbPM3ZqKpGLDfmDz/dyyOfHsAXAmErVaTwNda", "token": "dyyOfHsAXAmErVaTwNda"}, "6obWTKiEvCX7f8MbQ": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "LDKdDebB66Yp3qoog", "personName": "<PERSON>"}}, "F4vJMMbNXw4bmeWW5": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "LDKdDebB66Yp3qoog", "personName": "<PERSON>"}}, "WNyG43AGHd5CPkhSi": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "LDKdDebB66Yp3qoog", "personName": "<PERSON>"}}}, "lastInformedArrival": {"source": "familyCheckin", "createdAt": *************, "checkedInById": "LDKdDebB66Yp3qoog", "fieldValues": {"attending": "Yes"}, "absent": false}, "defaultGroupId": "fY5Jbi9vwMMcDQo8Q", "waitlistAddedDate": null, "invoiceReminders": {"emailsSent": 10, "lastSentTime": 1693296373302}}, {"_id": "v6DcRESMKEWQ9GfYZ", "firstName": "Alexia", "lastName": "<PERSON><PERSON><PERSON>", "designations": [], "type": "person", "createdBy": "XZ2uYC8CxnRFoWrTW", "createdAt": 1686063273284, "orgId": "iiadQjLkj2XAwEfPc", "inActive": false, "profileData": {"agencyIdentifier": "", "birthday": 1435982400000, "cacfpSubsidy": "", "document1": "yes", "enrollmentDate": 1686024000000, "ethnicIdentity": "", "gender": "Female", "healthInformation": {"primaryDoctor": "", "primaryDoctorPhone": ""}, "homeSchool": "", "mediaRequirements": {"mediaRelease": "", "mediaReviewRequired": "", "noMediaAllowed": ""}, "medicalConditions": "no", "nickname": "", "notesPrivate": "", "notesPublic": "", "payerIdentifier": "", "photoAttestation": "", "primaryFamily": "", "racialIdentity": "", "residesWith": "", "restrictedPickUp": {"restrictedPickup1": "", "restrictedPickup2": "", "restrictedPickup3": ""}, "schoolPickUp": "", "standardOutlook": {"allergies": "", "importantNotes": "", "specialNeeds": ""}, "studentGrade": "K", "subsidyCode": "", "subsidyReason": "", "withdrawDate": *************}, "billing": {"enrolledPlans": [{"_id": "JuQ2N3CjZ4vaHK5Bg", "planDetails": {"_id": "JuQ2N3CjZ4vaHK5Bg", "description": "After School", "type": "plan", "program": "QF32vJw9nMitXRwvq", "frequency": "scaledMonthly", "category": "tuition", "amount": 220, "scaledAmounts": [59, 112, 159, 193, 220], "ledgerAccountName": "4012", "details": {"startTime": "3:36 pm", "endTime": "6:00 pm", "regStartDate": *************, "grades": ["K", "1", "2", "3", "4", "5"], "scheduleType": "ivEwGqazmcWoWv6oJ", "dateType": "timePeriod", "timePeriod": "2pTsmADZMQBjXugS8"}}, "enrollmentDate": *************, "allocations": [{"allocationType": "discount", "amount": 25, "amountType": "percent", "discountType": "militaryDiscount", "allocationDescription": "Discount: Military Discount", "id": "uHLQCGTfwjR8iDzXJ"}], "createdAt": *************, "expirationDate": *************, "reservationId": "pe5iPsZtR4prpeJXZ", "enrollmentForecastStartDate": *************, "enrollmentForecastEndDate": null, "overrideRate": null, "updatedAt": *************, "updatedBy": "vMhKFfuTbxdNR7Gsy"}, {"_id": "S8Ff5ATxc4jdXbebo", "planDetails": {"_id": "S8Ff5ATxc4jdXbebo", "description": "Before School", "type": "plan", "program": "QF32vJw9nMitXRwvq", "frequency": "scaledMonthly", "category": "tuition", "amount": 163, "scaledAmounts": [44, 83, 118, 143, 163], "ledgerAccountName": "4013", "details": {"startTime": "6:00 am", "endTime": "9:00 am", "regStartDate": *************, "grades": ["K", "1", "2", "3", "4", "5"], "scheduleType": "Lm2tqkvGeTXaoi7cX", "dateType": "timePeriod", "timePeriod": "2pTsmADZMQBjXugS8"}}, "enrollmentDate": *************, "allocations": [{"allocationType": "discount", "amount": 25, "amountType": "percent", "discountType": "militaryDiscount", "allocationDescription": "Discount: Military Discount", "id": "Y3kpQdoERGHLj7geE"}], "createdAt": *************, "expirationDate": *************, "reservationId": "T8egNd5bGpFNo6guq", "enrollmentForecastStartDate": *************, "enrollmentForecastEndDate": null, "overrideRate": null, "updatedAt": *************, "updatedBy": "vMhKFfuTbxdNR7Gsy"}], "lastInvoiced": *************, "pendingCharges": [], "enrolledItems": [{"_id": "8uNxZ2PeFjWmaxptW", "originalItem": {"_id": "aJCcSa7vKvLdRNspz", "description": "Non-School Day", "type": "item", "program": "s4o83QyWj6LofKeR4", "amount": 30, "scaledAmounts": [], "ledgerAccountName": "4104", "details": {"startTime": "6:00 am", "endTime": "6:00 pm", "regStartDate": *************, "regEndDate": *************, "grades": ["K", "1", "2", "3", "4", "5"], "scheduleType": "oMrmtCgdtMadKETBm", "dateType": "individualDates", "individualDates": [*************]}}, "createdAt": *************, "price": 22.25, "quantity": "1", "notes": "9/1/2023", "type": "item", "appliedDiscounts": [{"type": "discount", "amount": 7.75, "source": "militaryDiscount", "originalAllocation": {"type": "militaryDiscount", "description": "Military Discount", "amount": 25, "amountType": "percent", "ledgerAccountName": "4044", "expiresWithGracePeriod": false, "overrideSingleDiscount": false}}]}], "enrolledPunchCards": [{"_id": "7wYG3SRPophqiPmGn", "originalItem": {"_id": "bzDTdwpJyStdmcHbv", "description": "Drop In", "type": "punchcard", "program": "QF32vJw9nMitXRwvq", "numberOfDays": "1", "category": "tuition", "amount": 30, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 30, "quantity": "1", "notes": "9/1 Unregistered Non School Day at Magruder", "type": "item", "datesUsed": [0]}, {"_id": "ZivK9TDQXgwvhMgnE", "originalItem": {"_id": "bzDTdwpJyStdmcHbv", "description": "Drop In", "type": "punchcard", "program": "QF32vJw9nMitXRwvq", "numberOfDays": "1", "category": "tuition", "amount": 30, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 30, "quantity": "1", "notes": "10/2 Unregistered Non School Day at Magruder", "type": "item", "datesUsed": [0]}]}, "deactivatedAt": *************, "deactivationReason": "Unknown", "defaultGroupId": "y2s6de6eCxSvjyW8G", "waitlistAddedDate": null, "checkInGroupId": null, "checkInGroupName": null, "checkedIn": false, "checkedInById": "RG9Mcgx9fdRjqcELP", "checkedInOutTime": *************, "presenceLastGroupId": "y2s6de6eCxSvjyW8G", "previousClassroomGroupId": null, "timestamps": {"moveMomentTimestamp": {"momentId": "YPcjofzCvPf4BJStQ", "timestamp": *************, "groupId": null, "oldGroupId": "y2s6de6eCxSvjyW8G"}}, "invoiceReminders": {"emailsSent": 4, "lastSentTime": *************}}, {"_id": "ZkLXvqLTPN5QsmfZv", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON>", "designations": [], "type": "person", "profileData": {"birthday": *************, "gender": "Female", "studentGrade": "1", "document1": "Yes", "photoAttestation": "Yes", "standardOutlook": {"allergies": "", "specialNeeds": "", "importantNotes": ""}, "agencyIdentifier": "", "cacfpSubsidy": "", "ethnicIdentity": "", "healthInformation": {"primaryDoctor": "", "primaryDoctorPhone": ""}, "homeSchool": "", "mediaRequirements": {"mediaRelease": "", "mediaReviewRequired": "", "noMediaAllowed": ""}, "medicalConditions": "", "nickname": "", "notesPrivate": "", "notesPublic": "", "payerIdentifier": "", "primaryFamily": "", "racialIdentity": "", "residesWith": "", "restrictedPickUp": {"restrictedPickup1": "", "restrictedPickup2": "", "restrictedPickup3": ""}, "schoolPickUp": "", "subsidyCode": "", "subsidyReason": ""}, "createdBy": "SYSTEM", "createdAt": 1686760743941, "orgId": "ExfcnDEu3Td4NZSWP", "inActive": false, "defaultGroupId": "GBXCPsT7fEPHFdf8R", "waitlistAddedDate": null, "documentItems": {"PFxmNxSYfzqTvT3ey": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "W3MXgLsyqbF8vTDnH", "personName": "<PERSON>"}}}, "billing": {"enrolledPlans": [{"_id": "JuQ2N3CjZ4vaHK5Bg", "planDetails": {"_id": "JuQ2N3CjZ4vaHK5Bg", "description": "After School", "type": "plan", "program": "8c8ALB98NFvsHqX8C", "frequency": "scaledMonthly", "category": "tuition", "amount": 220, "scaledAmounts": [59, 112, 159, 193, 220], "ledgerAccountName": "4012", "details": {"startTime": "3:36 pm", "endTime": "6:00 pm", "regStartDate": *************, "grades": ["K", "1", "2", "3", "4", "5"], "scheduleType": "ivEwGqazmcWoWv6oJ", "dateType": "timePeriod", "timePeriod": "WgBPRArg3iHbGs2jE"}}, "enrollmentDate": *************, "allocations": [{"allocationType": "discount", "amount": 57, "amountType": "dollars", "discountType": "bundleDiscount", "allocationDescription": "Discount: Bundle Discount", "id": "AAQ2Q29SpEroFcJRd"}], "createdAt": *************, "reservationId": "Ly3Yf9zCewrQvtkvE", "enrollmentForecastStartDate": *************, "enrollmentForecastEndDate": *************, "expirationDate": *************}, {"_id": "S8Ff5ATxc4jdXbebo", "planDetails": {"_id": "S8Ff5ATxc4jdXbebo", "description": "Before School", "type": "plan", "program": "8c8ALB98NFvsHqX8C", "frequency": "scaledMonthly", "category": "tuition", "amount": 163, "scaledAmounts": [44, 83, 118, 143, 163], "ledgerAccountName": "4013", "details": {"startTime": "6:00 am", "endTime": "9:00 am", "regStartDate": *************, "grades": ["K", "1", "2", "3", "4", "5"], "scheduleType": "Lm2tqkvGeTXaoi7cX", "dateType": "timePeriod", "timePeriod": "WgBPRArg3iHbGs2jE"}}, "enrollmentDate": *************, "allocations": [], "createdAt": *************, "reservationId": "Rc5dcFhFdh5WZchth", "enrollmentForecastStartDate": *************, "enrollmentForecastEndDate": *************, "expirationDate": *************}], "lastInvoiced": *************, "pendingCharges": [], "enrolledPunchCards": [{"_id": "pFL4L9SPEEn4FgJq6", "originalItem": {"_id": "Z6JG2Z2DWRbLCzEcE", "description": "Drop In", "type": "punchcard", "program": "8c8ALB98NFvsHqX8C", "numberOfDays": "1", "category": "tuition", "amount": 30, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 30, "quantity": "1", "notes": "9/1 Unregistered Non School Day at Grafton Bethel", "type": "item", "datesUsed": [0]}]}, "checkInGroupId": null, "checkInGroupName": null, "checkedIn": false, "checkedInById": "NuMeKighmjLsC8fxH", "checkedInOutTime": *************, "presenceLastGroupId": "GBXCPsT7fEPHFdf8R", "previousClassroomGroupId": null, "timestamps": {"moveMomentTimestamp": {"momentId": "ocQEgbChLYRCbNBkW", "timestamp": *************, "groupId": null, "oldGroupId": "GBXCPsT7fEPHFdf8R"}}, "invoiceReminders": {"emailsSent": 8, "lastSentTime": *************}}, {"_id": "eXioqgde3mqvKeTpe", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "designations": [], "defaultGroupId": "ntZyn2XRT8JZHCjtL", "type": "person", "createdBy": "QXQbqFHCj846ePnJq", "createdAt": *************, "orgId": "67BPr5coYDLFvHeHr", "inActive": false, "profileData": {"agencyIdentifier": "", "birthday": *************, "cacfpSubsidy": "", "document1": "yes", "enrollmentDate": *************, "ethnicIdentity": "", "gender": "Male", "healthInformation": {"primaryDoctor": "", "primaryDoctorPhone": ""}, "homeSchool": "", "mediaRequirements": {"mediaRelease": "", "mediaReviewRequired": "", "noMediaAllowed": ""}, "medicalConditions": "", "nickname": "", "notesPrivate": "", "notesPublic": "", "payerIdentifier": "", "photoAttestation": "Yes", "primaryFamily": "", "racialIdentity": "", "residesWith": "", "restrictedPickUp": {"restrictedPickup1": "", "restrictedPickup2": "", "restrictedPickup3": ""}, "schoolPickUp": "", "standardOutlook": {"allergies": "", "importantNotes": "", "specialNeeds": ""}, "studentGrade": "K", "subsidyCode": "", "subsidyReason": "", "withdrawDate": *************}, "billing": {"enrolledPlans": [{"_id": "XoAqnz6RB98w5P2jC", "planDetails": {"_id": "XoAqnz6RB98w5P2jC", "description": "After School", "type": "plan", "program": "WDZcNsjLKyJ47dKoD", "frequency": "scaledMonthly", "category": "tuition", "amount": 295, "scaledAmounts": [94, 174, 235, 275, 295], "ledgerAccountName": "4012", "details": {"startTime": "3:40 pm", "endTime": "6:00 pm", "regStartDate": *************, "regEndDate": *************, "grades": ["K", "1", "2", "3", "4", "5", "6", "7", "8"], "scheduleType": "ivEwGqazmcWoWv6oJ", "dateType": "timePeriod", "timePeriod": "vaMDvSLiBc48CC2ze"}}, "enrollmentDate": *************, "allocations": [], "createdAt": *************, "expirationDate": *************, "reservationId": "ha4FRonvZg79rFCEz", "enrollmentForecastStartDate": *************}], "lastInvoiced": *************, "pendingCharges": [], "enrolledPunchCards": [{"_id": "5KroMTRdwazE8ESGq", "originalItem": {"_id": "PEbJhPZBPoDP3qqqy", "description": "Before/After Care 10-Day Punch Card", "type": "punchcard", "program": "WDZcNsjLKyJ47dKoD", "numberOfDays": "10", "amount": 360, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 360, "quantity": "1", "notes": "mom emailed to add one to her account", "type": "item", "datesUsed": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}], "billingNotes": "10/13/23 mom emailed to order punch card for before care needed.  EKP"}, "documentItems": {"kQTiz4ePQj7CHeQgq": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "cCeSa4FogGcpPKDg3", "personName": "Autumn  Mathis "}}, "q72p22YNRH9CD6SNc": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "cCeSa4FogGcpPKDg3", "personName": "Autumn  Mathis "}}}, "checkInGroupId": null, "checkInGroupName": null, "checkedIn": false, "checkedInById": "cKbjtnL9DTzcvjRvQ", "checkedInOutTime": *************, "presenceLastGroupId": "ntZyn2XRT8JZHCjtL", "previousClassroomGroupId": null, "timestamps": {"moveMomentTimestamp": {"momentId": "mPEn9ZkaxWupHo3gh", "timestamp": *************, "groupId": null, "oldGroupId": "ntZyn2XRT8JZHCjtL"}}, "lastInteractionDate": *************, "lastMoment": {"attributionPersonId": null, "momentType": "checkin", "comment": "Checked into Right At School", "time": "7:17 am", "date": "10/20/2023", "sortStamp": *************, "attributionName": "Right at School  Maple Grove Primary ", "modifiedByName": "Right at School  Maple Grove Primary ", "modifiedBy": "Zhy8BSzRqBvzbdK3C", "taggedPeople": ["eXioqgde3mqvKeTpe"], "_id": "DNawg3DwjzLyPabw6"}, "lastMomentByType": {"checkout": {"attributionPersonId": null, "momentType": "checkout", "comment": "Checked out of Right At School", "time": "9:10 am", "date": "09/08/2023", "sortStamp": 1694189400000, "attributionName": "Right at School  Maple Grove Primary ", "modifiedByName": "Right at School  Maple Grove Primary ", "modifiedBy": "Zhy8BSzRqBvzbdK3C", "taggedPeople": ["eXioqgde3mqvKeTpe"], "_id": "bDNtNhRNPBPEHE22D"}, "checkin": {"attributionPersonId": null, "momentType": "checkin", "comment": "Checked into Right At School", "time": "7:17 am", "date": "10/20/2023", "sortStamp": *************, "attributionName": "Right at School  Maple Grove Primary ", "modifiedByName": "Right at School  Maple Grove Primary ", "modifiedBy": "Zhy8BSzRqBvzbdK3C", "taggedPeople": ["eXioqgde3mqvKeTpe"], "_id": "DNawg3DwjzLyPabw6"}}, "engagements": [{"orgId": "67BPr5coYDLFvHeHr", "type": "app", "subType": "created_moment", "detail": "checkin", "momentId": "DNawg3DwjzLyPabw6", "createdBy": "Zhy8BSzRqBvzbdK3C", "createdAt": 1697816871699, "targetPersonId": "eXioqgde3mqvKeTpe", "sourcePersonId": "Zhy8BSzRqBvzbdK3C", "_id": "9W9RKewSsHbbwfGas"}], "invoiceReminders": {"emailsSent": 2, "lastSentTime": 1698912676820}}, {"_id": "N7HuaxRwc6LtwGaKz", "firstName": "<PERSON>", "lastName": "Presgraves", "designations": [], "type": "person", "profileData": {"birthday": 1532664000000, "gender": "Male", "studentGrade": "K", "document1": "Yes", "photoAttestation": "Yes", "standardOutlook": {"allergies": "Amoxocillin", "specialNeeds": ""}, "enrollmentDate": *************}, "createdBy": "SYSTEM", "createdAt": *************, "orgId": "eXrCBS5roD8bh6z6B", "inActive": false, "billing": {"enrolledPlans": [{"_id": "S8Ff5ATxc4jdXbebo", "planDetails": {"_id": "S8Ff5ATxc4jdXbebo", "description": "Before School", "type": "plan", "program": "tvRH7NC83zZHm3Ys7", "frequency": "scaledMonthly", "category": "tuition", "amount": 163, "scaledAmounts": [44, 83, 118, 143, 163], "ledgerAccountName": "4013", "details": {"startTime": "6:00 am", "endTime": "9:00 am", "regStartDate": *************, "grades": ["K", "1", "2", "3", "4", "5"], "scheduleType": "Lm2tqkvGeTXaoi7cX", "dateType": "timePeriod", "timePeriod": "RbdBieKohNsHiae4p"}}, "enrollmentDate": *************, "allocations": [], "createdAt": *************, "expirationDate": *************, "reservationId": "9wD3yhczcu7MqBtz6", "enrollmentForecastStartDate": *************, "enrollmentForecastEndDate": *************}], "pendingCharges": [{"_id": "WPq6Wj2tkRZouhCkT", "originalItem": {"_id": "PEbJhPZBPoDP3qqqy", "description": "Before/After Care 10-Day Punch Card", "type": "punchcard", "program": "tvRH7NC83zZHm3Ys7", "numberOfDays": "10", "amount": 270, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 270, "quantity": "1", "notes": "", "type": "item", "datesUsed": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}], "lastInvoiced": *************, "enrolledPunchCards": [{"_id": "jpWK543HhQJfqNAAB", "originalItem": {"_id": "PEbJhPZBPoDP3qqqy", "description": "Before/After Care 10-Day Punch Card", "type": "punchcard", "program": "tvRH7NC83zZHm3Ys7", "numberOfDays": "10", "amount": 270, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 270, "quantity": "1", "notes": "", "type": "item", "datesUsed": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, {"_id": "WPq6Wj2tkRZouhCkT", "originalItem": {"_id": "PEbJhPZBPoDP3qqqy", "description": "Before/After Care 10-Day Punch Card", "type": "punchcard", "program": "tvRH7NC83zZHm3Ys7", "numberOfDays": "10", "amount": 270, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 270, "quantity": "1", "notes": "", "type": "item", "datesUsed": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}]}, "defaultGroupId": "trT9a5ZEpyALKSM2v", "waitlistAddedDate": null, "enrollmentDate": *************, "invoiceReminders": {"emailsSent": 10, "lastSentTime": *************}}, {"_id": "aZzS2z24L7ddYufS5", "firstName": "<PERSON>", "lastName": "Pocket", "designations": [], "type": "person", "profileData": {"birthday": *************, "gender": "Female"}, "createdBy": "SYSTEM", "createdAt": *************, "orgId": "cbhqcTxhdge5QYtjx", "inActive": false, "billing": {"enrolledPunchCards": [{"_id": "s95NSCN43me34xRNy", "originalItem": {"_id": "auBN6ncCwBqskywEh", "description": "10 Day Punch Card", "type": "punchcard", "numberOfDays": "10", "amount": 100, "scaledAmounts": [], "program": "8hDDGMaH2qu8JbR5P"}, "createdAt": *************, "price": 100, "quantity": "1", "notes": "", "type": "item", "appliedDiscounts": [{"type": "discount", "amount": 100, "source": "coupon", "originalAllocation": {"allocationType": "discount", "amount": 100, "amountType": "percent", "discountType": "coupon", "code": "100OFF", "allocationDescription": "100 % off", "id": "nFJp2vM2bZKxpzJvG", "discountAmount": 100}}]}], "pendingCharges": [], "lastInvoiced": 1687534931689}}, {"_id": "cgWMqpiCPh2YSjccC", "firstName": "<PERSON>", "lastName": "Pocket", "designations": [], "type": "person", "profileData": {"birthday": 1370145600000, "gender": "Male"}, "createdBy": "SYSTEM", "createdAt": 1687534930388, "orgId": "cbhqcTxhdge5QYtjx", "inActive": false, "billing": {"enrolledPunchCards": [{"_id": "5zCSy7J7iXzmcM39W", "originalItem": {"_id": "auBN6ncCwBqskywEh", "description": "10 Day Punch Card", "type": "punchcard", "numberOfDays": "10", "amount": 100, "scaledAmounts": [], "program": "8hDDGMaH2qu8JbR5P"}, "createdAt": 1687534932141, "price": 100, "quantity": "1", "notes": "", "type": "item", "appliedDiscounts": [{"type": "discount", "amount": 10, "source": "coupon", "originalAllocation": {"id": "hMSPE3ePxXqFNZ74S", "allocationType": "discount", "amount": 10, "amountType": "dollars", "discountType": "siblingDiscount", "allocationDescription": "Sibling Discount", "discountAmount": 10}}, {"type": "discount", "amount": 100, "source": "coupon", "originalAllocation": {"allocationType": "discount", "amount": 100, "amountType": "percent", "discountType": "coupon", "code": "100OFF", "allocationDescription": "100 % off", "id": "BwDLwj4fFbCrvJsJb", "discountAmount": 100}}]}], "pendingCharges": [], "lastInvoiced": 1687534932153}}, {"_id": "Zzs6ndhvF97JqAY2o", "firstName": "<PERSON>", "lastName": "<PERSON>", "designations": [], "type": "person", "profileData": {"birthday": 1305259200000, "gender": "Male"}, "createdBy": "SYSTEM", "createdAt": 1687537493204, "orgId": "cbhqcTxhdge5QYtjx", "inActive": false, "billing": {"enrolledPunchCards": [{"_id": "oJMxdsX6RySaYSp4A", "originalItem": {"_id": "auBN6ncCwBqskywEh", "description": "10 Day Punch Card", "type": "punchcard", "numberOfDays": "10", "amount": 100, "scaledAmounts": [], "program": "8hDDGMaH2qu8JbR5P"}, "createdAt": 1687537494454, "price": 100, "quantity": "1", "notes": "", "type": "item", "appliedDiscounts": [{"type": "discount", "amount": 100, "source": "coupon", "originalAllocation": {"allocationType": "discount", "amount": 100, "amountType": "percent", "discountType": "coupon", "code": "100OFF", "allocationDescription": "100 % off", "id": "RNKv6ExvcGCYtrdMM", "discountAmount": 100}}]}], "pendingCharges": [], "lastInvoiced": 1687537494475}}, {"_id": "eCvtmzaWTg6SLk2xu", "firstName": "<PERSON>", "lastName": "<PERSON>", "designations": [], "type": "person", "profileData": {"birthday": 1573448400000, "gender": "Male", "studentGrade": "K", "document1": "Yes", "photoAttestation": "Yes", "standardOutlook": {"allergies": "", "specialNeeds": ""}}, "createdBy": "SYSTEM", "createdAt": *************, "orgId": "7y5bsDP3Pnr4LvEju", "inActive": false, "billing": {"enrolledPlans": [{"_id": "9m88u4je4P4rQjAqL", "planDetails": {"_id": "9m88u4je4P4rQjAqL", "description": "After School", "type": "plan", "program": "cuPS2a63AEXWr8Rzs", "frequency": "scaledMonthly", "category": "tuition", "amount": 288, "scaledAmounts": [102, 164, 221, 266, 288], "ledgerAccountName": "4012", "details": {"startTime": "2:40 pm", "endTime": "6:00 pm", "regStartDate": *************, "grades": ["K", "1", "2", "3", "4", "5", "6", "7", "8"], "scheduleType": "ivEwGqazmcWoWv6oJ", "dateType": "timePeriod", "timePeriod": "cwfxok5bNERCh84TR"}}, "enrollmentDate": *************, "allocations": [{"allocationType": "discount", "amount": 100, "amountType": "percent", "discountType": "coupon", "code": "100OFF", "allocationDescription": "100% off - Testing Purposes", "id": "gKWaBwwkQg6M8KKmg", "discountAmount": 288}], "createdAt": *************, "reservationId": "7WtWP9nGLPBSw9hHQ", "enrollmentForecastStartDate": *************, "enrollmentForecastEndDate": *************, "expirationDate": *************, "overrideRate": null, "updatedAt": *************, "updatedBy": "TZnj9F36brucdDCsR"}], "lastInvoiced": *************, "pendingCharges": [], "enrolledPunchCards": [{"_id": "srAvmXP5L237ahZLq", "originalItem": {"_id": "PEbJhPZBPoDP3qqqy", "description": "Before/After Care 10-Day Punch Card", "type": "punchcard", "program": "cuPS2a63AEXWr8Rzs", "numberOfDays": "10", "amount": 315, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": 169**********, "price": 315, "quantity": "1", "notes": "", "type": "item", "datesUsed": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}]}}, {"_id": "EsYg6StQjPTr2ZDLc", "firstName": "<PERSON>", "lastName": "Running", "designations": [], "type": "person", "profileData": {"birthday": *************, "gender": "Male", "studentGrade": "K", "document1": "Yes", "photoAttestation": "Yes", "standardOutlook": {"allergies": "", "specialNeeds": "", "importantNotes": ""}, "agencyIdentifier": "", "cacfpSubsidy": "", "ethnicIdentity": "", "healthInformation": {"primaryDoctor": "", "primaryDoctorPhone": ""}, "homeSchool": "", "mediaRequirements": {"mediaRelease": "", "mediaReviewRequired": "", "noMediaAllowed": ""}, "medicalConditions": "", "nickname": "", "notesPrivate": "", "notesPublic": "", "payerIdentifier": "", "primaryFamily": "", "racialIdentity": "", "residesWith": "", "restrictedPickUp": {"restrictedPickup1": "", "restrictedPickup2": "", "restrictedPickup3": ""}, "schoolPickUp": "", "subsidyCode": "", "subsidyReason": ""}, "createdBy": "SYSTEM", "createdAt": *************, "orgId": "iiadQjLkj2XAwEfPc", "inActive": false, "billing": {"enrolledPlans": [{"_id": "JuQ2N3CjZ4vaHK5Bg", "planDetails": {"_id": "JuQ2N3CjZ4vaHK5Bg", "description": "After School", "type": "plan", "program": "QF32vJw9nMitXRwvq", "frequency": "scaledMonthly", "category": "tuition", "amount": 220, "scaledAmounts": [59, 112, 159, 193, 220], "ledgerAccountName": "4012", "details": {"startTime": "3:36 pm", "endTime": "6:00 pm", "regStartDate": *************, "grades": ["K", "1", "2", "3", "4", "5"], "scheduleType": "ivEwGqazmcWoWv6oJ", "dateType": "timePeriod", "timePeriod": "2pTsmADZMQBjXugS8"}}, "enrollmentDate": *************, "allocations": [], "createdAt": *************, "reservationId": "mw2qCEMzSYYfkPY4q", "enrollmentForecastStartDate": *************}], "pendingCharges": [], "lastInvoiced": *************, "enrolledPunchCards": [{"_id": "LCRH5uvpxG59v2kkA", "originalItem": {"_id": "bzDTdwpJyStdmcHbv", "description": "Drop In", "type": "punchcard", "program": "QF32vJw9nMitXRwvq", "numberOfDays": "1", "category": "tuition", "amount": 30, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 30, "quantity": "1", "notes": "10/2 Unregistered Non School Day at Magruder", "type": "item", "datesUsed": [0]}]}, "defaultGroupId": "y2s6de6eCxSvjyW8G", "waitlistAddedDate": null, "documentItems": {"LdzE6NrseiH2REHjq": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "g63a59Xfij6QmsjRq", "personName": "<PERSON>"}}, "Po3nvq8z6fiHCChgu": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "g63a59Xfij6QmsjRq", "personName": "<PERSON>"}}, "YjX4XZvt7QcbkFTLT": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "g63a59Xfij6QmsjRq", "personName": "<PERSON>"}}}, "invoiceReminders": {"emailsSent": 10, "lastSentTime": *************}, "checkInGroupId": null, "checkInGroupName": null, "checkedIn": false, "checkedInById": "yevKp8Zi2qSp7SKYW", "checkedInOutTime": *************, "presenceLastGroupId": "y2s6de6eCxSvjyW8G", "previousClassroomGroupId": null, "timestamps": {"moveMomentTimestamp": {"momentId": "XMaHnSRdwiR83gfXw", "timestamp": 1698872279597, "groupId": null, "oldGroupId": "y2s6de6eCxSvjyW8G"}}}, {"_id": "qfqC48KRiQCsxDEnP", "firstName": "<PERSON>", "lastName": "Cap<PERSON><PERSON>", "designations": [], "type": "person", "profileData": {"birthday": *************, "gender": "Male", "studentGrade": "4", "document1": "Yes", "photoAttestation": "No"}, "createdBy": "SYSTEM", "createdAt": *************, "orgId": "GcW3q95KzeCxoajHy", "inActive": false, "billing": {"enrolledPlans": [{"_id": "5MB2RbqcApyRRHyuJ", "planDetails": {"_id": "5MB2RbqcApyRRHyuJ", "description": "Before School", "type": "plan", "program": "jntKaXtaJwAAGTyJ9", "frequency": "scaledMonthly", "category": "tuition", "amount": 279, "scaledAmounts": [114, 179, 235, 261, 279], "ledgerAccountName": "4013", "details": {"startTime": "7:00 am", "endTime": "8:50 am", "regStartDate": *************, "regEndDate": *************, "grades": ["K", "1", "2", "3", "4", "5", "6"], "scheduleType": "Lm2tqkvGeTXaoi7cX", "dateType": "timePeriod", "timePeriod": "XL5CJ5jafFCDQuQ7H"}}, "enrollmentDate": *************, "allocations": [{"allocationType": "discount", "amount": 50, "amountType": "percent", "discountType": "districtDiscount", "allocationDescription": "District Discount", "id": "38r9tbrJwhhmmMFuA"}], "createdAt": *************, "reservationId": "M8e8rChXr482MStTZ", "enrollmentForecastStartDate": *************, "enrollmentForecastEndDate": null, "overrideRate": null, "updatedAt": *************, "updatedBy": "TZnj9F36brucdDCsR"}], "pendingCharges": [], "lastInvoiced": *************, "enrolledPunchCards": [{"_id": "nKTNMmDrJ9KpBiyFo", "originalItem": {"_id": "PEbJhPZBPoDP3qqqy", "description": "Before/After Care 10-Day Punch Card", "type": "punchcard", "program": "jntKaXtaJwAAGTyJ9", "numberOfDays": "10", "amount": 315, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 315, "quantity": "1", "notes": "", "type": "item", "appliedDiscounts": [{"type": "discount", "amount": null, "source": "coupon", "originalAllocation": {"allocationType": "discount", "amount": 50, "amountType": "percent", "discountType": "districtDiscount", "allocationDescription": "District Discount", "id": "qJqWXifrJS5TP5c2P"}}], "datesUsed": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, {"_id": "a7WZ9ETRvYz77EwWA", "originalItem": {"_id": "PEbJhPZBPoDP3qqqy", "description": "Before/After Care 10-Day Punch Card", "type": "punchcard", "program": "jntKaXtaJwAAGTyJ9", "numberOfDays": "10", "amount": 315, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 315, "quantity": "1", "notes": "", "type": "item", "appliedDiscounts": [{"type": "discount", "amount": 0, "source": "districtDiscount", "originalAllocation": {"type": "districtDiscount", "description": "District Discount", "amount": 50, "amountType": "percent", "ledgerAccountName": "4042", "expiresWithGracePeriod": false, "overrideSingleDiscount": false}}], "datesUsed": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, {"_id": "TnhzicYuXGADePfCA", "originalItem": {"_id": "PEbJhPZBPoDP3qqqy", "description": "Before/After Care 10-Day Punch Card", "type": "punchcard", "program": "jntKaXtaJwAAGTyJ9", "numberOfDays": "10", "amount": 315, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 157.5, "quantity": "1", "notes": "", "type": "item", "appliedDiscounts": [{"type": "discount", "amount": 157.5, "source": "districtDiscount", "originalAllocation": {"type": "districtDiscount", "description": "District Discount", "amount": 50, "amountType": "percent", "ledgerAccountName": "4042", "expiresWithGracePeriod": false, "overrideSingleDiscount": false}}], "datesUsed": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}]}, "documentAssignments": ["yzusTDFpotMG2Fvy3", "jhhCeRbiWQZaKKjLv"], "documentItems": {"WkNAQEeb6dJiqodnm": {"createdAt": *************, "createdByPersonId": "mN6c2eQZgMpbdWtoC", "createdByUserId": "CWDFwd9J56786viB8", "repositoryKey": "GcW3q95KzeCxoajHy/CWDFwd9J56786viB8/OhTh91UfM47sPzGSoGJ2", "token": "OhTh91UfM47sPzGSoGJ2"}, "zhu5fcgM6AgTEHLYQ": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "mN6c2eQZgMpbdWtoC", "personName": "<PERSON><PERSON>"}}, "ZjkZjrvf8GXSQ7oyA": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "mN6c2eQZgMpbdWtoC", "personName": "<PERSON><PERSON>"}}}, "defaultGroupId": "YPTxXkRq5JpxjXMXY", "waitlistAddedDate": null, "checkInGroupId": null, "checkInGroupName": null, "checkedIn": false, "checkedInById": "mZ9x3karSpTj4BCAj", "checkedInOutTime": *************, "presenceLastGroupId": "YPTxXkRq5JpxjXMXY", "previousClassroomGroupId": null, "timestamps": {"moveMomentTimestamp": {"momentId": "P82MmYXdY7s3axpPG", "timestamp": 1698930118261, "groupId": null, "oldGroupId": "YPTxXkRq5JpxjXMXY"}}, "invoiceReminders": {"emailsSent": 7, "lastSentTime": 1695024361484}}, {"_id": "hC7Wmro9u7RdAq3mF", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "designations": [], "type": "person", "profileData": {"birthday": 1687752000000, "gender": "Male", "studentGrade": "5", "document1": "Yes", "photoAttestation": "Yes", "standardOutlook": {"allergies": "", "specialNeeds": "", "importantNotes": ""}, "agencyIdentifier": "", "cacfpSubsidy": "", "ethnicIdentity": "", "healthInformation": {"primaryDoctor": "", "primaryDoctorPhone": ""}, "homeSchool": "", "mediaRequirements": {"mediaRelease": "", "mediaReviewRequired": "", "noMediaAllowed": ""}, "medicalConditions": "", "nickname": "", "notesPrivate": "", "notesPublic": "", "payerIdentifier": "", "primaryFamily": "", "racialIdentity": "", "residesWith": "", "restrictedPickUp": {"restrictedPickup1": "", "restrictedPickup2": "", "restrictedPickup3": ""}, "schoolPickUp": "", "subsidyCode": "", "subsidyReason": ""}, "createdBy": "hnEFX5czZQnoPi38M", "createdAt": *************, "orgId": "ogGcnQrn5jpkp8qZD", "inActive": false, "billing": {"enrolledPlans": [{"_id": "JuQ2N3CjZ4vaHK5Bg", "planDetails": {"_id": "JuQ2N3CjZ4vaHK5Bg", "description": "After School", "type": "plan", "program": "FoNhQk8yjCTRTbGzA", "frequency": "scaledMonthly", "category": "tuition", "amount": 220, "scaledAmounts": [59, 112, 159, 193, 220], "ledgerAccountName": "4012", "details": {"startTime": "3:21 pm", "endTime": "6:00 pm", "regStartDate": *************, "grades": ["K", "1", "2", "3", "4", "5"], "scheduleType": "ivEwGqazmcWoWv6oJ", "dateType": "timePeriod", "timePeriod": "nso3JAkmsXDqxuhiq"}}, "enrollmentDate": *************, "allocations": [{"allocationType": "discount", "amount": 50, "amountType": "percent", "discountType": "districtDiscount", "allocationDescription": "District Discount", "id": "pCmS7hn6uwKa4HPz3"}], "createdAt": *************, "reservationId": "DgEqpiW2FwpAx3fMs", "enrollmentForecastStartDate": *************}, {"_id": "S8Ff5ATxc4jdXbebo", "planDetails": {"_id": "S8Ff5ATxc4jdXbebo", "description": "Before School", "type": "plan", "program": "FoNhQk8yjCTRTbGzA", "frequency": "scaledMonthly", "category": "tuition", "amount": 163, "scaledAmounts": [44, 83, 118, 143, 163], "ledgerAccountName": "4013", "details": {"startTime": "6:00 am", "endTime": "8:45 am", "regStartDate": *************, "grades": ["K", "1", "2", "3", "4", "5"], "scheduleType": "Lm2tqkvGeTXaoi7cX", "dateType": "timePeriod", "timePeriod": "nso3JAkmsXDqxuhiq"}}, "enrollmentDate": *************, "allocations": [{"allocationType": "discount", "amount": 50, "amountType": "percent", "discountType": "districtDiscount", "allocationDescription": "Discount: District Discount", "id": "oL8WGvYe3J5znffNf"}], "createdAt": *************, "expirationDate": *************, "reservationId": "YCp3XeMBQJXNP9sHr", "enrollmentForecastStartDate": *************}], "enrolledPunchCards": [{"_id": "uTszwnvFXRCpY6m8x", "originalItem": {"_id": "PEbJhPZBPoDP3qqqy", "description": "Before/After Care 10-Day Punch Card", "type": "punchcard", "program": "FoNhQk8yjCTRTbGzA", "numberOfDays": "10", "amount": 270, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 270, "quantity": "1", "notes": "", "type": "item", "appliedDiscounts": [{"type": "discount", "amount": null, "source": "coupon", "originalAllocation": {"allocationType": "discount", "amount": 50, "amountType": "percent", "discountType": "districtDiscount", "allocationDescription": "District Discount", "id": "gJJEi6ntfZz29zcfc"}}], "datesUsed": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}], "pendingCharges": [], "lastInvoiced": *************}, "defaultGroupId": "wSMvyQR53mBkvp8EF", "waitlistAddedDate": null, "invoiceReminders": {"emailsSent": 10, "lastSentTime": *************}, "documentItems": {"QZb32qoQw45nf2PGe": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "pkLiaNqjxY97MF425", "personName": "<PERSON>"}}, "dcuuAvjv9NWkeCLfZ": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "pkLiaNqjxY97MF425", "personName": "<PERSON>"}}, "xDsj69L4s4Z3AaKPe": {"createdAt": *************, "createdByPersonId": "pkLiaNqjxY97MF425", "createdByUserId": "3btBhkYiJWCkddDtg", "repositoryKey": "ogGcnQrn5jpkp8qZD/3btBhkYiJWCkddDtg/TmF5tkypx7wJ9MWE9Znr", "token": "TmF5tkypx7wJ9MWE9Znr"}, "KuJHtuEjuCM56Go7h": {"createdAt": 1693248102663, "createdByPersonId": "pkLiaNqjxY97MF425", "createdByUserId": "3btBhkYiJWCkddDtg", "repositoryKey": "ogGcnQrn5jpkp8qZD/3btBhkYiJWCkddDtg/rxW58A3S1ALL2e8Nof3c", "token": "rxW58A3S1ALL2e8Nof3c"}, "JJtQ3h6n2wc8A8HC2": {"createdAt": 1693248728256, "createdByPersonId": "pkLiaNqjxY97MF425", "createdByUserId": "3btBhkYiJWCkddDtg", "repositoryKey": "ogGcnQrn5jpkp8qZD/3btBhkYiJWCkddDtg/W7Fp8p2KB7Xrvwz16pnE", "token": "W7Fp8p2KB7Xrvwz16pnE"}}, "checkInGroupId": null, "checkInGroupName": null, "checkedIn": false, "checkedInById": "BFbnuYprFXrmMHBLq", "checkedInOutTime": 1698928599305, "presenceLastGroupId": "wSMvyQR53mBkvp8EF", "previousClassroomGroupId": null, "timestamps": {"moveMomentTimestamp": {"momentId": "H483tuMRd6KKmNqu2", "timestamp": 1698928599502, "groupId": null, "oldGroupId": "wSMvyQR53mBkvp8EF"}}}, {"_id": "cZ7SSsKMfpTpKFJSr", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "designations": [], "type": "person", "profileData": {"birthday": 1383451200000, "gender": "Female", "studentGrade": "4", "document1": "Yes", "photoAttestation": "No", "standardOutlook": {"allergies": "", "specialNeeds": ""}}, "createdBy": "SYSTEM", "createdAt": *************, "orgId": "eXrCBS5roD8bh6z6B", "inActive": false, "billing": {"enrolledPlans": [{"_id": "S8Ff5ATxc4jdXbebo", "planDetails": {"_id": "S8Ff5ATxc4jdXbebo", "description": "Before School", "type": "plan", "program": "tvRH7NC83zZHm3Ys7", "frequency": "scaledMonthly", "category": "tuition", "amount": 163, "scaledAmounts": [44, 83, 118, 143, 163], "ledgerAccountName": "4013", "details": {"startTime": "6:00 am", "endTime": "9:00 am", "regStartDate": *************, "grades": ["K", "1", "2", "3", "4", "5"], "scheduleType": "Lm2tqkvGeTXaoi7cX", "dateType": "timePeriod", "timePeriod": "RbdBieKohNsHiae4p"}}, "enrollmentDate": *************, "allocations": [{"allocationType": "discount", "amount": 25, "amountType": "percent", "discountType": "militaryDiscount", "allocationDescription": "Discount: Military Discount", "id": "PpdPy5JLurjYg8zDA"}], "createdAt": *************, "reservationId": "TmD83EFmjiW6RKhX3", "enrollmentForecastStartDate": *************, "enrollmentForecastEndDate": *************, "overrideRate": null, "updatedAt": *************, "updatedBy": "bjnnqNFRK8sHWgSHK", "expirationDate": *************}], "pendingCharges": [], "lastInvoiced": *************, "billingNotes": "Oct 24, 2023 - After Care schedule had been ended but the billing plan had been unlinked and was still active. They were charged for After care in October. Credited the amount back to the invoice. - bry", "enrolledPunchCards": [{"_id": "4bzqev3kKC7qR3pf5", "originalItem": {"_id": "pT6t5C8N9sAa6oFD2", "description": "Drop In", "type": "punchcard", "program": "tvRH7NC83zZHm3Ys7", "numberOfDays": "1", "category": "tuition", "amount": 30, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 30, "quantity": "3", "notes": "", "type": "item", "datesUsed": [0]}]}, "defaultGroupId": "trT9a5ZEpyALKSM2v", "waitlistAddedDate": null, "documentItems": {"3rQgPRpgC9povJSv3": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "NwnbvQkj9suq8SXTz", "personName": "<PERSON>"}}, "BjBybtBpqPwz3MuAQ": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "NwnbvQkj9suq8SXTz", "personName": "<PERSON>"}}}, "checkInGroupId": null, "checkInGroupName": null, "checkedIn": false, "checkedInById": "NwnbvQkj9suq8SXTz", "checkedInOutTime": *************, "presenceLastGroupId": "trT9a5ZEpyALKSM2v", "previousClassroomGroupId": null, "timestamps": {"moveMomentTimestamp": {"momentId": "7SPiSJwtkWhTEnt9Q", "timestamp": *************, "groupId": null, "oldGroupId": "trT9a5ZEpyALKSM2v"}}, "invoiceReminders": {"emailsSent": 10, "lastSentTime": *************}}, {"_id": "w22Ftf9xPz4MayJzm", "firstName": "<PERSON>", "lastName": "Cline", "designations": [], "type": "person", "profileData": {"enrollmentDate": *************, "birthday": 1444892400000, "gender": "Male", "studentGrade": "2", "document1": "Yes", "homeSchool": "Maple Grove Primary", "photoAttestation": "Yes", "standardOutlook": {"allergies": "", "specialNeeds": "", "importantNotes": ""}, "agencyIdentifier": "", "cacfpSubsidy": "", "ethnicIdentity": "", "healthInformation": {"primaryDoctor": "", "primaryDoctorPhone": ""}, "mediaRequirements": {"mediaRelease": "", "mediaReviewRequired": "", "noMediaAllowed": ""}, "medicalConditions": "", "nickname": "", "notesPrivate": "", "notesPublic": "", "payerIdentifier": "", "primaryFamily": "", "racialIdentity": "", "residesWith": "", "restrictedPickUp": {"restrictedPickup1": "", "restrictedPickup2": "", "restrictedPickup3": ""}, "schoolPickUp": "", "subsidyCode": "", "subsidyReason": ""}, "createdBy": "SYSTEM", "createdAt": *************, "orgId": "67BPr5coYDLFvHeHr", "inActive": false, "billing": {"enrolledPlans": [{"_id": "iWbNeboHZwiqy5Wjt", "planDetails": {"_id": "iWbNeboHZwiqy5Wjt", "description": "Before School", "type": "plan", "program": "WDZcNsjLKyJ47dKoD", "frequency": "scaledMonthly", "category": "tuition", "amount": 295, "scaledAmounts": [94, 174, 235, 275, 295], "ledgerAccountName": "4013", "details": {"startTime": "6:30 am", "endTime": "9:10 am", "regStartDate": *************, "regEndDate": *************, "grades": ["K", "1", "2", "3", "4", "5", "6"], "scheduleType": "Lm2tqkvGeTXaoi7cX", "dateType": "timePeriod", "timePeriod": "vaMDvSLiBc48CC2ze"}}, "enrollmentDate": *************, "allocations": [], "createdAt": *************, "reservationId": "eeeaEJc9mv42FduuP", "enrollmentForecastStartDate": *************, "enrollmentForecastEndDate": *************, "expirationDate": *************}, {"_id": "iWbNeboHZwiqy5Wjt", "planDetails": {"_id": "iWbNeboHZwiqy5Wjt", "description": "Before School", "type": "plan", "program": "WDZcNsjLKyJ47dKoD", "frequency": "scaledMonthly", "category": "tuition", "amount": 295, "scaledAmounts": [94, 174, 235, 275, 295], "ledgerAccountName": "4013", "details": {"startTime": "6:30 am", "endTime": "9:10 am", "regStartDate": *************, "regEndDate": *************, "grades": ["K", "1", "2", "3", "4", "5", "6"], "scheduleType": "Lm2tqkvGeTXaoi7cX", "dateType": "timePeriod", "timePeriod": "vaMDvSLiBc48CC2ze"}}, "enrollmentDate": *************, "allocations": [], "createdAt": *************, "reservationId": "SSMLz9afNiTXCD4t8", "enrollmentForecastStartDate": *************, "enrollmentForecastEndDate": *************, "expirationDate": *************}], "pendingCharges": [], "lastInvoiced": *************, "billingNotes": "<PERSON><PERSON> started attending 2 days a week effective 09/05. Already paid for 1 day a week schedule on 09/01. Crediting the $94.00 towards new charge.", "enrolledPunchCards": [{"_id": "4b27TuedmB4WCtBPj", "originalItem": {"_id": "Ftk85wKozcMdPhLDF", "description": "Drop In", "type": "punchcard", "program": "WDZcNsjLKyJ47dKoD", "numberOfDays": "1", "category": "tuition", "amount": 40, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 40, "quantity": "1", "notes": "10/9", "type": "item", "appliedDiscounts": [{"type": "discount", "amount": 0, "source": "siblingDiscount", "originalAllocation": {"type": "siblingDiscount", "description": "Sibling Discount", "amount": 10, "amountType": "percent", "ledgerAccountName": "4045", "expiresWithGracePeriod": false, "overrideSingleDiscount": false}}], "datesUsed": [0]}]}, "defaultGroupId": "ntZyn2XRT8JZHCjtL", "waitlistAddedDate": null, "documentItems": {"kQTiz4ePQj7CHeQgq": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "eDDbwkKMe9p2xuGPA", "personName": "<PERSON>"}}, "q72p22YNRH9CD6SNc": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "eDDbwkKMe9p2xuGPA", "personName": "<PERSON>"}}}, "checkInGroupId": null, "checkInGroupName": null, "checkedIn": false, "checkedInById": "qogpRiLLXfgePicn7", "checkedInOutTime": *************, "presenceLastGroupId": "ntZyn2XRT8JZHCjtL", "previousClassroomGroupId": null, "timestamps": {"moveMomentTimestamp": {"momentId": "bojYrRwkLepvTgt6X", "timestamp": *************, "groupId": null, "oldGroupId": "ntZyn2XRT8JZHCjtL"}}, "invoiceReminders": {"emailsSent": 8, "lastSentTime": *************}}, {"_id": "2Mq2naFhJZAno7nPY", "firstName": "<PERSON>", "lastName": "Cline", "designations": [], "type": "person", "profileData": {"birthday": 1495350000000, "gender": "Male", "studentGrade": "1", "document1": "Yes", "homeSchool": "Maple Grove Primary", "photoAttestation": "Yes", "standardOutlook": {"allergies": "", "specialNeeds": "", "importantNotes": ""}, "agencyIdentifier": "", "cacfpSubsidy": "", "ethnicIdentity": "", "healthInformation": {"primaryDoctor": "", "primaryDoctorPhone": ""}, "mediaRequirements": {"mediaRelease": "", "mediaReviewRequired": "", "noMediaAllowed": ""}, "medicalConditions": "", "nickname": "", "notesPrivate": "", "notesPublic": "", "payerIdentifier": "", "primaryFamily": "", "racialIdentity": "", "residesWith": "", "restrictedPickUp": {"restrictedPickup1": "", "restrictedPickup2": "", "restrictedPickup3": ""}, "schoolPickUp": "", "subsidyCode": "", "subsidyReason": ""}, "createdBy": "SYSTEM", "createdAt": *************, "orgId": "67BPr5coYDLFvHeHr", "inActive": false, "billing": {"enrolledPlans": [{"_id": "iWbNeboHZwiqy5Wjt", "planDetails": {"_id": "iWbNeboHZwiqy5Wjt", "description": "Before School", "type": "plan", "program": "WDZcNsjLKyJ47dKoD", "frequency": "scaledMonthly", "category": "tuition", "amount": 295, "scaledAmounts": [94, 174, 235, 275, 295], "ledgerAccountName": "4013", "details": {"startTime": "6:30 am", "endTime": "9:10 am", "regStartDate": *************, "regEndDate": *************, "grades": ["K", "1", "2", "3", "4", "5", "6"], "scheduleType": "Lm2tqkvGeTXaoi7cX", "dateType": "timePeriod", "timePeriod": "vaMDvSLiBc48CC2ze"}}, "enrollmentDate": *************, "allocations": [{"id": "5m3vbqodnjHQxG2ze", "allocationType": "discount", "amount": 10, "amountType": "percent", "discountType": "siblingDiscount", "allocationDescription": "Sibling Discount", "discountAmount": 9.4}], "createdAt": *************, "reservationId": "sjuaqCQJfaT4mfLzt", "enrollmentForecastStartDate": *************, "enrollmentForecastEndDate": *************, "overrideRate": null, "updatedAt": *************, "updatedBy": "aLMvyrLWeL3ZW9MSn", "expirationDate": *************}, {"_id": "iWbNeboHZwiqy5Wjt", "planDetails": {"_id": "iWbNeboHZwiqy5Wjt", "description": "Before School", "type": "plan", "program": "WDZcNsjLKyJ47dKoD", "frequency": "scaledMonthly", "category": "tuition", "amount": 295, "scaledAmounts": [94, 174, 235, 275, 295], "ledgerAccountName": "4013", "details": {"startTime": "6:30 am", "endTime": "9:10 am", "regStartDate": *************, "regEndDate": *************, "grades": ["K", "1", "2", "3", "4", "5", "6"], "scheduleType": "Lm2tqkvGeTXaoi7cX", "dateType": "timePeriod", "timePeriod": "vaMDvSLiBc48CC2ze"}}, "enrollmentDate": *************, "allocations": [], "createdAt": *************, "expirationDate": *************, "reservationId": "mb3QRBfQsqJfhEQBb", "enrollmentForecastStartDate": *************, "enrollmentForecastEndDate": *************}], "lastInvoiced": *************, "pendingCharges": [], "billingNotes": "<PERSON><PERSON> started attending 2 days a week effective 09/05. Already paid for 1 day a week schedule on 09/01. Crediting the $84.60 towards new charge.", "enrolledPunchCards": [{"_id": "hxEkjoZB3Trze5L6p", "originalItem": {"_id": "Ftk85wKozcMdPhLDF", "description": "Drop In", "type": "punchcard", "program": "WDZcNsjLKyJ47dKoD", "numberOfDays": "1", "category": "tuition", "amount": 40, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 40, "quantity": "1", "notes": "10/9", "type": "item", "datesUsed": [0]}]}, "defaultGroupId": "ntZyn2XRT8JZHCjtL", "waitlistAddedDate": null, "documentItems": {"kQTiz4ePQj7CHeQgq": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "eDDbwkKMe9p2xuGPA", "personName": "<PERSON>"}}, "q72p22YNRH9CD6SNc": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "eDDbwkKMe9p2xuGPA", "personName": "<PERSON>"}}}, "checkInGroupId": null, "checkInGroupName": null, "checkedIn": false, "checkedInOutTime": *************, "presenceLastGroupId": "ntZyn2XRT8JZHCjtL", "previousClassroomGroupId": null, "timestamps": {"moveMomentTimestamp": {"momentId": "5G8JC8mWEimba748d", "timestamp": *************, "groupId": null, "oldGroupId": "ntZyn2XRT8JZHCjtL"}}, "checkedInById": "qogpRiLLXfgePicn7", "invoiceReminders": {"emailsSent": 8, "lastSentTime": *************}}, {"_id": "9Dicwsdu6P7KWb23G", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "designations": [], "type": "person", "profileData": {"enrollmentDate": *************, "birthday": *************, "gender": "Male", "studentGrade": "4", "document1": "Yes", "photoAttestation": "Yes"}, "createdBy": "SYSTEM", "createdAt": *************, "orgId": "TzSrjozxDHgvLW8bH", "inActive": false, "billing": {"enrolledPlans": [{"_id": "JuQ2N3CjZ4vaHK5Bg", "planDetails": {"_id": "JuQ2N3CjZ4vaHK5Bg", "description": "After School", "type": "plan", "program": "2KJBaxWJBGd4oawHP", "frequency": "scaledMonthly", "category": "tuition", "amount": 220, "scaledAmounts": [59, 112, 159, 193, 220], "ledgerAccountName": "4012", "details": {"startTime": "3:21 pm", "endTime": "6:00 pm", "regStartDate": *************, "grades": ["K", "1", "2", "3", "4", "5"], "scheduleType": "ivEwGqazmcWoWv6oJ", "dateType": "timePeriod", "timePeriod": "ib6PHcX5kvNWd5i7M"}}, "enrollmentDate": *************, "allocations": [], "createdAt": *************, "reservationId": "qutnpNvxRWCPSetHc", "enrollmentForecastStartDate": *************}], "pendingCharges": [], "lastInvoiced": *************, "enrolledPunchCards": [{"_id": "rK5cJMfXcr68mrtYd", "originalItem": {"_id": "PEbJhPZBPoDP3qqqy", "description": "Before/After Care 10-Day Punch Card", "type": "punchcard", "program": "2KJBaxWJBGd4oawHP", "numberOfDays": "10", "amount": 270, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 270, "quantity": "1", "notes": "", "type": "item", "datesUsed": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}]}, "defaultGroupId": "8pENy4sd8kfqAHNey", "waitlistAddedDate": null, "checkInGroupId": null, "checkInGroupName": null, "checkedIn": false, "checkedInById": "JuHBce7stRBwj5yex", "checkedInOutTime": *************, "presenceLastGroupId": null, "previousClassroomGroupId": null, "timestamps": {"moveMomentTimestamp": {"momentId": "43fEsPzJmdptPPEco", "timestamp": *************, "groupId": "8pENy4sd8kfqAHNey", "oldGroupId": null}}}, {"_id": "amWD2AD9Zb3uXhE8E", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "designations": [], "type": "person", "profileData": {"birthday": *************, "gender": "Male", "studentGrade": "K", "document1": "Yes", "photoAttestation": "Yes"}, "createdBy": "SYSTEM", "createdAt": *************, "orgId": "TzSrjozxDHgvLW8bH", "inActive": false, "billing": {"enrolledPlans": [{"_id": "JuQ2N3CjZ4vaHK5Bg", "planDetails": {"_id": "JuQ2N3CjZ4vaHK5Bg", "description": "After School", "type": "plan", "program": "2KJBaxWJBGd4oawHP", "frequency": "scaledMonthly", "category": "tuition", "amount": 220, "scaledAmounts": [59, 112, 159, 193, 220], "ledgerAccountName": "4012", "details": {"startTime": "3:21 pm", "endTime": "6:00 pm", "regStartDate": *************, "grades": ["K", "1", "2", "3", "4", "5"], "scheduleType": "ivEwGqazmcWoWv6oJ", "dateType": "timePeriod", "timePeriod": "ib6PHcX5kvNWd5i7M"}}, "enrollmentDate": *************, "allocations": [{"id": "6kHvgW4v8BgJj8SCm", "allocationType": "discount", "amount": 10, "amountType": "percent", "discountType": "siblingDiscount", "allocationDescription": "Sibling Discount", "discountAmount": 19.3}], "createdAt": *************, "reservationId": "SvwYGtfXnAD9zRiNT", "enrollmentForecastStartDate": *************}], "lastInvoiced": *************, "pendingCharges": [], "enrolledPunchCards": [{"_id": "5DvAso6WHiCpDq8zL", "originalItem": {"_id": "PEbJhPZBPoDP3qqqy", "description": "Before/After Care 10-Day Punch Card", "type": "punchcard", "program": "2KJBaxWJBGd4oawHP", "numberOfDays": "10", "amount": 270, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 270, "quantity": "1", "notes": "", "type": "item", "datesUsed": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}]}, "defaultGroupId": "8pENy4sd8kfqAHNey", "waitlistAddedDate": null, "checkInGroupId": null, "checkInGroupName": null, "checkedIn": false, "checkedInById": "JuHBce7stRBwj5yex", "checkedInOutTime": *************, "presenceLastGroupId": null, "previousClassroomGroupId": null, "timestamps": {"moveMomentTimestamp": {"momentId": "x3DqtXs8CAAkBAPaD", "timestamp": *************, "groupId": "8pENy4sd8kfqAHNey", "oldGroupId": null}}}, {"_id": "v6f2v3cRS8s9qKQXs", "firstName": "<PERSON>", "lastName": "<PERSON>", "designations": [], "type": "person", "profileData": {"birthday": *************, "gender": "Female", "studentGrade": "2", "document1": "Yes", "photoAttestation": "No"}, "createdBy": "SYSTEM", "createdAt": *************, "orgId": "eXrCBS5roD8bh6z6B", "inActive": false, "billing": {"enrolledPlans": [{"_id": "S8Ff5ATxc4jdXbebo", "planDetails": {"_id": "S8Ff5ATxc4jdXbebo", "description": "Before School", "type": "plan", "program": "tvRH7NC83zZHm3Ys7", "frequency": "scaledMonthly", "category": "tuition", "amount": 163, "scaledAmounts": [44, 83, 118, 143, 163], "ledgerAccountName": "4013", "details": {"startTime": "6:00 am", "endTime": "9:00 am", "regStartDate": *************, "grades": ["K", "1", "2", "3", "4", "5"], "scheduleType": "Lm2tqkvGeTXaoi7cX", "dateType": "timePeriod", "timePeriod": "RbdBieKohNsHiae4p"}}, "enrollmentDate": *************, "allocations": [{"allocationType": "discount", "amount": 20, "amountType": "dollars", "discountType": "bundleDiscount", "allocationDescription": "Discount: Bundle Discount", "id": "GRZ8TM7WR9gnQDAef"}], "createdAt": *************, "reservationId": "xzKZehiWd3QwNtJSH", "enrollmentForecastStartDate": *************, "enrollmentForecastEndDate": null, "overrideRate": null, "updatedAt": *************, "updatedBy": "TZnj9F36brucdDCsR"}, {"_id": "JuQ2N3CjZ4vaHK5Bg", "planDetails": {"_id": "JuQ2N3CjZ4vaHK5Bg", "description": "After School", "type": "plan", "program": "tvRH7NC83zZHm3Ys7", "frequency": "scaledMonthly", "category": "tuition", "amount": 220, "scaledAmounts": [59, 112, 159, 193, 220], "ledgerAccountName": "4012", "details": {"startTime": "3:36 pm", "endTime": "6:00 pm", "regStartDate": *************, "grades": ["K", "1", "2", "3", "4", "5"], "scheduleType": "ivEwGqazmcWoWv6oJ", "dateType": "timePeriod", "timePeriod": "RbdBieKohNsHiae4p"}}, "enrollmentDate": *************, "allocations": [{"allocationType": "discount", "amount": 20, "amountType": "dollars", "discountType": "bundleDiscount", "allocationDescription": "Discount: Bundle Discount", "id": "HYTpiCfqx6kfcgqbM"}], "createdAt": *************, "reservationId": "6CCpJQaimjyM4gffS", "enrollmentForecastStartDate": *************, "bundlePlanId": "PpynLRnoPjmbG9ucb", "enrollmentForecastEndDate": null, "overrideRate": null, "updatedAt": *************, "updatedBy": "TZnj9F36brucdDCsR"}], "pendingCharges": [], "lastInvoiced": *************, "billingNotes": "Oct 3, 2023 - Bundle discount was set incorrectly at $150. Corrected to $40 on their future schedule billing. - bcl", "enrolledPunchCards": [{"_id": "ss5KKBZM8nTHmu5eN", "originalItem": {"_id": "pT6t5C8N9sAa6oFD2", "description": "Drop In", "type": "punchcard", "program": "tvRH7NC83zZHm3Ys7", "numberOfDays": "1", "category": "tuition", "amount": 30, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 30, "quantity": "1", "notes": "9/1 Unregistered Non School Day at Grafton Bethel", "type": "item", "datesUsed": [0]}]}, "documentItems": {"3rQgPRpgC9povJSv3": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "kegXYipw5n4MZDEGo", "personName": "<PERSON>"}}, "BjBybtBpqPwz3MuAQ": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "kegXYipw5n4MZDEGo", "personName": "<PERSON>"}}, "7AhC4oExuN9KmR7TL": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "kegXYipw5n4MZDEGo", "personName": "<PERSON>"}}, "AbEn6b9b4gNDQCjxe": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "kegXYipw5n4MZDEGo", "personName": "<PERSON>"}}, "ZEeFRj3eajFPfPvYQ": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "kegXYipw5n4MZDEGo", "personName": "<PERSON>"}}}, "defaultGroupId": "trT9a5ZEpyALKSM2v", "checkInGroupId": null, "checkInGroupName": null, "checkedIn": false, "checkedInById": "SJkLFJSppCzi9dxCR", "checkedInOutTime": 1698221178278, "presenceLastGroupId": null, "previousClassroomGroupId": null, "timestamps": {"moveMomentTimestamp": {"momentId": "pChs55j9SsPwjssf9", "timestamp": 1698147519003, "groupId": "trT9a5ZEpyALKSM2v", "oldGroupId": null}}, "waitlistAddedDate": null, "invoiceReminders": {"emailsSent": 3, "lastSentTime": 1697098080444}}, {"_id": "2PR6ncDiWMLmrtDAN", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "designations": [], "type": "person", "profileData": {"birthday": 1423717200000, "gender": "Female", "studentGrade": "3", "document1": "Yes", "agencyIdentifier": "Unknown", "subsidyCode": "Unknown", "photoAttestation": "Yes"}, "createdBy": "K2zKEfyE3hQEmzHNw", "createdAt": *************, "orgId": "rwWMMmA3cjkBofa2S", "inActive": false, "billing": {"enrolledPunchCards": [{"_id": "Hgc4orKHsgKuQySyt", "originalItem": {"_id": "PEbJhPZBPoDP3qqqy", "description": "Before/After Care 10-Day Punch Card", "type": "punchcard", "program": "fX4R4HBbhgsFbMnFf", "numberOfDays": "10", "amount": 315, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 315, "quantity": "1", "notes": "", "type": "item", "datesUsed": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}], "pendingCharges": [], "lastInvoiced": *************, "enrolledPlans": [{"_id": "GNoX4Zk6JihnEvmsW", "planDetails": {"_id": "GNoX4Zk6JihnEvmsW", "description": "After School - Weekly", "type": "plan", "program": "", "frequency": "scaledWeekly", "category": "tuition", "amount": 66.51, "scaledAmounts": [23.56, 37.88, 51.04, 61.43, 66.51], "ledgerAccountName": "4012", "details": {"startTime": "3:25 pm", "endTime": "6:00 pm", "regStartDate": *************, "grades": ["K", "1", "2", "3", "4", "5"], "scheduleType": "ivEwGqazmcWoWv6oJ", "dateType": "timePeriod", "timePeriod": "5TfzwZBt3BfCG6T3g"}}, "enrollmentDate": *************, "allocations": [{"allocationType": "reimbursable", "amount": 64.01, "amountType": "dollars", "reimbursementType": "erieCountyDSS", "allocationDescription": "Reimbursable: Erie County DSS", "payerStartDate": *************, "payerEndDate": *************, "id": "iYBHuyHbaKD7K2LFp"}], "createdAt": *************, "reservationId": "vyba9Bbd6ZvP2tLGL", "enrollmentForecastStartDate": *************}, {"_id": "xRjm62Jym2aaPxRJn", "planDetails": {"_id": "xRjm62Jym2aaPxRJn", "description": "Before School - Weekly", "type": "plan", "program": "", "frequency": "scaledWeekly", "category": "tuition", "amount": 60.05, "scaledAmounts": [21.48, 34.41, 46.65, 55.89, 60.05], "ledgerAccountName": "4013", "details": {"startTime": "6:45 am", "endTime": "9:15 am", "regStartDate": *************, "grades": ["K", "1", "2", "3", "4", "5"], "scheduleType": "Lm2tqkvGeTXaoi7cX", "dateType": "timePeriod", "timePeriod": "5TfzwZBt3BfCG6T3g"}}, "enrollmentDate": *************, "allocations": [{"allocationType": "reimbursable", "amount": 100, "amountType": "percent", "reimbursementType": "erieCountyDSS", "allocationDescription": "Reimbursable: Erie County DSS", "payerStartDate": *************, "payerEndDate": *************, "id": "CdKpc7vsDuDeWykR6"}], "createdAt": *************, "reservationId": "7PBwgqTKD3kc2tj2d", "enrollmentForecastStartDate": *************}], "billingNotes": "Parent called regarding $315 invoice received (punchcard). Stated punchcard was not needed. Voided transaction."}, "invoiceReminders": {"emailsSent": 9, "lastSentTime": *************}, "documentItems": {"LaCzuWbri4AezpxW3": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "cWEnSwWNqMaFopSWa", "personName": "<PERSON>"}}, "5tZDcX5NqG4GhSsvy": {"templateOptionResult": {"action": "ack", "date": 1692049321385, "personId": "cWEnSwWNqMaFopSWa", "personName": "<PERSON>"}}, "X6M6pP6GfFt2exhwd": {"templateOptionResult": {"action": "ack", "date": 1692049326606, "personId": "cWEnSwWNqMaFopSWa", "personName": "<PERSON>"}}, "iAFGddgMsJBn8xqAh": {"createdAt": 1692400377990, "createdByPersonId": "cWEnSwWNqMaFopSWa", "createdByUserId": "qbFBGAGA3zhSkjyqz", "repositoryKey": "rwWMMmA3cjkBofa2S/qbFBGAGA3zhSkjyqz/ZkqCMsfUs7KZOtPxkrfD", "token": "ZkqCMsfUs7KZOtPxkrfD"}, "tiRkLeMAaCA2fMY9F": {"templateOptionResult": {"action": "ack", "date": 1693022486302, "personId": "cWEnSwWNqMaFopSWa", "personName": "<PERSON>"}}}, "defaultGroupId": "QuXAb8p4eAMeXhsmZ", "checkInGroupId": null, "checkInGroupName": null, "checkedIn": false, "checkedInOutTime": 1698930889922, "presenceLastGroupId": "QuXAb8p4eAMeXhsmZ", "previousClassroomGroupId": null, "timestamps": {"moveMomentTimestamp": {"momentId": "nfPMDro3wFeN9JXaZ", "timestamp": 1698930889958, "groupId": null, "oldGroupId": "QuXAb8p4eAMeXhsmZ"}}, "checkedInById": "anCmEABSGnKQqe6qF"}, {"_id": "nAFYTSsFBRb4adkpD", "firstName": "Jax", "lastName": "<PERSON><PERSON>", "designations": [], "type": "person", "profileData": {"birthday": *************, "gender": "Male", "studentGrade": "2", "document1": "Yes", "photoAttestation": "Yes"}, "createdBy": "SYSTEM", "createdAt": *************, "orgId": "iiadQjLkj2XAwEfPc", "inActive": false, "billing": {"enrolledPlans": [{"_id": "JuQ2N3CjZ4vaHK5Bg", "planDetails": {"_id": "JuQ2N3CjZ4vaHK5Bg", "description": "After School", "type": "plan", "program": "QF32vJw9nMitXRwvq", "frequency": "scaledMonthly", "category": "tuition", "amount": 220, "scaledAmounts": [59, 112, 159, 193, 220], "ledgerAccountName": "4012", "details": {"startTime": "3:36 pm", "endTime": "6:00 pm", "regStartDate": *************, "grades": ["K", "1", "2", "3", "4", "5"], "scheduleType": "ivEwGqazmcWoWv6oJ", "dateType": "timePeriod", "timePeriod": "2pTsmADZMQBjXugS8"}}, "enrollmentDate": *************, "allocations": [], "createdAt": *************, "reservationId": "Gk5YFiLrD3mqwBgpA", "enrollmentForecastStartDate": *************, "bundlePlanId": "PpynLRnoPjmbG9ucb", "enrollmentForecastEndDate": null, "overrideRate": null, "updatedAt": *************, "updatedBy": "vMhKFfuTbxdNR7Gsy"}, {"_id": "S8Ff5ATxc4jdXbebo", "planDetails": {"_id": "S8Ff5ATxc4jdXbebo", "description": "Before School", "type": "plan", "program": "QF32vJw9nMitXRwvq", "frequency": "scaledMonthly", "category": "tuition", "amount": 163, "scaledAmounts": [44, 83, 118, 143, 163], "ledgerAccountName": "4013", "details": {"startTime": "6:00 am", "endTime": "9:00 am", "regStartDate": *************, "grades": ["K", "1", "2", "3", "4", "5"], "scheduleType": "Lm2tqkvGeTXaoi7cX", "dateType": "timePeriod", "timePeriod": "2pTsmADZMQBjXugS8"}}, "enrollmentDate": *************, "allocations": [{"allocationType": "discount", "amount": 28.5, "amountType": "dollars", "discountType": "bundle", "allocationDescription": "Bundle: After School and Before School", "discountAmount": 28.5}], "createdAt": *************, "reservationId": "7XAF3LGMQmKkP2ikr", "enrollmentForecastStartDate": *************, "bundlePlanId": "PpynLRnoPjmbG9ucb", "enrollmentForecastEndDate": *************, "expirationDate": *************}], "pendingCharges": [], "lastInvoiced": *************, "enrolledItems": [{"_id": "FaTiWMtDrwcQidbJn", "originalItem": {"_id": "aJCcSa7vKvLdRNspz", "description": "Non-School Day", "type": "item", "program": "s4o83QyWj6LofKeR4", "amount": 30, "scaledAmounts": [], "ledgerAccountName": "4104", "details": {"startTime": "6:00 am", "endTime": "6:00 pm", "regStartDate": *************, "regEndDate": *************, "grades": ["K", "1", "2", "3", "4", "5"], "scheduleType": "oMrmtCgdtMadKETBm", "dateType": "individualDates", "individualDates": [*************]}}, "createdAt": *************, "price": 30, "quantity": "1", "notes": "9/1/2023", "type": "item"}], "enrolledPunchCards": [{"_id": "StKrxdy744oBm6NEZ", "originalItem": {"_id": "bzDTdwpJyStdmcHbv", "description": "Drop In", "type": "punchcard", "program": "QF32vJw9nMitXRwvq", "numberOfDays": "1", "category": "tuition", "amount": 30, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 30, "quantity": "1", "notes": "10/2 Unregistered Non School Day at Magruder", "type": "item", "datesUsed": [0]}]}, "documentItems": {"LdzE6NrseiH2REHjq": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "WCBBLRAHuq8hg43ob", "personName": "<PERSON>"}}, "Po3nvq8z6fiHCChgu": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "WCBBLRAHuq8hg43ob", "personName": "<PERSON>"}}, "YjX4XZvt7QcbkFTLT": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "WCBBLRAHuq8hg43ob", "personName": "<PERSON>"}}, "Yedj8g4Sw3dnNYSfp": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "WCBBLRAHuq8hg43ob", "personName": "<PERSON>"}}, "XY8uazqzAQcxmNxt6": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "WCBBLRAHuq8hg43ob", "personName": "<PERSON>"}}}, "defaultGroupId": "y2s6de6eCxSvjyW8G", "checkInGroupId": null, "checkInGroupName": null, "checkedIn": false, "checkedInById": "WCBBLRAHuq8hg43ob", "checkedInOutTime": 1698929684545, "presenceLastGroupId": "y2s6de6eCxSvjyW8G", "previousClassroomGroupId": null, "timestamps": {"moveMomentTimestamp": {"momentId": "Lf2Rnd4RRk8dDeb8b", "timestamp": 1698929684596, "groupId": null, "oldGroupId": "y2s6de6eCxSvjyW8G"}}}, {"_id": "gCGEJkm6Z9yqR3KEu", "firstName": "Xaiver", "lastName": "<PERSON>", "designations": [], "type": "person", "profileData": {"birthday": *************, "gender": "Male", "studentGrade": "3", "document1": "Yes", "photoAttestation": "Yes"}, "createdBy": "SYSTEM", "createdAt": *************, "orgId": "eXrCBS5roD8bh6z6B", "inActive": false, "billing": {"enrolledPlans": [{"_id": "JuQ2N3CjZ4vaHK5Bg", "planDetails": {"_id": "JuQ2N3CjZ4vaHK5Bg", "description": "After School", "type": "plan", "program": "tvRH7NC83zZHm3Ys7", "frequency": "scaledMonthly", "category": "tuition", "amount": 220, "scaledAmounts": [59, 112, 159, 193, 220], "ledgerAccountName": "4012", "details": {"startTime": "3:36 pm", "endTime": "6:00 pm", "regStartDate": *************, "grades": ["K", "1", "2", "3", "4", "5"], "scheduleType": "ivEwGqazmcWoWv6oJ", "dateType": "timePeriod", "timePeriod": "RbdBieKohNsHiae4p"}}, "enrollmentDate": *************, "allocations": [{"allocationType": "discount", "amount": 28.5, "amountType": "dollars", "discountType": "bundle", "allocationDescription": "Bundle: After School and Before School", "discountAmount": 28.5}, {"allocationType": "discount", "amount": 25, "amountType": "percent", "discountType": "militaryDiscount", "allocationDescription": "Discount: Military Discount", "id": "axiRwm6JMKQNy9EWJ"}], "createdAt": *************, "reservationId": "3uChY9GSZ9E4sYtKd", "enrollmentForecastStartDate": *************, "bundlePlanId": "PpynLRnoPjmbG9ucb", "enrollmentForecastEndDate": null, "overrideRate": null, "updatedAt": *************, "updatedBy": "y8ssP2R84duNZRdRQ"}, {"_id": "S8Ff5ATxc4jdXbebo", "planDetails": {"_id": "S8Ff5ATxc4jdXbebo", "description": "Before School", "type": "plan", "program": "tvRH7NC83zZHm3Ys7", "frequency": "scaledMonthly", "category": "tuition", "amount": 163, "scaledAmounts": [44, 83, 118, 143, 163], "ledgerAccountName": "4013", "details": {"startTime": "6:00 am", "endTime": "9:00 am", "regStartDate": *************, "grades": ["K", "1", "2", "3", "4", "5"], "scheduleType": "Lm2tqkvGeTXaoi7cX", "dateType": "timePeriod", "timePeriod": "RbdBieKohNsHiae4p"}}, "enrollmentDate": *************, "allocations": [{"allocationType": "discount", "amount": 28.5, "amountType": "dollars", "discountType": "bundle", "allocationDescription": "Bundle: After School and Before School", "discountAmount": 28.5}, {"allocationType": "discount", "amount": 25, "amountType": "percent", "discountType": "militaryDiscount", "allocationDescription": "Discount: Military Discount", "id": "DE7wT3xmdsnLxtL9N"}], "createdAt": *************, "reservationId": "rx5JEuz3hnaMQDBZc", "enrollmentForecastStartDate": *************, "bundlePlanId": "PpynLRnoPjmbG9ucb", "enrollmentForecastEndDate": null, "overrideRate": null, "updatedAt": *************, "updatedBy": "y8ssP2R84duNZRdRQ"}], "pendingCharges": [], "lastInvoiced": *************, "enrolledPunchCards": [{"_id": "AcHgALMjfjix4YfLx", "originalItem": {"_id": "pT6t5C8N9sAa6oFD2", "description": "Drop In", "type": "punchcard", "program": "tvRH7NC83zZHm3Ys7", "numberOfDays": "1", "category": "tuition", "amount": 30, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 30, "quantity": "1", "notes": "9/1 Unregistered Non School Day at Grafton Bethel", "type": "item", "datesUsed": [0]}]}, "documentItems": {"3rQgPRpgC9povJSv3": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "X4kinjvuqrQsN4n3x", "personName": "<PERSON>"}}, "BjBybtBpqPwz3MuAQ": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "X4kinjvuqrQsN4n3x", "personName": "<PERSON>"}}, "AbEn6b9b4gNDQCjxe": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "X4kinjvuqrQsN4n3x", "personName": "<PERSON>"}}, "7AhC4oExuN9KmR7TL": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "X4kinjvuqrQsN4n3x", "personName": "<PERSON>"}}}, "defaultGroupId": "trT9a5ZEpyALKSM2v", "checkInGroupId": null, "checkInGroupName": null, "checkedIn": false, "checkedInById": "SJkLFJSppCzi9dxCR", "checkedInOutTime": *************, "presenceLastGroupId": null, "previousClassroomGroupId": null, "timestamps": {"moveMomentTimestamp": {"momentId": "RyWGBnixL3cHMW5g7", "timestamp": 1696511986164, "groupId": "trT9a5ZEpyALKSM2v", "oldGroupId": null}}, "invoiceReminders": {"emailsSent": 10, "lastSentTime": 1694765241568}}, {"_id": "MutpqfXHjjrcMWDqP", "firstName": "<PERSON>", "lastName": "Garton-Gundling", "designations": [], "type": "person", "profileData": {"birthday": 1529899200000, "gender": "Male", "studentGrade": "K", "document1": "Yes", "photoAttestation": "Yes", "standardOutlook": {"allergies": "-cillin and -myacin medications", "specialNeeds": "<PERSON> has ADHD, so he may need behavioral support at times during before-school care. If you would like a copy of his update IEP for Coventry (once we have it), we are happy to share it with you."}}, "createdBy": "SYSTEM", "createdAt": *************, "orgId": "eXrCBS5roD8bh6z6B", "inActive": false, "billing": {"enrolledPlans": [{"_id": "S8Ff5ATxc4jdXbebo", "planDetails": {"_id": "S8Ff5ATxc4jdXbebo", "description": "Before School", "type": "plan", "program": "tvRH7NC83zZHm3Ys7", "frequency": "scaledMonthly", "category": "tuition", "amount": 163, "scaledAmounts": [44, 83, 118, 143, 163], "ledgerAccountName": "4013", "details": {"startTime": "6:00 am", "endTime": "9:00 am", "regStartDate": *************, "grades": ["K", "1", "2", "3", "4", "5"], "scheduleType": "Lm2tqkvGeTXaoi7cX", "dateType": "timePeriod", "timePeriod": "RbdBieKohNsHiae4p"}}, "enrollmentDate": *************, "allocations": [], "createdAt": *************, "reservationId": "pCuEWdNa6E9rewrwr", "enrollmentForecastStartDate": *************}], "pendingCharges": [], "lastInvoiced": *************, "enrolledPunchCards": [{"_id": "uuB36p8QAKx8hRvfw", "originalItem": {"_id": "pT6t5C8N9sAa6oFD2", "description": "Drop In", "type": "punchcard", "program": "tvRH7NC83zZHm3Ys7", "numberOfDays": "1", "category": "tuition", "amount": 30, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 30, "quantity": "1", "notes": "9/1 Unregistered Non School Day at Grafton Bethel", "type": "item", "datesUsed": [0]}]}, "documentItems": {"3rQgPRpgC9povJSv3": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "ya8ZWQM3yTfHbSoKc", "personName": "Victoria Garton-Gundling"}}, "BjBybtBpqPwz3MuAQ": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "Nt6j6xho4GzKaPtPr", "personName": "<PERSON>-Gundling"}}, "m7gj8gE79e2DpLS7v": {"createdAt": *************, "createdByPersonId": "Nt6j6xho4GzKaPtPr", "createdByUserId": "gRpFZDh8y9uCTqt2W", "repositoryKey": "eXrCBS5roD8bh6z6B/gRpFZDh8y9uCTqt2W/fVRymG2dSXCicTLpQgJu", "token": "fVRymG2dSXCicTLpQgJu"}}, "defaultGroupId": "trT9a5ZEpyALKSM2v", "checkInGroupId": null, "checkInGroupName": null, "checkedIn": false, "checkedInById": "SJkLFJSppCzi9dxCR", "checkedInOutTime": *************, "presenceLastGroupId": "trT9a5ZEpyALKSM2v", "previousClassroomGroupId": null, "timestamps": {"moveMomentTimestamp": {"momentId": "6bHXr5PwrPnqrLtq2", "timestamp": 1698842823930, "groupId": null, "oldGroupId": "trT9a5ZEpyALKSM2v"}}, "invoiceReminders": {"emailsSent": 3, "lastSentTime": 1696234249349}}, {"_id": "yCKDnMhnMRosvFab9", "firstName": "<PERSON>", "lastName": "<PERSON>", "designations": [], "type": "person", "profileData": {"birthday": *************, "gender": "Female", "studentGrade": "2", "document1": "Yes", "photoAttestation": "Yes"}, "createdBy": "SYSTEM", "createdAt": *************, "orgId": "9uZQpotkrDLbnr4aS", "inActive": false, "billing": {"enrolledPlans": [{"_id": "uhQ2Ny2QxfWqtEYnM", "planDetails": {"_id": "uhQ2Ny2QxfWqtEYnM", "description": "After School", "type": "plan", "program": "fAXB2y3crWFrzq6KD", "frequency": "scaledMonthly", "category": "tuition", "amount": 556, "scaledAmounts": [176, 310, 429, 515, 556], "ledgerAccountName": "4012", "details": {"startTime": "3:15 pm", "endTime": "6:00 pm", "regStartDate": *************, "regEndDate": *************, "grades": ["K", "1", "2", "3", "4"], "scheduleType": "ivEwGqazmcWoWv6oJ", "dateType": "timePeriod", "timePeriod": "PbP4ByH9Tu4nLKv2u"}}, "enrollmentDate": *************, "allocations": [], "createdAt": *************, "reservationId": "TDXFZ3RfCALMyhbYg", "enrollmentForecastStartDate": *************, "enrollmentForecastEndDate": *************, "expirationDate": *************}, {"_id": "uhQ2Ny2QxfWqtEYnM", "planDetails": {"_id": "uhQ2Ny2QxfWqtEYnM", "description": "After School", "type": "plan", "program": "fAXB2y3crWFrzq6KD", "frequency": "scaledMonthly", "category": "tuition", "amount": 556, "scaledAmounts": [176, 310, 429, 515, 556], "ledgerAccountName": "4012", "details": {"startTime": "3:15 pm", "endTime": "6:00 pm", "regStartDate": *************, "regEndDate": *************, "grades": ["K", "1", "2", "3", "4"], "scheduleType": "ivEwGqazmcWoWv6oJ", "dateType": "timePeriod", "timePeriod": "PbP4ByH9Tu4nLKv2u"}}, "enrollmentDate": *************, "allocations": [], "createdAt": *************, "reservationId": "4L3ggoqJGgv5oGaAX", "enrollmentForecastStartDate": *************}], "pendingCharges": [], "lastInvoiced": *************, "enrolledPunchCards": [{"_id": "mAN3mQtF64DpKjNst", "originalItem": {"_id": "bN4DXvpiWdo8gxsGe", "description": "Drop In", "type": "punchcard", "program": "fAXB2y3crWFrzq6KD", "numberOfDays": "1", "category": "tuition", "amount": 40, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 40, "quantity": "1", "notes": "", "type": "item", "datesUsed": [0]}, {"_id": "hAF2h6vWgPKigEvjq", "originalItem": {"_id": "bN4DXvpiWdo8gxsGe", "description": "Drop In", "type": "punchcard", "program": "fAXB2y3crWFrzq6KD", "numberOfDays": "1", "category": "tuition", "amount": 40, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 40, "quantity": "1", "notes": "", "type": "item", "datesUsed": [0]}]}, "documentItems": {"FWK54s82BTZpvSPhy": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "b8DuzDto3DnHSyfRf", "personName": "<PERSON>"}}, "J8qpgqp2cpJGj8TQc": {"createdAt": *************, "createdByPersonId": "b8DuzDto3DnHSyfRf", "createdByUserId": "ijvR62QAc4aFdvMwH", "repositoryKey": "9uZQpotkrDLbnr4aS/ijvR62QAc4aFdvMwH/sWeLf9bNWTHT8aL8xJt0", "token": "sWeLf9bNWTHT8aL8xJt0"}, "CLxv8KYnTH6PQejGw": {"createdAt": *************, "createdByPersonId": "b8DuzDto3DnHSyfRf", "createdByUserId": "ijvR62QAc4aFdvMwH", "repositoryKey": "9uZQpotkrDLbnr4aS/ijvR62QAc4aFdvMwH/6ReyIE8p17T8fTqKSNTA", "token": "6ReyIE8p17T8fTqKSNTA"}, "EsL2WjHrRvrkzfkif": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "b8DuzDto3DnHSyfRf", "personName": "<PERSON>"}}}, "defaultGroupId": "Bm7b3sGtWy66ETT8E", "checkInGroupId": null, "checkInGroupName": null, "checkedIn": false, "checkedInById": "CAGdaGYDNzXZ8FgGy", "checkedInOutTime": 1698875855760, "presenceLastGroupId": "Bm7b3sGtWy66ETT8E", "previousClassroomGroupId": null, "timestamps": {"moveMomentTimestamp": {"momentId": "mfQvHiR6PkugygEqv", "timestamp": 1698875855798, "groupId": null, "oldGroupId": "Bm7b3sGtWy66ETT8E"}}, "invoiceReminders": {"emailsSent": 10, "lastSentTime": 1695715547220}}, {"_id": "996yCdGLqKGbvZLQ5", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "designations": [], "type": "person", "profileData": {"birthday": *************, "gender": "Female", "studentGrade": "2", "document1": "Yes", "photoAttestation": "Yes"}, "createdBy": "SYSTEM", "createdAt": *************, "orgId": "orEBz3k4e2vRRvQC7", "inActive": false, "billing": {"enrolledPlans": [{"_id": "a7y3eaSckAtRJbKKd", "planDetails": {"_id": "a7y3eaSckAtRJbKKd", "description": "After School", "type": "plan", "program": "tHMpzYizW4JRw6WsC", "frequency": "scaledMonthly", "category": "tuition", "amount": 398, "scaledAmounts": [163, 254, 333, 372, 398], "ledgerAccountName": "4012", "details": {"startTime": "3:34 pm", "endTime": "6:00 pm", "regStartDate": *************, "grades": ["K", "1", "2", "3", "4", "5", "6"], "scheduleType": "cmy9CJcLEXarkF5Dn", "dateType": "timePeriod", "timePeriod": "ztauJQfFkpFmwsMAo"}}, "enrollmentDate": *************, "allocations": [], "createdAt": *************, "reservationId": "uZZSB9uBtyGxqZd3a", "enrollmentForecastStartDate": *************}], "pendingCharges": [], "lastInvoiced": *************, "enrolledPunchCards": [{"_id": "z9beYWc4Ls7FKD2Hr", "originalItem": {"_id": "4ztg6qnch58ngtMxb", "description": "Drop In", "type": "punchcard", "program": "tHMpzYizW4JRw6WsC", "numberOfDays": "1", "category": "tuition", "amount": 40, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 40, "quantity": "1", "notes": "Parent Requested 9/11 9/18/9/25 -********", "type": "item", "datesUsed": [0]}, {"_id": "SHtHmJzjzNCyCe6Tr", "originalItem": {"_id": "4ztg6qnch58ngtMxb", "description": "Drop In", "type": "punchcard", "program": "tHMpzYizW4JRw6WsC", "numberOfDays": "1", "category": "tuition", "amount": 40, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 40, "quantity": "1", "notes": "Parent Requested 9/11 9/18/9/25 -********", "type": "item", "datesUsed": [0]}]}, "documentItems": {"u3x2xrozAvsF7M7ff": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "FHtbT3PoZvDd8e6Mk", "personName": "<PERSON><PERSON>"}, "approvedAt": *************, "approvedByPersonId": "4miMBqxdjKYpztLE9", "approvedByUserId": "5Z9DAoFWSGFnRnJzz"}, "cr9J4jgagJx5o7adC": {"createdAt": *************, "createdByPersonId": "FHtbT3PoZvDd8e6Mk", "createdByUserId": "75vNZtFjhvPaBNYT6", "repositoryKey": "orEBz3k4e2vRRvQC7/75vNZtFjhvPaBNYT6/TdpnAv51fk3WNq03nGXg", "token": "TdpnAv51fk3WNq03nGXg", "approvedAt": *************, "approvedByPersonId": "4miMBqxdjKYpztLE9", "approvedByUserId": "5Z9DAoFWSGFnRnJzz"}, "ztNRu9T5mfauuGk8q": {"createdAt": *************, "createdByPersonId": "FHtbT3PoZvDd8e6Mk", "createdByUserId": "75vNZtFjhvPaBNYT6", "repositoryKey": "orEBz3k4e2vRRvQC7/75vNZtFjhvPaBNYT6/Sya8lIX1v8Pdq63cGiFT", "token": "Sya8lIX1v8Pdq63cGiFT", "approvedAt": 1692923643533, "approvedByPersonId": "4miMBqxdjKYpztLE9", "approvedByUserId": "5Z9DAoFWSGFnRnJzz", "archivedAt": 1698266584750, "archivedByPersonId": "4miMBqxdjKYpztLE9", "archivedByUserId": "5Z9DAoFWSGFnRnJzz"}}, "defaultGroupId": "DbbkFMrPhAFmNjhKW", "waitlistAddedDate": null, "checkInGroupId": null, "checkInGroupName": null, "checkedIn": false, "checkedInOutTime": 1698871448296, "presenceLastGroupId": "DbbkFMrPhAFmNjhKW", "previousClassroomGroupId": null, "timestamps": {"moveMomentTimestamp": {"momentId": "PnxdA5Ye9vxeLmCkt", "timestamp": 1698871448349, "groupId": null, "oldGroupId": "DbbkFMrPhAFmNjhKW"}}, "checkedInById": "cALcn5etD8GBqr9mQ"}, {"_id": "3f97vkG62rA3LAspR", "firstName": "<PERSON><PERSON>", "lastName": "Yeomans", "designations": [], "type": "person", "profileData": {"birthday": *************, "gender": "Female", "studentGrade": "K", "document1": "Yes", "photoAttestation": "Yes"}, "createdBy": "SYSTEM", "createdAt": *************, "orgId": "iiadQjLkj2XAwEfPc", "inActive": false, "billing": {"enrolledPlans": [{"_id": "JuQ2N3CjZ4vaHK5Bg", "planDetails": {"_id": "JuQ2N3CjZ4vaHK5Bg", "description": "After School", "type": "plan", "program": "QF32vJw9nMitXRwvq", "frequency": "scaledMonthly", "category": "tuition", "amount": 220, "scaledAmounts": [59, 112, 159, 193, 220], "ledgerAccountName": "4012", "details": {"startTime": "3:36 pm", "endTime": "6:00 pm", "regStartDate": *************, "grades": ["K", "1", "2", "3", "4", "5"], "scheduleType": "ivEwGqazmcWoWv6oJ", "dateType": "timePeriod", "timePeriod": "2pTsmADZMQBjXugS8"}}, "enrollmentDate": *************, "allocations": [{"allocationType": "discount", "amount": 28.5, "amountType": "dollars", "discountType": "bundle", "allocationDescription": "Bundle: After School and Before School", "discountAmount": 28.5}], "createdAt": *************, "reservationId": "Q3rofMvpQ8GiA8uzh", "enrollmentForecastStartDate": *************, "bundlePlanId": "PpynLRnoPjmbG9ucb"}, {"_id": "S8Ff5ATxc4jdXbebo", "planDetails": {"_id": "S8Ff5ATxc4jdXbebo", "description": "Before School", "type": "plan", "program": "QF32vJw9nMitXRwvq", "frequency": "scaledMonthly", "category": "tuition", "amount": 163, "scaledAmounts": [44, 83, 118, 143, 163], "ledgerAccountName": "4013", "details": {"startTime": "6:00 am", "endTime": "9:00 am", "regStartDate": *************, "grades": ["K", "1", "2", "3", "4", "5"], "scheduleType": "Lm2tqkvGeTXaoi7cX", "dateType": "timePeriod", "timePeriod": "2pTsmADZMQBjXugS8"}}, "enrollmentDate": *************, "allocations": [{"allocationType": "discount", "amount": 28.5, "amountType": "dollars", "discountType": "bundle", "allocationDescription": "Bundle: After School and Before School", "discountAmount": 28.5}], "createdAt": *************, "reservationId": "4m8HgpPcqwpSJxJXb", "enrollmentForecastStartDate": *************, "bundlePlanId": "PpynLRnoPjmbG9ucb"}], "pendingCharges": [], "lastInvoiced": *************, "enrolledItems": [{"_id": "iWmBPFJXL5hugRaG3", "originalItem": {"_id": "aJCcSa7vKvLdRNspz", "description": "Non-School Day", "type": "item", "program": "s4o83QyWj6LofKeR4", "amount": 30, "scaledAmounts": [], "ledgerAccountName": "4104", "details": {"startTime": "6:00 am", "endTime": "6:00 pm", "regStartDate": *************, "regEndDate": *************, "grades": ["K", "1", "2", "3", "4", "5"], "scheduleType": "oMrmtCgdtMadKETBm", "dateType": "individualDates", "individualDates": [*************]}}, "createdAt": *************, "price": 30, "quantity": "1", "notes": "9/1/2023", "type": "item"}], "enrolledPunchCards": [{"_id": "asnLhixp5dXTHeaCn", "originalItem": {"_id": "bzDTdwpJyStdmcHbv", "description": "Drop In", "type": "punchcard", "program": "QF32vJw9nMitXRwvq", "numberOfDays": "1", "category": "tuition", "amount": 30, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 30, "quantity": "1", "notes": "9/1 Unregistered Non School Day at Magruder", "type": "item", "datesUsed": [0]}]}, "documentItems": {"LdzE6NrseiH2REHjq": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "urChZ5CmDqPZM6HZJ", "personName": "<PERSON>"}}, "Po3nvq8z6fiHCChgu": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "urChZ5CmDqPZM6HZJ", "personName": "<PERSON>"}}, "CGLCdKi4NdT7HLbrd": {"createdAt": *************, "createdByPersonId": "urChZ5CmDqPZM6HZJ", "createdByUserId": "28soQjTBPHaqeNLB2", "repositoryKey": "iiadQjLkj2XAwEfPc/28soQjTBPHaqeNLB2/mcHr0yE3TncuPViLqbXO", "token": "mcHr0yE3TncuPViLqbXO"}, "YjX4XZvt7QcbkFTLT": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "urChZ5CmDqPZM6HZJ", "personName": "<PERSON>"}}, "Yedj8g4Sw3dnNYSfp": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "urChZ5CmDqPZM6HZJ", "personName": "<PERSON>"}}, "XY8uazqzAQcxmNxt6": {"templateOptionResult": {"action": "ack", "date": 1698162179422, "personId": "urChZ5CmDqPZM6HZJ", "personName": "<PERSON>"}}}, "defaultGroupId": "y2s6de6eCxSvjyW8G", "checkInGroupId": null, "checkInGroupName": null, "checkedIn": false, "checkedInById": "urChZ5CmDqPZM6HZJ", "checkedInOutTime": 1698929689857, "presenceLastGroupId": "y2s6de6eCxSvjyW8G", "previousClassroomGroupId": null, "timestamps": {"moveMomentTimestamp": {"momentId": "npCp2KcEbYTsNRrAW", "timestamp": 1698929689926, "groupId": null, "oldGroupId": "y2s6de6eCxSvjyW8G"}}, "invoiceReminders": {"emailsSent": 2, "lastSentTime": 1693383557215}}, {"_id": "RCvv2oJiw6ikXQDhs", "firstName": "<PERSON>", "lastName": "<PERSON>", "designations": [], "type": "person", "profileData": {"birthday": 1480665600000, "gender": "Male", "studentGrade": "1", "document1": "Yes", "homeSchool": "Maple Grove Primary", "photoAttestation": "Yes"}, "createdBy": "SYSTEM", "createdAt": *************, "orgId": "67BPr5coYDLFvHeHr", "inActive": false, "billing": {"enrolledPlans": [{"_id": "XoAqnz6RB98w5P2jC", "planDetails": {"_id": "XoAqnz6RB98w5P2jC", "description": "After School", "type": "plan", "program": "WDZcNsjLKyJ47dKoD", "frequency": "scaledMonthly", "category": "tuition", "amount": 295, "scaledAmounts": [94, 174, 235, 275, 295], "ledgerAccountName": "4012", "details": {"startTime": "3:40 pm", "endTime": "6:00 pm", "regStartDate": *************, "regEndDate": *************, "grades": ["K", "1", "2", "3", "4", "5", "6", "7", "8"], "scheduleType": "ivEwGqazmcWoWv6oJ", "dateType": "timePeriod", "timePeriod": "vaMDvSLiBc48CC2ze"}}, "enrollmentDate": *************, "allocations": [{"id": "wRgkmX9v95DRf5zCe", "allocationType": "discount", "amount": 20, "amountType": "percent", "discountType": "coupon", "code": "WNSEHWBJY@", "allocationDescription": "Free & Reduced", "discountExpires": *************, "discountAmount": 47}], "createdAt": *************, "reservationId": "jPGQ6Qx3tWsgXtz6H", "enrollmentForecastStartDate": *************, "enrollmentForecastEndDate": *************, "expirationDate": *************}], "pendingCharges": [], "lastInvoiced": *************, "enrolledPunchCards": [{"_id": "aDq24KDQRqwY6EdCu", "originalItem": {"_id": "Ftk85wKozcMdPhLDF", "description": "Drop In", "type": "punchcard", "program": "WDZcNsjLKyJ47dKoD", "numberOfDays": "1", "category": "tuition", "amount": 40, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 40, "quantity": "1", "notes": "10/10", "type": "item", "datesUsed": [0]}]}, "documentItems": {"kQTiz4ePQj7CHeQgq": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "So5fgi6beeSsHLJzN", "personName": "<PERSON>"}}, "q72p22YNRH9CD6SNc": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "So5fgi6beeSsHLJzN", "personName": "<PERSON>"}}}, "defaultGroupId": "ntZyn2XRT8JZHCjtL", "checkInGroupId": null, "checkInGroupName": null, "checkedIn": false, "checkedInById": "HC47XM7skHtPCiTSg", "checkedInOutTime": *************, "presenceLastGroupId": "ntZyn2XRT8JZHCjtL", "previousClassroomGroupId": null, "timestamps": {"moveMomentTimestamp": {"momentId": "KyAZz8PEwemy35f2D", "timestamp": *************, "groupId": null, "oldGroupId": "ntZyn2XRT8JZHCjtL"}}, "invoiceReminders": {"emailsSent": 10, "lastSentTime": *************}}, {"_id": "Eq83voMteoqhXwELv", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "Cross", "designations": [], "type": "person", "profileData": {"birthday": *************, "gender": "Female", "studentGrade": "K", "document1": "Yes", "photoAttestation": "Yes"}, "createdBy": "SYSTEM", "createdAt": *************, "orgId": "9uZQpotkrDLbnr4aS", "inActive": false, "billing": {"enrolledPunchCards": [{"_id": "vNv7L2uNATwWxHC3v", "originalItem": {"_id": "PEbJhPZBPoDP3qqqy", "description": "After Care 10-Day Punch Card", "type": "punchcard", "program": "fAXB2y3crWFrzq6KD", "numberOfDays": "10", "amount": 360, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 360, "quantity": "1", "notes": "", "type": "item", "appliedDiscounts": [{"type": "discount", "amount": 36, "source": "coupon", "originalAllocation": {"id": "NC8iRdsdQZuk5rPnj", "allocationType": "discount", "amount": 10, "amountType": "percent", "discountType": "siblingDiscount", "allocationDescription": "Sibling Discount", "discountAmount": 36}}], "datesUsed": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}], "pendingCharges": [], "lastInvoiced": *************}, "documentItems": {"FWK54s82BTZpvSPhy": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "Drptv6g2AmZxmKEGW", "personName": "<PERSON>"}}, "J8qpgqp2cpJGj8TQc": {"createdAt": *************, "createdByPersonId": "Drptv6g2AmZxmKEGW", "createdByUserId": "ktbPM6uuJpu8RMtAe", "repositoryKey": "9uZQpotkrDLbnr4aS/ktbPM6uuJpu8RMtAe/SKKePTQ0wgU1q3k1NKJd", "token": "SKKePTQ0wgU1q3k1NKJd"}, "CLxv8KYnTH6PQejGw": {"createdAt": *************, "createdByPersonId": "Drptv6g2AmZxmKEGW", "createdByUserId": "ktbPM6uuJpu8RMtAe", "repositoryKey": "9uZQpotkrDLbnr4aS/ktbPM6uuJpu8RMtAe/ob3kerUuEoFGG7DIvAPI", "token": "ob3kerUuEoFGG7DIvAPI"}}, "defaultGroupId": "Bm7b3sGtWy66ETT8E", "checkInGroupId": null, "checkInGroupName": null, "checkedIn": false, "checkedInOutTime": 1698356779905, "presenceLastGroupId": "Bm7b3sGtWy66ETT8E", "previousClassroomGroupId": null, "timestamps": {"moveMomentTimestamp": {"momentId": "7TZFehyJiXNdAokQ9", "timestamp": 1698356779957, "groupId": null, "oldGroupId": "Bm7b3sGtWy66ETT8E"}}}, {"_id": "tiuGduBhmpJakkAcx", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "designations": [], "defaultGroupId": "gJYvsbNA8v7ANvS2P", "type": "person", "profileData": {"birthday": *************, "gender": "Female", "studentGrade": "5", "document1": "Yes", "photoAttestation": "Yes"}, "createdBy": "SYSTEM", "createdAt": *************, "orgId": "mDN7x8LzZDTmGpctP", "inActive": false, "billing": {"enrolledPlans": [{"_id": "AHchv4iR7sBku2LEi", "planDetails": {"_id": "AHchv4iR7sBku2LEi", "description": "Before School", "type": "plan", "program": "skb5YvziKPBjvgoyx", "frequency": "scaledMonthly", "category": "tuition", "amount": 180, "scaledAmounts": [73, 116, 152, 169, 180], "ledgerAccountName": "4013", "details": {"startTime": "7:00 am", "endTime": "8:15 am", "regStartDate": *************, "regEndDate": *************, "grades": ["1", "2", "3", "4", "5"], "scheduleType": "Lm2tqkvGeTXaoi7cX", "dateType": "timePeriod", "timePeriod": "yx2eZZgpeZzb8G2vW"}}, "enrollmentDate": *************, "allocations": [], "createdAt": *************, "reservationId": "JWMDJQwNzG8u9mXZq", "enrollmentForecastStartDate": *************}], "pendingCharges": [], "lastInvoiced": *************, "enrolledPunchCards": [{"_id": "ho3zpq2nfohLrm7at", "originalItem": {"_id": "GPESsJQZrfBZve6zf", "description": "Drop In", "type": "punchcard", "program": "skb5YvziKPBjvgoyx", "numberOfDays": "1", "category": "tuition", "amount": 30, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 30, "quantity": "1", "notes": "", "type": "item", "datesUsed": [0]}, {"_id": "PWrEajKoydAn3pKmC", "originalItem": {"_id": "GPESsJQZrfBZve6zf", "description": "Drop In", "type": "punchcard", "program": "skb5YvziKPBjvgoyx", "numberOfDays": "1", "category": "tuition", "amount": 30, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 30, "quantity": "1", "notes": "", "type": "item", "datesUsed": [0]}]}, "checkInGroupId": null, "checkInGroupName": null, "checkedIn": false, "checkedInById": "6TvBePNKukXgywJQR", "checkedInOutTime": *************, "presenceLastGroupId": "gJYvsbNA8v7ANvS2P", "previousClassroomGroupId": null, "timestamps": {"moveMomentTimestamp": {"momentId": "Wg3P2aCFhiqp9x75R", "timestamp": *************, "groupId": null, "oldGroupId": "gJYvsbNA8v7ANvS2P"}}, "invoiceReminders": {"emailsSent": 1, "lastSentTime": *************}, "waitlistAddedDate": null}, {"_id": "D9iD6mPj96azaBgQg", "firstName": "<PERSON>", "lastName": "Istomin-Wood", "designations": [], "defaultGroupId": "DbbkFMrPhAFmNjhKW", "type": "person", "profileData": {"birthday": *************, "gender": "Male", "studentGrade": "5", "document1": "Yes", "photoAttestation": "No"}, "createdBy": "SYSTEM", "createdAt": *************, "orgId": "orEBz3k4e2vRRvQC7", "inActive": false, "billing": {"enrolledPlans": [{"_id": "a7y3eaSckAtRJbKKd", "planDetails": {"_id": "a7y3eaSckAtRJbKKd", "description": "After School", "type": "plan", "program": "tHMpzYizW4JRw6WsC", "frequency": "scaledMonthly", "category": "tuition", "amount": 398, "scaledAmounts": [163, 254, 333, 372, 398], "ledgerAccountName": "4012", "details": {"startTime": "3:24 pm", "endTime": "6:00 pm", "regStartDate": *************, "grades": ["K", "1", "2", "3", "4", "5", "6"], "scheduleType": "cmy9CJcLEXarkF5Dn", "dateType": "timePeriod", "timePeriod": "ztauJQfFkpFmwsMAo"}}, "enrollmentDate": *************, "allocations": [], "createdAt": *************, "reservationId": "rqPDedSrH7ea5DfpQ", "enrollmentForecastStartDate": *************, "enrollmentForecastEndDate": *************, "expirationDate": *************, "overrideRate": null, "updatedAt": *************, "updatedBy": "knML8Hv4M9c9Qk7v7"}, {"_id": "a7y3eaSckAtRJbKKd", "planDetails": {"_id": "a7y3eaSckAtRJbKKd", "description": "After School", "type": "plan", "program": "tHMpzYizW4JRw6WsC", "frequency": "scaledMonthly", "category": "tuition", "amount": 398, "scaledAmounts": [163, 254, 333, 372, 398], "ledgerAccountName": "4012", "details": {"startTime": "3:24 pm", "endTime": "6:00 pm", "regStartDate": *************, "grades": ["K", "1", "2", "3", "4", "5", "6"], "scheduleType": "cmy9CJcLEXarkF5Dn", "dateType": "timePeriod", "timePeriod": "ztauJQfFkpFmwsMAo"}}, "enrollmentDate": *************, "allocations": [], "createdAt": *************, "reservationId": "AjCSwFCrJRf2QEBYv", "enrollmentForecastStartDate": *************}], "pendingCharges": [], "lastInvoiced": *************, "enrolledPunchCards": [{"_id": "t5QCzHftz993Mzrai", "originalItem": {"_id": "4ztg6qnch58ngtMxb", "description": "Drop In", "type": "punchcard", "program": "tHMpzYizW4JRw6WsC", "numberOfDays": "1", "category": "tuition", "amount": 40, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 40, "quantity": "1", "notes": "", "type": "item", "datesUsed": [0]}, {"_id": "jHJjwmdCug9xC8yqx", "originalItem": {"_id": "4ztg6qnch58ngtMxb", "description": "Drop In", "type": "punchcard", "program": "tHMpzYizW4JRw6WsC", "numberOfDays": "1", "category": "tuition", "amount": 40, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 40, "quantity": "1", "notes": "", "type": "item", "datesUsed": [0]}]}, "documentItems": {"u3x2xrozAvsF7M7ff": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "gH4FZBJCkFFxX8GAn", "personName": "<PERSON>"}, "approvedAt": *************, "approvedByPersonId": "4miMBqxdjKYpztLE9", "approvedByUserId": "5Z9DAoFWSGFnRnJzz"}, "ztNRu9T5mfauuGk8q": {"createdAt": *************, "createdByPersonId": "gH4FZBJCkFFxX8GAn", "createdByUserId": "NTJusQbZK9yR7duP4", "repositoryKey": "orEBz3k4e2vRRvQC7/NTJusQbZK9yR7duP4/bF954Kn8gLV5WTqk4EAM", "token": "bF954Kn8gLV5WTqk4EAM", "approvedAt": *************, "approvedByPersonId": "4miMBqxdjKYpztLE9", "approvedByUserId": "5Z9DAoFWSGFnRnJzz"}, "cr9J4jgagJx5o7adC": {"createdAt": *************, "createdByPersonId": "gH4FZBJCkFFxX8GAn", "createdByUserId": "NTJusQbZK9yR7duP4", "repositoryKey": "orEBz3k4e2vRRvQC7/NTJusQbZK9yR7duP4/ys3eJ3MToyB2dmXwQLRr", "token": "ys3eJ3MToyB2dmXwQLRr", "approvedAt": 1692923165701, "approvedByPersonId": "4miMBqxdjKYpztLE9", "approvedByUserId": "5Z9DAoFWSGFnRnJzz"}}, "documentExemptions": ["CPieC9YGQtgvDTtvF"], "invoiceReminders": {"emailsSent": 10, "lastSentTime": 1694765162928}, "checkInGroupId": null, "checkInGroupName": null, "checkedIn": false, "checkedInOutTime": 1698871448649, "presenceLastGroupId": "DbbkFMrPhAFmNjhKW", "previousClassroomGroupId": null, "timestamps": {"moveMomentTimestamp": {"momentId": "2XFMiXjd3hFm8JjAB", "timestamp": 1698871448706, "groupId": null, "oldGroupId": "DbbkFMrPhAFmNjhKW"}}, "checkedInById": "cALcn5etD8GBqr9mQ", "waitlistAddedDate": null}, {"_id": "5xapBAzHHC7o5Jkw6", "firstName": "Daria", "lastName": "Istomina-Wood", "designations": [], "defaultGroupId": "DbbkFMrPhAFmNjhKW", "type": "person", "profileData": {"birthday": *************, "gender": "Female", "studentGrade": "2", "document1": "Yes", "photoAttestation": "No"}, "createdBy": "SYSTEM", "createdAt": *************, "orgId": "orEBz3k4e2vRRvQC7", "inActive": false, "billing": {"enrolledPlans": [{"_id": "a7y3eaSckAtRJbKKd", "planDetails": {"_id": "a7y3eaSckAtRJbKKd", "description": "After School", "type": "plan", "program": "tHMpzYizW4JRw6WsC", "frequency": "scaledMonthly", "category": "tuition", "amount": 398, "scaledAmounts": [163, 254, 333, 372, 398], "ledgerAccountName": "4012", "details": {"startTime": "3:24 pm", "endTime": "6:00 pm", "regStartDate": *************, "grades": ["K", "1", "2", "3", "4", "5", "6"], "scheduleType": "cmy9CJcLEXarkF5Dn", "dateType": "timePeriod", "timePeriod": "ztauJQfFkpFmwsMAo"}}, "enrollmentDate": *************, "allocations": [{"id": "Pq5A6RAYvvFttpfaX", "allocationType": "discount", "amount": 10, "amountType": "percent", "discountType": "siblingDiscount", "allocationDescription": "Sibling Discount", "discountAmount": 25.4}], "createdAt": *************, "reservationId": "h2B5kPgD5H8qx6CBR", "enrollmentForecastStartDate": *************, "enrollmentForecastEndDate": *************, "expirationDate": *************}, {"_id": "a7y3eaSckAtRJbKKd", "planDetails": {"_id": "a7y3eaSckAtRJbKKd", "description": "After School", "type": "plan", "program": "tHMpzYizW4JRw6WsC", "frequency": "scaledMonthly", "category": "tuition", "amount": 398, "scaledAmounts": [163, 254, 333, 372, 398], "ledgerAccountName": "4012", "details": {"startTime": "3:24 pm", "endTime": "6:00 pm", "regStartDate": *************, "grades": ["K", "1", "2", "3", "4", "5", "6"], "scheduleType": "cmy9CJcLEXarkF5Dn", "dateType": "timePeriod", "timePeriod": "ztauJQfFkpFmwsMAo"}}, "enrollmentDate": *************, "allocations": [{"id": "Pq5A6RAYvvFttpfaX", "allocationType": "discount", "amount": 10, "amountType": "percent", "discountType": "siblingDiscount", "allocationDescription": "Sibling Discount", "discountAmount": 25.4}], "createdAt": *************, "reservationId": "eS892NL7hDiBk4Ksq", "enrollmentForecastStartDate": *************}], "lastInvoiced": *************, "pendingCharges": [], "enrolledPunchCards": [{"_id": "GBnCYayjEm4qTXPme", "originalItem": {"_id": "4ztg6qnch58ngtMxb", "description": "Drop In", "type": "punchcard", "program": "tHMpzYizW4JRw6WsC", "numberOfDays": "1", "category": "tuition", "amount": 40, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 40, "quantity": "1", "notes": "", "type": "item", "datesUsed": [0]}, {"_id": "ZomQZWm8Ft3F3nM29", "originalItem": {"_id": "4ztg6qnch58ngtMxb", "description": "Drop In", "type": "punchcard", "program": "tHMpzYizW4JRw6WsC", "numberOfDays": "1", "category": "tuition", "amount": 40, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 40, "quantity": "1", "notes": "", "type": "item", "datesUsed": [0]}, {"_id": "et2oCknpjsL2Rpjyn", "originalItem": {"_id": "4ztg6qnch58ngtMxb", "description": "Drop In", "type": "punchcard", "program": "tHMpzYizW4JRw6WsC", "numberOfDays": "1", "category": "tuition", "amount": 40, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 40, "quantity": "1", "notes": "", "type": "item", "datesUsed": [0]}]}, "documentItems": {"u3x2xrozAvsF7M7ff": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "gH4FZBJCkFFxX8GAn", "personName": "<PERSON>"}, "approvedAt": *************, "approvedByPersonId": "4miMBqxdjKYpztLE9", "approvedByUserId": "5Z9DAoFWSGFnRnJzz"}, "ztNRu9T5mfauuGk8q": {"createdAt": *************, "createdByPersonId": "gH4FZBJCkFFxX8GAn", "createdByUserId": "NTJusQbZK9yR7duP4", "repositoryKey": "orEBz3k4e2vRRvQC7/NTJusQbZK9yR7duP4/ytc2nBh0vdrhZ3u7B4Da", "token": "ytc2nBh0vdrhZ3u7B4Da", "approvedAt": *************, "approvedByPersonId": "4miMBqxdjKYpztLE9", "approvedByUserId": "5Z9DAoFWSGFnRnJzz"}, "cr9J4jgagJx5o7adC": {"createdAt": *************, "createdByPersonId": "gH4FZBJCkFFxX8GAn", "createdByUserId": "NTJusQbZK9yR7duP4", "repositoryKey": "orEBz3k4e2vRRvQC7/NTJusQbZK9yR7duP4/yKl9Fz0eozI2CVDpKHL7", "token": "yKl9Fz0eozI2CVDpKHL7", "approvedAt": 1692923338900, "approvedByPersonId": "4miMBqxdjKYpztLE9", "approvedByUserId": "5Z9DAoFWSGFnRnJzz"}}, "invoiceReminders": {"emailsSent": 10, "lastSentTime": 1694765155754}, "checkInGroupId": null, "checkInGroupName": null, "checkedIn": false, "checkedInOutTime": 1698871449080, "presenceLastGroupId": "DbbkFMrPhAFmNjhKW", "previousClassroomGroupId": null, "timestamps": {"moveMomentTimestamp": {"momentId": "v374pdrY7QDhRGDky", "timestamp": 1698871449157, "groupId": null, "oldGroupId": "DbbkFMrPhAFmNjhKW"}}, "checkedInById": "cALcn5etD8GBqr9mQ", "waitlistAddedDate": null}, {"_id": "Hgf3Hqtgfd9yoeavo", "firstName": "Ashton", "lastName": "<PERSON>", "designations": [], "defaultGroupId": "fY5Jbi9vwMMcDQo8Q", "type": "person", "profileData": {"birthday": *************, "gender": "Male", "studentGrade": "K", "document1": "Yes", "photoAttestation": "Yes"}, "createdBy": "SYSTEM", "createdAt": *************, "orgId": "zRmHbZaQSinBvwdRE", "inActive": false, "billing": {"enrolledPunchCards": [{"_id": "dNZBTueBy3CZbRDEn", "originalItem": {"_id": "PEbJhPZBPoDP3qqqy", "description": "Before/After Care 10-Day Punch Card", "type": "punchcard", "program": "huPQWjWjfEGh3rYG6", "numberOfDays": "10", "amount": 315, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 315, "quantity": "1", "notes": "", "type": "item", "datesUsed": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}], "pendingCharges": [], "lastInvoiced": *************}, "documentItems": {"6obWTKiEvCX7f8MbQ": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "p97bttR4RhCQYx6Mn", "personName": "Brittanie Smerka"}}, "F4vJMMbNXw4bmeWW5": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "p97bttR4RhCQYx6Mn", "personName": "Brittanie Smerka"}}, "WNyG43AGHd5CPkhSi": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "p97bttR4RhCQYx6Mn", "personName": "Brittanie Smerka"}}, "5un5mvfp5zD4Mq26S": {"createdAt": *************, "createdByPersonId": "p97bttR4RhCQYx6Mn", "createdByUserId": "8uRgicYN6SAYFZBvk", "repositoryKey": "zRmHbZaQSinBvwdRE/8uRgicYN6SAYFZBvk/12xT6R0VnixDTNdFT5Rz", "token": "12xT6R0VnixDTNdFT5Rz"}}, "checkInGroupId": null, "checkInGroupName": null, "checkedIn": false, "checkedInOutTime": 1695215628719, "presenceLastGroupId": "fY5Jbi9vwMMcDQo8Q", "previousClassroomGroupId": null, "timestamps": {"moveMomentTimestamp": {"momentId": "ETpBmM3zBwe5w7zK3", "timestamp": 1695215628750, "groupId": null, "oldGroupId": "fY5Jbi9vwMMcDQo8Q"}}}, {"_id": "cYpZAykXZBtzpH6gb", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "designations": [], "defaultGroupId": "rkigXAyTJoLsy3LfX", "type": "person", "profileData": {"birthday": 1458360000000, "gender": "Female", "studentGrade": "2", "document1": "Yes", "photoAttestation": "Yes", "healthInformation": {"primaryDoctor": "", "primaryDoctorPhone": ""}, "homeSchool": "Bethel Manor Elementary"}, "createdBy": "SYSTEM", "createdAt": *************, "orgId": "RSyNjABpiJuekeKtS", "inActive": false, "billing": {"enrolledPlans": [{"_id": "JuQ2N3CjZ4vaHK5Bg", "planDetails": {"_id": "JuQ2N3CjZ4vaHK5Bg", "description": "After School", "type": "plan", "program": "guEPTMJLi9iWE3ndM", "frequency": "scaledMonthly", "category": "tuition", "amount": 220, "scaledAmounts": [59, 112, 159, 193, 220], "ledgerAccountName": "4012", "details": {"startTime": "3:21 pm", "endTime": "6:00 pm", "regStartDate": *************, "grades": ["K", "1", "2", "3", "4", "5"], "scheduleType": "ivEwGqazmcWoWv6oJ", "dateType": "timePeriod", "timePeriod": "kMGj2PLNXwGTtsqFW"}}, "enrollmentDate": *************, "allocations": [{"allocationType": "discount", "amount": 15, "amountType": "dollars", "discountType": "bundle", "allocationDescription": "Bundle: After School and Before School", "discountAmount": 15}, {"allocationType": "discount", "amount": 50, "amountType": "percent", "discountType": "districtDiscount", "allocationDescription": "Discount: District Discount", "id": "JdfkJogThJAazvz3t"}], "createdAt": *************, "reservationId": "YMTpnxirH98TNkwQF", "enrollmentForecastStartDate": *************, "bundlePlanId": "PpynLRnoPjmbG9ucb", "enrollmentForecastEndDate": *************, "overrideRate": null, "updatedAt": *************, "updatedBy": "K2zKEfyE3hQEmzHNw", "expirationDate": *************}, {"_id": "S8Ff5ATxc4jdXbebo", "planDetails": {"_id": "S8Ff5ATxc4jdXbebo", "description": "Before School", "type": "plan", "program": "guEPTMJLi9iWE3ndM", "frequency": "scaledMonthly", "category": "tuition", "amount": 163, "scaledAmounts": [44, 83, 118, 143, 163], "ledgerAccountName": "4013", "details": {"startTime": "6:00 am", "endTime": "8:45 am", "regStartDate": *************, "grades": ["K", "1", "2", "3", "4", "5"], "scheduleType": "Lm2tqkvGeTXaoi7cX", "dateType": "timePeriod", "timePeriod": "kMGj2PLNXwGTtsqFW"}}, "enrollmentDate": *************, "allocations": [{"allocationType": "discount", "amount": 15, "amountType": "dollars", "discountType": "bundle", "allocationDescription": "Bundle: After School and Before School", "discountAmount": 15}, {"allocationType": "discount", "amount": 25, "amountType": "percent", "discountType": "militaryDiscount", "allocationDescription": "Discount: Military Discount", "id": "EJd7ntTuHLQAFnDJt"}], "createdAt": *************, "reservationId": "AemBdaKMsmAooow9g", "enrollmentForecastStartDate": *************, "bundlePlanId": "PpynLRnoPjmbG9ucb", "enrollmentForecastEndDate": *************, "overrideRate": null, "updatedAt": *************, "updatedBy": "vMhKFfuTbxdNR7Gsy", "expirationDate": *************}, {"_id": "S8Ff5ATxc4jdXbebo", "planDetails": {"_id": "S8Ff5ATxc4jdXbebo", "description": "Before School", "type": "plan", "program": "guEPTMJLi9iWE3ndM", "frequency": "scaledMonthly", "category": "tuition", "amount": 163, "scaledAmounts": [44, 83, 118, 143, 163], "ledgerAccountName": "4013", "details": {"startTime": "6:00 am", "endTime": "8:45 am", "regStartDate": *************, "grades": ["K", "1", "2", "3", "4", "5"], "scheduleType": "Lm2tqkvGeTXaoi7cX", "dateType": "timePeriod", "timePeriod": "kMGj2PLNXwGTtsqFW"}}, "enrollmentDate": *************, "allocations": [{"allocationType": "discount", "amount": 25, "amountType": "percent", "discountType": "militaryDiscount", "allocationDescription": "Discount: Military Discount", "id": "EJd7ntTuHLQAFnDJt"}], "createdAt": *************, "reservationId": "4WAjgjz73vfpy2bhZ", "enrollmentForecastStartDate": *************, "enrollmentForecastEndDate": *************, "expirationDate": *************}, {"_id": "S8Ff5ATxc4jdXbebo", "planDetails": {"_id": "S8Ff5ATxc4jdXbebo", "description": "Before School", "type": "plan", "program": "guEPTMJLi9iWE3ndM", "frequency": "scaledMonthly", "category": "tuition", "amount": 163, "scaledAmounts": [44, 83, 118, 143, 163], "ledgerAccountName": "4013", "details": {"startTime": "6:00 am", "endTime": "8:45 am", "regStartDate": *************, "grades": ["K", "1", "2", "3", "4", "5"], "scheduleType": "Lm2tqkvGeTXaoi7cX", "dateType": "timePeriod", "timePeriod": "kMGj2PLNXwGTtsqFW"}}, "enrollmentDate": *************, "allocations": [{"allocationType": "discount", "amount": 50, "amountType": "percent", "discountType": "districtDiscount", "allocationDescription": "Discount: District Discount", "id": "ni8QwpTRYMKEBZET4"}], "createdAt": *************, "reservationId": "CHjWbPcknYPx8ZdTf", "enrollmentForecastStartDate": *************, "enrollmentForecastEndDate": null, "overrideRate": null, "updatedAt": *************, "updatedBy": "K2zKEfyE3hQEmzHNw"}], "pendingCharges": [], "lastInvoiced": *************, "enrolledPunchCards": [{"_id": "zegq3ho7sEXfRgp9b", "originalItem": {"_id": "SJrMCAiFYmBGbqq7q", "description": "Drop In", "type": "punchcard", "program": "guEPTMJLi9iWE3ndM", "numberOfDays": "1", "category": "tuition", "amount": 30, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 30, "quantity": "1", "notes": "", "type": "item", "datesUsed": [0]}, {"_id": "Jz4DnZDNEsNPXqkHM", "originalItem": {"_id": "SJrMCAiFYmBGbqq7q", "description": "Drop In", "type": "punchcard", "program": "guEPTMJLi9iWE3ndM", "numberOfDays": "1", "category": "tuition", "amount": 30, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 30, "quantity": "1", "notes": "Drop In for 10/26/23", "type": "item", "datesUsed": [0]}], "billingNotes": "10/26/23 Mom called in to add drop in for 10/26/23, added 30.00 charge and sent invoice. TS"}, "invoiceReminders": {"emailsSent": 9, "lastSentTime": *************}, "documentItems": {"cYbPTfZr4pew6qQaY": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "Z4YvgbRMzxsfroW8X", "personName": "<PERSON>"}}, "qrthkcrak5SoPv6pH": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "Z4YvgbRMzxsfroW8X", "personName": "<PERSON>"}}, "qQEYQBd3mHpFxWnN4": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "Z4YvgbRMzxsfroW8X", "personName": "<PERSON>"}}, "4h4WEQGswGwsj5ccs": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "Z4YvgbRMzxsfroW8X", "personName": "<PERSON>"}}, "beJozFiQtHvH5Py88": {"templateOptionResult": {"action": "ack", "date": 1696573963616, "personId": "Z4YvgbRMzxsfroW8X", "personName": "<PERSON>"}}}, "checkInGroupId": null, "checkInGroupName": null, "checkedIn": false, "checkedInById": "BrMiveGEiaxoi9M9Q", "checkedInOutTime": 1698928223803, "presenceLastGroupId": "rkigXAyTJoLsy3LfX", "previousClassroomGroupId": null, "timestamps": {"moveMomentTimestamp": {"momentId": "cJeGDTrBcDaSSshmj", "timestamp": 1698928223852, "groupId": null, "oldGroupId": "rkigXAyTJoLsy3LfX"}}}, {"_id": "v9uzJDanBCh8ka4sR", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON>", "designations": [], "defaultGroupId": "CC3fzCTCbzRMJGys5", "type": "person", "profileData": {"birthday": *************, "gender": "Female", "studentGrade": "5", "document1": "Yes", "photoAttestation": "Yes"}, "createdBy": "SYSTEM", "createdAt": *************, "orgId": "NcQvtvp2KWBpAFopN", "inActive": false, "billing": {"enrolledPunchCards": [{"_id": "6hisKrfch9Rbrpoum", "originalItem": {"_id": "PEbJhPZBPoDP3qqqy", "description": "Before/After Care 10-Day Punch Card", "type": "punchcard", "program": "GumirkZjpvRLLsEPX", "numberOfDays": "10", "amount": 270, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 270, "quantity": "1", "notes": "", "type": "item", "datesUsed": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}], "pendingCharges": [], "lastInvoiced": *************}, "documentItems": {"N8J2nK2u4j3rg3uMK": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "P5ECD65De7Eo86Mqf", "personName": "<PERSON> "}}, "vZ8wFASBFB3nwp6eK": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "P5ECD65De7Eo86Mqf", "personName": "<PERSON> "}}}}, {"_id": "v73NizFWCks2Dy8GA", "firstName": "<PERSON>", "lastName": "<PERSON>", "designations": [], "defaultGroupId": "CC3fzCTCbzRMJGys5", "type": "person", "profileData": {"birthday": *************, "gender": "Male", "studentGrade": "2", "document1": "Yes", "photoAttestation": "Yes"}, "createdBy": "SYSTEM", "createdAt": *************, "orgId": "NcQvtvp2KWBpAFopN", "inActive": false, "billing": {"enrolledPunchCards": [{"_id": "vKFQ8CyrMMKFRACGj", "originalItem": {"_id": "PEbJhPZBPoDP3qqqy", "description": "Before/After Care 10-Day Punch Card", "type": "punchcard", "program": "GumirkZjpvRLLsEPX", "numberOfDays": "10", "amount": 270, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 270, "quantity": "1", "notes": "", "type": "item", "appliedDiscounts": [{"type": "discount", "amount": 27, "source": "coupon", "originalAllocation": {"id": "uRAjAoWpGdG6qbBeq", "allocationType": "discount", "amount": 10, "amountType": "percent", "discountType": "siblingDiscount", "allocationDescription": "Sibling Discount", "discountAmount": 27}}], "datesUsed": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}], "pendingCharges": [], "lastInvoiced": *************}}, {"_id": "dTsE6ZN53BnBatbTT", "firstName": "Omani", "lastName": "<PERSON>", "designations": [], "defaultGroupId": "CC3fzCTCbzRMJGys5", "type": "person", "profileData": {"birthday": *************, "gender": "Female", "studentGrade": "4", "document1": "Yes", "agencyIdentifier": "Social service", "subsidyCode": "********", "photoAttestation": "No"}, "createdBy": "K2zKEfyE3hQEmzHNw", "createdAt": *************, "orgId": "NcQvtvp2KWBpAFopN", "inActive": false, "billing": {"enrolledPunchCards": [{"_id": "sKPWRCNX4fezt53z7", "originalItem": {"_id": "PEbJhPZBPoDP3qqqy", "description": "Before/After Care 10-Day Punch Card", "type": "punchcard", "program": "GumirkZjpvRLLsEPX", "numberOfDays": "10", "amount": 270, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 270, "quantity": "1", "notes": "", "type": "item", "datesUsed": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}], "pendingCharges": [], "lastInvoiced": *************, "enrolledPlans": [{"_id": "S8Ff5ATxc4jdXbebo", "planDetails": {"_id": "S8Ff5ATxc4jdXbebo", "description": "Before School", "type": "plan", "program": "GumirkZjpvRLLsEPX", "frequency": "scaledMonthly", "category": "tuition", "amount": 163, "scaledAmounts": [44, 83, 118, 143, 163], "ledgerAccountName": "4013", "details": {"startTime": "6:00 am", "endTime": "9:00 am", "regStartDate": *************, "grades": ["K", "1", "2", "3", "4", "5"], "scheduleType": "Lm2tqkvGeTXaoi7cX", "dateType": "timePeriod", "timePeriod": "6AgjCSwzBCx8hYJ5S"}}, "enrollmentDate": *************, "allocations": [{"allocationType": "reimbursable", "amount": 100, "amountType": "percent", "reimbursementType": "virginiaDSS", "allocationDescription": "Reimbursable: Virginia DSS", "payerStartDate": *************, "payerEndDate": *************, "id": "L36t28yZuK3JchnLt"}], "createdAt": *************, "reservationId": "nhjdZSbS7gLD2aiYT", "enrollmentForecastStartDate": *************}], "billingNotes": "11/2/23 - 5 day before school schedule retroactively added with subsidy. - <PERSON><PERSON> A"}, "invoiceReminders": {"emailsSent": 10, "lastSentTime": *************}, "checkInGroupId": null, "checkInGroupName": null, "checkedIn": false, "checkedInById": "BnZtWEv5bDfMLv2th", "checkedInOutTime": ************9, "presenceLastGroupId": "CC3fzCTCbzRMJGys5", "previousClassroomGroupId": null, "timestamps": {"moveMomentTimestamp": {"momentId": "5wkKx9fdRBvoBAu5j", "timestamp": 1698929177472, "groupId": null, "oldGroupId": "CC3fzCTCbzRMJGys5"}}, "avatarPath": "NcQvtvp2KWBpAFopN/dzDCdMtEojuLTb2va/bMdrrjspt8nQnaZ4t", "avatarToken": "bMdrrjspt8nQnaZ4t", "lastInteractionDate": 1696852080000, "lastMoment": {"attributionPersonId": null, "momentType": "comment", "comment": "Our craft of the day 💐 ", "time": "7:48 am", "date": "10/9/2023", "sortStamp": 1696852080000, "momentTypePretty": "Comment", "createdAt": 1696852228404, "createdBy": "dzDCdMtEojuLTb2va", "attributionName": "Right at School Yorktown", "createdByPersonId": "jA5v6sv24wGpWxsn6", "orgId": "NcQvtvp2KWBpAFopN", "taggedPeople": ["jA5v6sv24wGpWxsn6", "f9Pw79NovnJPscTAx", "8wis5A8JK5PgMhotP", "2t3a9apdWfLCu9Y65", "GfQJAPaWaDK4rPACi", "zhwfkNyaBKPegndE5", "wpLuzJc2gTaCEucTX", "hFTpZZ3NZm885MXrN", "Fqi6CrgMypaSsLYoT", "qCWRksdNedS3S9x5o", "Pzqb6qwv9P3kj7S3p", "uNbvK7R9A9Yjq4Gja", "pTq3hMnmNEfYPYRrX", "6hThDcGMhvpFfcetH", "ntqsXxRKjC7dNb72a", "W4ty9EvBCHG6EhB3t", "zbTKK5E7dEjoeAoar", "rdMLDBSDYc5BP8xNK", "2cXL8Txwc6gPvy8za", "Fs336BSppFsAYyPxy", "5fKCasjCjiMF6eWeQ", "nczC5fmPAT49696AN", "J7vSf2zxsedmZ9EX5", "GDn6L2GSTrrBnAXY5", "PjooSKoH5xqAbTBPk", "dTsE6ZN53BnBatbTT", "tkWRoDZBEeSK2ju3N", "T4ysuWBYEAfSp5SgD"], "mediaFiles": [{"mediaUrl": "https://s3.amazonaws.com/tendlymruploads/NcQvtvp2KWBpAFopN/dzDCdMtEojuLTb2va/gYLYuQ2bMYcXmMZNx", "mediaToken": "gYLYuQ2bMYcXmMZNx", "mediaFileType": "image", "mediaPath": "NcQvtvp2KWBpAFopN/dzDCdMtEojuLTb2va/gYLYuQ2bMYcXmMZNx"}, {"mediaUrl": "https://s3.amazonaws.com/tendlymruploads/NcQvtvp2KWBpAFopN/dzDCdMtEojuLTb2va/ze2jwJGdYza2bDnAr", "mediaToken": "ze2jwJGdYza2bDnAr", "mediaFileType": "image", "mediaPath": "NcQvtvp2KWBpAFopN/dzDCdMtEojuLTb2va/ze2jwJGdYza2bDnAr"}], "_id": "HmY4xkkS93ZQ3dPzd", "orgName": "Yorktown Elementary"}, "lastMomentByType": {"comment": {"attributionPersonId": null, "momentType": "comment", "comment": "Our craft of the day 💐 ", "time": "7:48 am", "date": "10/9/2023", "sortStamp": 1696852080000, "momentTypePretty": "Comment", "createdAt": 1696852228404, "createdBy": "dzDCdMtEojuLTb2va", "attributionName": "Right at School Yorktown", "createdByPersonId": "jA5v6sv24wGpWxsn6", "orgId": "NcQvtvp2KWBpAFopN", "taggedPeople": ["jA5v6sv24wGpWxsn6", "f9Pw79NovnJPscTAx", "8wis5A8JK5PgMhotP", "2t3a9apdWfLCu9Y65", "GfQJAPaWaDK4rPACi", "zhwfkNyaBKPegndE5", "wpLuzJc2gTaCEucTX", "hFTpZZ3NZm885MXrN", "Fqi6CrgMypaSsLYoT", "qCWRksdNedS3S9x5o", "Pzqb6qwv9P3kj7S3p", "uNbvK7R9A9Yjq4Gja", "pTq3hMnmNEfYPYRrX", "6hThDcGMhvpFfcetH", "ntqsXxRKjC7dNb72a", "W4ty9EvBCHG6EhB3t", "zbTKK5E7dEjoeAoar", "rdMLDBSDYc5BP8xNK", "2cXL8Txwc6gPvy8za", "Fs336BSppFsAYyPxy", "5fKCasjCjiMF6eWeQ", "nczC5fmPAT49696AN", "J7vSf2zxsedmZ9EX5", "GDn6L2GSTrrBnAXY5", "PjooSKoH5xqAbTBPk", "dTsE6ZN53BnBatbTT", "tkWRoDZBEeSK2ju3N", "T4ysuWBYEAfSp5SgD"], "mediaFiles": [{"mediaUrl": "https://s3.amazonaws.com/tendlymruploads/NcQvtvp2KWBpAFopN/dzDCdMtEojuLTb2va/gYLYuQ2bMYcXmMZNx", "mediaToken": "gYLYuQ2bMYcXmMZNx", "mediaFileType": "image", "mediaPath": "NcQvtvp2KWBpAFopN/dzDCdMtEojuLTb2va/gYLYuQ2bMYcXmMZNx"}, {"mediaUrl": "https://s3.amazonaws.com/tendlymruploads/NcQvtvp2KWBpAFopN/dzDCdMtEojuLTb2va/ze2jwJGdYza2bDnAr", "mediaToken": "ze2jwJGdYza2bDnAr", "mediaFileType": "image", "mediaPath": "NcQvtvp2KWBpAFopN/dzDCdMtEojuLTb2va/ze2jwJGdYza2bDnAr"}], "_id": "HmY4xkkS93ZQ3dPzd", "orgName": "Yorktown Elementary"}}, "engagements": [{"orgId": "NcQvtvp2KWBpAFopN", "type": "app", "subType": "created_moment", "detail": "comment_with_media", "momentId": "HmY4xkkS93ZQ3dPzd", "createdBy": "jA5v6sv24wGpWxsn6", "createdAt": 1696852229568, "targetPersonId": "dTsE6ZN53BnBatbTT", "sourcePersonId": "jA5v6sv24wGpWxsn6", "_id": "G32LQnZYHGZjBJRuD"}]}, {"_id": "hnAt5d4hew85mTvmA", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "designations": [], "defaultGroupId": "2nx5QPv9nRNw4etEH", "type": "person", "profileData": {"birthday": *************, "gender": "Male", "studentGrade": "3", "document1": "Yes", "photoAttestation": "Yes"}, "createdBy": "SYSTEM", "createdAt": *************, "orgId": "aTAhaqGzDFQXN8g5e", "inActive": false, "billing": {"enrolledPunchCards": [{"_id": "nj3N2Xbnfj4th59xp", "originalItem": {"_id": "PEbJhPZBPoDP3qqqy", "description": "Before/After Care 10-Day Punch Card", "type": "punchcard", "program": "FnCys7JwbXhcpRr5T", "numberOfDays": "10", "amount": 270, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 270, "quantity": "1", "notes": "", "type": "item", "datesUsed": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}], "pendingCharges": [], "lastInvoiced": *************}, "documentItems": {"HpmhRx6EPyiTrWfuh": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "sxgmeRRpainj2oypQ", "personName": "<PERSON>"}}, "K9tWPi6sY4cSrQCTs": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "sxgmeRRpainj2oypQ", "personName": "<PERSON>"}}, "6uuqAPEddNrxBzwrp": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "SvcimHyY9FsBzWJr9", "personName": "<PERSON><PERSON><PERSON>"}}, "DNay23mYZXJLKMi3a": {"createdAt": *************, "createdByPersonId": "SvcimHyY9FsBzWJr9", "createdByUserId": "hLDddPLDxwCCPgFgS", "repositoryKey": "aTAhaqGzDFQXN8g5e/hLDddPLDxwCCPgFgS/4RtnqUd8eAcG1Dq4w3kO", "token": "4RtnqUd8eAcG1Dq4w3kO"}}, "checkInGroupId": null, "checkInGroupName": null, "checkedIn": false, "checkedInOutTime": 1698357764286, "presenceLastGroupId": "2nx5QPv9nRNw4etEH", "previousClassroomGroupId": null, "timestamps": {"moveMomentTimestamp": {"momentId": "s5jRaejWGKR9PNsbm", "timestamp": 1698357764337, "groupId": null, "oldGroupId": "2nx5QPv9nRNw4etEH"}}, "checkedInById": "Lymt24BzrAjBA3KZx"}, {"_id": "DEoRAoDtnoNr5sPza", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON>", "designations": [], "defaultGroupId": "CC3fzCTCbzRMJGys5", "type": "person", "profileData": {"birthday": *************, "gender": "Male", "studentGrade": "5", "document1": "Yes", "photoAttestation": "Yes"}, "createdBy": "SYSTEM", "createdAt": *************, "orgId": "NcQvtvp2KWBpAFopN", "inActive": false, "billing": {"enrolledPunchCards": [{"_id": "d44LAnsDHcx7iKFdR", "originalItem": {"_id": "vCdA97tx9retrgQiJ", "description": "Drop In", "type": "punchcard", "program": "GumirkZjpvRLLsEPX", "numberOfDays": "1", "category": "tuition", "amount": 30, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 30, "quantity": "1", "notes": "", "type": "item", "datesUsed": [0]}, {"_id": "aeC2MYTjm8FFMiNtz", "originalItem": {"_id": "vCdA97tx9retrgQiJ", "description": "Drop In", "type": "punchcard", "program": "GumirkZjpvRLLsEPX", "numberOfDays": "1", "category": "tuition", "amount": 30, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 30, "quantity": "1", "notes": "", "type": "item", "datesUsed": [0]}, {"_id": "exmAQTDDbgpMoxRg8", "originalItem": {"_id": "vCdA97tx9retrgQiJ", "description": "Drop In", "type": "punchcard", "program": "GumirkZjpvRLLsEPX", "numberOfDays": "1", "category": "tuition", "amount": 30, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 30, "quantity": "1", "notes": "", "type": "item", "datesUsed": [0]}], "pendingCharges": [], "lastInvoiced": *************}, "documentItems": {"vZ8wFASBFB3nwp6eK": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "8jHJwp73TmjFCJM26", "personName": "<PERSON><PERSON>"}}, "N8J2nK2u4j3rg3uMK": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "8jHJwp73TmjFCJM26", "personName": "<PERSON><PERSON>"}}}, "checkInGroupId": null, "checkInGroupName": null, "checkedIn": false, "checkedInById": "8jHJwp73TmjFCJM26", "checkedInOutTime": *************, "presenceLastGroupId": null, "previousClassroomGroupId": null, "timestamps": {"moveMomentTimestamp": {"momentId": "vxwPJzbbqNbX3hCro", "timestamp": *************, "groupId": "CC3fzCTCbzRMJGys5", "oldGroupId": null}}, "waitlistAddedDate": null}, {"_id": "5iRMCD4ApAf5axqbt", "firstName": "<PERSON>", "lastName": "<PERSON>", "designations": [], "defaultGroupId": "CC3fzCTCbzRMJGys5", "type": "person", "profileData": {"birthday": *************, "gender": "Female", "studentGrade": "1", "document1": "Yes", "photoAttestation": "Yes"}, "createdBy": "SYSTEM", "createdAt": *************, "orgId": "NcQvtvp2KWBpAFopN", "inActive": false, "billing": {"enrolledPunchCards": [{"_id": "PdA5B83yRzMxat89y", "originalItem": {"_id": "vCdA97tx9retrgQiJ", "description": "Drop In", "type": "punchcard", "program": "GumirkZjpvRLLsEPX", "numberOfDays": "1", "category": "tuition", "amount": 30, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 30, "quantity": "1", "notes": "", "type": "item", "appliedDiscounts": [{"type": "discount", "amount": 3, "source": "coupon", "originalAllocation": {"id": "QpFh4cHHnGZEtCxSY", "allocationType": "discount", "amount": 10, "amountType": "percent", "discountType": "siblingDiscount", "allocationDescription": "Sibling Discount", "discountAmount": 3}}], "datesUsed": [0]}, {"_id": "LMA4bsp3LL5XWFYYC", "originalItem": {"_id": "vCdA97tx9retrgQiJ", "description": "Drop In", "type": "punchcard", "program": "GumirkZjpvRLLsEPX", "numberOfDays": "1", "category": "tuition", "amount": 30, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 30, "quantity": "1", "notes": "", "type": "item", "datesUsed": [0]}, {"_id": "HaHidaYAZjRsEe46H", "originalItem": {"_id": "vCdA97tx9retrgQiJ", "description": "Drop In", "type": "punchcard", "program": "GumirkZjpvRLLsEPX", "numberOfDays": "1", "category": "tuition", "amount": 30, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 30, "quantity": "1", "notes": "", "type": "item", "datesUsed": [0]}], "pendingCharges": [], "lastInvoiced": *************}, "documentItems": {"vZ8wFASBFB3nwp6eK": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "8jHJwp73TmjFCJM26", "personName": "<PERSON><PERSON>"}}, "N8J2nK2u4j3rg3uMK": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "8jHJwp73TmjFCJM26", "personName": "<PERSON><PERSON>"}}, "HbKT2fvwnuoEsA6WC": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "8jHJwp73TmjFCJM26", "personName": "<PERSON><PERSON>"}}}, "waitlistAddedDate": null, "checkInGroupId": null, "checkInGroupName": null, "checkedIn": false, "checkedInById": "8jHJwp73TmjFCJM26", "checkedInOutTime": *************, "presenceLastGroupId": null, "previousClassroomGroupId": null, "timestamps": {"moveMomentTimestamp": {"momentId": "fSswX4AMrhQ3hAz7P", "timestamp": ***********95, "groupId": "CC3fzCTCbzRMJGys5", "oldGroupId": null}}}, {"_id": "omkXjgGFaj2zLoaYS", "firstName": "Charlotte", "lastName": "<PERSON>", "designations": [], "defaultGroupId": "e3Fvr9uWLeuMYL9zD", "type": "person", "profileData": {"birthday": 1474588800000, "gender": "Female", "studentGrade": "1", "document1": "<PERSON><PERSON><PERSON> carroll", "agencyIdentifier": "Unknown", "subsidyCode": "Unknown", "homeSchool": "Daybreak Primary", "photoAttestation": "Yes"}, "createdBy": "K2zKEfyE3hQEmzHNw", "createdAt": *************, "orgId": "K2dEM4ZHLuTkrRMQN", "inActive": false, "billing": {"enrolledPunchCards": [{"_id": "XReGDyjMp45A7uYP5", "originalItem": {"_id": "PEbJhPZBPoDP3qqqy", "description": "Before/After Care 10-Day Punch Card", "type": "punchcard", "program": "SP2BkLP8reokigQoM", "numberOfDays": "10", "amount": 360, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 360, "quantity": "1", "notes": "", "type": "item", "datesUsed": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}], "pendingCharges": [], "lastInvoiced": *************, "enrolledPlans": [{"_id": "XoAqnz6RB98w5P2jC", "planDetails": {"_id": "XoAqnz6RB98w5P2jC", "description": "After School", "type": "plan", "program": "SP2BkLP8reokigQoM", "frequency": "scaledMonthly", "category": "tuition", "amount": 295, "scaledAmounts": [94, 174, 235, 275, 295], "ledgerAccountName": "4012", "details": {"startTime": "3:40 pm", "endTime": "6:00 pm", "regStartDate": *************, "regEndDate": *************, "grades": ["Transitional Kindergarten", "K", "1", "2", "3", "4", "5", "6", "7", "8"], "scheduleType": "ivEwGqazmcWoWv6oJ", "dateType": "timePeriod", "timePeriod": "LxTvgDfhEnbKLdADn"}}, "enrollmentDate": *************, "allocations": [{"allocationType": "reimbursable", "amount": 205, "amountType": "dollars", "reimbursementType": "washingtonDSHS", "allocationDescription": "Reimbursable: Washington DSHS", "payerStartDate": *************, "payerEndDate": *************, "id": "9vZsDnmWSt9xuKAQw"}], "createdAt": *************, "reservationId": "nZDnwaLLNsFWhLFg5", "enrollmentForecastStartDate": *************}, {"_id": "iWbNeboHZwiqy5Wjt", "planDetails": {"_id": "iWbNeboHZwiqy5Wjt", "description": "Before School", "type": "plan", "program": "SP2BkLP8reokigQoM", "frequency": "scaledMonthly", "category": "tuition", "amount": 295, "scaledAmounts": [94, 174, 235, 275, 295], "ledgerAccountName": "4013", "details": {"startTime": "6:30 am", "endTime": "9:10 am", "regStartDate": *************, "regEndDate": *************, "grades": ["Transitional Kindergarten", "K", "1", "2", "3", "4", "5", "6"], "scheduleType": "Lm2tqkvGeTXaoi7cX", "dateType": "timePeriod", "timePeriod": "LxTvgDfhEnbKLdADn"}}, "enrollmentDate": *************, "allocations": [{"allocationType": "reimbursable", "amount": 100, "amountType": "percent", "reimbursementType": "washingtonDSHS", "allocationDescription": "Reimbursable: Washington DSHS", "payerStartDate": *************, "payerEndDate": *************, "id": "XWymt7yZdPERGNDzv"}], "createdAt": *************, "reservationId": "SkpnHR4DkxreWSHF3", "enrollmentForecastStartDate": *************}]}, "invoiceReminders": {"emailsSent": 2, "lastSentTime": *************}, "documentItems": {"fo6nNX5AEZCbNWvoc": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "9zdhDfysRBirXrdBC", "personName": "<PERSON><PERSON><PERSON> carroll"}}, "JRNPj6SXnAAt4HW32": {"templateOptionResult": {"action": "ack", "date": 1693317679336, "personId": "9zdhDfysRBirXrdBC", "personName": "<PERSON><PERSON><PERSON> carroll"}}}, "checkInGroupId": null, "checkInGroupName": null, "checkedIn": false, "checkedInById": "9zdhDfysRBirXrdBC", "checkedInOutTime": *************, "presenceLastGroupId": "e3Fvr9uWLeuMYL9zD", "previousClassroomGroupId": null, "timestamps": {"moveMomentTimestamp": {"momentId": "ZEq7NCgphixT76GAE", "timestamp": 1698940060342, "groupId": null, "oldGroupId": "e3Fvr9uWLeuMYL9zD"}}}, {"_id": "jp4zvvZyHEjsKcJnn", "firstName": "<PERSON>", "lastName": "<PERSON>", "designations": [], "defaultGroupId": "GBXCPsT7fEPHFdf8R", "type": "person", "profileData": {"birthday": *************, "gender": "Male", "studentGrade": "2", "document1": "Yes", "photoAttestation": "Yes"}, "createdBy": "SYSTEM", "createdAt": *************, "orgId": "ExfcnDEu3Td4NZSWP", "inActive": false, "billing": {"enrolledPlans": [{"_id": "S8Ff5ATxc4jdXbebo", "planDetails": {"_id": "S8Ff5ATxc4jdXbebo", "description": "Before School", "type": "plan", "program": "8c8ALB98NFvsHqX8C", "frequency": "scaledMonthly", "category": "tuition", "amount": 163, "scaledAmounts": [44, 83, 118, 143, 163], "ledgerAccountName": "4013", "details": {"startTime": "6:00 am", "endTime": "9:00 am", "regStartDate": *************, "grades": ["K", "1", "2", "3", "4", "5"], "scheduleType": "Lm2tqkvGeTXaoi7cX", "dateType": "timePeriod", "timePeriod": "WgBPRArg3iHbGs2jE"}}, "enrollmentDate": *************, "allocations": [{"allocationType": "discount", "amount": 50, "amountType": "percent", "discountType": "districtDiscount", "allocationDescription": "Discount: District Discount", "id": "BX5dEJNSFazPJknfC"}], "createdAt": *************, "reservationId": "9rtywBxoohqPrQs6W", "enrollmentForecastStartDate": *************, "enrollmentForecastEndDate": *************, "overrideRate": null, "updatedAt": *************, "updatedBy": "eCxkeyCoi7B9rAWMi", "expirationDate": *************}, {"_id": "S8Ff5ATxc4jdXbebo", "planDetails": {"_id": "S8Ff5ATxc4jdXbebo", "description": "Before School", "type": "plan", "program": "8c8ALB98NFvsHqX8C", "frequency": "scaledMonthly", "category": "tuition", "amount": 163, "scaledAmounts": [44, 83, 118, 143, 163], "ledgerAccountName": "4013", "details": {"startTime": "6:00 am", "endTime": "9:00 am", "regStartDate": *************, "grades": ["K", "1", "2", "3", "4", "5"], "scheduleType": "Lm2tqkvGeTXaoi7cX", "dateType": "timePeriod", "timePeriod": "WgBPRArg3iHbGs2jE"}}, "enrollmentDate": *************, "allocations": [{"allocationType": "discount", "amount": 50, "amountType": "percent", "discountType": "districtDiscount", "allocationDescription": "Discount: District Discount", "id": "J77u5NQmZsgzgagWP"}], "createdAt": *************, "reservationId": "WkJi6GaeJaqaEC2s8", "enrollmentForecastStartDate": *************}], "pendingCharges": [], "lastInvoiced": *************, "enrolledPunchCards": [{"_id": "Yj44DApZx5dCzqjT7", "originalItem": {"_id": "Z6JG2Z2DWRbLCzEcE", "description": "Drop In", "type": "punchcard", "program": "8c8ALB98NFvsHqX8C", "numberOfDays": "1", "category": "tuition", "amount": 30, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 4.1, "quantity": "3", "notes": "Schedule Change from M/F to M-F. Charging for 3 additional days in the current billing period before new schedule billing kicks in on 11/1.", "type": "item", "datesUsed": [0]}], "billingNotes": "Oct 26, 2023 - Notified of scheduled change from M & F BC to M-F BC effective yesterday. Since close to end of month, simplified things by setting new M-F schedule to start 11/1, ending current schedule on 10/31 and manually charged for 3 additional days in the current period and added those 3 days to his schedule for checkin. - bry"}, "documentItems": {"PFxmNxSYfzqTvT3ey": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "8AmjTdhxBFcnjNeiX", "personName": "<PERSON>."}}, "oM2e5Hpd5wdLq2gT6": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "8AmjTdhxBFcnjNeiX", "personName": "<PERSON>."}}}, "invoiceReminders": {"emailsSent": 10, "lastSentTime": 1697443526691}, "checkInGroupId": null, "checkInGroupName": null, "checkedIn": false, "checkedInById": "8AmjTdhxBFcnjNeiX", "checkedInOutTime": 1698929134309, "presenceLastGroupId": "GBXCPsT7fEPHFdf8R", "previousClassroomGroupId": null, "timestamps": {"moveMomentTimestamp": {"momentId": "8SkMt78LzkE5FSqps", "timestamp": 1698929134368, "groupId": null, "oldGroupId": "GBXCPsT7fEPHFdf8R"}}}, {"_id": "GQ6Hr6s9cxPfLFyCg", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "designations": [], "defaultGroupId": "4Z7DBmYvabQvj7Lff", "type": "person", "profileData": {"birthday": 1481328000000, "gender": "Female", "studentGrade": "1", "document1": "Yes", "agencyIdentifier": "Right at school /subsidy", "subsidyCode": "Unknown ", "photoAttestation": "Yes"}, "createdBy": "K2zKEfyE3hQEmzHNw", "createdAt": *************, "orgId": "LScnMh4byx2Ct47WL", "inActive": false, "billing": {"enrolledPunchCards": [{"_id": "tiJADrrjzW9J3As52", "originalItem": {"_id": "PEbJhPZBPoDP3qqqy", "description": "Before/After Care 10-Day Punch Card", "type": "punchcard", "program": "YbdKNZ9zSL3i4iFgS", "numberOfDays": "10", "amount": 360, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 360, "quantity": "1", "notes": "", "type": "item", "datesUsed": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}], "pendingCharges": [], "lastInvoiced": *************}, "invoiceReminders": {"emailsSent": 10, "lastSentTime": *************}, "checkInGroupId": null, "checkInGroupName": null, "checkedIn": false, "checkedInById": "b7Kn2JtRu4P3Q5JjD", "checkedInOutTime": *************, "presenceLastGroupId": "4Z7DBmYvabQvj7Lff", "previousClassroomGroupId": null, "timestamps": {"moveMomentTimestamp": {"momentId": "agpBvzYbsdjSMx79n", "timestamp": *************, "groupId": null, "oldGroupId": "4Z7DBmYvabQvj7Lff"}}}, {"_id": "tntKuiFqEyoKvihoX", "firstName": "<PERSON> ", "lastName": "<PERSON><PERSON>", "designations": [], "defaultGroupId": "Bm7b3sGtWy66ETT8E", "type": "person", "createdBy": "5Z9DAoFWSGFnRnJzz", "createdAt": *************, "orgId": "9uZQpotkrDLbnr4aS", "inActive": false, "waitlistAddedDate": null, "checkInGroupId": null, "checkInGroupName": null, "checkedIn": false, "checkedInOutTime": 1693518229837, "presenceLastGroupId": "Bm7b3sGtWy66ETT8E", "previousClassroomGroupId": null, "timestamps": {"moveMomentTimestamp": {"momentId": "jdMKDnC5A2SMJcPrS", "timestamp": *************, "groupId": null, "oldGroupId": "Bm7b3sGtWy66ETT8E"}}, "billing": {"enrolledPunchCards": [{"_id": "QCw3FCd3DaPbWQDrt", "originalItem": {"_id": "PEbJhPZBPoDP3qqqy", "description": "After Care 10-Day Punch Card", "type": "punchcard", "program": "fAXB2y3crWFrzq6KD", "numberOfDays": "10", "amount": 360, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 180, "quantity": "1", "notes": "AM approved credit from last SY 5 unused days (Case ********) ", "type": "item", "appliedDiscounts": [{"type": "discount", "amount": 180, "source": "amDiscount", "originalAllocation": {"type": "amDiscount", "description": "AM Discount", "overrideSingleDiscount": true, "ledgerAccountName": "4041"}}], "datesUsed": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}], "pendingCharges": [], "lastInvoiced": *************, "billingNotes": "AM requested to waive the punch card fee, family will be charged if they attend past the credited 5 days: Case ******** (<PERSON><PERSON>.)"}, "invoiceReminders": {"emailsSent": 4, "lastSentTime": *************}}, {"_id": "cr8qYBh3tdPs4tniG", "firstName": "<PERSON>", "lastName": "<PERSON>", "designations": [], "defaultGroupId": "Bm7b3sGtWy66ETT8E", "type": "person", "createdBy": "5Z9DAoFWSGFnRnJzz", "createdAt": *************, "orgId": "9uZQpotkrDLbnr4aS", "inActive": false, "waitlistAddedDate": null, "profileData": {"agencyIdentifier": "", "birthday": *************, "cacfpSubsidy": "", "document1": "", "ethnicIdentity": "", "gender": "", "healthInformation": {"primaryDoctor": "", "primaryDoctorPhone": ""}, "homeSchool": "", "mediaRequirements": {"mediaRelease": "", "mediaReviewRequired": "", "noMediaAllowed": ""}, "nickname": "", "notesPrivate": "", "notesPublic": "", "payerIdentifier": "", "photoAttestation": "", "primaryFamily": "", "racialIdentity": "", "residesWith": "", "schoolPickUp": "", "standardOutlook": {"allergies": "", "importantNotes": "", "specialNeeds": ""}, "studentGrade": "", "subsidyCode": "", "subsidyReason": ""}, "checkInGroupId": null, "checkInGroupName": null, "checkedIn": false, "checkedInOutTime": 1693518236952, "presenceLastGroupId": "Bm7b3sGtWy66ETT8E", "previousClassroomGroupId": null, "timestamps": {"moveMomentTimestamp": {"momentId": "CL5X2Hz6LwEzqBuuw", "timestamp": *************, "groupId": null, "oldGroupId": "Bm7b3sGtWy66ETT8E"}}, "billing": {"enrolledPunchCards": [{"_id": "cyTCXLkAruC596uhe", "originalItem": {"_id": "PEbJhPZBPoDP3qqqy", "description": "After Care 10-Day Punch Card", "type": "punchcard", "program": "fAXB2y3crWFrzq6KD", "numberOfDays": "10", "amount": 360, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 216, "quantity": "1", "notes": "AM approved credit from last SY 5 unused days (Case ********) Sibling discount was applied to previous punch-card. Discount rate is slightly different.", "type": "item", "appliedDiscounts": [{"type": "discount", "amount": 144, "source": "amDiscount", "originalAllocation": {"type": "amDiscount", "description": "AM Discount", "overrideSingleDiscount": true, "ledgerAccountName": "4041"}}], "datesUsed": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}], "pendingCharges": [], "lastInvoiced": *************, "billingNotes": "AM requested to waive the punch card fee, family will be charged if they attend past the credited 5 days: Case ******** (<PERSON><PERSON>.)"}, "invoiceReminders": {"emailsSent": 1, "lastSentTime": *************}}, {"_id": "uGn5HvzNwXWfb2RTB", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "designations": [], "defaultGroupId": "DpMoZbQFKNpFcJYLg", "type": "person", "profileData": {"birthday": *************, "gender": "Male", "studentGrade": "1", "document1": "Yes", "photoAttestation": "Yes"}, "createdBy": "SYSTEM", "createdAt": *************, "orgId": "iHZSWasaCNuxKvYcg", "inActive": false, "billing": {"enrolledPunchCards": [{"_id": "DXijQspmxuQ6cucNQ", "originalItem": {"_id": "PEbJhPZBPoDP3qqqy", "description": "After Care 10-Day Punch Card", "type": "punchcard", "program": "oxeGTPQPHG6M4SC94", "numberOfDays": "10", "amount": 360, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 360, "quantity": "1", "notes": "", "type": "item", "appliedDiscounts": [{"type": "discount", "amount": 36, "source": "coupon", "originalAllocation": {"id": "dXrJofyWaLcAjyT8m", "allocationType": "discount", "amount": 10, "amountType": "percent", "discountType": "siblingDiscount", "allocationDescription": "Sibling Discount", "discountAmount": 36}}], "datesUsed": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}], "pendingCharges": [], "lastInvoiced": *************}, "documentItems": {"mJgoYSpndRLCfedFv": {"createdAt": *************, "createdByPersonId": "tHMdM28hfSd47Y5EM", "createdByUserId": "3FhFGPBbDeQNhkMTM", "repositoryKey": "iHZSWasaCNuxKvYcg/3FhFGPBbDeQNhkMTM/UaPzQgughcMHKLsAXHQK", "token": "UaPzQgughcMHKLsAXHQK"}, "fLhkcpywsDBZPbWsT": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "tHMdM28hfSd47Y5EM", "personName": "<PERSON>"}}, "xfeLmHtKnu53AzxZW": {"templateOptionResult": {"action": "ack", "date": 1693409570084, "personId": "tHMdM28hfSd47Y5EM", "personName": "<PERSON>"}}, "czpbraHbtwGc4S7zd": {"createdAt": 1693418610796, "createdByPersonId": "tHMdM28hfSd47Y5EM", "createdByUserId": "3FhFGPBbDeQNhkMTM", "repositoryKey": "iHZSWasaCNuxKvYcg/3FhFGPBbDeQNhkMTM/H8W7mt7dTBeQ67HT67Bo", "token": "H8W7mt7dTBeQ67HT67Bo"}}}, {"_id": "wRd3hwMGQBLXipjsL", "firstName": "<PERSON>", "lastName": "<PERSON>", "designations": [], "defaultGroupId": "QHNX8p3tPTAzhwZvn", "type": "person", "profileData": {"birthday": *************, "gender": "Male", "studentGrade": "2", "document1": "Yes", "photoAttestation": "Yes"}, "createdBy": "SYSTEM", "createdAt": *************, "orgId": "uGed33FMA34PtHWzo", "inActive": false, "billing": {"enrolledPlans": [{"_id": "yAj2k94NqJbZXevce", "planDetails": {"_id": "yAj2k94NqJbZXevce", "description": "Before School", "type": "plan", "program": "BQgauajLsqogBHcZE", "frequency": "scaledMonthly", "category": "tuition", "amount": 301, "scaledAmounts": [123, 192, 252, 281, 301], "ledgerAccountName": "4013", "details": {"startTime": "7:00 am", "endTime": "9:25 am", "regStartDate": *************, "grades": ["K", "1", "2", "3", "4"], "scheduleType": "Lm2tqkvGeTXaoi7cX", "dateType": "timePeriod", "timePeriod": "XhLRg27eHSpQZwoya"}}, "enrollmentDate": *************, "allocations": [{"allocationType": "discount", "amount": 10, "amountType": "percent", "discountType": "siblingDiscount", "allocationDescription": "Discount: Sibling Discount", "id": "JRTGFvogZMqJgp8aW"}], "createdAt": *************, "expirationDate": *************, "enrollmentForecastStartDate": *************, "enrollmentForecastEndDate": *************}, {"_id": "yAj2k94NqJbZXevce", "planDetails": {"_id": "yAj2k94NqJbZXevce", "description": "Before School", "type": "plan", "program": "BQgauajLsqogBHcZE", "frequency": "scaledMonthly", "category": "tuition", "amount": 301, "scaledAmounts": [123, 192, 252, 281, 301], "ledgerAccountName": "4013", "details": {"startTime": "7:00 am", "endTime": "9:25 am", "regStartDate": *************, "grades": ["K", "1", "2", "3", "4"], "scheduleType": "Lm2tqkvGeTXaoi7cX", "dateType": "timePeriod", "timePeriod": "XhLRg27eHSpQZwoya"}}, "enrollmentDate": *************, "allocations": [{"allocationType": "discount", "amount": 10, "amountType": "percent", "discountType": "siblingDiscount", "allocationDescription": "Discount: Sibling Discount", "id": "JRTGFvogZMqJgp8aW"}], "createdAt": *************, "enrollmentForecastStartDate": *************, "enrollmentForecastEndDate": *************, "expirationDate": *************}], "lastInvoiced": *************, "enrolledPunchCards": [{"_id": "z3sM2vFzDi7dbPmhH", "originalItem": {"_id": "PEbJhPZBPoDP3qqqy", "description": "Before/After Care 10-Day Punch Card", "type": "punchcard", "program": "BQgauajLsqogBHcZE", "numberOfDays": "10", "amount": 315, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 315, "quantity": "1", "notes": "", "type": "item", "datesUsed": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}], "pendingCharges": []}, "documentItems": {"jwji2PA7Lxbj8zCuQ": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "P2EQvFhmykB4hMAD5", "personName": "<PERSON><PERSON> "}}, "wbSavEGyik8FsfFoW": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "P2EQvFhmykB4hMAD5", "personName": "<PERSON><PERSON> "}}, "QnDuKrqYpQJaqs2dC": {"createdAt": *************, "createdByPersonId": "P2EQvFhmykB4hMAD5", "createdByUserId": "2qjjqHd2hrQrm2Ymy", "repositoryKey": "uGed33FMA34PtHWzo/2qjjqHd2hrQrm2Ymy/oLygRVXcsfOveo8myzRw", "token": "oLygRVXcsfOveo8myzRw"}}, "checkInGroupId": null, "checkInGroupName": null, "checkedIn": false, "checkedInOutTime": *************, "presenceLastGroupId": null, "previousClassroomGroupId": null, "timestamps": {"moveMomentTimestamp": {"momentId": "SouB5Qhvf4PTjdAzn", "timestamp": 1698867612683, "groupId": "QHNX8p3tPTAzhwZvn", "oldGroupId": null}}, "checkedInById": "xWRPWaNrFwjKyHxhK", "invoiceReminders": {"emailsSent": 10, "lastSentTime": 1695801993340}}, {"_id": "exxQRTmGML9rdrkhf", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON>", "designations": [], "defaultGroupId": "cugchZrTjnoPPAg9i", "type": "person", "profileData": {"birthday": 1484697600000, "gender": "Female", "studentGrade": "1", "document1": "Yes", "photoAttestation": "Yes", "enrollmentDate": *************}, "createdBy": "SYSTEM", "createdAt": *************, "orgId": "GwjBB4Tc4wZf5tgCT", "inActive": false, "billing": {"enrolledPlans": [{"_id": "JuQ2N3CjZ4vaHK5Bg", "planDetails": {"_id": "JuQ2N3CjZ4vaHK5Bg", "description": "After School", "type": "plan", "program": "ugnprEYjyAEKwsReP", "frequency": "scaledMonthly", "category": "tuition", "amount": 220, "scaledAmounts": [59, 112, 159, 193, 220], "ledgerAccountName": "4012", "details": {"startTime": "3:36 pm", "endTime": "6:00 pm", "regStartDate": *************, "grades": ["K", "1", "2", "3", "4", "5"], "scheduleType": "ivEwGqazmcWoWv6oJ", "dateType": "timePeriod", "timePeriod": "4nJv25qusd7S5jFcK"}}, "enrollmentDate": *************, "allocations": [], "createdAt": *************, "expirationDate": *************, "reservationId": "n8d7NEj9SkSFBgS9F", "enrollmentForecastStartDate": *************, "enrollmentForecastEndDate": *************}], "pendingCharges": [], "lastInvoiced": *************, "enrolledPunchCards": [{"_id": "iqnFoGAMQYsf4bLWF", "originalItem": {"_id": "s6d3kecHmqDTftPax", "description": "Drop In", "type": "punchcard", "program": "ugnprEYjyAEKwsReP", "numberOfDays": "1", "category": "tuition", "amount": 30, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 30, "quantity": "1", "notes": "9/1 Unregistered Non School Day at Grafton Bethel", "type": "item", "datesUsed": [0]}]}, "enrollmentDate": *************, "checkInGroupId": null, "checkInGroupName": null, "checkedIn": false, "checkedInOutTime": *************, "presenceLastGroupId": "cugchZrTjnoPPAg9i", "previousClassroomGroupId": null, "timestamps": {"moveMomentTimestamp": {"momentId": "8pPqHgcBTQ4XYcmFj", "timestamp": *************, "groupId": null, "oldGroupId": "cugchZrTjnoPPAg9i"}}, "avatarPath": "GwjBB4Tc4wZf5tgCT/Knw5aFJX4xTn7SJjx/RrhaXHYcfWwJcXuyb", "avatarToken": "RrhaXHYcfWwJcXuyb", "invoiceReminders": {"emailsSent": 10, "lastSentTime": *************}, "lastInteractionDate": *************, "lastMoment": {"attributionPersonId": null, "momentType": "comment", "comment": "monopoly squad!", "time": "5:02 pm", "date": "9/12/2023", "sortStamp": *************, "momentTypePretty": "Comment", "createdAt": *************, "createdBy": "Knw5aFJX4xTn7SJjx", "attributionName": "Right at School Seaford", "createdByPersonId": "pCNELZh9MrfwiC6Ex", "orgId": "GwjBB4Tc4wZf5tgCT", "taggedPeople": ["exxQRTmGML9rdrkhf", "7NwdnKFBg8kY6SHzD", "87ZsFgqih5QHdCB4S", "Jm5rt3W2DTrTt2uxe"], "createdByPersonGroupId": "cugchZrTjnoPPAg9i", "createdByPersonCheckInGroupId": "cugchZrTjnoPPAg9i", "mediaFiles": [{"mediaUrl": "https://s3.amazonaws.com/tendlymruploads/GwjBB4Tc4wZf5tgCT/Knw5aFJX4xTn7SJjx/9XGgfh9DX9zwkpazS", "mediaToken": "9XGgfh9DX9zwkpazS", "mediaFileType": "image", "mediaPath": "GwjBB4Tc4wZf5tgCT/Knw5aFJX4xTn7SJjx/9XGgfh9DX9zwkpazS"}], "_id": "bEMGFhMLZLw7Pk7Y7", "orgName": "Seaford Elementary"}, "lastMomentByType": {"comment": {"attributionPersonId": null, "momentType": "comment", "comment": "monopoly squad!", "time": "5:02 pm", "date": "9/12/2023", "sortStamp": *************, "momentTypePretty": "Comment", "createdAt": *************, "createdBy": "Knw5aFJX4xTn7SJjx", "attributionName": "Right at School Seaford", "createdByPersonId": "pCNELZh9MrfwiC6Ex", "orgId": "GwjBB4Tc4wZf5tgCT", "taggedPeople": ["exxQRTmGML9rdrkhf", "7NwdnKFBg8kY6SHzD", "87ZsFgqih5QHdCB4S", "Jm5rt3W2DTrTt2uxe"], "createdByPersonGroupId": "cugchZrTjnoPPAg9i", "createdByPersonCheckInGroupId": "cugchZrTjnoPPAg9i", "mediaFiles": [{"mediaUrl": "https://s3.amazonaws.com/tendlymruploads/GwjBB4Tc4wZf5tgCT/Knw5aFJX4xTn7SJjx/9XGgfh9DX9zwkpazS", "mediaToken": "9XGgfh9DX9zwkpazS", "mediaFileType": "image", "mediaPath": "GwjBB4Tc4wZf5tgCT/Knw5aFJX4xTn7SJjx/9XGgfh9DX9zwkpazS"}], "_id": "bEMGFhMLZLw7Pk7Y7", "orgName": "Seaford Elementary"}}, "engagements": [{"orgId": "GwjBB4Tc4wZf5tgCT", "type": "app", "subType": "created_moment", "detail": "comment_with_media", "momentId": "62NnAqFd7Grf57j5e", "createdBy": "pCNELZh9MrfwiC6Ex", "createdAt": 1694121252693, "targetPersonId": "exxQRTmGML9rdrkhf", "sourcePersonId": "pCNELZh9MrfwiC6Ex", "_id": "ht98pb7HJ8kcWqGAD"}, {"orgId": "GwjBB4Tc4wZf5tgCT", "type": "app", "subType": "created_moment", "detail": "comment_with_media", "momentId": "bEMGFhMLZLw7Pk7Y7", "createdBy": "pCNELZh9MrfwiC6Ex", "createdAt": 1694552594258, "targetPersonId": "exxQRTmGML9rdrkhf", "sourcePersonId": "pCNELZh9MrfwiC6Ex", "_id": "p732LtLbhLzXqsPMA"}], "checkedInById": "pCNELZh9MrfwiC6Ex"}, {"_id": "qNaJy4EsBrKvT7hh8", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON>", "designations": [], "defaultGroupId": "2nx5QPv9nRNw4etEH", "type": "person", "profileData": {"birthday": *************, "gender": "Female", "studentGrade": "4", "document1": "Yes", "photoAttestation": "Yes"}, "createdBy": "SYSTEM", "createdAt": *************, "orgId": "aTAhaqGzDFQXN8g5e", "inActive": false, "billing": {"enrolledPunchCards": [{"_id": "e6A62oZXDKmRuemKf", "originalItem": {"_id": "PEbJhPZBPoDP3qqqy", "description": "Before/After Care 10-Day Punch Card", "type": "punchcard", "program": "FnCys7JwbXhcpRr5T", "numberOfDays": "10", "amount": 270, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 270, "quantity": "1", "notes": "", "type": "item", "datesUsed": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}], "pendingCharges": [], "lastInvoiced": *************}, "invoiceReminders": {"emailsSent": 10, "lastSentTime": *************}}, {"_id": "MvzDn4u72tvMEoXgq", "firstName": "<PERSON>", "lastName": "<PERSON>", "designations": [], "defaultGroupId": "2nx5QPv9nRNw4etEH", "type": "person", "profileData": {"birthday": *************, "gender": "Male", "studentGrade": "1", "document1": "Yes", "photoAttestation": "No"}, "createdBy": "SYSTEM", "createdAt": *************, "orgId": "aTAhaqGzDFQXN8g5e", "inActive": false, "billing": {"enrolledPunchCards": [{"_id": "PtwydkRbYzkHXRG34", "originalItem": {"_id": "PEbJhPZBPoDP3qqqy", "description": "Before/After Care 10-Day Punch Card", "type": "punchcard", "program": "FnCys7JwbXhcpRr5T", "numberOfDays": "10", "amount": 270, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 270, "quantity": "1", "notes": "", "type": "item", "datesUsed": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}], "pendingCharges": [], "lastInvoiced": *************}, "invoiceReminders": {"emailsSent": 10, "lastSentTime": *************}}, {"_id": "BtfRB9cTn9CKuuMfC", "firstName": "<PERSON>", "lastName": "<PERSON>", "designations": [], "defaultGroupId": "mfjDrH64pcaFGtYNQ", "type": "person", "profileData": {"birthday": *************, "gender": "Male", "studentGrade": "1", "document1": "Yes", "homeSchool": "Glenwood Heights Primary", "photoAttestation": "Yes"}, "createdBy": "SYSTEM", "createdAt": *************, "orgId": "3uAfjiBbHaGuEx86L", "inActive": false, "billing": {"enrolledPunchCards": [{"_id": "CkW7cyXistN33vKM5", "originalItem": {"_id": "PEbJhPZBPoDP3qqqy", "description": "Before/After Care 10-Day Punch Card", "type": "punchcard", "program": "SzBF9MxYWLbsAc7Ey", "numberOfDays": "10", "amount": 360, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 360, "quantity": "1", "notes": "", "type": "item", "datesUsed": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}], "pendingCharges": [], "lastInvoiced": *************, "enrolledPlans": [{"_id": "XoAqnz6RB98w5P2jC", "planDetails": {"_id": "XoAqnz6RB98w5P2jC", "description": "After School", "type": "plan", "program": "SzBF9MxYWLbsAc7Ey", "frequency": "scaledMonthly", "category": "tuition", "amount": 295, "scaledAmounts": [94, 174, 235, 275, 295], "ledgerAccountName": "4012", "details": {"startTime": "3:40 pm", "endTime": "6:00 pm", "regStartDate": *************, "regEndDate": *************, "grades": ["K", "1", "2", "3", "4", "5", "6", "7", "8"], "scheduleType": "ivEwGqazmcWoWv6oJ", "dateType": "timePeriod", "timePeriod": "wDfTwNb7QjP7AKdLk"}}, "enrollmentDate": *************, "allocations": [{"allocationType": "reimbursable", "amount": 100, "amountType": "percent", "reimbursementType": "washingtonDSHS", "allocationDescription": "Reimbursable: Washington DSHS", "payerStartDate": *************, "payerEndDate": *************, "id": "s9eyqHk4RJ3LNMuqL"}], "createdAt": *************, "reservationId": "yM5ZdHBFXYPz2kHAB", "enrollmentForecastStartDate": *************, "enrollmentForecastEndDate": null, "overrideRate": null, "updatedAt": *************, "updatedBy": "K2zKEfyE3hQEmzHNw"}, {"_id": "iWbNeboHZwiqy5Wjt", "planDetails": {"_id": "iWbNeboHZwiqy5Wjt", "description": "Before School", "type": "plan", "program": "SzBF9MxYWLbsAc7Ey", "frequency": "scaledMonthly", "category": "tuition", "amount": 295, "scaledAmounts": [94, 174, 235, 275, 295], "ledgerAccountName": "4013", "details": {"startTime": "6:30 am", "endTime": "9:10 am", "regStartDate": *************, "regEndDate": *************, "grades": ["K", "1", "2", "3", "4", "5", "6", "7", "8"], "scheduleType": "Lm2tqkvGeTXaoi7cX", "dateType": "timePeriod", "timePeriod": "wDfTwNb7QjP7AKdLk"}}, "enrollmentDate": *************, "allocations": [{"allocationType": "reimbursable", "amount": 100, "amountType": "percent", "reimbursementType": "washingtonDSHS", "allocationDescription": "Reimbursable: Washington DSHS", "payerStartDate": *************, "payerEndDate": *************, "id": "RMpLWPxLF92pXd57d"}], "createdAt": *************, "reservationId": "b3Kqm4cx99Ebidm5y", "enrollmentForecastStartDate": *************}]}, "checkInGroupId": null, "checkInGroupName": null, "checkedIn": false, "checkedInById": "WYJFaDDwENqPhcqHM", "checkedInOutTime": *************, "presenceLastGroupId": "mfjDrH64pcaFGtYNQ", "previousClassroomGroupId": null, "timestamps": {"moveMomentTimestamp": {"momentId": "ScgBFLusbT4qk7qet", "timestamp": 1698884852497, "groupId": null, "oldGroupId": "mfjDrH64pcaFGtYNQ"}}, "invoiceReminders": {"emailsSent": 6, "lastSentTime": 1694419771107}}, {"_id": "6pEwHWJomy4FE86hP", "firstName": "Berlin", "lastName": "<PERSON><PERSON><PERSON>", "designations": [], "defaultGroupId": "y2s6de6eCxSvjyW8G", "type": "person", "profileData": {"birthday": *************, "gender": "Female", "studentGrade": "2", "document1": "Yes", "photoAttestation": "No", "agencyIdentifier": "", "cacfpSubsidy": "", "ethnicIdentity": "", "healthInformation": {"primaryDoctor": "", "primaryDoctorPhone": ""}, "homeSchool": "", "mediaRequirements": {"mediaRelease": "", "mediaReviewRequired": "", "noMediaAllowed": ""}, "nickname": "", "notesPrivate": "Oct 3, 2023 - parent created duplicate accounts. Deactivating this one. ", "notesPublic": "", "payerIdentifier": "", "primaryFamily": "", "racialIdentity": "", "residesWith": "", "schoolPickUp": "", "standardOutlook": {"allergies": "", "importantNotes": "", "specialNeeds": ""}, "subsidyCode": "", "subsidyReason": "", "withdrawDate": *************}, "createdBy": "SYSTEM", "createdAt": *************, "orgId": "iiadQjLkj2XAwEfPc", "inActive": true, "billing": {"enrolledItems": [{"_id": "Th9LWtStpychBT39W", "originalItem": {"_id": "aJCcSa7vKvLdRNspz", "description": "Non-School Day", "type": "item", "program": "s4o83QyWj6LofKeR4", "amount": 30, "scaledAmounts": [], "ledgerAccountName": "4104", "details": {"startTime": "6:00 am", "endTime": "6:00 pm", "regStartDate": *************, "regEndDate": *************, "grades": ["K", "1", "2", "3", "4", "5"], "scheduleType": "oMrmtCgdtMadKETBm", "dateType": "individualDates", "individualDates": [*************]}}, "createdAt": *************, "price": 30, "quantity": "1", "notes": "", "type": "item"}], "pendingCharges": [], "lastInvoiced": *************, "enrolledPunchCards": [{"_id": "wP68yAkcEnw6wgjwu", "originalItem": {"_id": "bzDTdwpJyStdmcHbv", "description": "Drop In", "type": "punchcard", "program": "QF32vJw9nMitXRwvq", "numberOfDays": "1", "category": "tuition", "amount": 30, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 30, "quantity": "1", "notes": "", "type": "item", "datesUsed": [0]}, {"_id": "DrXpZJyaLQnbS482n", "originalItem": {"_id": "bzDTdwpJyStdmcHbv", "description": "Drop In", "type": "punchcard", "program": "QF32vJw9nMitXRwvq", "numberOfDays": "1", "category": "tuition", "amount": 30, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 30, "quantity": "1", "notes": "", "type": "item", "datesUsed": [0]}]}, "documentItems": {"Po3nvq8z6fiHCChgu": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "yZ5NwYtGWBLDM3M7x", "personName": "<PERSON>"}}, "LdzE6NrseiH2REHjq": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "yZ5NwYtGWBLDM3M7x", "personName": "<PERSON>"}}, "Yedj8g4Sw3dnNYSfp": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "yZ5NwYtGWBLDM3M7x", "personName": "<PERSON>"}}, "YjX4XZvt7QcbkFTLT": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "yZ5NwYtGWBLDM3M7x", "personName": "<PERSON>"}}, "CGLCdKi4NdT7HLbrd": {"createdAt": *************, "createdByPersonId": "yZ5NwYtGWBLDM3M7x", "createdByUserId": "r3Y9qWuThwhdrwHhj", "repositoryKey": "iiadQjLkj2XAwEfPc/r3Y9qWuThwhdrwHhj/7Uyf4NsZs4EZqQcWwzxq", "token": "7Uyf4NsZs4EZqQcWwzxq"}}, "waitlistAddedDate": null, "deactivatedAt": 1696360460018, "deactivationReason": "Unknown"}, {"_id": "6a9bMBxrKeEvaqAEB", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON>", "designations": [], "type": "person", "createdBy": "eCxkeyCoi7B9rAWMi", "createdAt": *************, "orgId": "uGed33FMA34PtHWzo", "inActive": false, "billing": {"enrolledPlans": [{"_id": "JqAAQAm2Pv2T48jfB", "planDetails": {"_id": "JqAAQAm2Pv2T48jfB", "description": "After School", "type": "plan", "program": "BQgauajLsqogBHcZE", "frequency": "scaledMonthly", "category": "tuition", "amount": 375, "scaledAmounts": [154, 240, 315, 350, 375], "ledgerAccountName": "4012", "details": {"startTime": "3:25 pm", "endTime": "6:30 pm", "regStartDate": *************, "grades": ["K", "1", "2", "3", "4"], "scheduleType": "ivEwGqazmcWoWv6oJ", "dateType": "timePeriod", "timePeriod": "XhLRg27eHSpQZwoya"}}, "enrollmentDate": *************, "allocations": [], "createdAt": *************, "enrollmentForecastStartDate": *************, "enrollmentForecastEndDate": *************, "expirationDate": *************}, {"_id": "yAj2k94NqJbZXevce", "planDetails": {"_id": "yAj2k94NqJbZXevce", "description": "Before School", "type": "plan", "program": "BQgauajLsqogBHcZE", "frequency": "scaledMonthly", "category": "tuition", "amount": 301, "scaledAmounts": [123, 192, 252, 281, 301], "ledgerAccountName": "4013", "details": {"startTime": "7:00 am", "endTime": "9:25 am", "regStartDate": *************, "grades": ["K", "1", "2", "3", "4"], "scheduleType": "Lm2tqkvGeTXaoi7cX", "dateType": "timePeriod", "timePeriod": "XhLRg27eHSpQZwoya"}}, "enrollmentDate": *************, "allocations": [], "createdAt": *************, "expirationDate": *************, "enrollmentForecastStartDate": *************, "enrollmentForecastEndDate": *************}], "pendingCharges": [], "lastInvoiced": *************, "enrolledPunchCards": [{"_id": "WfmYfx5gGuZADLx22", "originalItem": {"_id": "PEbJhPZBPoDP3qqqy", "description": "Before/After Care 10-Day Punch Card", "type": "punchcard", "program": "BQgauajLsqogBHcZE", "numberOfDays": "10", "amount": 315, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 315, "quantity": "1", "notes": "", "type": "item", "datesUsed": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}]}, "profileData": {"agencyIdentifier": "", "birthday": *************, "cacfpSubsidy": "", "document1": "", "ethnicIdentity": "", "gender": "", "healthInformation": {"primaryDoctor": "", "primaryDoctorPhone": ""}, "homeSchool": "", "mediaRequirements": {"mediaRelease": "", "mediaReviewRequired": "", "noMediaAllowed": ""}, "nickname": "", "notesPrivate": "", "notesPublic": "", "payerIdentifier": "", "photoAttestation": "", "primaryFamily": "", "racialIdentity": "", "residesWith": "", "schoolPickUp": "", "standardOutlook": {"allergies": "", "importantNotes": "", "specialNeeds": ""}, "studentGrade": "K", "subsidyCode": "", "subsidyReason": ""}, "documentItems": {"jwji2PA7Lxbj8zCuQ": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "P2EQvFhmykB4hMAD5", "personName": "<PERSON><PERSON> "}}, "wbSavEGyik8FsfFoW": {"templateOptionResult": {"action": "ack", "date": 1693655639696, "personId": "P2EQvFhmykB4hMAD5", "personName": "<PERSON><PERSON> "}}, "QnDuKrqYpQJaqs2dC": {"createdAt": 1693656784834, "createdByPersonId": "P2EQvFhmykB4hMAD5", "createdByUserId": "2qjjqHd2hrQrm2Ymy", "repositoryKey": "uGed33FMA34PtHWzo/2qjjqHd2hrQrm2Ymy/zd5t7E9UlBxH0cP2eumN", "token": "zd5t7E9UlBxH0cP2eumN"}}, "defaultGroupId": "QHNX8p3tPTAzhwZvn", "waitlistAddedDate": null, "checkInGroupId": null, "checkInGroupName": null, "checkedIn": false, "checkedInOutTime": 1698912257631, "presenceLastGroupId": null, "previousClassroomGroupId": null, "timestamps": {"moveMomentTimestamp": {"momentId": "9yc6NNj8KJBggtPbY", "timestamp": 1698867241631, "groupId": "QHNX8p3tPTAzhwZvn", "oldGroupId": null}}, "checkedInById": "xWRPWaNrFwjKyHxhK", "invoiceReminders": {"emailsSent": 10, "lastSentTime": 1695801992716}}, {"_id": "uNf69zjYZdnACfWvx", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "designations": [], "defaultGroupId": "ZETWnspXWBXsoX9ju", "type": "person", "profileData": {"birthday": *************, "gender": "Male", "studentGrade": "6", "document1": "Yes", "photoAttestation": "Yes"}, "createdBy": "SYSTEM", "createdAt": *************, "orgId": "ef65692zy34BFMFtv", "inActive": false, "billing": {"enrolledPunchCards": [{"_id": "nLwE8vPm5Z9mrJddz", "originalItem": {"_id": "PEbJhPZBPoDP3qqqy", "description": "After Care 10-Day Punch Card", "type": "punchcard", "program": "9Fw6rPf6oo8hS3Wfm", "numberOfDays": "10", "amount": 315, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 315, "quantity": "1", "notes": "", "type": "item", "datesUsed": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}], "pendingCharges": [], "lastInvoiced": *************}, "invoiceReminders": {"emailsSent": 10, "lastSentTime": *************}}, {"_id": "gmWowCQteKXpJnTfk", "firstName": "<PERSON>", "lastName": "<PERSON>", "designations": [], "defaultGroupId": "WhkYNRqYBnBk5YXwY", "type": "person", "profileData": {"birthday": *************, "gender": "Male", "studentGrade": "2", "document1": "Yes", "photoAttestation": "Yes", "standardOutlook": {"specialNeeds": "Autistic"}}, "createdBy": "SYSTEM", "createdAt": *************, "orgId": "CvN7CCDsxzHboLXBH", "inActive": false, "documentItems": {"N7WgnY8g98tg3LgGC": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "BD4xah298GY946usu", "personName": "<PERSON>"}}, "FmTh7pqnGPdCSkMrc": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "BD4xah298GY946usu", "personName": "<PERSON>"}}}, "checkInGroupId": null, "checkInGroupName": null, "checkedIn": false, "checkedInById": "BD4xah298GY946usu", "checkedInOutTime": 1697146800000, "presenceLastGroupId": "WhkYNRqYBnBk5YXwY", "previousClassroomGroupId": null, "timestamps": {"moveMomentTimestamp": {"momentId": "X8KMcaRBN283h44fD", "timestamp": *************, "groupId": null, "oldGroupId": "WhkYNRqYBnBk5YXwY"}}, "billing": {"enrolledPunchCards": [{"_id": "vYbJA7xkXwuGAi5eT", "originalItem": {"_id": "KzhFrceFcDvjJeNMc", "description": "Drop In", "type": "punchcard", "program": "cy7EFNQR8xaTtJRW4", "numberOfDays": "1", "category": "tuition", "amount": 35, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 35, "quantity": "1", "notes": "10/12 Unscheduled Drop In", "type": "item", "datesUsed": [0]}], "pendingCharges": [], "lastInvoiced": *************}}, {"_id": "iEhhK2F7gnJis8PdD", "firstName": "<PERSON>", "lastName": "<PERSON>", "designations": [], "defaultGroupId": "2nx5QPv9nRNw4etEH", "type": "person", "profileData": {"birthday": *************, "gender": "Male", "studentGrade": "2", "document1": "Yes", "photoAttestation": "No", "healthInformation": {"primaryDoctor": "", "primaryDoctorPhone": ""}, "homeSchool": ""}, "createdBy": "SYSTEM", "createdAt": *************, "orgId": "aTAhaqGzDFQXN8g5e", "inActive": false, "billing": {"enrolledPunchCards": [{"_id": "LotopraiFYBzLcfs9", "originalItem": {"_id": "kmbBD75y39i7j6dvF", "description": "Drop In", "type": "punchcard", "program": "FnCys7JwbXhcpRr5T", "numberOfDays": "1", "category": "tuition", "amount": 30, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 30, "quantity": "1", "notes": "", "type": "item", "datesUsed": [0]}], "pendingCharges": [], "lastInvoiced": *************, "enrolledItems": [{"_id": "miPfyqs3izTcsnGLS", "originalItem": {"_id": "PLZ5L2vhm3Kye4gZ6", "description": "Non-School Day", "type": "item", "program": "Y3LWz63aoP8YMzJyw", "amount": 30, "scaledAmounts": [], "ledgerAccountName": "4104", "details": {"startTime": "6:00 am", "endTime": "6:00 pm", "regStartDate": *************, "regEndDate": *************, "grades": ["K", "1", "2", "3", "4", "5"], "scheduleType": "oMrmtCgdtMadKETBm", "dateType": "individualDates", "individualDates": [*************]}}, "createdAt": *************, "price": 30, "quantity": "1", "notes": "", "type": "item"}, {"_id": "bCwt5g8yqt9n9PTAN", "originalItem": {"_id": "6KFArgdExpXuZMkjP", "description": "Non School Day: 10/2/2023", "type": "item", "program": "Y3LWz63aoP8YMzJyw", "dropInDailyRate": true, "amount": 30, "scaledAmounts": [], "ledgerAccountName": "4104", "details": {"startTime": "6:00 am", "endTime": "6:00 pm", "regStartDate": *************, "regEndDate": *************, "grades": ["K", "1", "2", "3", "4", "5"], "scheduleType": "52QiAsuJuDxHRMRnz", "dateType": "individualDates", "individualDates": [*************]}}, "createdAt": *************, "price": 30, "quantity": "1", "notes": "", "type": "item"}, {"_id": "kMALDYeQ4uF9qmfCq", "originalItem": {"_id": "9MuASzTRxZEQNieAB", "description": "Non School Day: 11/7/2023", "type": "item", "program": "Y3LWz63aoP8YMzJyw", "amount": 30, "scaledAmounts": [], "ledgerAccountName": "4020", "details": {"startTime": "6:00 am", "endTime": "6:00 pm", "regStartDate": *************, "regEndDate": *************, "grades": ["K", "1", "2", "3", "4", "5"], "scheduleType": "52QiAsuJuDxHRMRnz", "dateType": "individualDates", "individualDates": [*************]}}, "createdAt": *************, "price": 22.5, "quantity": "1", "notes": "10/30 non school day for 11/7. JL", "type": "item", "appliedDiscounts": [{"type": "discount", "amount": 7.5, "source": "militaryDiscount", "originalAllocation": {"type": "militaryDiscount", "description": "Military Discount", "amount": 25, "amountType": "percent", "ledgerAccountName": "4044", "expiresWithGracePeriod": false, "overrideSingleDiscount": false}}]}]}, "invoiceReminders": {"emailsSent": 3, "lastSentTime": *************}, "documentItems": {"6uuqAPEddNrxBzwrp": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "BvRFtMivLj7sgP6z9", "personName": "<PERSON>"}}, "rg9dQHMQAXA6xymLa": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "BvRFtMivLj7sgP6z9", "personName": "<PERSON>"}}, "K9tWPi6sY4cSrQCTs": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "BvRFtMivLj7sgP6z9", "personName": "<PERSON>"}}, "HpmhRx6EPyiTrWfuh": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "BvRFtMivLj7sgP6z9", "personName": "<PERSON>"}}, "nNbCdT3HXJ5PJcRQr": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "BvRFtMivLj7sgP6z9", "personName": "<PERSON>"}}}}, {"_id": "ecX9CEfYMywhyhqJ5", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON>", "designations": [], "defaultGroupId": "e3Fvr9uWLeuMYL9zD", "type": "person", "profileData": {"birthday": 1486252800000, "gender": "Male", "studentGrade": "1", "document1": "No", "agencyIdentifier": "Childcare ", "homeSchool": "Daybreak Primary", "photoAttestation": "Yes"}, "createdBy": "K2zKEfyE3hQEmzHNw", "createdAt": *************, "orgId": "K2dEM4ZHLuTkrRMQN", "inActive": false, "billing": {"enrolledPunchCards": [{"_id": "5sCAbLTPBDb58xF6d", "originalItem": {"_id": "PEbJhPZBPoDP3qqqy", "description": "Before/After Care 10-Day Punch Card", "type": "punchcard", "program": "SP2BkLP8reokigQoM", "numberOfDays": "10", "amount": 360, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 360, "quantity": "1", "notes": "", "type": "item", "datesUsed": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}], "pendingCharges": [], "lastInvoiced": *************}, "invoiceReminders": {"emailsSent": 10, "lastSentTime": *************}, "checkInGroupId": null, "checkInGroupName": null, "checkedIn": false, "checkedInById": "JKTm45z7R9huvnKbT", "checkedInOutTime": *************, "presenceLastGroupId": "e3Fvr9uWLeuMYL9zD", "previousClassroomGroupId": null, "timestamps": {"moveMomentTimestamp": {"momentId": "AZsEBA9WCLbJkNjSa", "timestamp": *************, "groupId": null, "oldGroupId": "e3Fvr9uWLeuMYL9zD"}}}, {"_id": "dmSx7nbNrEoYqz7FS", "firstName": "<PERSON>", "lastName": "Fields", "designations": [], "defaultGroupId": "e3Fvr9uWLeuMYL9zD", "type": "person", "profileData": {"birthday": *************, "gender": "Male", "studentGrade": "5", "document1": "No", "agencyIdentifier": "Childcare", "subsidyCode": "1859182", "homeSchool": "Daybreak Middle", "photoAttestation": "Yes"}, "createdBy": "K2zKEfyE3hQEmzHNw", "createdAt": *************, "orgId": "K2dEM4ZHLuTkrRMQN", "inActive": false, "billing": {"enrolledPunchCards": [{"_id": "WjTGwgsPd7WBYykkE", "originalItem": {"_id": "PEbJhPZBPoDP3qqqy", "description": "Before/After Care 10-Day Punch Card", "type": "punchcard", "program": "SP2BkLP8reokigQoM", "numberOfDays": "10", "amount": 360, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 360, "quantity": "1", "notes": "", "type": "item", "appliedDiscounts": [{"type": "discount", "amount": 36, "source": "coupon", "originalAllocation": {"id": "zZwhBP7eDaYBE9Cut", "allocationType": "discount", "amount": 10, "amountType": "percent", "discountType": "siblingDiscount", "allocationDescription": "Sibling Discount", "discountAmount": 36}}], "datesUsed": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}], "pendingCharges": [], "lastInvoiced": *************}, "checkInGroupId": null, "checkInGroupName": null, "checkedIn": false, "checkedInById": "nFAKtPdoytLyfa4eE", "checkedInOutTime": *************, "presenceLastGroupId": "e3Fvr9uWLeuMYL9zD", "previousClassroomGroupId": null, "timestamps": {"moveMomentTimestamp": {"momentId": "Cp6oEwZf5XZB2adaD", "timestamp": *************, "groupId": null, "oldGroupId": "e3Fvr9uWLeuMYL9zD"}}, "invoiceReminders": {"emailsSent": 10, "lastSentTime": *************}}, {"_id": "Bp2ceoRWNxxestoDy", "firstName": "<PERSON>", "lastName": "<PERSON>", "designations": [], "defaultGroupId": "AcDNs4mYr8xpPd6AP", "type": "person", "profileData": {"birthday": 1436313600000, "gender": "Male", "studentGrade": "3", "document1": "Yes", "photoAttestation": "Yes"}, "createdBy": "SYSTEM", "createdAt": 1693600012143, "orgId": "TDse7r5EQdouemeDn", "inActive": false, "checkInGroupId": null, "checkInGroupName": null, "checkedIn": false, "checkedInById": "FX8n4kmqHFDXjkWny", "checkedInOutTime": 1698431220000, "presenceLastGroupId": "AcDNs4mYr8xpPd6AP", "previousClassroomGroupId": null, "timestamps": {"moveMomentTimestamp": {"momentId": "ziZGQexLo94uThxvx", "timestamp": *************, "groupId": null, "oldGroupId": "AcDNs4mYr8xpPd6AP"}}, "billing": {"enrolledPlans": [{"_id": "TRTDx2h4GjmQ9p4G7", "planDetails": {"_id": "TRTDx2h4GjmQ9p4G7", "description": "Before School", "type": "plan", "program": "cXwvEW8rr5eE4m3nX", "frequency": "scaledMonthly", "category": "tuition", "amount": 260, "scaledAmounts": [93, 149, 202, 242, 260], "ledgerAccountName": "4013", "details": {"startTime": "6:45 am", "endTime": "8:35 am", "regStartDate": *************, "grades": ["K", "1", "2", "3", "4", "5"], "scheduleType": "Lm2tqkvGeTXaoi7cX", "dateType": "timePeriod", "timePeriod": "uktQJ5Py6gwaJSPq8"}}, "enrollmentDate": *************, "allocations": [], "createdAt": *************, "expirationDate": *************, "reservationId": "ACoduCeMBfKGthXnP", "enrollmentForecastStartDate": *************}], "pendingCharges": [], "enrolledPunchCards": [{"_id": "z2wwDk6jZqTazmJ9v", "originalItem": {"_id": "ZCv4kFKDZWEcGToPK", "description": "Drop In", "type": "punchcard", "program": "cXwvEW8rr5eE4m3nX", "numberOfDays": "1", "category": "tuition", "amount": 35, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 35, "quantity": "1", "notes": "9/8 AM Drop In", "type": "item", "datesUsed": [0]}], "lastInvoiced": *************}}, {"_id": "Jf4ZpAfcRipWeQCRm", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "designations": [], "defaultGroupId": "y2s6de6eCxSvjyW8G", "type": "person", "profileData": {"birthday": *************, "gender": "Female", "studentGrade": "2", "document1": "Yes", "photoAttestation": "No"}, "createdBy": "SYSTEM", "createdAt": *************, "orgId": "iiadQjLkj2XAwEfPc", "inActive": false, "billing": {"enrolledPunchCards": [{"_id": "ZH8qk5wmWgPSqAt5f", "originalItem": {"_id": "bzDTdwpJyStdmcHbv", "description": "Drop In", "type": "punchcard", "program": "QF32vJw9nMitXRwvq", "numberOfDays": "1", "category": "tuition", "amount": 30, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 30, "quantity": "1", "notes": "", "type": "item", "datesUsed": [0]}], "pendingCharges": [], "lastInvoiced": *************}, "invoiceReminders": {"emailsSent": 2, "lastSentTime": *************}}, {"_id": "P2QyBRibvA2mcPRjT", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "designations": [], "defaultGroupId": "y2s6de6eCxSvjyW8G", "type": "person", "profileData": {"birthday": *************, "gender": "Male", "studentGrade": "4", "document1": "Yes", "photoAttestation": "No", "standardOutlook": {"specialNeeds": "ADHD"}}, "createdBy": "SYSTEM", "createdAt": *************, "orgId": "iiadQjLkj2XAwEfPc", "inActive": false, "billing": {"enrolledPunchCards": [{"_id": "ziXNu7aaQycuPziuJ", "originalItem": {"_id": "bzDTdwpJyStdmcHbv", "description": "Drop In", "type": "punchcard", "program": "QF32vJw9nMitXRwvq", "numberOfDays": "1", "category": "tuition", "amount": 30, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 30, "quantity": "1", "notes": "", "type": "item", "appliedDiscounts": [{"type": "discount", "amount": 3, "source": "coupon", "originalAllocation": {"id": "dTfAm363KpA5oHiYT", "allocationType": "discount", "amount": 10, "amountType": "percent", "discountType": "siblingDiscount", "allocationDescription": "Sibling Discount", "discountAmount": 3}}], "datesUsed": [0]}], "pendingCharges": [], "lastInvoiced": *************}, "invoiceReminders": {"emailsSent": 5, "lastSentTime": *************}}, {"_id": "GNtduLbdZRR27PJtA", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "designations": [], "defaultGroupId": "WhkYNRqYBnBk5YXwY", "type": "person", "profileData": {"birthday": *************, "gender": "Female", "studentGrade": "4", "document1": "Yes", "photoAttestation": "Yes", "standardOutlook": {"allergies": "Dairy free"}}, "createdBy": "SYSTEM", "createdAt": *************, "orgId": "CvN7CCDsxzHboLXBH", "inActive": false, "checkInGroupId": null, "checkInGroupName": null, "checkedIn": false, "checkedInOutTime": 1697148240000, "presenceLastGroupId": "WhkYNRqYBnBk5YXwY", "previousClassroomGroupId": null, "timestamps": {"moveMomentTimestamp": {"momentId": "jJY4kq6yRnvvA38fN", "timestamp": *************, "groupId": null, "oldGroupId": "WhkYNRqYBnBk5YXwY"}}, "billing": {"enrolledPunchCards": [{"_id": "dGfFgr6zPMnPBtGCb", "originalItem": {"_id": "KzhFrceFcDvjJeNMc", "description": "Drop In", "type": "punchcard", "program": "cy7EFNQR8xaTtJRW4", "numberOfDays": "1", "category": "tuition", "amount": 35, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 35, "quantity": "1", "notes": "10/12 Unscheduled Drop In", "type": "item", "datesUsed": [0]}], "pendingCharges": [], "lastInvoiced": *************}}, {"_id": "NJoYg5DWvjWFhiZXT", "firstName": "<PERSON>", "lastName": "<PERSON>ers", "designations": [], "defaultGroupId": "jhHyeEMuL3PTtxNQk", "type": "person", "profileData": {"birthday": *************, "gender": "Female", "studentGrade": "4", "document1": "Yes", "photoAttestation": "Yes", "standardOutlook": {"allergies": "Ant bites"}}, "createdBy": "SYSTEM", "createdAt": *************, "orgId": "gJ66DDKJf5ZktvQXP", "inActive": false, "billing": {"enrolledPunchCards": [{"_id": "sZ2xMgGS5KuRsoK8N", "originalItem": {"_id": "PEbJhPZBPoDP3qqqy", "description": "Before/After Care 10-Day Punch Card", "type": "punchcard", "program": "L4uwGjJKSCDXwT6Nz", "numberOfDays": "10", "amount": 270, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 270, "quantity": "1", "notes": "", "type": "item", "datesUsed": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}], "pendingCharges": [], "lastInvoiced": *************}, "checkInGroupId": null, "checkInGroupName": null, "checkedIn": false, "checkedInById": "5XtXbG7cPGCeYyW4Z", "checkedInOutTime": *************, "presenceLastGroupId": "jhHyeEMuL3PTtxNQk", "previousClassroomGroupId": null, "timestamps": {"moveMomentTimestamp": {"momentId": "7ECtjJMbTYMnKwxE8", "timestamp": *************, "groupId": null, "oldGroupId": "jhHyeEMuL3PTtxNQk"}}}, {"_id": "NaqbagobiNazoFvRF", "firstName": "Micaela", "lastName": "<PERSON><PERSON><PERSON>", "designations": [], "defaultGroupId": "ZETWnspXWBXsoX9ju", "type": "person", "profileData": {"birthday": *************, "gender": "Female", "studentGrade": "2", "document1": "Yes", "photoAttestation": "Yes"}, "createdBy": "SYSTEM", "createdAt": *************, "orgId": "ef65692zy34BFMFtv", "inActive": false, "billing": {"enrolledPlans": [{"_id": "vAfENyN7awd8sPow9", "planDetails": {"_id": "vAfENyN7awd8sPow9", "description": "After School", "type": "plan", "program": "9Fw6rPf6oo8hS3Wfm", "frequency": "scaledMonthly", "category": "tuition", "amount": 352, "scaledAmounts": [145, 225, 296, 330, 352], "ledgerAccountName": "4012", "details": {"startTime": "2:25 pm", "endTime": "6:30 pm", "regStartDate": *************, "regEndDate": *************, "grades": ["K", "1", "2", "3", "4", "5", "6"], "scheduleType": "ivEwGqazmcWoWv6oJ", "dateType": "timePeriod", "timePeriod": "SkJH8i7dK5CtCHE5G"}}, "enrollmentDate": *************, "allocations": [{"allocationType": "discount", "amount": 25, "amountType": "percent", "discountType": "freeReducedDiscount", "allocationDescription": "Discount: Free and Reduced Discount", "id": "PXnitMjLYuHNSFqTK"}], "createdAt": *************, "reservationId": "MsBzNKqjpBHwoMNbK", "enrollmentForecastStartDate": *************, "enrollmentForecastEndDate": *************, "overrideRate": null, "updatedAt": *************, "updatedBy": "TZnj9F36brucdDCsR", "expirationDate": *************}, {"_id": "vAfENyN7awd8sPow9", "planDetails": {"_id": "vAfENyN7awd8sPow9", "description": "After School", "type": "plan", "program": "9Fw6rPf6oo8hS3Wfm", "frequency": "scaledMonthly", "category": "tuition", "amount": 352, "scaledAmounts": [145, 225, 296, 330, 352], "ledgerAccountName": "4012", "details": {"startTime": "2:25 pm", "endTime": "6:30 pm", "regStartDate": *************, "regEndDate": *************, "grades": ["K", "1", "2", "3", "4", "5", "6"], "scheduleType": "ivEwGqazmcWoWv6oJ", "dateType": "timePeriod", "timePeriod": "SkJH8i7dK5CtCHE5G"}}, "enrollmentDate": *************, "allocations": [], "createdAt": *************, "reservationId": "ziA7wJuesoHjmrz2g", "enrollmentForecastStartDate": *************, "enrollmentForecastEndDate": *************, "expirationDate": *************}, {"_id": "vAfENyN7awd8sPow9", "planDetails": {"_id": "vAfENyN7awd8sPow9", "description": "After School", "type": "plan", "program": "9Fw6rPf6oo8hS3Wfm", "frequency": "scaledMonthly", "category": "tuition", "amount": 352, "scaledAmounts": [145, 225, 296, 330, 352], "ledgerAccountName": "4012", "details": {"startTime": "2:25 pm", "endTime": "6:30 pm", "regStartDate": *************, "regEndDate": *************, "grades": ["K", "1", "2", "3", "4", "5", "6"], "scheduleType": "ivEwGqazmcWoWv6oJ", "dateType": "timePeriod", "timePeriod": "SkJH8i7dK5CtCHE5G"}}, "enrollmentDate": *************, "allocations": [], "createdAt": *************, "reservationId": "r8MWpXBbJ6GPtKXu7", "enrollmentForecastStartDate": *************}], "pendingCharges": [], "lastInvoiced": *************, "enrolledPunchCards": [{"_id": "Agz3Z83sNAJR3C2cF", "originalItem": {"_id": "sgTTiGZxfZDNXBJRQ", "description": "Drop In", "type": "punchcard", "program": "9Fw6rPf6oo8hS3Wfm", "numberOfDays": "1", "category": "tuition", "amount": 35, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 35, "quantity": "1", "notes": "", "type": "item", "datesUsed": [0]}], "billingNotes": "Oct 23, 2023 - <PERSON><PERSON> requested 2 schedule changes: 1) Switch to thursdays only as of 10/30. 2) Switch to T/W/R as of 11/6. Created schedule chain. Expecting 10/30 change to be invoiced prorated since it has an end date and 11/6 to be prorated as it starts after the 1st. If system does not invoice properly on 11/2, credit has needed. - bry"}, "documentItems": {"jRM8dCCmjFaPSjge3": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "3Q54uTprmSosMxT4x", "personName": "<PERSON>"}}, "7tQLbAb4Xke9BkJze": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "3Q54uTprmSosMxT4x", "personName": "<PERSON>"}}}, "checkInGroupId": null, "checkInGroupName": null, "checkedIn": false, "checkedInOutTime": *************, "presenceLastGroupId": "ZETWnspXWBXsoX9ju", "previousClassroomGroupId": null, "timestamps": {"moveMomentTimestamp": {"momentId": "LsYNdtsPyHfZroCcP", "timestamp": 1698874924297, "groupId": null, "oldGroupId": "ZETWnspXWBXsoX9ju"}}, "invoiceReminders": {"emailsSent": 10, "lastSentTime": 1698221263633}, "checkedInById": "4jtgjGTvgNpwmjhYM"}, {"_id": "C3uRt9ch7kqX87mKm", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "designations": [], "defaultGroupId": "hFw4TEghT9dJBNy5C", "type": "person", "profileData": {"birthday": 1444262400000, "gender": "Male", "studentGrade": "3", "document1": "Yes", "photoAttestation": "Yes"}, "createdBy": "SYSTEM", "createdAt": *************, "orgId": "mPbYpYddE4Pikmh2Q", "inActive": false, "waitlistAddedDate": null, "billing": {"enrolledPlans": [{"_id": "68RKfQTwLgMRm7oW2", "planDetails": {"_id": "68RKfQTwLgMRm7oW2", "description": "After School", "type": "plan", "program": "AwFANXRzSKZmjY6xN", "frequency": "scaledMonthly", "category": "tuition", "amount": 454, "scaledAmounts": [164, 268, 359, 411, 454], "ledgerAccountName": "4012", "details": {"startTime": "2:40 pm", "endTime": "6:00 pm", "regStartDate": *************, "grades": ["K", "1", "2", "3", "4", "5"], "scheduleType": "ivEwGqazmcWoWv6oJ", "dateType": "timePeriod", "timePeriod": "8eTiiuq3e2zpw9HwM"}}, "enrollmentDate": *************, "allocations": [], "createdAt": *************, "reservationId": "kKt7xQZarbi4TQLcC", "enrollmentForecastStartDate": *************}], "enrolledPunchCards": [{"_id": "eaLixQxAtBairehL6", "originalItem": {"_id": "caJKwkbomLgryWWxH", "description": "Drop In", "type": "punchcard", "program": "AwFANXRzSKZmjY6xN", "numberOfDays": "1", "category": "tuition", "amount": 35, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 35, "quantity": "1", "notes": "", "type": "item", "datesUsed": [0]}], "pendingCharges": [], "lastInvoiced": *************}, "checkInGroupId": null, "checkInGroupName": null, "checkedIn": false, "checkedInOutTime": *************, "presenceLastGroupId": "hFw4TEghT9dJBNy5C", "previousClassroomGroupId": null, "timestamps": {"moveMomentTimestamp": {"momentId": "7Kp3t48MeBaKMZude", "timestamp": *************, "groupId": null, "oldGroupId": "hFw4TEghT9dJBNy5C"}}, "invoiceReminders": {"emailsSent": 1, "lastSentTime": *************}}, {"_id": "FxvevvBXbpJALqpwq", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "designations": [], "defaultGroupId": "hFw4TEghT9dJBNy5C", "type": "person", "profileData": {"birthday": *************, "gender": "Male", "studentGrade": "1", "document1": "Yes", "photoAttestation": "Yes"}, "createdBy": "SYSTEM", "createdAt": *************, "orgId": "mPbYpYddE4Pikmh2Q", "inActive": false, "waitlistAddedDate": null, "billing": {"enrolledPlans": [{"_id": "68RKfQTwLgMRm7oW2", "planDetails": {"_id": "68RKfQTwLgMRm7oW2", "description": "After School", "type": "plan", "program": "AwFANXRzSKZmjY6xN", "frequency": "scaledMonthly", "category": "tuition", "amount": 454, "scaledAmounts": [164, 268, 359, 411, 454], "ledgerAccountName": "4012", "details": {"startTime": "2:40 pm", "endTime": "6:00 pm", "regStartDate": *************, "grades": ["K", "1", "2", "3", "4", "5"], "scheduleType": "ivEwGqazmcWoWv6oJ", "dateType": "timePeriod", "timePeriod": "8eTiiuq3e2zpw9HwM"}}, "enrollmentDate": *************, "allocations": [{"allocationType": "discount", "amount": 10, "amountType": "percent", "discountType": "siblingDiscount", "allocationDescription": "Discount: Sibling Discount", "id": "xDGxu8WydJLmfsAnZ"}], "createdAt": *************, "reservationId": "4Ae9AE7Ddnjijtrnt", "enrollmentForecastStartDate": *************, "enrollmentForecastEndDate": null, "overrideRate": null, "updatedAt": *************, "updatedBy": "XZ2uYC8CxnRFoWrTW"}], "enrolledPunchCards": [{"_id": "3mzZbLHgjxvAKYzEq", "originalItem": {"_id": "caJKwkbomLgryWWxH", "description": "Drop In", "type": "punchcard", "program": "AwFANXRzSKZmjY6xN", "numberOfDays": "1", "category": "tuition", "amount": 35, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 35, "quantity": "1", "notes": "", "type": "item", "datesUsed": [0]}], "pendingCharges": [], "lastInvoiced": *************}, "documentItems": {"iS9aLZ9akw42aXPzK": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "FieEzjjpEDZPALDL6", "personName": "<PERSON><PERSON>"}}}, "checkInGroupId": null, "checkInGroupName": null, "checkedIn": false, "checkedInOutTime": *************, "presenceLastGroupId": "hFw4TEghT9dJBNy5C", "previousClassroomGroupId": null, "timestamps": {"moveMomentTimestamp": {"momentId": "idJMiEGgbQpNSitYY", "timestamp": *************, "groupId": null, "oldGroupId": "hFw4TEghT9dJBNy5C"}}, "invoiceReminders": {"emailsSent": 1, "lastSentTime": *************}}, {"_id": "ToagdsweEAmZxEQJm", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "designations": [], "defaultGroupId": "edarMyu487ryNiLqF", "type": "person", "profileData": {"birthday": *************, "gender": "Male", "studentGrade": "4", "document1": "Yes", "photoAttestation": "Yes"}, "createdBy": "SYSTEM", "createdAt": *************, "orgId": "ZHK6xEPdCpMfXk988", "inActive": false, "billing": {"enrolledPlans": [{"_id": "6pnHjz9XzhbCNSeLP", "planDetails": {"_id": "6pnHjz9XzhbCNSeLP", "description": "After School", "type": "plan", "program": "6tGZSuegaGmw4EWvf", "frequency": "scaledMonthly", "category": "tuition", "amount": 318, "scaledAmounts": [122, 173, 238, 285, 318], "ledgerAccountName": "4012", "details": {"startTime": "2:45 pm", "endTime": "6:30 pm", "regStartDate": *************, "grades": ["1", "2", "3", "4", "5"], "scheduleType": "ivEwGqazmcWoWv6oJ", "dateType": "timePeriod", "timePeriod": "m4oRzL25EjAvenD3K"}}, "enrollmentDate": *************, "allocations": [], "createdAt": *************, "reservationId": "rpQqcKcDyJYQ6M5oi", "enrollmentForecastStartDate": *************, "enrollmentForecastEndDate": *************, "expirationDate": *************}, {"_id": "6pnHjz9XzhbCNSeLP", "planDetails": {"_id": "6pnHjz9XzhbCNSeLP", "description": "After School", "type": "plan", "program": "6tGZSuegaGmw4EWvf", "frequency": "scaledMonthly", "category": "tuition", "amount": 318, "scaledAmounts": [122, 173, 238, 285, 318], "ledgerAccountName": "4012", "details": {"startTime": "2:45 pm", "endTime": "6:30 pm", "regStartDate": *************, "grades": ["1", "2", "3", "4", "5"], "scheduleType": "ivEwGqazmcWoWv6oJ", "dateType": "timePeriod", "timePeriod": "m4oRzL25EjAvenD3K"}}, "enrollmentDate": *************, "allocations": [], "createdAt": *************, "enrollmentForecastStartDate": *************, "enrollmentForecastEndDate": *************, "expirationDate": *************}], "enrolledPunchCards": [{"_id": "uKt7XbkEmP4YvC8B4", "originalItem": {"_id": "PEbJhPZBPoDP3qqqy", "description": "Before/After Care 10-Day Punch Card", "type": "punchcard", "program": "6tGZSuegaGmw4EWvf", "numberOfDays": "10", "amount": 315, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 315, "quantity": "1", "notes": "", "type": "item", "datesUsed": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, {"_id": "DBCrP7CWem6ujHXkC", "originalItem": {"_id": "PEbJhPZBPoDP3qqqy", "description": "Before/After Care 10-Day Punch Card", "type": "punchcard", "program": "6tGZSuegaGmw4EWvf", "numberOfDays": "10", "amount": 315, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 315, "quantity": "1", "notes": "Was charged two registration fees for prior registration; edited and removed incorrect schedule. -- Mimi", "type": "item", "datesUsed": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}], "pendingCharges": [], "lastInvoiced": *************}, "invoiceReminders": {"emailsSent": 6, "lastSentTime": *************}, "checkInGroupId": null, "checkInGroupName": null, "checkedIn": false, "checkedInById": "g9XwJvGhQpn5aKAJR", "checkedInOutTime": *************, "presenceLastGroupId": "edarMyu487ryNiLqF", "previousClassroomGroupId": null, "timestamps": {"moveMomentTimestamp": {"momentId": "j6Dk6eYZt55wPwBgN", "timestamp": *************, "groupId": null, "oldGroupId": "edarMyu487ryNiLqF"}}}, {"_id": "JPETTiokTbLJ99LHy", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "designations": [], "defaultGroupId": "edarMyu487ryNiLqF", "type": "person", "profileData": {"birthday": *************, "gender": "Male", "studentGrade": "4", "document1": "Yes", "photoAttestation": "Yes"}, "createdBy": "SYSTEM", "createdAt": 1694006829060, "orgId": "ZHK6xEPdCpMfXk988", "inActive": false, "checkInGroupId": null, "checkInGroupName": null, "checkedIn": false, "checkedInById": "g9XwJvGhQpn5aKAJR", "checkedInOutTime": 1698698540338, "presenceLastGroupId": "edarMyu487ryNiLqF", "previousClassroomGroupId": null, "timestamps": {"moveMomentTimestamp": {"momentId": "rQR7TLyLwzhiAkMx6", "timestamp": *************, "groupId": null, "oldGroupId": "edarMyu487ryNiLqF"}}, "billing": {"enrolledPunchCards": [{"_id": "EdZ6dzx2KKtyMkPKE", "originalItem": {"_id": "PEbJhPZBPoDP3qqqy", "description": "Before/After Care 10-Day Punch Card", "type": "punchcard", "program": "6tGZSuegaGmw4EWvf", "numberOfDays": "10", "amount": 315, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 315, "quantity": "1", "notes": "", "type": "item", "datesUsed": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}], "pendingCharges": [], "lastInvoiced": *************}, "invoiceReminders": {"emailsSent": 2, "lastSentTime": *************}}, {"_id": "sXb9vfZnqd5MdhZ8R", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON>", "designations": [], "defaultGroupId": "hFw4TEghT9dJBNy5C", "type": "person", "profileData": {"birthday": *************, "gender": "Female", "studentGrade": "3", "document1": "Yes", "photoAttestation": "Yes"}, "createdBy": "SYSTEM", "createdAt": *************, "orgId": "mPbYpYddE4Pikmh2Q", "inActive": false, "waitlistAddedDate": null, "billing": {"enrolledPunchCards": [{"_id": "rSf8Q3jptJE3fJBKe", "originalItem": {"_id": "PEbJhPZBPoDP3qqqy", "description": "After Care 10-Day Punch Card", "type": "punchcard", "program": "AwFANXRzSKZmjY6xN", "numberOfDays": "10", "amount": 315, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 315, "quantity": "1", "notes": "", "type": "item", "datesUsed": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}], "pendingCharges": [], "lastInvoiced": *************}, "checkInGroupId": null, "checkInGroupName": null, "checkedIn": false, "checkedInOutTime": *************, "presenceLastGroupId": "hFw4TEghT9dJBNy5C", "previousClassroomGroupId": null, "timestamps": {"moveMomentTimestamp": {"momentId": "BY7bJ5B8t85F2dqQ6", "timestamp": *************, "groupId": null, "oldGroupId": "hFw4TEghT9dJBNy5C"}}}, {"_id": "D9pqnzmi82zdsrPf2", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON>", "designations": [], "defaultGroupId": "28Tm98JCpS4XCjvRG", "type": "person", "profileData": {"birthday": *************, "gender": "Female", "studentGrade": "1", "document1": "Yes", "photoAttestation": "No", "standardOutlook": {"specialNeeds": "<PERSON><PERSON> has a dual diagnosis of ADHD and Autism. She can become overstimulated and excited with new surroundings, new people, and crowds. She also gets easily distracted and wanders off. We ask that an adult stay near her and redirects her when needed."}}, "createdBy": "SYSTEM", "createdAt": 1694049210094, "orgId": "XsCRf7Y6zoFjjZmEZ", "inActive": false, "documentItems": {"scZQqEN8uRgpTgNRt": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "3gMxzj33MYAvTwhLm", "personName": "<PERSON>"}}, "g6it87zAuFDhqTyM3": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "3gMxzj33MYAvTwhLm", "personName": "<PERSON>"}}}, "billing": {"enrolledPunchCards": [{"_id": "YDY3KCJCSnSNNj7dq", "originalItem": {"_id": "PEbJhPZBPoDP3qqqy", "description": "Before/After Care 10-Day Punch Card", "type": "punchcard", "program": "QbbTmP5RqJKydrdHi", "numberOfDays": "10", "amount": 315, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 315, "quantity": "1", "notes": "", "type": "item", "datesUsed": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}], "pendingCharges": [], "lastInvoiced": *************}, "invoiceReminders": {"emailsSent": 2, "lastSentTime": *************}, "checkInGroupId": null, "checkInGroupName": null, "checkedIn": false, "checkedInOutTime": *************, "presenceLastGroupId": "28Tm98JCpS4XCjvRG", "previousClassroomGroupId": null, "timestamps": {"moveMomentTimestamp": {"momentId": "cEMTivgQtRBfcQq7Y", "timestamp": *************, "groupId": null, "oldGroupId": "28Tm98JCpS4XCjvRG"}}}, {"_id": "RPSCB2c6wLK6tuZpd", "firstName": "<PERSON>", "lastName": "<PERSON>", "designations": [], "defaultGroupId": "GBXCPsT7fEPHFdf8R", "type": "person", "profileData": {"birthday": *************, "gender": "Female", "studentGrade": "3", "document1": "Yes", "agencyIdentifier": "Virginia  Department of social services", "subsidyCode": "*********", "photoAttestation": "No"}, "createdBy": "K2zKEfyE3hQEmzHNw", "createdAt": *************, "orgId": "ExfcnDEu3Td4NZSWP", "inActive": false, "billing": {"enrolledPlans": [{"_id": "JuQ2N3CjZ4vaHK5Bg", "planDetails": {"_id": "JuQ2N3CjZ4vaHK5Bg", "description": "After School", "type": "plan", "program": "8c8ALB98NFvsHqX8C", "frequency": "scaledMonthly", "category": "tuition", "amount": 220, "scaledAmounts": [59, 112, 159, 193, 220], "ledgerAccountName": "4012", "details": {"startTime": "3:36 pm", "endTime": "6:00 pm", "regStartDate": *************, "grades": ["K", "1", "2", "3", "4", "5"], "scheduleType": "ivEwGqazmcWoWv6oJ", "dateType": "timePeriod", "timePeriod": "WgBPRArg3iHbGs2jE"}}, "enrollmentDate": *************, "allocations": [{"allocationType": "reimbursable", "amount": 99, "amountType": "dollars", "reimbursementType": "virginiaDSS", "allocationDescription": "Reimbursable: Virginia DSS", "payerStartDate": *************, "payerEndDate": *************, "id": "A5MCkGKssSagowtNM"}], "createdAt": *************, "reservationId": "CRs9rYBLpaHoKywcP", "enrollmentForecastStartDate": *************, "enrollmentForecastEndDate": *************, "overrideRate": null, "updatedAt": *************, "updatedBy": "K2zKEfyE3hQEmzHNw", "expirationDate": *************}, {"_id": "S8Ff5ATxc4jdXbebo", "planDetails": {"_id": "S8Ff5ATxc4jdXbebo", "description": "Before School", "type": "plan", "program": "8c8ALB98NFvsHqX8C", "frequency": "scaledMonthly", "category": "tuition", "amount": 163, "scaledAmounts": [44, 83, 118, 143, 163], "ledgerAccountName": "4013", "details": {"startTime": "6:00 am", "endTime": "9:00 am", "regStartDate": *************, "grades": ["K", "1", "2", "3", "4", "5"], "scheduleType": "Lm2tqkvGeTXaoi7cX", "dateType": "timePeriod", "timePeriod": "WgBPRArg3iHbGs2jE"}}, "enrollmentDate": *************, "allocations": [{"allocationType": "reimbursable", "amount": 100, "id": "A5MCkGKssSagowtNM", "amountType": "percent", "reimbursementType": "virginiaDSS", "allocationDescription": "Reimbursable: Virginia DSS", "payerStartDate": *************, "payerEndDate": *************}], "createdAt": *************, "reservationId": "PxweCEJTxAWk7PbLu", "enrollmentForecastStartDate": *************, "enrollmentForecastEndDate": *************, "overrideRate": null, "updatedAt": *************, "updatedBy": "K2zKEfyE3hQEmzHNw", "expirationDate": *************}, {"_id": "JuQ2N3CjZ4vaHK5Bg", "planDetails": {"_id": "JuQ2N3CjZ4vaHK5Bg", "description": "After School", "type": "plan", "program": "8c8ALB98NFvsHqX8C", "frequency": "scaledMonthly", "category": "tuition", "amount": 220, "scaledAmounts": [59, 112, 159, 193, 220], "ledgerAccountName": "4012", "details": {"startTime": "3:36 pm", "endTime": "6:00 pm", "regStartDate": *************, "grades": ["K", "1", "2", "3", "4", "5"], "scheduleType": "ivEwGqazmcWoWv6oJ", "dateType": "timePeriod", "timePeriod": "WgBPRArg3iHbGs2jE"}}, "enrollmentDate": *************, "allocations": [{"allocationType": "reimbursable", "amount": 160, "amountType": "dollars", "reimbursementType": "virginiaDSS", "allocationDescription": "Reimbursable: Virginia DSS", "payerStartDate": *************, "payerEndDate": *************, "id": "A5MCkGKssSagowtNM"}], "createdAt": *************, "reservationId": "qg3QmtBZ8vQPF2wHK", "enrollmentForecastStartDate": *************, "enrollmentForecastEndDate": null, "overrideRate": null, "updatedAt": *************, "updatedBy": "K2zKEfyE3hQEmzHNw"}, {"_id": "S8Ff5ATxc4jdXbebo", "planDetails": {"_id": "S8Ff5ATxc4jdXbebo", "description": "Before School", "type": "plan", "program": "8c8ALB98NFvsHqX8C", "frequency": "scaledMonthly", "category": "tuition", "amount": 163, "scaledAmounts": [44, 83, 118, 143, 163], "ledgerAccountName": "4013", "details": {"startTime": "6:00 am", "endTime": "9:00 am", "regStartDate": *************, "grades": ["K", "1", "2", "3", "4", "5"], "scheduleType": "Lm2tqkvGeTXaoi7cX", "dateType": "timePeriod", "timePeriod": "WgBPRArg3iHbGs2jE"}}, "enrollmentDate": *************, "allocations": [{"allocationType": "reimbursable", "amount": 100, "id": "A5MCkGKssSagowtNM", "amountType": "percent", "reimbursementType": "virginiaDSS", "allocationDescription": "Reimbursable: Virginia DSS", "payerStartDate": *************, "payerEndDate": *************}], "createdAt": *************, "reservationId": "v52zerrQ5PD7vQBS7", "enrollmentForecastStartDate": *************}], "lastInvoiced": *************, "pendingCharges": [], "enrolledPunchCards": [{"_id": "Xdx9j9s39gkvCxeTC", "originalItem": {"_id": "Z6JG2Z2DWRbLCzEcE", "description": "Drop In", "type": "punchcard", "program": "8c8ALB98NFvsHqX8C", "numberOfDays": "1", "category": "tuition", "amount": 30, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 30, "quantity": "1", "notes": "9/1 Non School Day at Grafton Bethel", "type": "item", "datesUsed": [0]}]}, "documentItems": {"PFxmNxSYfzqTvT3ey": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "cFjQvsNLFWS5ryjY3", "personName": "<PERSON>"}}, "oM2e5Hpd5wdLq2gT6": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "cFjQvsNLFWS5ryjY3", "personName": "<PERSON>"}}, "AQxinfbGPuFQk7DyS": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "cFjQvsNLFWS5ryjY3", "personName": "<PERSON>"}}}, "invoiceReminders": {"emailsSent": 10, "lastSentTime": *************}, "checkInGroupId": null, "checkInGroupName": null, "checkedIn": false, "checkedInById": "sb6hYLiRTfmdHL3ia", "checkedInOutTime": *************, "presenceLastGroupId": "GBXCPsT7fEPHFdf8R", "previousClassroomGroupId": null, "timestamps": {"moveMomentTimestamp": {"momentId": "iY63ZCf6eHaF9xrvH", "timestamp": 1698929129716, "groupId": null, "oldGroupId": "GBXCPsT7fEPHFdf8R"}}}, {"_id": "QhaFeA2FLzpynhnS4", "firstName": "<PERSON>", "lastName": "<PERSON>", "designations": [], "defaultGroupId": "3ws44DCKokex5Za4S", "type": "person", "profileData": {"birthday": 1360281600000, "gender": "Female", "studentGrade": "5", "document1": "Yes", "photoAttestation": "Yes", "standardOutlook": {"allergies": "Allergy to Sulfa Medication"}}, "createdBy": "SYSTEM", "createdAt": 1694056387785, "orgId": "oCjPmSBQNXtSemwJx", "inActive": false, "checkInGroupId": null, "checkInGroupName": null, "checkedIn": false, "checkedInOutTime": 1698929694085, "presenceLastGroupId": "3ws44DCKokex5Za4S", "previousClassroomGroupId": null, "timestamps": {"moveMomentTimestamp": {"momentId": "EAyFj8q29fToDP5rW", "timestamp": *************, "groupId": null, "oldGroupId": "3ws44DCKokex5Za4S"}}, "billing": {"enrolledPunchCards": [{"_id": "xQNsSNDHJRbww5va7", "originalItem": {"_id": "PWQMsSRScHF7PRrve", "description": "Drop In", "type": "punchcard", "program": "hfCyQBfD3fJW4yG4o", "numberOfDays": "1", "category": "tuition", "amount": 40, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 40, "quantity": "7", "notes": "9/7, 9/18, 9/21, 9/27, 9/28, 9/29 & 10/2", "type": "item", "datesUsed": [0]}], "pendingCharges": [], "lastInvoiced": *************}, "invoiceReminders": {"emailsSent": 10, "lastSentTime": *************}, "documentAssignments": ["kqJQonSSq6Arnjue5"]}, {"_id": "djMQrcGMKmvohimrS", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "designations": [], "defaultGroupId": "mhZes6Eqd975XZxjp", "type": "person", "profileData": {"birthday": *************, "gender": "Male", "studentGrade": "1", "document1": "Yes", "photoAttestation": "Yes"}, "createdBy": "SYSTEM", "createdAt": *************, "orgId": "9t8CuxxHdpnJdQTY7", "inActive": false, "billing": {"enrolledPunchCards": [{"_id": "9ARwKtS5tFC6wbCC2", "originalItem": {"_id": "PEbJhPZBPoDP3qqqy", "description": "Before/After Care 10-Day Punch Card", "type": "punchcard", "program": "ggMPoYpEjXwrHmjfF", "numberOfDays": "10", "amount": 315, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 315, "quantity": "1", "notes": "", "type": "item", "datesUsed": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}], "pendingCharges": [], "lastInvoiced": *************}, "checkInGroupId": null, "checkInGroupName": null, "checkedIn": false, "checkedInById": "ZdQiL9ur3GJWB58Aw", "checkedInOutTime": *************, "presenceLastGroupId": "mhZes6Eqd975XZxjp", "previousClassroomGroupId": null, "timestamps": {"moveMomentTimestamp": {"momentId": "aiYKi9ksyYtcRvd2W", "timestamp": *************, "groupId": null, "oldGroupId": "mhZes6Eqd975XZxjp"}}, "documentItems": {"GthN9prXcK6xo3WvY": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "BWTPHbXBcT9He5tvq", "personName": "<PERSON>"}}, "6kwTZAXL2feikQK45": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "BWTPHbXBcT9He5tvq", "personName": "<PERSON>"}}, "by2PjccTmXKcwGxZB": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "BWTPHbXBcT9He5tvq", "personName": "<PERSON>"}}}, "invoiceReminders": {"emailsSent": 9, "lastSentTime": 1696234225884}}, {"_id": "pfyWhfsLoEGBZgBBu", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "designations": [], "defaultGroupId": "gJYvsbNA8v7ANvS2P", "type": "person", "createdBy": "kJW6jPyrvWapJ2kZH", "createdAt": *************, "orgId": "mDN7x8LzZDTmGpctP", "inActive": false, "billing": {"enrolledPunchCards": [{"_id": "ZoMJ9yD9pcDeoJpYC", "originalItem": {"_id": "PEbJhPZBPoDP3qqqy", "description": "Before/After Care 10-Day Punch Card", "type": "punchcard", "program": "skb5YvziKPBjvgoyx", "numberOfDays": "10", "amount": 270, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 270, "quantity": "1", "notes": "", "type": "item", "datesUsed": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}], "pendingCharges": [], "lastInvoiced": *************, "billingNotes": "Oct 10, 2023 - removed extra punch card added on 9/8 by <PERSON><PERSON><PERSON> Lee. Sent invoice for original - bry"}, "checkInGroupId": null, "checkInGroupName": null, "checkedIn": false, "checkedInById": "6TvBePNKukXgywJQR", "checkedInOutTime": *************, "presenceLastGroupId": "gJYvsbNA8v7ANvS2P", "previousClassroomGroupId": null, "timestamps": {"moveMomentTimestamp": {"momentId": "qPfCzL7gqxTKQuhdn", "timestamp": *************, "groupId": null, "oldGroupId": "gJYvsbNA8v7ANvS2P"}}, "invoiceReminders": {"emailsSent": 1, "lastSentTime": *************}}, {"_id": "E8Nkh8J8ecwJhuFjG", "firstName": "<PERSON>", "lastName": "Roa", "designations": [], "defaultGroupId": "gJYvsbNA8v7ANvS2P", "type": "person", "profileData": {"birthday": *************, "gender": "Male", "studentGrade": "2", "document1": "Yes", "photoAttestation": "No", "standardOutlook": {"allergies": "Allergic to cashews."}}, "createdBy": "SYSTEM", "createdAt": *************, "orgId": "mDN7x8LzZDTmGpctP", "inActive": false, "billing": {"enrolledPunchCards": [{"_id": "KAQ4cMcvNRGBdcxeF", "originalItem": {"_id": "PEbJhPZBPoDP3qqqy", "description": "Before/After Care 10-Day Punch Card", "type": "punchcard", "program": "skb5YvziKPBjvgoyx", "numberOfDays": "10", "amount": 270, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 270, "quantity": "1", "notes": "", "type": "item", "datesUsed": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}], "pendingCharges": [], "lastInvoiced": *************, "enrolledPlans": [{"_id": "hgn9bEoqERx7NNgTa", "planDetails": {"_id": "hgn9bEoqERx7NNgTa", "description": "After School", "type": "plan", "program": "skb5YvziKPBjvgoyx", "frequency": "scaledMonthly", "category": "tuition", "amount": 278, "scaledAmounts": [114, 177, 234, 259, 278], "ledgerAccountName": "4012", "details": {"startTime": "3:20 pm", "endTime": "6:00 pm", "regStartDate": *************, "regEndDate": *************, "grades": ["1", "2", "3", "4", "5"], "scheduleType": "ivEwGqazmcWoWv6oJ", "dateType": "timePeriod", "timePeriod": "yx2eZZgpeZzb8G2vW"}}, "enrollmentDate": *************, "allocations": [{"allocationType": "discount", "amount": 100, "amountType": "percent", "discountType": "amDiscount", "allocationDescription": "Discount: AM Discount", "id": "EbxiEyyj9d8YBGomh"}], "createdAt": *************, "reservationId": "bQCAZTiKnQrQ7q8uW", "enrollmentForecastStartDate": *************, "enrollmentForecastEndDate": *************, "expirationDate": *************}], "billingNotes": "AM approved 100% scholarship; See Case ******** (<PERSON><PERSON> C<PERSON>)"}, "checkInGroupId": null, "checkInGroupName": null, "checkedIn": false, "checkedInById": "6TvBePNKukXgywJQR", "checkedInOutTime": *************, "presenceLastGroupId": "gJYvsbNA8v7ANvS2P", "previousClassroomGroupId": null, "timestamps": {"moveMomentTimestamp": {"momentId": "zaAKqwCn4AGeixW5m", "timestamp": 1698268374655, "groupId": null, "oldGroupId": "gJYvsbNA8v7ANvS2P"}}}, {"_id": "WXAbkkuHdJbeWmzhX", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON>", "designations": [], "defaultGroupId": "hFw4TEghT9dJBNy5C", "type": "person", "profileData": {"birthday": *************, "gender": "Female", "studentGrade": "3", "document1": "Yes", "photoAttestation": "Yes"}, "createdBy": "SYSTEM", "createdAt": *************, "orgId": "mPbYpYddE4Pikmh2Q", "inActive": false, "billing": {"enrolledPunchCards": [{"_id": "GzkZKqurSegQaH2Ac", "originalItem": {"_id": "PEbJhPZBPoDP3qqqy", "description": "After Care 10-Day Punch Card", "type": "punchcard", "program": "AwFANXRzSKZmjY6xN", "numberOfDays": "10", "amount": 315, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 315, "quantity": "1", "notes": "", "type": "item", "datesUsed": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}], "pendingCharges": [], "lastInvoiced": *************}, "checkInGroupId": null, "checkInGroupName": null, "checkedIn": false, "checkedInOutTime": *************, "presenceLastGroupId": "hFw4TEghT9dJBNy5C", "previousClassroomGroupId": null, "timestamps": {"moveMomentTimestamp": {"momentId": "2opwrhph3YZdg8ABA", "timestamp": *************, "groupId": null, "oldGroupId": "hFw4TEghT9dJBNy5C"}}, "documentItems": {"iS9aLZ9akw42aXPzK": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "kPYdjaLQghJDNSCCt", "personName": "<PERSON><PERSON>"}}}}, {"_id": "5P5dZPLZvTToT4XZb", "firstName": "<PERSON> ", "lastName": "Sc<PERSON><PERSON><PERSON>", "designations": [], "defaultGroupId": "hFw4TEghT9dJBNy5C", "type": "person", "createdBy": "aLMvyrLWeL3ZW9MSn", "createdAt": *************, "orgId": "mPbYpYddE4Pikmh2Q", "inActive": false, "billing": {"enrolledPunchCards": [{"_id": "kpjbdfyzNWoRYGYfs", "originalItem": {"_id": "PEbJhPZBPoDP3qqqy", "description": "After Care 10-Day Punch Card", "type": "punchcard", "program": "AwFANXRzSKZmjY6xN", "numberOfDays": "10", "amount": 315, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 315, "quantity": "1", "notes": "", "type": "item", "datesUsed": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}], "pendingCharges": [], "lastInvoiced": *************}, "documentItems": {"iS9aLZ9akw42aXPzK": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "eS53x5c6ixYZqSWeL", "personName": "<PERSON>"}}}}, {"_id": "XsfBZtJgxoEpBLvtX", "firstName": "Brayden", "lastName": "Draper", "designations": [], "defaultGroupId": "jhHyeEMuL3PTtxNQk", "type": "person", "profileData": {"birthday": *************, "gender": "Male", "studentGrade": "3", "document1": "Yes", "photoAttestation": "Yes"}, "createdBy": "hnEFX5czZQnoPi38M", "createdAt": *************, "orgId": "gJ66DDKJf5ZktvQXP", "inActive": false, "billing": {"enrolledPunchCards": [{"_id": "a27PK2XMJLhpziQg5", "originalItem": {"_id": "nTim89qqQQjfX4dtg", "description": "Drop In", "type": "punchcard", "program": "L4uwGjJKSCDXwT6Nz", "numberOfDays": "1", "category": "tuition", "amount": 30, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 30, "quantity": "1", "notes": "", "type": "item", "appliedDiscounts": [{"type": "discount", "amount": null, "source": "coupon", "originalAllocation": {"allocationType": "discount", "amount": 50, "amountType": "percent", "discountType": "districtDiscount", "allocationDescription": "District Discount", "id": "iS4r8nS7eynjkRKvb"}}], "datesUsed": [0]}], "pendingCharges": [], "lastInvoiced": *************}, "invoiceReminders": {"emailsSent": 10, "lastSentTime": *************}}, {"_id": "7aubz2SN45zWrdzbA", "firstName": "<PERSON>", "lastName": "Draper", "designations": [], "defaultGroupId": "jhHyeEMuL3PTtxNQk", "type": "person", "profileData": {"birthday": *************, "gender": "Female", "studentGrade": "1", "document1": "Yes", "photoAttestation": "Yes"}, "createdBy": "hnEFX5czZQnoPi38M", "createdAt": *************, "orgId": "gJ66DDKJf5ZktvQXP", "inActive": false, "billing": {"enrolledPunchCards": [{"_id": "iA2YgonQYtmFALyBZ", "originalItem": {"_id": "nTim89qqQQjfX4dtg", "description": "Drop In", "type": "punchcard", "program": "L4uwGjJKSCDXwT6Nz", "numberOfDays": "1", "category": "tuition", "amount": 30, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 30, "quantity": "1", "notes": "", "type": "item", "appliedDiscounts": [{"type": "discount", "amount": 3, "source": "coupon", "originalAllocation": {"id": "nqDHpGXwHjh3fbmKa", "allocationType": "discount", "amount": 10, "amountType": "percent", "discountType": "siblingDiscount", "allocationDescription": "Sibling Discount", "discountAmount": 3}}, {"type": "discount", "amount": null, "source": "coupon", "originalAllocation": {"allocationType": "discount", "amount": 50, "amountType": "percent", "discountType": "districtDiscount", "allocationDescription": "District Discount", "id": "H8BMYWcA22hFa6hyH"}}], "datesUsed": [0]}], "pendingCharges": [], "lastInvoiced": *************}, "invoiceReminders": {"emailsSent": 10, "lastSentTime": *************}}, {"_id": "34YmcQydWgfFvPtBT", "firstName": "<PERSON>", "lastName": "Stellon", "designations": [], "defaultGroupId": "CC3fzCTCbzRMJGys5", "type": "person", "profileData": {"birthday": *************, "gender": "Male", "studentGrade": "2", "document1": "Yes", "photoAttestation": "Yes"}, "createdBy": "SYSTEM", "createdAt": *************, "orgId": "NcQvtvp2KWBpAFopN", "inActive": false, "billing": {"enrolledPunchCards": [{"_id": "QTDDaSnjr73rdhnp7", "originalItem": {"_id": "PEbJhPZBPoDP3qqqy", "description": "Before/After Care 10-Day Punch Card", "type": "punchcard", "program": "GumirkZjpvRLLsEPX", "numberOfDays": "10", "amount": 270, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 270, "quantity": "1", "notes": "", "type": "item", "datesUsed": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}], "pendingCharges": [], "lastInvoiced": *************}, "documentItems": {"vZ8wFASBFB3nwp6eK": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "emvR6QCYky5Nqc959", "personName": "<PERSON>"}}, "LP3xSDjEW4Z7W9YfK": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "emvR6QCYky5Nqc959", "personName": "<PERSON>"}}, "N8J2nK2u4j3rg3uMK": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "emvR6QCYky5Nqc959", "personName": "<PERSON>"}}, "YgLMZ6Er9SyQvYJBL": {"createdAt": *************, "createdByPersonId": "emvR6QCYky5Nqc959", "createdByUserId": "9gk5kE7gcHbgB372k", "repositoryKey": "NcQvtvp2KWBpAFopN/9gk5kE7gcHbgB372k/hvpfTN2AEQTrRZUFRdEc", "token": "hvpfTN2AEQTrRZUFRdEc"}}, "invoiceReminders": {"emailsSent": 5, "lastSentTime": *************}, "checkInGroupId": null, "checkInGroupName": null, "checkedIn": false, "checkedInOutTime": 1695801977314, "presenceLastGroupId": null, "previousClassroomGroupId": null, "timestamps": {"moveMomentTimestamp": {"momentId": "kLhvHTLrfcYjLXyDD", "timestamp": 1695757706541, "groupId": "CC3fzCTCbzRMJGys5", "oldGroupId": null}}}, {"_id": "TatGLPitoxeKJHQkn", "firstName": "<PERSON>", "lastName": "Stellon", "designations": [], "defaultGroupId": "CC3fzCTCbzRMJGys5", "type": "person", "profileData": {"birthday": *************, "gender": "Female", "studentGrade": "K", "document1": "Yes", "photoAttestation": "Yes"}, "createdBy": "SYSTEM", "createdAt": *************, "orgId": "NcQvtvp2KWBpAFopN", "inActive": false, "billing": {"enrolledPunchCards": [{"_id": "5fdofqREwg2gQ7TGM", "originalItem": {"_id": "PEbJhPZBPoDP3qqqy", "description": "Before/After Care 10-Day Punch Card", "type": "punchcard", "program": "GumirkZjpvRLLsEPX", "numberOfDays": "10", "amount": 270, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 270, "quantity": "1", "notes": "", "type": "item", "appliedDiscounts": [{"type": "discount", "amount": 27, "source": "coupon", "originalAllocation": {"id": "kqEc6PsqNsvhey7ja", "allocationType": "discount", "amount": 10, "amountType": "percent", "discountType": "siblingDiscount", "allocationDescription": "Sibling Discount", "discountAmount": 27}}], "datesUsed": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}], "pendingCharges": [], "lastInvoiced": *************}, "documentItems": {"vZ8wFASBFB3nwp6eK": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "emvR6QCYky5Nqc959", "personName": "<PERSON>"}}, "LP3xSDjEW4Z7W9YfK": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "emvR6QCYky5Nqc959", "personName": "<PERSON>"}}, "N8J2nK2u4j3rg3uMK": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "emvR6QCYky5Nqc959", "personName": "<PERSON>"}}, "YgLMZ6Er9SyQvYJBL": {"createdAt": 1695133279206, "createdByPersonId": "emvR6QCYky5Nqc959", "createdByUserId": "9gk5kE7gcHbgB372k", "repositoryKey": "NcQvtvp2KWBpAFopN/9gk5kE7gcHbgB372k/49tWnT24ciBqDQSVJHbE", "token": "49tWnT24ciBqDQSVJHbE"}}, "invoiceReminders": {"emailsSent": 5, "lastSentTime": 1696234285569}, "checkInGroupId": null, "checkInGroupName": null, "checkedIn": false, "checkedInOutTime": 1695801978134, "presenceLastGroupId": null, "previousClassroomGroupId": null, "timestamps": {"moveMomentTimestamp": {"momentId": "Y3Zh3CZ5Lsk9fQYXP", "timestamp": 1695757706914, "groupId": "CC3fzCTCbzRMJGys5", "oldGroupId": null}}}, {"_id": "dZphfbgsMe2N9fdB8", "firstName": "<PERSON><PERSON>", "lastName": "Troy", "designations": [], "defaultGroupId": "DpMoZbQFKNpFcJYLg", "type": "person", "profileData": {"birthday": *************, "gender": "Female", "studentGrade": "2", "document1": "Yes", "photoAttestation": "Yes"}, "createdBy": "SYSTEM", "createdAt": *************, "orgId": "iHZSWasaCNuxKvYcg", "inActive": false, "billing": {"enrolledPunchCards": [{"_id": "ZCRcLpCyotY2x2sEE", "originalItem": {"_id": "EFjvjQmwr4NSYvw2N", "description": "Drop In", "type": "punchcard", "program": "oxeGTPQPHG6M4SC94", "numberOfDays": "1", "category": "tuition", "amount": 40, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 40, "quantity": "1", "notes": "", "type": "item", "appliedDiscounts": [{"type": "discount", "amount": 4, "source": "coupon", "originalAllocation": {"id": "hEm7dirq3JX5hm3M2", "allocationType": "discount", "amount": 10, "amountType": "percent", "discountType": "siblingDiscount", "allocationDescription": "Sibling Discount", "discountAmount": 4}}], "datesUsed": [0]}, {"_id": "X5CQiWnofAXEdWwpn", "originalItem": {"_id": "EFjvjQmwr4NSYvw2N", "description": "Drop In", "type": "punchcard", "program": "oxeGTPQPHG6M4SC94", "numberOfDays": "1", "category": "tuition", "amount": 40, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 40, "quantity": "1", "notes": "", "type": "item", "datesUsed": [0]}], "pendingCharges": [], "lastInvoiced": *************}, "documentItems": {"fLhkcpywsDBZPbWsT": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "h3WHCLeEDHRuQFb84", "personName": "julia troy"}}, "xfeLmHtKnu53AzxZW": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "h3WHCLeEDHRuQFb84", "personName": "julia troy"}}, "czpbraHbtwGc4S7zd": {"createdAt": *************, "createdByPersonId": "h3WHCLeEDHRuQFb84", "createdByUserId": "dxCTSDdYBMgMLXTkB", "repositoryKey": "iHZSWasaCNuxKvYcg/dxCTSDdYBMgMLXTkB/wibsFHRRDkX6wewoCNtp", "token": "wibsFHRRDkX6wewoCNtp"}}, "waitlistAddedDate": null}, {"_id": "peNb5NpfEZQ4pmZ7c", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "designations": [], "defaultGroupId": "DpMoZbQFKNpFcJYLg", "type": "person", "profileData": {"birthday": *************, "gender": "Male", "studentGrade": "3", "document1": "Yes", "photoAttestation": "Yes"}, "createdBy": "SYSTEM", "createdAt": *************, "orgId": "iHZSWasaCNuxKvYcg", "inActive": false, "billing": {"enrolledPunchCards": [{"_id": "DDSskMCDGoR3pQjDf", "originalItem": {"_id": "EFjvjQmwr4NSYvw2N", "description": "Drop In", "type": "punchcard", "program": "oxeGTPQPHG6M4SC94", "numberOfDays": "1", "category": "tuition", "amount": 40, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 40, "quantity": "1", "notes": "", "type": "item", "datesUsed": [0]}], "pendingCharges": [], "lastInvoiced": *************}, "documentItems": {"fLhkcpywsDBZPbWsT": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "ezkRkCEyvaJiw8cny", "personName": "<PERSON>"}}, "xfeLmHtKnu53AzxZW": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "ezkRkCEyvaJiw8cny", "personName": "<PERSON>"}}}}, {"_id": "vHcxagJwvfAKpT7NL", "firstName": "<PERSON>", "lastName": "Blette", "designations": [], "defaultGroupId": "8aofWk2QiLuByLubc", "type": "person", "profileData": {"birthday": *************, "gender": "Female", "studentGrade": "2", "document1": "Yes", "photoAttestation": "Yes"}, "createdBy": "SYSTEM", "createdAt": *************, "orgId": "tkW6vSxiqnyhu5RWb", "inActive": false, "billing": {"enrolledPunchCards": [{"_id": "bC97i7FyYSSWcSwn6", "originalItem": {"_id": "PEbJhPZBPoDP3qqqy", "description": "Before/After Care 10-Day Punch Card", "type": "punchcard", "program": "hMzsQubE7mxcM7QnR", "numberOfDays": "10", "amount": 315, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 315, "quantity": "1", "notes": "", "type": "item", "datesUsed": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}], "pendingCharges": [], "lastInvoiced": *************}, "checkInGroupId": null, "checkInGroupName": null, "checkedIn": false, "checkedInOutTime": *************, "presenceLastGroupId": "8aofWk2QiLuByLubc", "previousClassroomGroupId": null, "timestamps": {"moveMomentTimestamp": {"momentId": "ZAKcFejh66nKP7BEF", "timestamp": *************, "groupId": null, "oldGroupId": "8aofWk2QiLuByLubc"}}}, {"_id": "ztXbjFSmx3BDHTKAQ", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "designations": [], "defaultGroupId": "2nx5QPv9nRNw4etEH", "type": "person", "profileData": {"birthday": *************, "gender": "Male", "studentGrade": "2", "document1": "Yes", "photoAttestation": "Yes"}, "createdBy": "SYSTEM", "createdAt": *************, "orgId": "aTAhaqGzDFQXN8g5e", "inActive": false, "billing": {"enrolledPunchCards": [{"_id": "q4NSmPn3eEkuNnWNR", "originalItem": {"_id": "kmbBD75y39i7j6dvF", "description": "Drop In", "type": "punchcard", "program": "FnCys7JwbXhcpRr5T", "numberOfDays": "1", "category": "tuition", "amount": 30, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 30, "quantity": "1", "notes": "", "type": "item", "appliedDiscounts": [{"type": "discount", "amount": 3, "source": "coupon", "originalAllocation": {"id": "kxgnqwoeF5dYa8EAH", "allocationType": "discount", "amount": 10, "amountType": "percent", "discountType": "siblingDiscount", "allocationDescription": "Sibling Discount", "discountAmount": 3}}], "datesUsed": [0]}], "pendingCharges": [], "lastInvoiced": *************, "enrolledItems": [{"_id": "yi7PqABeQqmAc3578", "originalItem": {"_id": "6KFArgdExpXuZMkjP", "description": "Non School Day: 10/2/2023", "type": "item", "program": "Y3LWz63aoP8YMzJyw", "dropInDailyRate": true, "amount": 30, "scaledAmounts": [], "ledgerAccountName": "4104", "details": {"startTime": "6:00 am", "endTime": "6:00 pm", "regStartDate": *************, "regEndDate": *************, "grades": ["K", "1", "2", "3", "4", "5"], "scheduleType": "52QiAsuJuDxHRMRnz", "dateType": "individualDates", "individualDates": [*************]}}, "createdAt": *************, "price": 30, "quantity": "1", "notes": "", "type": "item"}]}, "documentItems": {"HpmhRx6EPyiTrWfuh": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "ieWsj8tNzxK6t5nhG", "personName": "<PERSON>"}}, "K9tWPi6sY4cSrQCTs": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "ieWsj8tNzxK6t5nhG", "personName": "<PERSON>"}}, "6uuqAPEddNrxBzwrp": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "ieWsj8tNzxK6t5nhG", "personName": "<PERSON>"}}, "rg9dQHMQAXA6xymLa": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "ieWsj8tNzxK6t5nhG", "personName": "<PERSON>"}}}, "waitlistAddedDate": null}, {"_id": "JQWKTdwau7xoxLM9h", "firstName": "Melody", "lastName": "<PERSON><PERSON><PERSON>", "designations": [], "defaultGroupId": "CC3fzCTCbzRMJGys5", "type": "person", "profileData": {"birthday": *************, "gender": "Female", "studentGrade": "K", "document1": "Yes", "photoAttestation": "Yes"}, "createdBy": "SYSTEM", "createdAt": *************, "orgId": "NcQvtvp2KWBpAFopN", "inActive": false, "billing": {"enrolledPunchCards": [{"_id": "r8zxBRwDekrX8PS3m", "originalItem": {"_id": "PEbJhPZBPoDP3qqqy", "description": "Before/After Care 10-Day Punch Card", "type": "punchcard", "program": "GumirkZjpvRLLsEPX", "numberOfDays": "10", "amount": 270, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 270, "quantity": "1", "notes": "", "type": "item", "datesUsed": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}], "pendingCharges": [], "lastInvoiced": *************}, "documentItems": {"vZ8wFASBFB3nwp6eK": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "FsChbia2wM3YxFqiM", "personName": "<PERSON> "}}, "LP3xSDjEW4Z7W9YfK": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "FsChbia2wM3YxFqiM", "personName": "<PERSON> "}}, "N8J2nK2u4j3rg3uMK": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "FsChbia2wM3YxFqiM", "personName": "<PERSON> "}}, "7JDzx75RraTgM49Xt": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "FsChbia2wM3YxFqiM", "personName": "<PERSON> "}}, "HbKT2fvwnuoEsA6WC": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "FsChbia2wM3YxFqiM", "personName": "<PERSON> "}}}, "checkInGroupId": null, "checkInGroupName": null, "checkedIn": false, "checkedInById": "FsChbia2wM3YxFqiM", "checkedInOutTime": 1698843480000, "presenceLastGroupId": "CC3fzCTCbzRMJGys5", "previousClassroomGroupId": null, "timestamps": {"moveMomentTimestamp": {"momentId": "K8cArMbwc8cqYYMLx", "timestamp": 1698877402447, "groupId": null, "oldGroupId": "CC3fzCTCbzRMJGys5"}}}, {"_id": "HiYmALqKMr3zJ6Ba5", "firstName": "Roman", "lastName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "designations": [], "defaultGroupId": "wSMvyQR53mBkvp8EF", "type": "person", "profileData": {"birthday": *************, "gender": "Male", "studentGrade": "2", "document1": "Yes", "photoAttestation": "Yes"}, "createdBy": "SYSTEM", "createdAt": *************, "orgId": "ogGcnQrn5jpkp8qZD", "inActive": false, "billing": {"enrolledItems": [{"_id": "9BWhTM7iiYbRs7foW", "originalItem": {"_id": "oLzpT4rf5hyoJjZvS", "description": "Non School Day: 01/02/2024", "type": "item", "program": "MnpZ5RfAcjmmKAYw4", "amount": 30, "scaledAmounts": [], "ledgerAccountName": "4104", "details": {"startTime": "6:00 am", "endTime": "6:00 pm", "regStartDate": *************, "regEndDate": *************, "grades": ["K", "1", "2", "3", "4", "5"], "scheduleType": "TeNPa4ooxkvPj49Kq", "dateType": "individualDates", "individualDates": [*************]}}, "createdAt": *************, "price": 30, "quantity": "1", "notes": "", "type": "item", "appliedDiscounts": [{"type": "discount", "amount": 30, "source": "coupon", "originalAllocation": {"allocationType": "discount", "amount": 100, "amountType": "percent", "discountType": "staffDiscount", "allocationDescription": "Employee Discount", "id": "WpQrCBs79h8cyGxwb", "discountAmount": 30}}]}, {"_id": "wi98MQbkZDDjpSSv3", "originalItem": {"_id": "oAkiyFuxh9EQDQaFc", "description": "Non School Day: 11/7/2023", "type": "item", "program": "MnpZ5RfAcjmmKAYw4", "amount": 30, "scaledAmounts": [], "ledgerAccountName": "4104", "details": {"startTime": "6:00 am", "endTime": "6:00 pm", "regStartDate": *************, "regEndDate": *************, "grades": ["K", "1", "2", "3", "4", "5"], "scheduleType": "TeNPa4ooxkvPj49Kq", "dateType": "individualDates", "individualDates": [*************]}}, "createdAt": *************, "price": 30, "quantity": "1", "notes": "", "type": "item", "appliedDiscounts": [{"type": "discount", "amount": 30, "source": "coupon", "originalAllocation": {"allocationType": "discount", "amount": 100, "amountType": "percent", "discountType": "staffDiscount", "allocationDescription": "Employee Discount", "id": "DPcMPyFobaztCxN8H", "discountAmount": 30}}]}, {"_id": "kuoQfCAiGDg9bLz2Q", "originalItem": {"_id": "oLzpT4rf5hyoJjZvS", "description": "Non School Day: 01/02/2024", "type": "item", "program": "MnpZ5RfAcjmmKAYw4", "amount": 30, "scaledAmounts": [], "ledgerAccountName": "4104", "details": {"startTime": "6:00 am", "endTime": "6:00 pm", "regStartDate": *************, "regEndDate": *************, "grades": ["K", "1", "2", "3", "4", "5"], "scheduleType": "TeNPa4ooxkvPj49Kq", "dateType": "individualDates", "individualDates": [*************]}}, "createdAt": *************, "price": 30, "quantity": "1", "notes": "", "type": "item", "appliedDiscounts": [{"type": "discount", "amount": 30, "source": "coupon", "originalAllocation": {"allocationType": "discount", "amount": 100, "amountType": "percent", "discountType": "coupon", "code": "lewis123", "allocationDescription": "100% Discount for Bryan's NSD and Camp Tests", "id": "QxPazoHTsjrMAdZBS", "discountAmount": 30}}]}, {"_id": "mKx4naqmGyPg7rtYX", "originalItem": {"_id": "xZ2axGHi55JRtXc4G", "description": "Non School Day: 01/26/2024", "type": "item", "program": "MnpZ5RfAcjmmKAYw4", "amount": 30, "scaledAmounts": [], "ledgerAccountName": "4104", "details": {"startTime": "6:00 am", "endTime": "6:00 pm", "regStartDate": *************, "regEndDate": *************, "grades": ["K", "1", "2", "3", "4", "5"], "scheduleType": "TeNPa4ooxkvPj49Kq", "dateType": "individualDates", "individualDates": [*************]}}, "createdAt": *************, "price": 30, "quantity": "1", "notes": "", "type": "item", "appliedDiscounts": [{"type": "discount", "amount": 30, "source": "coupon", "originalAllocation": {"allocationType": "discount", "amount": 100, "amountType": "percent", "discountType": "coupon", "code": "lewis123", "allocationDescription": "100% Discount for Bryan's NSD and Camp Tests", "id": "tvuaskAhtqmbRkCJF", "discountAmount": 30}}]}, {"_id": "KvRbHaeGEyNB6Lbps", "originalItem": {"_id": "oLzpT4rf5hyoJjZvS", "description": "Non School Day: 01/02/2024", "type": "item", "program": "MnpZ5RfAcjmmKAYw4", "amount": 30, "scaledAmounts": [], "ledgerAccountName": "4104", "details": {"startTime": "6:00 am", "endTime": "6:00 pm", "regStartDate": *************, "regEndDate": *************, "grades": ["K", "1", "2", "3", "4", "5"], "scheduleType": "TeNPa4ooxkvPj49Kq", "dateType": "individualDates", "individualDates": [*************]}}, "createdAt": *************, "price": 30, "quantity": "1", "notes": "", "type": "item", "appliedDiscounts": [{"type": "discount", "amount": 30, "source": "coupon", "originalAllocation": {"allocationType": "discount", "amount": 100, "amountType": "percent", "discountType": "coupon", "code": "lewis123", "allocationDescription": "100% Discount for Bryan's NSD and Camp Tests", "id": "QxPazoHTsjrMAdZBS", "discountAmount": 30}}]}, {"_id": "BBFCfwgMePdbQqQ5i", "originalItem": {"_id": "xZ2axGHi55JRtXc4G", "description": "Non School Day: 01/26/2024", "type": "item", "program": "MnpZ5RfAcjmmKAYw4", "amount": 30, "scaledAmounts": [], "ledgerAccountName": "4104", "details": {"startTime": "6:00 am", "endTime": "6:00 pm", "regStartDate": *************, "regEndDate": *************, "grades": ["K", "1", "2", "3", "4", "5"], "scheduleType": "TeNPa4ooxkvPj49Kq", "dateType": "individualDates", "individualDates": [*************]}}, "createdAt": *************, "price": 30, "quantity": "1", "notes": "", "type": "item", "appliedDiscounts": [{"type": "discount", "amount": 30, "source": "coupon", "originalAllocation": {"allocationType": "discount", "amount": 100, "amountType": "percent", "discountType": "coupon", "code": "lewis123", "allocationDescription": "100% Discount for Bryan's NSD and Camp Tests", "id": "tvuaskAhtqmbRkCJF", "discountAmount": 30}}]}, {"_id": "7EP3xsxEMbtpPbB5W", "originalItem": {"_id": "oLzpT4rf5hyoJjZvS", "description": "Non School Day: 01/02/2024", "type": "item", "program": "MnpZ5RfAcjmmKAYw4", "amount": 30, "scaledAmounts": [], "ledgerAccountName": "4104", "details": {"startTime": "6:00 am", "endTime": "6:00 pm", "regStartDate": *************, "regEndDate": *************, "grades": ["K", "1", "2", "3", "4", "5"], "scheduleType": "TeNPa4ooxkvPj49Kq", "dateType": "individualDates", "individualDates": [*************]}}, "createdAt": *************, "price": 30, "quantity": "1", "notes": "", "type": "item", "appliedDiscounts": [{"type": "discount", "amount": 30, "source": "coupon", "originalAllocation": {"id": "4o3bMxFTwfry3QcPp", "allocationType": "discount", "amount": 100, "amountType": "percent", "discountType": "coupon", "code": "lewis123", "allocationDescription": "100% Discount for Bryan's NSD and Camp Tests", "discountAmount": 30}}, {"type": "discount", "amount": 30, "source": "coupon", "originalAllocation": {"allocationType": "discount", "amount": 100, "amountType": "percent", "discountType": "coupon", "code": "lewis123", "allocationDescription": "100% Discount for Bryan's NSD and Camp Tests", "id": "dETjcretqiDpcZSJ4", "discountAmount": 30}}]}, {"_id": "sY6aJrFSMCEs43N6W", "originalItem": {"_id": "xZ2axGHi55JRtXc4G", "description": "Non School Day: 01/26/2024", "type": "item", "program": "MnpZ5RfAcjmmKAYw4", "amount": 30, "scaledAmounts": [], "ledgerAccountName": "4104", "details": {"startTime": "6:00 am", "endTime": "6:00 pm", "regStartDate": *************, "regEndDate": *************, "grades": ["K", "1", "2", "3", "4", "5"], "scheduleType": "TeNPa4ooxkvPj49Kq", "dateType": "individualDates", "individualDates": [*************]}}, "createdAt": *************, "price": 30, "quantity": "1", "notes": "", "type": "item", "appliedDiscounts": [{"type": "discount", "amount": 30, "source": "coupon", "originalAllocation": {"id": "RHHwzxiBCiRJ9F6QG", "allocationType": "discount", "amount": 100, "amountType": "percent", "discountType": "coupon", "code": "lewis123", "allocationDescription": "100% Discount for Bryan's NSD and Camp Tests", "discountAmount": 30}}, {"type": "discount", "amount": 30, "source": "coupon", "originalAllocation": {"allocationType": "discount", "amount": 100, "amountType": "percent", "discountType": "coupon", "code": "lewis123", "allocationDescription": "100% Discount for Bryan's NSD and Camp Tests", "id": "qMuAvADfgthcYS3xz", "discountAmount": 30}}]}, {"_id": "qZSjYnE99uTirxpz5", "originalItem": {"_id": "BsBN5pKzDCjtHdN8z", "description": "Non School Day: 10/02/2023", "type": "item", "program": "MnpZ5RfAcjmmKAYw4", "amount": 30, "scaledAmounts": [], "ledgerAccountName": "4104", "details": {"startTime": "6:00 am", "endTime": "6:00 pm", "regStartDate": *************, "regEndDate": *************, "grades": ["K", "1", "2", "3", "4", "5"], "scheduleType": "TeNPa4ooxkvPj49Kq", "dateType": "individualDates", "individualDates": [*************]}}, "createdAt": *************, "price": 30, "quantity": "1", "notes": "", "type": "item", "appliedDiscounts": [{"type": "discount", "amount": 30, "source": "coupon", "originalAllocation": {"allocationType": "discount", "amount": 100, "amountType": "percent", "discountType": "coupon", "code": "lewis123", "allocationDescription": "100% Discount for Bryan's NSD and Camp Tests", "id": "x3P9cQxocxB2DeAs7", "discountAmount": 30}}]}, {"_id": "4Kc7nMNY2b6ortsxJ", "originalItem": {"_id": "oLzpT4rf5hyoJjZvS", "description": "Non School Day: 01/02/2024", "type": "item", "program": "MnpZ5RfAcjmmKAYw4", "amount": 30, "scaledAmounts": [], "ledgerAccountName": "4020", "details": {"startTime": "6:00 am", "endTime": "6:00 pm", "regStartDate": *************, "regEndDate": *************, "grades": ["K", "1", "2", "3", "4", "5"], "scheduleType": "TeNPa4ooxkvPj49Kq", "dateType": "individualDates", "individualDates": [*************]}}, "createdAt": *************, "price": 30, "quantity": "1", "notes": "", "type": "item", "appliedDiscounts": [{"type": "discount", "amount": 30, "source": "coupon", "originalAllocation": {"allocationType": "discount", "amount": 100, "amountType": "percent", "discountType": "coupon", "code": "lewis123", "allocationDescription": "100% Discount for Bryan's NSD and Camp Tests", "id": "vBFCrPaNxvMEMoZ39", "discountAmount": 30}}]}, {"_id": "yB9cxijBJYkY6ZRtc", "originalItem": {"_id": "xZ2axGHi55JRtXc4G", "description": "Non School Day: 01/26/2024", "type": "item", "program": "MnpZ5RfAcjmmKAYw4", "amount": 30, "scaledAmounts": [], "ledgerAccountName": "4020", "details": {"startTime": "6:00 am", "endTime": "6:00 pm", "regStartDate": *************, "regEndDate": *************, "grades": ["K", "1", "2", "3", "4", "5"], "scheduleType": "TeNPa4ooxkvPj49Kq", "dateType": "individualDates", "individualDates": [*************]}}, "createdAt": *************, "price": 30, "quantity": "1", "notes": "", "type": "item", "appliedDiscounts": [{"type": "discount", "amount": 30, "source": "coupon", "originalAllocation": {"allocationType": "discount", "amount": 100, "amountType": "percent", "discountType": "coupon", "code": "lewis123", "allocationDescription": "100% Discount for Bryan's NSD and Camp Tests", "id": "uTzhQxD93kPjYhwLB", "discountAmount": 30}}]}, {"_id": "2gfxD8hjDZsetq6DA", "originalItem": {"_id": "qFYWHPgkr8qnsDkva", "description": "Non School Day: 02/19/2024", "type": "item", "program": "MnpZ5RfAcjmmKAYw4", "amount": 30, "scaledAmounts": [], "ledgerAccountName": "4020", "details": {"startTime": "6:00 am", "endTime": "6:00 pm", "regStartDate": *************, "regEndDate": *************, "grades": ["K", "1", "2", "3", "4", "5"], "scheduleType": "TeNPa4ooxkvPj49Kq", "dateType": "individualDates", "individualDates": [*************]}}, "createdAt": *************, "price": 30, "quantity": "1", "notes": "", "type": "item", "appliedDiscounts": [{"type": "discount", "amount": 30, "source": "coupon", "originalAllocation": {"allocationType": "discount", "amount": 100, "amountType": "percent", "discountType": "coupon", "code": "lewis123", "allocationDescription": "100% Discount for Bryan's NSD and Camp Tests", "id": "RGRxcvL7bn8wNFnPM", "discountAmount": 30}}]}, {"_id": "Xzf4q74XQmBKx7iTD", "originalItem": {"_id": "qFYWHPgkr8qnsDkva", "description": "Non School Day: 02/19/2024", "type": "item", "program": "MnpZ5RfAcjmmKAYw4", "amount": 30, "scaledAmounts": [], "ledgerAccountName": "4020", "details": {"startTime": "6:00 am", "endTime": "6:00 pm", "regStartDate": *************, "regEndDate": *************, "grades": ["K", "1", "2", "3", "4", "5"], "scheduleType": "TeNPa4ooxkvPj49Kq", "dateType": "individualDates", "individualDates": [*************]}}, "createdAt": *************, "price": 30, "quantity": "1", "notes": "", "type": "item", "appliedDiscounts": [{"type": "discount", "amount": 30, "source": "coupon", "originalAllocation": {"allocationType": "discount", "amount": 100, "amountType": "percent", "discountType": "coupon", "code": "lewis123", "allocationDescription": "100% Discount for Bryan's NSD and Camp Tests", "id": "sapJxjsq383JJot6e", "discountAmount": 30}}]}, {"_id": "7semmFTGDTsSj5tYX", "originalItem": {"_id": "fGc3Ri983S2rcrnAo", "description": "Non School Day: 1/15/2024", "type": "item", "program": "MnpZ5RfAcjmmKAYw4", "amount": 30, "scaledAmounts": [], "ledgerAccountName": "4020", "details": {"startTime": "6:00 am", "endTime": "6:00 pm", "regStartDate": *************, "regEndDate": *************, "grades": ["K", "1", "2", "3", "4", "5"], "scheduleType": "TeNPa4ooxkvPj49Kq", "dateType": "individualDates", "individualDates": [*************]}}, "createdAt": *************, "price": 30, "quantity": "1", "notes": "", "type": "item", "appliedDiscounts": [{"type": "discount", "amount": 30, "source": "coupon", "originalAllocation": {"allocationType": "discount", "amount": 100, "amountType": "percent", "discountType": "coupon", "code": "lewis123", "allocationDescription": "100% Discount for Bryan's NSD and Camp Tests", "id": "hMo733pYcgdgW5p6R", "discountAmount": 30}}]}, {"_id": "LavnDDAxKGGPtb4RD", "originalItem": {"_id": "oAkiyFuxh9EQDQaFc", "description": "Non School Day: 11/7/2023", "type": "item", "program": "MnpZ5RfAcjmmKAYw4", "amount": 30, "scaledAmounts": [], "ledgerAccountName": "4020", "details": {"startTime": "6:00 am", "endTime": "6:00 pm", "regStartDate": *************, "regEndDate": *************, "grades": ["K", "1", "2", "3", "4", "5"], "scheduleType": "TeNPa4ooxkvPj49Kq", "dateType": "individualDates", "individualDates": [*************]}}, "createdAt": *************, "price": 30, "quantity": "1", "notes": "", "type": "item", "appliedDiscounts": [{"type": "discount", "amount": 30, "source": "coupon", "originalAllocation": {"allocationType": "discount", "amount": 100, "amountType": "percent", "discountType": "coupon", "code": "lewis123", "allocationDescription": "100% Discount for Bryan's NSD and Camp Tests", "id": "6FtaxkHXcjj2DCdzo", "discountAmount": 30}}]}, {"_id": "j4Kng5qHABdQPBD6S", "originalItem": {"_id": "n5EQtt3JRQwhqpuDQ", "description": "Winter Break Camp 12/20/2023", "type": "item", "program": "9exgtTHtMeb9ZHnr3", "amount": 30, "scaledAmounts": [], "ledgerAccountName": "4020", "details": {"startTime": "6:00 am", "endTime": "6:00 pm", "regStartDate": *************, "regEndDate": *************, "grades": ["K", "1", "2", "3", "4", "5"], "scheduleType": "hqbAxAAofG5Rqtvtr", "dateType": "individualDates", "individualDates": [*************]}}, "createdAt": *************, "price": 30, "quantity": "1", "notes": "", "type": "item", "appliedDiscounts": [{"type": "discount", "amount": 30, "source": "coupon", "originalAllocation": {"allocationType": "discount", "amount": 100, "amountType": "percent", "discountType": "coupon", "code": "lewis123", "allocationDescription": "100% Discount for Bryan's NSD and Camp Tests", "id": "QRudFmtREG9ve2uZH", "discountAmount": 30}}]}], "pendingCharges": [], "lastInvoiced": *************, "enrolledPlans": [{"_id": "JuQ2N3CjZ4vaHK5Bg", "planDetails": {"_id": "JuQ2N3CjZ4vaHK5Bg", "description": "After School", "type": "plan", "program": "FoNhQk8yjCTRTbGzA", "frequency": "scaledMonthly", "category": "tuition", "amount": 220, "scaledAmounts": [59, 112, 159, 193, 220], "ledgerAccountName": "4012", "details": {"startTime": "3:21 pm", "endTime": "6:00 pm", "regStartDate": *************, "grades": ["K", "1", "2", "3", "4", "5"], "scheduleType": "ivEwGqazmcWoWv6oJ", "dateType": "timePeriod", "timePeriod": "nso3JAkmsXDqxuhiq"}}, "enrollmentDate": *************, "allocations": [{"allocationType": "discount", "amount": 100, "amountType": "percent", "discountType": "coupon", "code": "lewis123", "allocationDescription": "100% Discount for Bryan's NSD and Camp Tests", "id": "urdQ6EFG4nsHF96xj", "discountAmount": 159}, {"allocationType": "discount", "amount": 100, "amountType": "percent", "discountType": "coupon", "code": "lewis123", "allocationDescription": "100% Discount for Bryan's NSD and Camp Tests", "id": "WbXWpoDunmGiwMNHD", "discountAmount": 159}, {"allocationType": "discount", "amount": 100, "amountType": "percent", "discountType": "coupon", "code": "lewis123", "allocationDescription": "100% Discount for Bryan's NSD and Camp Tests", "id": "aYpJe5MP6BHAonM9E", "discountAmount": 159}, {"allocationType": "discount", "amount": 100, "amountType": "percent", "discountType": "coupon", "code": "lewis123", "allocationDescription": "100% Discount for Bryan's NSD and Camp Tests", "id": "Mk3e4cmZqR7nhPpAo", "discountAmount": 159}, {"allocationType": "discount", "amount": 100, "amountType": "percent", "discountType": "coupon", "code": "lewis123", "allocationDescription": "100% Discount for Bryan's NSD and Camp Tests", "id": "W5NdJYnxEo5YgLzbz", "discountAmount": 159}], "createdAt": *************, "reservationId": "yXfDiQ7DcYBeJ29X9", "enrollmentForecastStartDate": *************, "enrollmentForecastEndDate": *************, "expirationDate": *************}, {"_id": "JuQ2N3CjZ4vaHK5Bg", "planDetails": {"_id": "JuQ2N3CjZ4vaHK5Bg", "description": "After School", "type": "plan", "program": "FoNhQk8yjCTRTbGzA", "frequency": "scaledMonthly", "category": "tuition", "amount": 220, "scaledAmounts": [59, 112, 159, 193, 220], "ledgerAccountName": "4012", "details": {"startTime": "3:21 pm", "endTime": "6:00 pm", "regStartDate": *************, "grades": ["K", "1", "2", "3", "4", "5"], "scheduleType": "ivEwGqazmcWoWv6oJ", "dateType": "timePeriod", "timePeriod": "nso3JAkmsXDqxuhiq"}}, "enrollmentDate": *************, "allocations": [{"allocationType": "discount", "amount": 100, "amountType": "percent", "discountType": "coupon", "code": "lewis123", "allocationDescription": "100% Discount for Bryan's NSD and Camp Tests", "id": "urdQ6EFG4nsHF96xj", "discountAmount": 159}, {"allocationType": "discount", "amount": 100, "amountType": "percent", "discountType": "coupon", "code": "lewis123", "allocationDescription": "100% Discount for Bryan's NSD and Camp Tests", "id": "PwsZ2Pnrm2ZHgz8X2", "discountAmount": 159}, {"allocationType": "discount", "amount": 100, "amountType": "percent", "discountType": "coupon", "code": "lewis123", "allocationDescription": "100% Discount for Bryan's NSD and Camp Tests", "id": "QBfQZ6rSdQcGXeJMe", "discountAmount": 159}, {"allocationType": "discount", "amount": 100, "amountType": "percent", "discountType": "coupon", "code": "lewis123", "allocationDescription": "100% Discount for Bryan's NSD and Camp Tests", "id": "558KtxveT8ENDHYW5", "discountAmount": 159}, {"allocationType": "discount", "amount": 100, "amountType": "percent", "discountType": "coupon", "code": "lewis123", "allocationDescription": "100% Discount for Bryan's NSD and Camp Tests", "id": "9k7MXxN3yCAGKurwg", "discountAmount": 159}], "createdAt": *************, "reservationId": "DYbjqpeFZE2PaBQJu", "enrollmentForecastStartDate": *************, "enrollmentForecastEndDate": *************, "expirationDate": *************}, {"_id": "JuQ2N3CjZ4vaHK5Bg", "planDetails": {"_id": "JuQ2N3CjZ4vaHK5Bg", "description": "After School", "type": "plan", "program": "FoNhQk8yjCTRTbGzA", "frequency": "scaledMonthly", "category": "tuition", "amount": 220, "scaledAmounts": [59, 112, 159, 193, 220], "ledgerAccountName": "4012", "details": {"startTime": "3:21 pm", "endTime": "6:00 pm", "regStartDate": *************, "grades": ["K", "1", "2", "3", "4", "5"], "scheduleType": "ivEwGqazmcWoWv6oJ", "dateType": "timePeriod", "timePeriod": "nso3JAkmsXDqxuhiq"}}, "enrollmentDate": *************, "allocations": [{"allocationType": "discount", "amount": 100, "amountType": "percent", "discountType": "coupon", "code": "lewis123", "allocationDescription": "100% Discount for Bryan's NSD and Camp Tests", "id": "urdQ6EFG4nsHF96xj", "discountAmount": 159}, {"allocationType": "discount", "amount": 100, "amountType": "percent", "discountType": "coupon", "code": "lewis123", "allocationDescription": "100% Discount for Bryan's NSD and Camp Tests", "id": "gekR3T6KBPaWKMstv", "discountAmount": 159}, {"allocationType": "discount", "amount": 100, "amountType": "percent", "discountType": "coupon", "code": "lewis123", "allocationDescription": "100% Discount for Bryan's NSD and Camp Tests", "id": "tQyxv4urPYXZPoqXn", "discountAmount": 159}, {"allocationType": "discount", "amount": 100, "amountType": "percent", "discountType": "coupon", "code": "lewis123", "allocationDescription": "100% Discount for Bryan's NSD and Camp Tests", "id": "24bjrbDbRJTSKzqFs", "discountAmount": 159}, {"allocationType": "discount", "amount": 100, "amountType": "percent", "discountType": "coupon", "code": "lewis123", "allocationDescription": "100% Discount for Bryan's NSD and Camp Tests", "id": "7G9tnzAdBh8i7fhWr", "discountAmount": 159}, {"allocationType": "discount", "amount": 100, "amountType": "percent", "discountType": "staffDiscount", "allocationDescription": "Employee Discount", "id": "ZoRyy9mZ35hgR96Lp", "discountAmount": 159}], "createdAt": *************, "reservationId": "EiWrTt9f4mLJg7AEY", "enrollmentForecastStartDate": *************, "enrollmentForecastEndDate": *************, "expirationDate": *************}, {"_id": "JuQ2N3CjZ4vaHK5Bg", "planDetails": {"_id": "JuQ2N3CjZ4vaHK5Bg", "description": "After School", "type": "plan", "program": "FoNhQk8yjCTRTbGzA", "frequency": "scaledMonthly", "category": "tuition", "amount": 220, "scaledAmounts": [59, 112, 159, 193, 220], "ledgerAccountName": "4012", "details": {"startTime": "3:21 pm", "endTime": "6:00 pm", "regStartDate": *************, "grades": ["K", "1", "2", "3", "4", "5"], "scheduleType": "ivEwGqazmcWoWv6oJ", "dateType": "timePeriod", "timePeriod": "nso3JAkmsXDqxuhiq"}}, "enrollmentDate": *************, "allocations": [{"allocationType": "discount", "amount": 100, "amountType": "percent", "discountType": "coupon", "code": "lewis123", "allocationDescription": "100% Discount for Bryan's NSD and Camp Tests", "id": "urdQ6EFG4nsHF96xj", "discountAmount": 159}, {"allocationType": "discount", "amount": 100, "amountType": "percent", "discountType": "coupon", "code": "lewis123", "allocationDescription": "100% Discount for Bryan's NSD and Camp Tests", "id": "PcDrGHgM2yvDP3EWu", "discountAmount": 159}, {"allocationType": "discount", "amount": 100, "amountType": "percent", "discountType": "coupon", "code": "lewis123", "allocationDescription": "100% Discount for Bryan's NSD and Camp Tests", "id": "rXHftZaXSH5Epzicq", "discountAmount": 159}, {"allocationType": "discount", "amount": 100, "amountType": "percent", "discountType": "coupon", "code": "lewis123", "allocationDescription": "100% Discount for Bryan's NSD and Camp Tests", "id": "psR7KsCn33oJG6pHK", "discountAmount": 159}, {"allocationType": "discount", "amount": 100, "amountType": "percent", "discountType": "coupon", "code": "lewis123", "allocationDescription": "100% Discount for Bryan's NSD and Camp Tests", "id": "XKNBS2uEo5AHFog47", "discountAmount": 159}, {"allocationType": "discount", "amount": 100, "amountType": "percent", "discountType": "staffDiscount", "allocationDescription": "Employee Discount", "id": "9bSTuK3soEGgcL3hA", "discountAmount": 159}], "createdAt": *************, "reservationId": "arKv5CmpAGDENPKJm", "enrollmentForecastStartDate": *************}], "enrolledPunchCards": [{"_id": "f6rPEdA6oxxotbGG2", "originalItem": {"_id": "TFNrTNPLcfjNNSAHw", "description": "Drop In", "type": "punchcard", "program": "FoNhQk8yjCTRTbGzA", "numberOfDays": "1", "category": "tuition", "amount": 30, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 30, "quantity": "1", "notes": "", "type": "item", "datesUsed": [0]}, {"_id": "XDrFyrqxgAu5B3teC", "originalItem": {"_id": "PEbJhPZBPoDP3qqqy", "description": "Before/After Care 10-Day Punch Card", "type": "punchcard", "program": "FoNhQk8yjCTRTbGzA", "numberOfDays": "10", "amount": 270, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 270, "quantity": "1", "notes": "", "type": "item", "appliedDiscounts": [{"type": "discount", "amount": 270, "source": "coupon", "originalAllocation": {"allocationType": "discount", "amount": 100, "amountType": "percent", "discountType": "staffDiscount", "allocationDescription": "Employee Discount", "id": "hNgx33ABWoRHFJdxd", "discountAmount": 270}}], "datesUsed": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, {"_id": "hCnAxvRnZo2YoaX5i", "originalItem": {"_id": "TFNrTNPLcfjNNSAHw", "description": "Drop In", "type": "punchcard", "program": "FoNhQk8yjCTRTbGzA", "numberOfDays": "1", "category": "tuition", "amount": 30, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 30, "quantity": "1", "notes": "", "type": "item", "datesUsed": [0]}]}, "waitlistAddedDate": null, "invoiceReminders": {"emailsSent": 10, "lastSentTime": *************}}, {"_id": "WxyGcSQzNTEhqHHSb", "firstName": "<PERSON>", "lastName": "Zarzuela", "designations": [], "defaultGroupId": "28Tm98JCpS4XCjvRG", "type": "person", "profileData": {"birthday": *************, "gender": "Male", "studentGrade": "4", "document1": "Yes", "photoAttestation": "Yes"}, "createdBy": "SYSTEM", "createdAt": *************, "orgId": "XsCRf7Y6zoFjjZmEZ", "inActive": false, "billing": {"enrolledPunchCards": [{"_id": "ixFCz72hhoK4aBSM7", "originalItem": {"_id": "PEbJhPZBPoDP3qqqy", "description": "Before/After Care 10-Day Punch Card", "type": "punchcard", "program": "QbbTmP5RqJKydrdHi", "numberOfDays": "10", "amount": 315, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 315, "quantity": "1", "notes": "", "type": "item", "datesUsed": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}], "pendingCharges": [], "lastInvoiced": *************}, "invoiceReminders": {"emailsSent": 10, "lastSentTime": *************}}, {"_id": "TRSctN8ZdYH2wnNRg", "firstName": "<PERSON>", "lastName": "Kilkenny", "designations": [], "defaultGroupId": "3ws44DCKokex5Za4S", "type": "person", "profileData": {"birthday": *************, "gender": "Male", "studentGrade": "6", "document1": "Yes", "photoAttestation": "Yes"}, "createdBy": "SYSTEM", "createdAt": *************, "orgId": "oCjPmSBQNXtSemwJx", "inActive": false, "billing": {"enrolledPunchCards": [{"_id": "RZj6aYS6XTXsjNvL2", "originalItem": {"_id": "PEbJhPZBPoDP3qqqy", "description": "Before Care 10-Day Punch Card", "type": "punchcard", "program": "hfCyQBfD3fJW4yG4o", "numberOfDays": "10", "amount": 360, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 360, "quantity": "1", "notes": "", "type": "item", "datesUsed": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}], "pendingCharges": [], "lastInvoiced": *************}, "documentItems": {"437BbyTizMMr4LPK2": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "4fmQYMaaTaBNeERru", "personName": "<PERSON><PERSON>"}}, "DnAN6PTnt9C94556w": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "4fmQYMaaTaBNeERru", "personName": "<PERSON><PERSON>"}}, "GoTaWbME5q7T5XKvf": {"createdAt": *************, "createdByPersonId": "4fmQYMaaTaBNeERru", "createdByUserId": "seWNX9p9RWXw2h3g4", "repositoryKey": "oCjPmSBQNXtSemwJx/seWNX9p9RWXw2h3g4/ZInHJnzTUc7WTPMvM9Tn", "token": "ZInHJnzTUc7WTPMvM9Tn"}}, "checkInGroupId": null, "checkInGroupName": null, "checkedIn": false, "checkedInOutTime": *************, "presenceLastGroupId": "3ws44DCKokex5Za4S", "previousClassroomGroupId": null, "timestamps": {"moveMomentTimestamp": {"momentId": "84FSynE9WB2DRyeRe", "timestamp": 1698929687662, "groupId": null, "oldGroupId": "3ws44DCKokex5Za4S"}}}, {"_id": "qYCaeRuMnNxRc2o8n", "firstName": "Roman", "lastName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "designations": [], "defaultGroupId": "3g8rDKkWMPrdRT3HF", "type": "person", "profileData": {"birthday": *************, "gender": "Male", "studentGrade": "3", "document1": "Yes", "photoAttestation": "Yes"}, "createdBy": "SYSTEM", "createdAt": *************, "orgId": "7y5bsDP3Pnr4LvEju", "inActive": false, "billing": {"enrolledItems": [{"_id": "ZnxNe8kwNMZDwvcxZ", "originalItem": {"_id": "rd5Zjv5RFafmB6fnE", "description": "Teacher In-Service Day", "type": "item", "program": "54yvgchpuKgdPKKuy", "amount": 35, "scaledAmounts": [], "ledgerAccountName": "0000", "details": {"startTime": "8:00 am", "endTime": "6:00 pm", "regStartDate": *************, "regEndDate": *************, "grades": ["K", "1", "2", "3", "4", "5", "6"], "scheduleType": "oMrmtCgdtMadKETBm", "dateType": "individualDates", "individualDates": [*************, *************]}}, "createdAt": *************, "price": 35, "quantity": "1", "notes": "", "type": "item", "appliedDiscounts": [{"type": "discount", "amount": 35, "source": "coupon", "originalAllocation": {"allocationType": "discount", "amount": 100, "amountType": "percent", "discountType": "staffDiscount", "allocationDescription": "Employee Discount", "id": "se6mw7i3mKBYZEJGu", "discountAmount": 35}}]}, {"_id": "H4nNsfbkB9kskFEHG", "originalItem": {"_id": "WnDyENo3RRz5s2gTs", "description": "Winter Break 2023 (10/16 - 10/20)", "type": "item", "program": "sEs4BCR8CR2ZbrvTd", "amount": 100, "scaledAmounts": [], "ledgerAccountName": "0000", "details": {"startTime": "6:30 am", "endTime": "6:00 pm", "regStartDate": *************, "regEndDate": *************, "grades": ["K", "1", "2", "3", "4"], "scheduleType": "kHgTKbFfMaMYysEjN", "dateType": "date<PERSON><PERSON><PERSON>", "serviceStartDate": *************, "serviceEndDate": *************}}, "createdAt": *************, "price": 100, "quantity": "1", "notes": "", "type": "item", "appliedDiscounts": [{"type": "discount", "amount": 100, "source": "coupon", "originalAllocation": {"allocationType": "discount", "amount": 100, "amountType": "percent", "discountType": "coupon", "code": "lewis123", "allocationDescription": "Discount: Coupon Code lewis123", "id": "7LHzjbDfb46ZrpAQX", "discountAmount": 100}}]}, {"_id": "k7LnTHvQuhfuf7g5u", "originalItem": {"_id": "WnDyENo3RRz5s2gTs", "description": "Winter Break 2023 (10/16 - 10/20)", "type": "item", "program": "sEs4BCR8CR2ZbrvTd", "amount": 100, "scaledAmounts": [], "ledgerAccountName": "0000", "details": {"startTime": "6:30 am", "endTime": "6:00 pm", "regStartDate": *************, "regEndDate": *************, "grades": ["K", "1", "2", "3", "4"], "scheduleType": "kHgTKbFfMaMYysEjN", "dateType": "date<PERSON><PERSON><PERSON>", "serviceStartDate": *************, "serviceEndDate": *************}}, "createdAt": *************, "price": 100, "quantity": "1", "notes": "", "type": "item", "appliedDiscounts": [{"type": "discount", "amount": 100, "source": "coupon", "originalAllocation": {"id": "Wt4imnjinoBXmQfKo", "allocationType": "discount", "amount": 100, "amountType": "percent", "discountType": "coupon", "code": "lewis123", "allocationDescription": "Discount: Coupon Code lewis123", "discountAmount": 100}}]}], "pendingCharges": [], "lastInvoiced": *************, "enrolledPlans": [{"_id": "9m88u4je4P4rQjAqL", "planDetails": {"_id": "9m88u4je4P4rQjAqL", "description": "After School", "type": "plan", "program": "cuPS2a63AEXWr8Rzs", "frequency": "scaledMonthly", "category": "tuition", "amount": 288, "scaledAmounts": [102, 164, 221, 266, 288], "ledgerAccountName": "4012", "details": {"startTime": "2:40 pm", "endTime": "6:00 pm", "regStartDate": *************, "grades": ["K", "1", "2", "3", "4", "5", "6", "7", "8"], "scheduleType": "ivEwGqazmcWoWv6oJ", "dateType": "timePeriod", "timePeriod": "cwfxok5bNERCh84TR"}}, "enrollmentDate": *************, "allocations": [], "createdAt": *************, "reservationId": "Q9PLHcR5ojw25unDJ", "enrollmentForecastStartDate": *************, "enrollmentForecastEndDate": *************, "expirationDate": *************}], "enrolledPunchCards": [{"_id": "gi8oegnAFWjQbgwiv", "originalItem": {"_id": "PEbJhPZBPoDP3qqqy", "description": "Before/After Care 10-Day Punch Card", "type": "punchcard", "program": "cuPS2a63AEXWr8Rzs", "numberOfDays": "10", "amount": 315, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 315, "quantity": "1", "notes": "", "type": "item", "appliedDiscounts": [{"type": "discount", "amount": 315, "source": "coupon", "originalAllocation": {"allocationType": "discount", "amount": 100, "amountType": "percent", "discountType": "coupon", "code": "lewis123", "allocationDescription": "Discount: Coupon Code lewis123", "id": "KEvgHP57b3mvGx55E", "discountAmount": 315}}], "datesUsed": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}]}, "waitlistAddedDate": null}, {"_id": "bvke4m6CcvMsP7DxQ", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON>", "designations": [], "defaultGroupId": "mEyhNjBNXqB9adx7p", "type": "person", "profileData": {"birthday": *************, "gender": "Male", "studentGrade": "K", "document1": "Yes", "homeSchool": "Pleasant Valley Primary", "photoAttestation": "Yes", "agencyIdentifier": "", "cacfpSubsidy": "", "ethnicIdentity": "", "healthInformation": {"primaryDoctor": "", "primaryDoctorPhone": ""}, "mediaRequirements": {"mediaRelease": "", "mediaReviewRequired": "", "noMediaAllowed": ""}, "nickname": "", "notesPrivate": "Oct 4, 2023 - dupe. deactivating. - bcl", "notesPublic": "", "payerIdentifier": "", "primaryFamily": "", "racialIdentity": "", "residesWith": "", "schoolPickUp": "", "standardOutlook": {"allergies": "", "importantNotes": "", "specialNeeds": ""}, "subsidyCode": "", "subsidyReason": "", "withdrawDate": *************}, "createdBy": "hnEFX5czZQnoPi38M", "createdAt": *************, "orgId": "SNLNnwBvou3QNE9qt", "inActive": true, "billing": {"enrolledPunchCards": [{"_id": "xxmKh8A4n5mw8DNza", "originalItem": {"_id": "qMThS7Kw2zzbnkLJm", "description": "Drop In", "type": "punchcard", "program": "W9hQEyypjKZkYbkqc", "numberOfDays": "1", "category": "tuition", "amount": 40, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 40, "quantity": "1", "notes": "", "type": "item", "appliedDiscounts": [{"type": "discount", "amount": null, "source": "coupon", "originalAllocation": {"allocationType": "discount", "amount": 50, "amountType": "percent", "discountType": "districtDiscount", "allocationDescription": "District Discount", "id": "8SBRGmZuBzHAEFd5N"}}], "datesUsed": [0]}], "pendingCharges": [], "lastInvoiced": *************, "billingNotes": "Oct 4, 2023 - <PERSON> accidentally put the drop in on an empty dupe acct instead of the actively billed account. Voiding and deactivating."}, "invoiceReminders": {"emailsSent": 3, "lastSentTime": *************}, "deactivatedAt": *************, "deactivationReason": "Unknown"}, {"_id": "T46JGpCjEbt7wygXJ", "firstName": "<PERSON><PERSON>", "lastName": "Strickland", "designations": [], "defaultGroupId": "2nx5QPv9nRNw4etEH", "type": "person", "profileData": {"birthday": *************, "gender": "Female", "studentGrade": "1", "document1": "Yes", "photoAttestation": "No"}, "createdBy": "hnEFX5czZQnoPi38M", "createdAt": *************, "orgId": "aTAhaqGzDFQXN8g5e", "inActive": false, "billing": {"enrolledPunchCards": [{"_id": "E4SKmbWPF468kw4qo", "originalItem": {"_id": "kmbBD75y39i7j6dvF", "description": "Drop In", "type": "punchcard", "program": "FnCys7JwbXhcpRr5T", "numberOfDays": "1", "category": "tuition", "amount": 30, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 30, "quantity": "1", "notes": "", "type": "item", "appliedDiscounts": [{"type": "discount", "amount": 3, "source": "coupon", "originalAllocation": {"id": "oBbCW3DHkS9NrAjbP", "allocationType": "discount", "amount": 10, "amountType": "percent", "discountType": "siblingDiscount", "allocationDescription": "Sibling Discount", "discountAmount": 3}}, {"type": "discount", "amount": null, "source": "coupon", "originalAllocation": {"allocationType": "discount", "amount": 50, "amountType": "percent", "discountType": "districtDiscount", "allocationDescription": "District Discount", "id": "BnEYQ86Ljfhy3C5Ln"}}], "datesUsed": [0]}], "pendingCharges": [], "lastInvoiced": *************}, "invoiceReminders": {"emailsSent": 2, "lastSentTime": *************}}, {"_id": "vqqycyPRwZyCTAvna", "firstName": "LaCota", "lastName": "<PERSON>", "designations": [], "defaultGroupId": "mfjDrH64pcaFGtYNQ", "type": "person", "profileData": {"birthday": 1348704000000, "gender": "Male", "studentGrade": "5", "document1": "Yes", "agencyIdentifier": "DCYF/ WCCC", "subsidyCode": "Unknown ", "homeSchool": "<PERSON><PERSON>", "photoAttestation": "Yes", "standardOutlook": {"allergies": "Seasonal"}}, "createdBy": "K2zKEfyE3hQEmzHNw", "createdAt": *************, "orgId": "3uAfjiBbHaGuEx86L", "inActive": false, "billing": {"enrolledPunchCards": [{"_id": "LrE8EBhAeC8hQnBEv", "originalItem": {"_id": "PEbJhPZBPoDP3qqqy", "description": "Before/After Care 10-Day Punch Card", "type": "punchcard", "program": "SzBF9MxYWLbsAc7Ey", "numberOfDays": "10", "amount": 360, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 360, "quantity": "1", "notes": "", "type": "item", "datesUsed": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}], "pendingCharges": [], "lastInvoiced": *************, "enrolledPlans": [{"_id": "XoAqnz6RB98w5P2jC", "planDetails": {"_id": "XoAqnz6RB98w5P2jC", "description": "After School", "type": "plan", "program": "SzBF9MxYWLbsAc7Ey", "frequency": "scaledMonthly", "category": "tuition", "amount": 295, "scaledAmounts": [94, 174, 235, 275, 295], "ledgerAccountName": "4012", "details": {"startTime": "3:40 pm", "endTime": "6:00 pm", "regStartDate": *************, "regEndDate": *************, "grades": ["K", "1", "2", "3", "4", "5", "6", "7", "8"], "scheduleType": "ivEwGqazmcWoWv6oJ", "dateType": "timePeriod", "timePeriod": "wDfTwNb7QjP7AKdLk"}}, "enrollmentDate": *************, "allocations": [{"allocationType": "reimbursable", "amount": 100, "amountType": "percent", "reimbursementType": "washingtonDCYF", "allocationDescription": "Reimbursable: Washington DCYF", "payerStartDate": *************, "payerEndDate": *************, "id": "d76WSuwBPaNXvXrLH"}, {"allocationType": "reimbursable", "amount": 100, "amountType": "percent", "reimbursementType": "washingtonDSHS", "allocationDescription": "Reimbursable: Washington DSHS", "payerStartDate": *************, "payerEndDate": *************, "id": "yE5Qi6FJwsokrWZ63"}], "createdAt": *************, "reservationId": "6btcCTMdJxGmMo9Zo", "enrollmentForecastStartDate": *************, "enrollmentForecastEndDate": null, "overrideRate": null, "updatedAt": *************, "updatedBy": "K2zKEfyE3hQEmzHNw"}, {"_id": "iWbNeboHZwiqy5Wjt", "planDetails": {"_id": "iWbNeboHZwiqy5Wjt", "description": "Before School", "type": "plan", "program": "SzBF9MxYWLbsAc7Ey", "frequency": "scaledMonthly", "category": "tuition", "amount": 295, "scaledAmounts": [94, 174, 235, 275, 295], "ledgerAccountName": "4013", "details": {"startTime": "6:30 am", "endTime": "9:10 am", "regStartDate": *************, "regEndDate": *************, "grades": ["K", "1", "2", "3", "4", "5", "6", "7", "8"], "scheduleType": "Lm2tqkvGeTXaoi7cX", "dateType": "timePeriod", "timePeriod": "wDfTwNb7QjP7AKdLk"}}, "enrollmentDate": *************, "allocations": [{"allocationType": "reimbursable", "amount": 100, "amountType": "percent", "reimbursementType": "washingtonDCYF", "allocationDescription": "Reimbursable: Washington DCYF", "payerStartDate": *************, "payerEndDate": *************, "id": "GdR72yvqfFtf6gaSZ"}, {"allocationType": "reimbursable", "amount": 100, "amountType": "percent", "reimbursementType": "washingtonDSHS", "allocationDescription": "Reimbursable: Washington DSHS", "payerStartDate": *************, "payerEndDate": *************, "id": "KAWquawbdv2thdfPX"}], "createdAt": *************, "reservationId": "p7pugTwy9P6j7PWJZ", "enrollmentForecastStartDate": *************, "enrollmentForecastEndDate": null, "overrideRate": null, "updatedAt": 1697475124287, "updatedBy": "K2zKEfyE3hQEmzHNw"}]}, "waitlistAddedDate": null, "checkInGroupId": null, "checkInGroupName": null, "checkedIn": false, "checkedInById": "XpAL6Fks6xnLyMrk5", "checkedInOutTime": 1698940343142, "presenceLastGroupId": "mfjDrH64pcaFGtYNQ", "previousClassroomGroupId": null, "timestamps": {"moveMomentTimestamp": {"momentId": "LN7dMMrmaBydWF92S", "timestamp": 1698940343216, "groupId": null, "oldGroupId": "mfjDrH64pcaFGtYNQ"}}}, {"_id": "B65Yk9dTiXufD5BHY", "firstName": "<PERSON>en", "lastName": "<PERSON>", "designations": [], "defaultGroupId": "mfjDrH64pcaFGtYNQ", "type": "person", "profileData": {"birthday": 1383696000000, "gender": "Male", "studentGrade": "4", "document1": "Yes", "agencyIdentifier": "DCYF/ WCCC", "subsidyCode": "Unknown ", "homeSchool": "Glenwood Heights Primary", "photoAttestation": "Yes", "standardOutlook": {"allergies": "Seasonal"}}, "createdBy": "K2zKEfyE3hQEmzHNw", "createdAt": *************, "orgId": "3uAfjiBbHaGuEx86L", "inActive": false, "billing": {"enrolledPunchCards": [{"_id": "s76aPymXN8MgAL8wM", "originalItem": {"_id": "PEbJhPZBPoDP3qqqy", "description": "Before/After Care 10-Day Punch Card", "type": "punchcard", "program": "SzBF9MxYWLbsAc7Ey", "numberOfDays": "10", "amount": 360, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 360, "quantity": "1", "notes": "", "type": "item", "appliedDiscounts": [{"type": "discount", "amount": 36, "source": "coupon", "originalAllocation": {"id": "w3sXFhLH9WbDWC46B", "allocationType": "discount", "amount": 10, "amountType": "percent", "discountType": "siblingDiscount", "allocationDescription": "Sibling Discount", "discountAmount": 36}}], "datesUsed": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}], "pendingCharges": [], "lastInvoiced": *************, "enrolledPlans": [{"_id": "XoAqnz6RB98w5P2jC", "planDetails": {"_id": "XoAqnz6RB98w5P2jC", "description": "After School", "type": "plan", "program": "SzBF9MxYWLbsAc7Ey", "frequency": "scaledMonthly", "category": "tuition", "amount": 295, "scaledAmounts": [94, 174, 235, 275, 295], "ledgerAccountName": "4012", "details": {"startTime": "3:40 pm", "endTime": "6:00 pm", "regStartDate": *************, "regEndDate": *************, "grades": ["K", "1", "2", "3", "4", "5", "6", "7", "8"], "scheduleType": "ivEwGqazmcWoWv6oJ", "dateType": "timePeriod", "timePeriod": "wDfTwNb7QjP7AKdLk"}}, "enrollmentDate": *************, "allocations": [{"allocationType": "reimbursable", "amount": 100, "amountType": "percent", "reimbursementType": "washingtonDCYF", "allocationDescription": "Reimbursable: Washington DCYF", "payerStartDate": *************, "payerEndDate": *************, "id": "gCot8eHa7vEEAbhcR"}, {"allocationType": "reimbursable", "amount": 100, "amountType": "percent", "reimbursementType": "washingtonDSHS", "allocationDescription": "Reimbursable: Washington DSHS", "payerStartDate": *************, "payerEndDate": *************, "id": "TcwbubD4hDDFKGDj8"}], "createdAt": *************, "reservationId": "jbBT3hAZkNeRKcaCP", "enrollmentForecastStartDate": *************, "enrollmentForecastEndDate": null, "overrideRate": null, "updatedAt": *************, "updatedBy": "K2zKEfyE3hQEmzHNw"}, {"_id": "iWbNeboHZwiqy5Wjt", "planDetails": {"_id": "iWbNeboHZwiqy5Wjt", "description": "Before School", "type": "plan", "program": "SzBF9MxYWLbsAc7Ey", "frequency": "scaledMonthly", "category": "tuition", "amount": 295, "scaledAmounts": [94, 174, 235, 275, 295], "ledgerAccountName": "4013", "details": {"startTime": "6:30 am", "endTime": "9:10 am", "regStartDate": *************, "regEndDate": *************, "grades": ["K", "1", "2", "3", "4", "5", "6", "7", "8"], "scheduleType": "Lm2tqkvGeTXaoi7cX", "dateType": "timePeriod", "timePeriod": "wDfTwNb7QjP7AKdLk"}}, "enrollmentDate": *************, "allocations": [{"allocationType": "reimbursable", "amount": 100, "amountType": "percent", "reimbursementType": "washingtonDCYF", "allocationDescription": "Reimbursable: Washington DCYF", "payerStartDate": *************, "payerEndDate": *************, "id": "qaZvbs5W8QzagcwhC"}, {"allocationType": "reimbursable", "amount": 100, "amountType": "percent", "reimbursementType": "washingtonDSHS", "allocationDescription": "Reimbursable: Washington DSHS", "payerStartDate": *************, "payerEndDate": *************, "id": "Grwz7xBnLDqDyZCYM"}], "createdAt": *************, "reservationId": "SSbQFJ4gGLLiFqXpo", "enrollmentForecastStartDate": *************, "enrollmentForecastEndDate": null, "overrideRate": null, "updatedAt": 1697475231223, "updatedBy": "K2zKEfyE3hQEmzHNw"}]}, "checkInGroupId": null, "checkInGroupName": null, "checkedIn": false, "checkedInById": "XpAL6Fks6xnLyMrk5", "checkedInOutTime": 1698940344029, "presenceLastGroupId": "mfjDrH64pcaFGtYNQ", "previousClassroomGroupId": null, "timestamps": {"moveMomentTimestamp": {"momentId": "LcQrfEMrf2MmzD6CE", "timestamp": 1698940344128, "groupId": null, "oldGroupId": "mfjDrH64pcaFGtYNQ"}}}, {"_id": "e7aTpYysPAkq9o8zg", "firstName": "<PERSON>", "lastName": "Castaneda", "designations": [], "defaultGroupId": "gJYvsbNA8v7ANvS2P", "type": "person", "profileData": {"birthday": 1447221600000, "gender": "Male", "studentGrade": "2", "document1": "Yes", "photoAttestation": "Yes", "agencyIdentifier": "", "cacfpSubsidy": "", "ethnicIdentity": "", "healthInformation": {"primaryDoctor": "", "primaryDoctorPhone": ""}, "homeSchool": "", "mediaRequirements": {"mediaRelease": "", "mediaReviewRequired": "", "noMediaAllowed": ""}, "nickname": "", "notesPrivate": "Oct 10, 2023 - dupe. deactivating. - bry", "notesPublic": "", "payerIdentifier": "", "primaryFamily": "", "racialIdentity": "", "residesWith": "", "schoolPickUp": "", "standardOutlook": {"allergies": "", "importantNotes": "", "specialNeeds": ""}, "subsidyCode": "", "subsidyReason": "", "withdrawDate": *************}, "createdBy": "hnEFX5czZQnoPi38M", "createdAt": *************, "orgId": "mDN7x8LzZDTmGpctP", "inActive": true, "billing": {"enrolledPunchCards": [{"_id": "9xCuEwbXYZLv7tFq2", "originalItem": {"_id": "GPESsJQZrfBZve6zf", "description": "Drop In", "type": "punchcard", "program": "skb5YvziKPBjvgoyx", "numberOfDays": "1", "category": "tuition", "amount": 30, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 30, "quantity": "1", "notes": "", "type": "item", "appliedDiscounts": [{"type": "discount", "amount": null, "source": "coupon", "originalAllocation": {"allocationType": "discount", "amount": 50, "amountType": "percent", "discountType": "districtDiscount", "allocationDescription": "District Discount", "id": "7t84HXEMqM3P7RYwH"}}], "datesUsed": [0]}], "pendingCharges": [], "lastInvoiced": *************}, "deactivatedAt": *************, "deactivationReason": "Unknown"}, {"_id": "84sXBFoDiq9uo43vL", "firstName": "<PERSON>", "lastName": "Castaneda", "designations": [], "defaultGroupId": "gJYvsbNA8v7ANvS2P", "type": "person", "profileData": {"birthday": *************, "gender": "Male", "studentGrade": "4", "document1": "Yes", "photoAttestation": "Yes", "agencyIdentifier": "", "cacfpSubsidy": "", "ethnicIdentity": "", "healthInformation": {"primaryDoctor": "", "primaryDoctorPhone": ""}, "homeSchool": "", "mediaRequirements": {"mediaRelease": "", "mediaReviewRequired": "", "noMediaAllowed": ""}, "nickname": "", "notesPrivate": "Oct 10, 2023 - dupe. deactivating. - bry", "notesPublic": "", "payerIdentifier": "", "primaryFamily": "", "racialIdentity": "", "residesWith": "", "schoolPickUp": "", "standardOutlook": {"allergies": "", "importantNotes": "", "specialNeeds": ""}, "subsidyCode": "", "subsidyReason": "", "withdrawDate": *************}, "createdBy": "hnEFX5czZQnoPi38M", "createdAt": *************, "orgId": "mDN7x8LzZDTmGpctP", "inActive": true, "billing": {"enrolledPunchCards": [{"_id": "NRTYe6Cc2GcWTx2WM", "originalItem": {"_id": "GPESsJQZrfBZve6zf", "description": "Drop In", "type": "punchcard", "program": "skb5YvziKPBjvgoyx", "numberOfDays": "1", "category": "tuition", "amount": 30, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 30, "quantity": "1", "notes": "", "type": "item", "appliedDiscounts": [{"type": "discount", "amount": 3, "source": "coupon", "originalAllocation": {"id": "QaRSwNC7gDGNurnc6", "allocationType": "discount", "amount": 10, "amountType": "percent", "discountType": "siblingDiscount", "allocationDescription": "Sibling Discount", "discountAmount": 3}}, {"type": "discount", "amount": null, "source": "coupon", "originalAllocation": {"allocationType": "discount", "amount": 50, "amountType": "percent", "discountType": "districtDiscount", "allocationDescription": "District Discount", "id": "jjhkrQDxPu5myX2bE"}}], "datesUsed": [0]}], "pendingCharges": [], "lastInvoiced": *************}, "deactivatedAt": *************, "deactivationReason": "Unknown"}, {"_id": "xL9WzKd9eSHELc6JQ", "firstName": "<PERSON>", "lastName": "<PERSON>", "designations": [], "defaultGroupId": "CC3fzCTCbzRMJGys5", "type": "person", "profileData": {"birthday": *************, "gender": "Male", "studentGrade": "1", "document1": "Yes", "photoAttestation": "Yes"}, "createdBy": "SYSTEM", "createdAt": *************, "orgId": "NcQvtvp2KWBpAFopN", "inActive": false, "billing": {"enrolledPlans": [{"_id": "JuQ2N3CjZ4vaHK5Bg", "planDetails": {"_id": "JuQ2N3CjZ4vaHK5Bg", "description": "After School", "type": "plan", "program": "GumirkZjpvRLLsEPX", "frequency": "scaledMonthly", "category": "tuition", "amount": 220, "scaledAmounts": [59, 112, 159, 193, 220], "ledgerAccountName": "4012", "details": {"startTime": "3:36 pm", "endTime": "6:00 pm", "regStartDate": *************, "grades": ["K", "1", "2", "3", "4", "5"], "scheduleType": "ivEwGqazmcWoWv6oJ", "dateType": "timePeriod", "timePeriod": "6AgjCSwzBCx8hYJ5S"}}, "enrollmentDate": *************, "allocations": [], "createdAt": *************, "reservationId": "d6E7YTJQEKYYTX6jc", "enrollmentForecastStartDate": *************}], "pendingCharges": [], "lastInvoiced": *************, "enrolledPunchCards": [{"_id": "PWWKchS9fiXx7GqGb", "originalItem": {"_id": "vCdA97tx9retrgQiJ", "description": "Drop In", "type": "punchcard", "program": "GumirkZjpvRLLsEPX", "numberOfDays": "1", "category": "tuition", "amount": 30, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 30, "quantity": "1", "notes": "10/11 Drop In", "type": "item", "datesUsed": [0]}]}, "documentItems": {"vZ8wFASBFB3nwp6eK": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "E6GoiS5oKSHMqEi4f", "personName": "<PERSON>"}}, "HbKT2fvwnuoEsA6WC": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "E6GoiS5oKSHMqEi4f", "personName": "<PERSON>"}}, "7JDzx75RraTgM49Xt": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "E6GoiS5oKSHMqEi4f", "personName": "<PERSON>"}}, "LP3xSDjEW4Z7W9YfK": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "E6GoiS5oKSHMqEi4f", "personName": "<PERSON>"}}, "N8J2nK2u4j3rg3uMK": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "E6GoiS5oKSHMqEi4f", "personName": "<PERSON>"}}}, "checkInGroupId": null, "checkInGroupName": null, "checkedIn": false, "checkedInOutTime": 1698877073682, "presenceLastGroupId": "CC3fzCTCbzRMJGys5", "previousClassroomGroupId": null, "timestamps": {"moveMomentTimestamp": {"momentId": "nbqpysFYuq4C5Fuk6", "timestamp": 1698877073755, "groupId": null, "oldGroupId": "CC3fzCTCbzRMJGys5"}}, "checkedInById": "E6GoiS5oKSHMqEi4f"}, {"_id": "6zMBFTvSrxRkkcSLh", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "designations": [], "defaultGroupId": "8pENy4sd8kfqAHNey", "type": "person", "profileData": {"birthday": *************, "gender": "Female", "studentGrade": "3", "document1": "Yes", "photoAttestation": "Yes"}, "createdBy": "SYSTEM", "createdAt": *************, "orgId": "TzSrjozxDHgvLW8bH", "inActive": false, "billing": {"enrolledPunchCards": [{"_id": "wXwx9962LmGKGdXMx", "originalItem": {"_id": "5rr5xZqNji7e3NuKZ", "description": "Drop In", "type": "punchcard", "program": "2KJBaxWJBGd4oawHP", "numberOfDays": "1", "category": "tuition", "amount": 30, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 30, "quantity": "1", "notes": "", "type": "item", "datesUsed": [0]}], "pendingCharges": [], "lastInvoiced": *************}, "documentItems": {"MMYTYi4dM9BuA8Bec": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "m7h2fGW7jP52Ddrwb", "personName": "<PERSON><PERSON><PERSON><PERSON>"}}, "ETAZG2EJZKEiWybdB": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "m7h2fGW7jP52Ddrwb", "personName": "<PERSON><PERSON><PERSON><PERSON>"}}, "KocmWyR4r3Edm5ct9": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "m7h2fGW7jP52Ddrwb", "personName": "<PERSON><PERSON><PERSON><PERSON>"}}, "DkkGmbD7zX8EcxJpH": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "m7h2fGW7jP52Ddrwb", "personName": "<PERSON><PERSON><PERSON><PERSON>"}}, "4YZAkJNs9nxwJaA92": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "m7h2fGW7jP52Ddrwb", "personName": "<PERSON><PERSON><PERSON><PERSON>"}}}}, {"_id": "dRnXAWyEnG5bmiQQn", "firstName": "Abiagil", "lastName": "<PERSON>", "designations": [], "defaultGroupId": "2nx5QPv9nRNw4etEH", "type": "person", "profileData": {"birthday": *************, "gender": "Female", "studentGrade": "2", "document1": "Yes", "photoAttestation": "Yes"}, "createdBy": "SYSTEM", "createdAt": *************, "orgId": "aTAhaqGzDFQXN8g5e", "inActive": false, "billing": {"enrolledPunchCards": [{"_id": "e8FY8BsWJX2SvoMxm", "originalItem": {"_id": "kmbBD75y39i7j6dvF", "description": "Drop In", "type": "punchcard", "program": "FnCys7JwbXhcpRr5T", "numberOfDays": "1", "category": "tuition", "amount": 30, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 30, "quantity": "1", "notes": "", "type": "item", "datesUsed": [0]}], "pendingCharges": [], "lastInvoiced": *************}, "documentItems": {"HpmhRx6EPyiTrWfuh": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "noTGr8FGQhDTQQMwr", "personName": "<PERSON>"}}, "K9tWPi6sY4cSrQCTs": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "noTGr8FGQhDTQQMwr", "personName": "<PERSON>"}}, "rg9dQHMQAXA6xymLa": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "noTGr8FGQhDTQQMwr", "personName": "<PERSON>"}}, "nNbCdT3HXJ5PJcRQr": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "noTGr8FGQhDTQQMwr", "personName": "<PERSON>"}}, "6uuqAPEddNrxBzwrp": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "noTGr8FGQhDTQQMwr", "personName": "<PERSON>"}}, "DNay23mYZXJLKMi3a": {"createdAt": *************, "createdByPersonId": "noTGr8FGQhDTQQMwr", "createdByUserId": "GtHpwnj88aWiRZpkN", "repositoryKey": "aTAhaqGzDFQXN8g5e/GtHpwnj88aWiRZpkN/twNx6B42qgIovxpNW2Q2", "token": "twNx6B42qgIovxpNW2Q2"}}}, {"_id": "w3cfQHxTFs2PoXouy", "firstName": "<PERSON>", "lastName": "Ascencao", "designations": [], "defaultGroupId": "jhHyeEMuL3PTtxNQk", "type": "person", "profileData": {"birthday": 1386288000000, "gender": "Female", "studentGrade": "4", "document1": "Yes", "photoAttestation": "Yes", "standardOutlook": {"allergies": "epipen bug bites"}}, "createdBy": "hnEFX5czZQnoPi38M", "createdAt": *************, "orgId": "gJ66DDKJf5ZktvQXP", "inActive": false, "billing": {"enrolledPunchCards": [{"_id": "CtMmkyyG9nkaeEbYs", "originalItem": {"_id": "nTim89qqQQjfX4dtg", "description": "Drop In", "type": "punchcard", "program": "L4uwGjJKSCDXwT6Nz", "numberOfDays": "1", "category": "tuition", "amount": 30, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 30, "quantity": "1", "notes": "", "type": "item", "appliedDiscounts": [{"type": "discount", "amount": null, "source": "coupon", "originalAllocation": {"allocationType": "discount", "amount": 50, "amountType": "percent", "discountType": "districtDiscount", "allocationDescription": "District Discount", "id": "ECA4ALEf7hyFhdRWv"}}], "datesUsed": [0]}], "pendingCharges": [], "lastInvoiced": *************}, "invoiceReminders": {"emailsSent": 10, "lastSentTime": *************}}, {"_id": "WcP3ux2Z2w4SaxZiw", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "designations": [], "defaultGroupId": "gJYvsbNA8v7ANvS2P", "type": "person", "profileData": {"birthday": *************, "gender": "Male", "studentGrade": "5", "document1": "Yes", "photoAttestation": "No"}, "createdBy": "SYSTEM", "createdAt": *************, "orgId": "mDN7x8LzZDTmGpctP", "inActive": false, "billing": {"enrolledPunchCards": [{"_id": "CTRaCKx5q4Ro2xfi3", "originalItem": {"_id": "PEbJhPZBPoDP3qqqy", "description": "Before/After Care 10-Day Punch Card", "type": "punchcard", "program": "skb5YvziKPBjvgoyx", "numberOfDays": "10", "amount": 270, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 270, "quantity": "1", "notes": "", "type": "item", "datesUsed": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}], "pendingCharges": [], "lastInvoiced": *************}}, {"_id": "egHWgXjcknjCskvXB", "firstName": "<PERSON>", "lastName": "<PERSON>", "designations": [], "defaultGroupId": "Qk3mNvHbFjGCZRpab", "type": "person", "profileData": {"birthday": *************, "gender": "Male", "studentGrade": "2", "document1": "Yes", "photoAttestation": "Yes", "standardOutlook": {"allergies": "dust mites and coachroaches."}}, "createdBy": "SYSTEM", "createdAt": *************, "orgId": "7nQuovAbC5vJmbDtk", "inActive": false, "billing": {"enrolledPunchCards": [{"_id": "3Hs6Fqhrva2FR6JLN", "originalItem": {"_id": "2AexhKAibnmDigFs8", "description": "Drop In", "type": "punchcard", "program": "gsC2xtc44Jpawy6tv", "numberOfDays": "1", "category": "tuition", "amount": 35, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 35, "quantity": "1", "notes": "", "type": "item", "datesUsed": [0]}], "pendingCharges": [], "lastInvoiced": *************}}, {"_id": "avHFN48XzfvYvnZr6", "firstName": "August", "lastName": "<PERSON>", "designations": [], "defaultGroupId": "trT9a5ZEpyALKSM2v", "type": "person", "profileData": {"birthday": *************, "gender": "Male", "studentGrade": "4", "document1": "Yes", "photoAttestation": "Yes"}, "createdBy": "SYSTEM", "createdAt": *************, "orgId": "eXrCBS5roD8bh6z6B", "inActive": false, "billing": {"enrolledPunchCards": [{"_id": "6uGnSDeHeHumrtfAS", "originalItem": {"_id": "pT6t5C8N9sAa6oFD2", "description": "Drop In", "type": "punchcard", "program": "tvRH7NC83zZHm3Ys7", "numberOfDays": "1", "category": "tuition", "amount": 30, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 30, "quantity": "1", "notes": "", "type": "item", "datesUsed": [0]}], "pendingCharges": [], "lastInvoiced": *************}, "documentItems": {"3rQgPRpgC9povJSv3": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "BxaTPsoo3w3RYbQpM", "personName": "<PERSON><PERSON>"}}, "ZEeFRj3eajFPfPvYQ": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "BxaTPsoo3w3RYbQpM", "personName": "<PERSON><PERSON>"}}, "BjBybtBpqPwz3MuAQ": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "BxaTPsoo3w3RYbQpM", "personName": "<PERSON><PERSON>"}}, "AbEn6b9b4gNDQCjxe": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "BxaTPsoo3w3RYbQpM", "personName": "<PERSON><PERSON>"}}, "7AhC4oExuN9KmR7TL": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "BxaTPsoo3w3RYbQpM", "personName": "<PERSON><PERSON>"}}, "m7gj8gE79e2DpLS7v": {"createdAt": 1697640947589, "createdByPersonId": "CpcJrqhyg9ah9XcS2", "createdByUserId": "ij8yDNvpeWrT6we5Y", "repositoryKey": "eXrCBS5roD8bh6z6B/ij8yDNvpeWrT6we5Y/aT5bS3XfqXnuv4MVJba1", "token": "aT5bS3XfqXnuv4MVJba1"}}}, {"_id": "rahzxD3ePukh5owYW", "firstName": "<PERSON>", "lastName": "Fox", "designations": [], "defaultGroupId": "Qk3mNvHbFjGCZRpab", "type": "person", "profileData": {"birthday": *************, "gender": "Female", "studentGrade": "4", "document1": "Yes", "photoAttestation": "Yes"}, "createdBy": "SYSTEM", "createdAt": *************, "orgId": "7nQuovAbC5vJmbDtk", "inActive": false, "billing": {"enrolledPunchCards": [{"_id": "PBBhxpbkjTz7QsuWx", "originalItem": {"_id": "2AexhKAibnmDigFs8", "description": "Drop In", "type": "punchcard", "program": "gsC2xtc44Jpawy6tv", "numberOfDays": "1", "category": "tuition", "amount": 35, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 35, "quantity": "1", "notes": "", "type": "item", "datesUsed": [0]}], "pendingCharges": [], "lastInvoiced": *************}, "documentItems": {"iAF4BL2uFi6Qxfik3": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "Ex23qNFKbdj9nJDee", "personName": "<PERSON>"}}, "xNQYQxy5DkAJhKeug": {"createdAt": *************, "createdByPersonId": "Ex23qNFKbdj9nJDee", "createdByUserId": "qSgvsBNypb9PfNvff", "repositoryKey": "7nQuovAbC5vJmbDtk/qSgvsBNypb9PfNvff/5AU27twUhyAxgcp4z9TN", "token": "5AU27twUhyAxgcp4z9TN"}}}, {"_id": "sKNvAnQpeiEcT3aqG", "firstName": "<PERSON>", "lastName": "<PERSON>", "designations": [], "defaultGroupId": "wSMvyQR53mBkvp8EF", "type": "person", "profileData": {"birthday": *************, "gender": "Male", "studentGrade": "3", "document1": "Yes", "photoAttestation": "Yes"}, "createdBy": "SYSTEM", "createdAt": *************, "orgId": "ogGcnQrn5jpkp8qZD", "inActive": false, "billing": {"enrolledPunchCards": [{"_id": "AHx3ygX54mwDjtfL5", "originalItem": {"_id": "PEbJhPZBPoDP3qqqy", "description": "Before/After Care 10-Day Punch Card", "type": "punchcard", "program": "FoNhQk8yjCTRTbGzA", "numberOfDays": "10", "amount": 270, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 270, "quantity": "1", "notes": "", "type": "item", "appliedDiscounts": [{"type": "discount", "amount": 270, "source": "coupon", "originalAllocation": {"allocationType": "discount", "amount": 100, "amountType": "percent", "discountType": "staffDiscount", "allocationDescription": "Employee Discount", "id": "DeTweXdiPxGuvaEzj", "discountAmount": 270}}], "datesUsed": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}], "pendingCharges": [], "enrolledItems": [{"_id": "a8aRjaAg7jhaNg2mc", "originalItem": {"_id": "RatfgrxgvEnC7B9Tj", "description": "Spring Break Camp 04/01/2024", "type": "item", "program": "Rxd26E6QuEybzmFvS", "amount": 30, "scaledAmounts": [], "ledgerAccountName": "4020", "details": {"startTime": "6:00 am", "endTime": "6:00 pm", "regStartDate": *************, "regEndDate": *************, "grades": ["K", "1", "2", "3", "4", "5"], "scheduleType": "3jjD62n7ewwd6omkj", "dateType": "individualDates", "individualDates": [*************]}}, "createdAt": *************, "price": 30, "quantity": "1", "notes": "", "type": "item", "appliedDiscounts": [{"type": "discount", "amount": 30, "source": "coupon", "originalAllocation": {"allocationType": "discount", "amount": 100, "amountType": "percent", "discountType": "staffDiscount", "allocationDescription": "Employee Discount", "id": "zpNQ6ZQp9MLLPtuZX", "discountAmount": 30}}]}], "lastInvoiced": *************}}, {"_id": "P9mQKCEJ84kNx9E6g", "firstName": "<PERSON>", "lastName": "Velasco", "designations": [], "defaultGroupId": "EcBgE9WCB4rrcjhMd", "type": "person", "profileData": {"birthday": *************, "gender": "Male", "studentGrade": "1", "document1": "Yes", "homeSchool": "Captain <PERSON> Primary", "photoAttestation": "Yes"}, "createdBy": "SYSTEM", "createdAt": *************, "orgId": "h3yzWCBnkJXY2sTqK", "inActive": false, "billing": {"enrolledPlans": [{"_id": "XoAqnz6RB98w5P2jC", "planDetails": {"_id": "XoAqnz6RB98w5P2jC", "description": "After School", "type": "plan", "program": "6rZSmCdD7e8koMbtf", "frequency": "scaledMonthly", "category": "tuition", "amount": 295, "scaledAmounts": [94, 174, 235, 275, 295], "ledgerAccountName": "4012", "details": {"startTime": "3:40 pm", "endTime": "6:00 pm", "regStartDate": *************, "regEndDate": *************, "grades": ["K", "1", "2", "3", "4", "5", "6", "7", "8"], "scheduleType": "ivEwGqazmcWoWv6oJ", "dateType": "timePeriod", "timePeriod": "ZdjXjy9auSxy4Jc5t"}}, "enrollmentDate": *************, "allocations": [], "createdAt": *************, "reservationId": "MjHb2QecC4HNukizQ", "enrollmentForecastStartDate": *************}], "pendingCharges": [], "lastInvoiced": *************, "enrolledPunchCards": [{"_id": "s9ZrhD4HARw4acmdC", "originalItem": {"_id": "R5rtrdG4JQrMvTuF3", "description": "Drop In", "type": "punchcard", "program": "6rZSmCdD7e8koMbtf", "numberOfDays": "1", "category": "tuition", "amount": 40, "scaledAmounts": [], "ledgerAccountName": "4104"}, "createdAt": *************, "price": 40, "quantity": "1", "notes": "", "type": "item", "datesUsed": [0]}]}, "documentItems": {"6wYGiq5TibxSsjp5T": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "Riwd8eYPZqZdfhkdY", "personName": "Carolina  Moreno"}}, "RHxqxTv8iXPmhCtSq": {"templateOptionResult": {"action": "ack", "date": *************, "personId": "Riwd8eYPZqZdfhkdY", "personName": "Carolina  Moreno"}}}, "invoiceReminders": {"emailsSent": 1, "lastSentTime": *************}}]