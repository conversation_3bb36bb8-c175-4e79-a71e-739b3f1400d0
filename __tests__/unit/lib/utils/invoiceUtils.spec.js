import {describe, it, expect} from '@jest/globals';
import { InvoiceUtils } from '../../../../lib/util/invoiceUtils';

describe('invoiceUtils', () => {
    let invoice1;
    let invoice2;
    let invoice3;
    let invoice4;
    let invoice5;
    let invoice6;
    let invoice7;
    let invoice8;
    let invoice9;
    let invoice10;

    beforeEach(() => {
        invoice1 = {
            originalAmount: 100.01,
            familySplits: {
                'familyPerson123': 50,
                'familyPerson456': 30,
                'familyPerson789': 20,
            },
        };
        invoice2 = {
            originalAmount: 100.01,
            familySplits: {
                'familyPerson123': 50,
                'familyPerson456': 50,
            },
        };
        invoice3 = {
            originalAmount: 100.01,
            familySplits: {
                'familyPerson123': 40,
                'familyPerson456': 30,
                'familyPerson789': 30,
            },
        };
        invoice4 = {
            originalAmount: 100.03,
            familySplits: {
                'familyPerson123': 40,
                'familyPerson456': 30,
                'familyPerson789': 30,
            },
        };
        invoice5 = {
            originalAmount: 100.01,
            familySplits: {
                'familyPerson123': 55,
                'familyPerson456': 45,
            },
        };
        invoice6 = {
            originalAmount: 333.33,
            familySplits: {
                'familyPerson123': 50,
                'familyPerson456': 25,
                'familyPerson789': 25,
            },
        };
        invoice7 = {
            originalAmount: 99.99,
            familySplits: {
                'familyPerson123': 33,
                'familyPerson456': 33,
                'familyPerson789': 34,
            },
        };
        invoice8 = {
            originalAmount: 250.55,
            familySplits: {
                'familyPerson123': 40,
                'familyPerson456': 35,
                'familyPerson789': 25,
            },
        };
        invoice9 = {
            originalAmount: 100.00,
            familySplits: {
                'familyPerson123': 0,
                'familyPerson456': 100,
            }
        };
        invoice10 = {
            originalAmount: 100.00,
            familySplits: {
                'familyPerson123': 100,
                'familyPerson456': 0,
            }
        };
    });

    describe('amountDueForFamilyMember', () => {

        it('calculate amount due for first family member with 50/50 split (100.01)', () => {
            const keys = Object.keys(invoice2['familySplits']);
            const familyPersonId = keys[0];
            const amountDue = InvoiceUtils.amountDueForFamilyMember(invoice2, familyPersonId);
            expect(amountDue).toBe(50.01);
        });

        it('calculate amount due for second family member with 50/50 split (100.01)', () => {
            const keys = Object.keys(invoice2['familySplits']);
            const familyPersonId = keys[1];
            const amountDue = InvoiceUtils.amountDueForFamilyMember(invoice2, familyPersonId);
            expect(amountDue).toBe(50);
        });

        it('calculate amount due for first family member with 55/45 split (100.01)', () => {
            const keys = Object.keys(invoice5['familySplits']);
            const familyPersonId = keys[0];
            const amountDue = InvoiceUtils.amountDueForFamilyMember(invoice5, familyPersonId);
            expect(amountDue).toBe(55.01);
        });

        it('calculate amount due for second family member with 55/45 split (100.01)', () => {
            const keys = Object.keys(invoice5['familySplits']);
            const familyPersonId = keys[1];
            const amountDue = InvoiceUtils.amountDueForFamilyMember(invoice5, familyPersonId);
            expect(amountDue).toBe(45);
        });

        it('calculate amount due for first family member with 50/30/20 (100.01)', () => {
            const keys = Object.keys(invoice1['familySplits']);
            const familyPersonId = keys[0];
            const amountDue = InvoiceUtils.amountDueForFamilyMember(invoice1, familyPersonId);
            expect(amountDue).toBe(50.01);
        });

        it('calculate amount due for second family member with 50/30/20 (100.01)', () => {
            const keys = Object.keys(invoice1['familySplits']);
            const familyPersonId = keys[1];
            const amountDue = InvoiceUtils.amountDueForFamilyMember(invoice1, familyPersonId);
            expect(amountDue).toBe(30);
        });

        it('calculate amount due for third family member with 50/30/20 (100.01)', () => {
            const keys = Object.keys(invoice1['familySplits']);
            const familyPersonId = keys[2];
            const amountDue = InvoiceUtils.amountDueForFamilyMember(invoice1, familyPersonId);
            expect(amountDue).toBe(20);
        });

        it('calculate amount due for first family member in 40/30/30 (100.01)', () => {
            const keys = Object.keys(invoice3['familySplits']);
            const familyPersonId = keys[0];
            const amountDue = InvoiceUtils.amountDueForFamilyMember(invoice3, familyPersonId);
            expect(amountDue).toBe(40.01);
        });

        it('calculate amount due for second family member in 40/30/30 (100.01)', () => {
            const keys = Object.keys(invoice3['familySplits']);
            const familyPersonId = keys[1];
            const amountDue = InvoiceUtils.amountDueForFamilyMember(invoice3, familyPersonId);
            expect(amountDue).toBe(30);
        });

        it('calculate amount due for third family member in 40/30/30 (100.01)', () => {
            const keys = Object.keys(invoice3['familySplits']);
            const familyPersonId = keys[2];
            const amountDue = InvoiceUtils.amountDueForFamilyMember(invoice3, familyPersonId);
            expect(amountDue).toBe(30);
        });

        it('calculate amount due for first family member in 40/30/30 (100.03)', () => {
            const keys = Object.keys(invoice4['familySplits']);
            const familyPersonId = keys[0];
            const amountDue = InvoiceUtils.amountDueForFamilyMember(invoice4, familyPersonId);
            expect(amountDue).toBe(40.01);
        });

        it('calculate amount due for second family member in 40/30/30 (100.03)', () => {
            const keys = Object.keys(invoice4['familySplits']);
            const familyPersonId = keys[1];
            const amountDue = InvoiceUtils.amountDueForFamilyMember(invoice4, familyPersonId);
            expect(amountDue).toBe(30.01);
        });

        it('calculate amount due for third family member in 40/30/30 (100.03)', () => {
            const keys = Object.keys(invoice4['familySplits']);
            const familyPersonId = keys[2];
            const amountDue = InvoiceUtils.amountDueForFamilyMember(invoice4, familyPersonId);
            expect(amountDue).toBe(30.01);
        });

        it('calculate amount due for first family member in 40/35/25 (250.55)', () => {
            const keys = Object.keys(invoice8['familySplits']);
            const familyPersonId = keys[0];
            const amountDue = InvoiceUtils.amountDueForFamilyMember(invoice8, familyPersonId);
            expect(amountDue).toBe(100.22);
        });

        it('calculate amount due for second family member in 40/35/25 (250.55)', () => {
            const keys = Object.keys(invoice8['familySplits']);
            const familyPersonId = keys[1];
            const amountDue = InvoiceUtils.amountDueForFamilyMember(invoice8, familyPersonId);
            expect(amountDue).toBe(87.69);
        });

        it('calculate amount due for third family member in 40/35/25 (250.55)', () => {
            const keys = Object.keys(invoice8['familySplits']);
            const familyPersonId = keys[2];
            const amountDue = InvoiceUtils.amountDueForFamilyMember(invoice8, familyPersonId);
            expect(amountDue).toBe(62.64);
        });

        it('calculate amount due for first family member in 50/25/25 (333.33)', () => {
            const keys = Object.keys(invoice6['familySplits']);
            const familyPersonId = keys[0];
            const amountDue = InvoiceUtils.amountDueForFamilyMember(invoice6, familyPersonId);
            expect(amountDue).toBe(166.67);
        });

        it('calculate amount due for second family member in 50/25/25 (333.33)', () => {
            const keys = Object.keys(invoice6['familySplits']);
            const familyPersonId = keys[1];
            const amountDue = InvoiceUtils.amountDueForFamilyMember(invoice6, familyPersonId);
            expect(amountDue).toBe(83.33);
        });

        it('calculate amount due for third family member in 50/25/25 (333.33)', () => {
            const keys = Object.keys(invoice6['familySplits']);
            const familyPersonId = keys[2];
            const amountDue = InvoiceUtils.amountDueForFamilyMember(invoice6, familyPersonId);
            expect(amountDue).toBe(83.33);
        });

        it('calculate amount due for first family member in 33/33/34 (99.99)', () => {
            const keys = Object.keys(invoice7['familySplits']);
            const familyPersonId = keys[0];
            const amountDue = InvoiceUtils.amountDueForFamilyMember(invoice7, familyPersonId);
            expect(amountDue).toBe(33);
        });

        it('calculate amount due for second family member in 33/33/34 (99.99)', () => {
            const keys = Object.keys(invoice7['familySplits']);
            const familyPersonId = keys[1];
            const amountDue = InvoiceUtils.amountDueForFamilyMember(invoice7, familyPersonId);
            expect(amountDue).toBe(33);
        });

        it('calculate amount due for third family member in 33/33/34 (99.99)', () => {
            const keys = Object.keys(invoice7['familySplits']);
            const familyPersonId = keys[2];
            const amountDue = InvoiceUtils.amountDueForFamilyMember(invoice7, familyPersonId);
            expect(amountDue).toBe(33.99);
        });

        it('calculate amount due for first family member in 0/100 (100)', () => {
            const keys = Object.keys(invoice9['familySplits']);
            const familyPersonId = keys[0];
            const amountDue = InvoiceUtils.amountDueForFamilyMember(invoice9, familyPersonId);
            expect(amountDue).toBe(0);
        });

        it('calculate amount due for second family member in 0/100 (100)', () => {
            const keys = Object.keys(invoice9['familySplits']);
            const familyPersonId = keys[1];
            const amountDue = InvoiceUtils.amountDueForFamilyMember(invoice9, familyPersonId);
            expect(amountDue).toBe(100);
        });

        it('calculate amount due for third family member in 100/0 (100)', () => {
            const keys = Object.keys(invoice10['familySplits']);
            const familyPersonId = keys[0];
            const amountDue = InvoiceUtils.amountDueForFamilyMember(invoice10, familyPersonId);
            expect(amountDue).toBe(100);
        });

        it('calculate amount due for third family member in 100/0 (100)', () => {
            const keys = Object.keys(invoice10['familySplits']);
            const familyPersonId = keys[1];
            const amountDue = InvoiceUtils.amountDueForFamilyMember(invoice10, familyPersonId);
            expect(amountDue).toBe(0);
        });

        it('calculates the amount owed without floating point errors', () => {
            // BUGS-2553 floating point errors leading to rejected payments due to overpayments.
           const invoice =  {
               "_id": "2bMnKHg7jQdTeAmH9",
               "invoiceDate": "7/05/2024",
               "createdAt": 1720153858840,
               "createdBy": "SYSTEM",
               "lineItems": [
                   {
                       "_id": "LqRFc7JD7PyzZjvoG",
                       "description": "Pre-Kindergarten 4 Full Days (Schedule A)",
                       "type": "plan",
                       "frequency": "weekly",
                       "category": "tuition",
                       "program": "",
                       "amount": 329,
                       "scaledAmounts": [],
                       "ledgerAccountName": "420000",
                       "enrolledPlan": {
                           "_id": "LqRFc7JD7PyzZjvoG",
                           "planDetails": {
                               "_id": "LqRFc7JD7PyzZjvoG",
                               "description": "Pre-Kindergarten 4 Full Days (Schedule A)",
                               "type": "plan",
                               "frequency": "weekly",
                               "category": "tuition",
                               "program": "",
                               "amount": 329,
                               "scaledAmounts": [],
                               "ledgerAccountName": "420000"
                           },
                           "enrollmentDate": *************,
                           "allocations": [
                               {
                                   "allocationType": "discount",
                                   "amount": 10,
                                   "amountType": "percent",
                                   "discountType": "sibling10",
                                   "allocationDescription": "Discount: Sibling Discount 10%",
                                   "id": "DH7jAibm9qW3rhyWh"
                               }
                           ],
                           "createdAt": *************,
                           "enrollmentForecastStartDate": *************,
                           "lastInvoiced": *************,
                           "enrollmentForecastEndDate": null,
                           "expirationDate": null,
                           "overrideRate": null,
                           "updatedAt": 1718382766832,
                           "updatedBy": "L4rGdKMT73H34xvnt"
                       },
                       "coversPeriodDesc": "Weekly period 07/08/2024 - 07/12/2024",
                       "periodStartDate": *************,
                       "periodEndDate": *************,
                       "appliedDiscounts": [
                           {
                               "type": "discount",
                               "source": "sibling10",
                               "amount": 32.9,
                               "originalAllocation": {
                                   "allocationType": "discount",
                                   "amount": 10,
                                   "amountType": "percent",
                                   "discountType": "sibling10",
                                   "allocationDescription": "Discount: Sibling Discount 10%",
                                   "id": "DH7jAibm9qW3rhyWh"
                               }
                           }
                       ]
                   },
                   {
                       "_id": "yPPgdcbWavAA3pZ9e",
                       "description": "Activity Fees - Summer Camp (Weekly)",
                       "type": "plan",
                       "program": "",
                       "frequency": "weekly",
                       "category": "tuition",
                       "amount": 38,
                       "scaledAmounts": [],
                       "ledgerAccountName": "450045",
                       "enrolledPlan": {
                           "_id": "yPPgdcbWavAA3pZ9e",
                           "planDetails": {
                               "_id": "yPPgdcbWavAA3pZ9e",
                               "description": "Activity Fees - Summer Camp (Weekly)",
                               "type": "plan",
                               "program": "",
                               "frequency": "weekly",
                               "category": "tuition",
                               "amount": 38,
                               "scaledAmounts": [],
                               "ledgerAccountName": "450045"
                           },
                           "enrollmentDate": *************,
                           "allocations": [],
                           "createdAt": *************,
                           "enrollmentForecastStartDate": *************,
                           "lastInvoiced": *************
                       },
                       "coversPeriodDesc": "Weekly period 07/08/2024 - 07/12/2024",
                       "periodStartDate": *************,
                       "periodEndDate": *************
                   }
               ],
               "orgId": "7S6yG8wtyupDXatX8",
               "personId": "i7No5o3fPwFrzhxpD",
               "type": "planInvoice",
               "invoiceNumber": "5676",
               "openAmount": 116.53,
               "originalAmount": 334.1,
               "discountAmount": 32.9,
               "dueDate": *************,
               "dueDateString": "07/08/2024",
               "batchStamp": 1720153852627,
               "openPayerAmounts": {},
               "familySplits": null,
               "credits": [
                   {
                       "type": "credit",
                       "amount": 150.75,
                       "createdAt": 1720186483667,
                       "creditedBy": "9zyrhxepQW55F5rcu",
                       "creditReason": "other",
                       "creditNote": "2 flex days for 7/3/24 and 7/5/24",
                       "creditLineItemIndex": 0,
                       "creditLineItemOriginal": {
                           "_id": "LqRFc7JD7PyzZjvoG",
                           "description": "Pre-Kindergarten 4 Full Days (Schedule A)",
                           "type": "plan",
                           "frequency": "weekly",
                           "category": "tuition",
                           "program": "",
                           "amount": 329,
                           "scaledAmounts": [],
                           "ledgerAccountName": "420000",
                           "enrolledPlan": {
                               "_id": "LqRFc7JD7PyzZjvoG",
                               "planDetails": {
                                   "_id": "LqRFc7JD7PyzZjvoG",
                                   "description": "Pre-Kindergarten 4 Full Days (Schedule A)",
                                   "type": "plan",
                                   "frequency": "weekly",
                                   "category": "tuition",
                                   "program": "",
                                   "amount": 329,
                                   "scaledAmounts": [],
                                   "ledgerAccountName": "420000"
                               },
                               "enrollmentDate": *************,
                               "allocations": [
                                   {
                                       "allocationType": "discount",
                                       "amount": 10,
                                       "amountType": "percent",
                                       "discountType": "sibling10",
                                       "allocationDescription": "Discount: Sibling Discount 10%",
                                       "id": "DH7jAibm9qW3rhyWh"
                                   }
                               ],
                               "createdAt": *************,
                               "enrollmentForecastStartDate": *************,
                               "lastInvoiced": *************,
                               "enrollmentForecastEndDate": null,
                               "expirationDate": null,
                               "overrideRate": null,
                               "updatedAt": 1718382766832,
                               "updatedBy": "L4rGdKMT73H34xvnt"
                           },
                           "coversPeriodDesc": "Weekly period 07/08/2024 - 07/12/2024",
                           "periodStartDate": *************,
                           "periodEndDate": *************,
                           "appliedDiscounts": [
                               {
                                   "type": "discount",
                                   "source": "sibling10",
                                   "amount": 32.9,
                                   "originalAllocation": {
                                       "allocationType": "discount",
                                       "amount": 10,
                                       "amountType": "percent",
                                       "discountType": "sibling10",
                                       "allocationDescription": "Discount: Sibling Discount 10%",
                                       "id": "DH7jAibm9qW3rhyWh"
                                   }
                               }
                           ]
                       }
                   }
               ]
           }
           const familyPersonId = 'fZKHA2eR7y2DY5Eff';
           const amountDue = InvoiceUtils.amountDueForFamilyMember(invoice, familyPersonId);
           expect(amountDue).toBe(183.35);
           expect(amountDue).not.toBe(183.35000000000002);
        });

        it('calculates the amount owed for each family member with other credits applied', () => {
            const invoice = {
                "_id": "zeJXb6QAoi95vXpQQ",
                "invoiceDate": "6/26/2024",
                "createdAt": *************,
                "createdBy": "SYSTEM",
                "lineItems": [
                    {
                        "_id": "twdGAscCgiKdc7gyq",
                        "description": "Prek: Full Time 4-5 Days (Weekly)",
                        "amount": 340,
                        "type": "plan",
                        "frequency": "weekly",
                        "category": "tuition",
                        "ledgerAccountName": "4109",
                        "scaledAmounts": [],
                        "program": "",
                        "enrolledPlan": {
                            "_id": "twdGAscCgiKdc7gyq",
                            "planDetails": {
                                "_id": "twdGAscCgiKdc7gyq",
                                "description": "Prek: Full Time 4-5 Days (Weekly)",
                                "amount": 340,
                                "type": "plan",
                                "frequency": "weekly",
                                "category": "tuition",
                                "ledgerAccountName": "4109",
                                "scaledAmounts": [],
                                "program": ""
                            },
                            "enrollmentDate": *************,
                            "allocations": [
                                {
                                    "allocationType": "discount",
                                    "amount": 10,
                                    "amountType": "percent",
                                    "discountType": "MecklenburgCountyEmployees",
                                    "allocationDescription": "Discount: Mecklenburg County Employees - 10%",
                                    "id": "yzbPfc5nkxGZNvdZS"
                                }
                            ],
                            "createdAt": *************,
                            "enrollmentForecastStartDate": *************,
                            "lastInvoiced": *************
                        },
                        "coversPeriodDesc": "Weekly period 07/01/2024 - 07/05/2024",
                        "periodStartDate": 1719806400000,
                        "periodEndDate": 1720152000000,
                        "appliedDiscounts": [
                            {
                                "type": "discount",
                                "source": "MecklenburgCountyEmployees",
                                "amount": 34,
                                "originalAllocation": {
                                    "allocationType": "discount",
                                    "amount": 10,
                                    "amountType": "percent",
                                    "discountType": "MecklenburgCountyEmployees",
                                    "allocationDescription": "Discount: Mecklenburg County Employees - 10%",
                                    "id": "yzbPfc5nkxGZNvdZS"
                                }
                            }
                        ]
                    }
                ],
                "orgId": "sFQEMvtoW4aLZhtcD",
                "personId": "xovBziDciZvpiNvT3",
                "type": "planInvoice",
                "invoiceNumber": "10281",
                "openAmount": 15,
                "originalAmount": 306,
                "discountAmount": 34,
                "dueDate": 1719892800000,
                "dueDateString": "07/02/2024",
                "batchStamp": 1719376284606,
                "openPayerAmounts": {},
                "familySplits": {
                    "PEZsh0LNoYPUPU0ge": 50,
                    "TEd5gmnXttYzB8mCs": 50
                },
                "credits": [
                    {
                        "type": "credit",
                        "amount": 15,
                        "createdAt": 1719503315809,
                        "creditedBy": "TjMNRkkHZnsYTXRjM",
                        "creditReason": "other",
                        "creditNote": "partial payment to duplicate late fee KO ",
                        "creditLineItemIndex": 0,
                        "creditLineItemOriginal": {
                            "_id": "twdGAscCgiKdc7gyq",
                            "description": "Prek: Full Time 4-5 Days (Weekly)",
                            "amount": 340,
                            "type": "plan",
                            "frequency": "weekly",
                            "category": "tuition",
                            "ledgerAccountName": "4109",
                            "scaledAmounts": [],
                            "program": "",
                            "enrolledPlan": {
                                "_id": "twdGAscCgiKdc7gyq",
                                "planDetails": {
                                    "_id": "twdGAscCgiKdc7gyq",
                                    "description": "Prek: Full Time 4-5 Days (Weekly)",
                                    "amount": 340,
                                    "type": "plan",
                                    "frequency": "weekly",
                                    "category": "tuition",
                                    "ledgerAccountName": "4109",
                                    "scaledAmounts": [],
                                    "program": ""
                                },
                                "enrollmentDate": *************,
                                "allocations": [
                                    {
                                        "allocationType": "discount",
                                        "amount": 10,
                                        "amountType": "percent",
                                        "discountType": "MecklenburgCountyEmployees",
                                        "allocationDescription": "Discount: Mecklenburg County Employees - 10%",
                                        "id": "yzbPfc5nkxGZNvdZS"
                                    }
                                ],
                                "createdAt": *************,
                                "enrollmentForecastStartDate": *************,
                                "lastInvoiced": *************
                            },
                            "coversPeriodDesc": "Weekly period 07/01/2024 - 07/05/2024",
                            "periodStartDate": 1719806400000,
                            "periodEndDate": 1720152000000,
                            "appliedDiscounts": [
                                {
                                    "type": "discount",
                                    "source": "MecklenburgCountyEmployees",
                                    "amount": 34,
                                    "originalAllocation": {
                                        "allocationType": "discount",
                                        "amount": 10,
                                        "amountType": "percent",
                                        "discountType": "MecklenburgCountyEmployees",
                                        "allocationDescription": "Discount: Mecklenburg County Employees - 10%",
                                        "id": "yzbPfc5nkxGZNvdZS"
                                    }
                                }
                            ]
                        }
                    }
                ],
                "paymentTransactions": [
                    {
                        "pspReference": "RWL82C8QJTVBTRD3",
                        "resultCode": "Authorised",
                        "amount": {
                            "currency": "USD",
                            "value": 14155
                        },
                        "merchantReference": "zeJXb6QAoi95vXpQQ",
                        "mappedAdditionalData": {
                            "expiryDate": "8/2025",
                            "cardSummary": "7574",
                            "recurringProcessingModel": "Subscription",
                            "paymentMethod": "visa",
                            "paymentMethodVariant": "visastandarddebit",
                            "cardFunction": "Consumer",
                            "merchantReference": "zeJXb6QAoi95vXpQQ"
                        },
                        "createdAt": 1719964268857,
                        "totalAmount": 141.55,
                        "paidByDesc": "William Moore"
                    },
                    {
                        "pspReference": "HNLQKH27CTQK8LD3",
                        "resultCode": "Authorised",
                        "amount": {
                            "currency": "USD",
                            "value": 14155
                        },
                        "merchantReference": "zeJXb6QAoi95vXpQQ",
                        "mappedAdditionalData": {
                            "expiryDate": "7/2027",
                            "cardSummary": "6445",
                            "recurringProcessingModel": "Subscription",
                            "paymentMethod": "mc",
                            "paymentMethodVariant": "mcstandarddebit",
                            "cardFunction": "Consumer",
                            "merchantReference": "zeJXb6QAoi95vXpQQ"
                        },
                        "createdAt": 1720435570634,
                        "totalAmount": 141.55,
                        "paidByDesc": "Kayla Donnelly"
                    }
                ],
                "paymentRefused": false,
                "lateFeeAssessedAt": 1719981058825,
                "lateFeeAssessedInvoiceId": "BdTwKNFYQhnQZw37x"
            }
            const familyPersonId1 = 'PEZsh0LNoYPUPU0ge';
            const familyPersonId2 = 'TEd5gmnXttYzB8mCs';
            const amountDue1 = InvoiceUtils.amountDueForFamilyMember(invoice, familyPersonId1);
            const amountDue2 = InvoiceUtils.amountDueForFamilyMember(invoice, familyPersonId2);
            expect(amountDue1).toBe(145.50);
            expect(amountDue1).not.toBe(138.00);

            expect(amountDue2).toBe(145.50);
            expect(amountDue2).not.toBe(138.00);
        });
    });






    it('payerDistributionOfDiscount should return the correct payer distribution', () => {
        // standard amountType, no split in the month.
        const options = {
            enrollmentDateValue: 1697169600000,
            discount: {
                type: 'reimbursable',
                source: 'CCAP',
                amount: 235,
                originalAllocation: {
                    allocationType: 'reimbursable',
                    amount: 100,
                    amountType: 'dollars',
                    reimbursementType: 'CCAP',
                    allocationDescription: 'Reimbursable: KY Childcare Assistance Program',
                    id: 'hSkzxBrWj6azpptCX'
                },
                coveredDays: [
                    '2023-11-13',
                    '2023-11-14',
                    '2023-11-15',
                    '2023-11-16',
                    '2023-11-17'
                ]
            },
            dailyRate: 50
        }

        let expected = [
            {day: '2023-11-13', dailySubsidyRate: 30, dailyRate: 50, dailyCopayRate: 20},
            {day: '2023-11-14', dailySubsidyRate: 30, dailyRate: 50, dailyCopayRate: 20},
            {day: '2023-11-15', dailySubsidyRate: 30, dailyRate: 50, dailyCopayRate: 20},
            {day: '2023-11-16', dailySubsidyRate: 30, dailyRate: 50, dailyCopayRate: 20},
            {day: '2023-11-17', dailySubsidyRate: 30, dailyRate: 50, dailyCopayRate: 20}
        ]
        // Rate should be 30 because the discount is 100 and 100 / 5 = 20. 50 - 20 = 30.
        let result = InvoiceUtils.payerDistributionOfDiscount(options);
        expect(result).toEqual(expected);

        // percentage amountType, no split in the month.
        options.discount.originalAllocation.amountType = 'percent';
        options.discount.originalAllocation.amount = 50;

        expected = [
            {day: '2023-11-13', dailySubsidyRate: 25, dailyRate: 50, dailyCopayRate: 25},
            {day: '2023-11-14', dailySubsidyRate: 25, dailyRate: 50, dailyCopayRate: 25},
            {day: '2023-11-15', dailySubsidyRate: 25, dailyRate: 50, dailyCopayRate: 25},
            {day: '2023-11-16', dailySubsidyRate: 25, dailyRate: 50, dailyCopayRate: 25},
            {day: '2023-11-17', dailySubsidyRate: 25, dailyRate: 50, dailyCopayRate: 25}
        ]

        // Rate should be 25 because the discount is 50% and 50% * 50 = 25. BUGS-2160
        result = InvoiceUtils.payerDistributionOfDiscount(options);
        expect(result).toEqual(expected);

        // standard amountType, with split in the month.
        options.discount.originalAllocation.amountType = 'dollars';
        options.discount.originalAllocation.amount = 100;
        options.discount.coveredDays = [
            '2023-11-27',
            '2023-11-28',
            '2023-11-29',
            '2023-11-30',
            '2023-12-01',
        ];

        expected = [
            {day: '2023-11-27', dailySubsidyRate: 25, dailyRate: 50, dailyCopayRate: 25},
            {day: '2023-11-28', dailySubsidyRate: 25, dailyRate: 50, dailyCopayRate: 25},
            {day: '2023-11-29', dailySubsidyRate: 25, dailyRate: 50, dailyCopayRate: 25},
            {day: '2023-11-30', dailySubsidyRate: 25, dailyRate: 50, dailyCopayRate: 25},
            {day: '2023-12-01', dailySubsidyRate: 50, dailyRate: 50, dailyCopayRate: 0}
        ]

        // Rate should be 25 because the discount is 100 with 4 days left in the month and 100 / 4 = 25. 50 - 25 = 25. The new month day should have a rate that is equal to dailyRate.
        result = InvoiceUtils.payerDistributionOfDiscount(options);
        expect(result).toEqual(expected);

        // percentage amountType, with split in the month.
        options.discount.originalAllocation.amountType = 'percent';
        options.discount.originalAllocation.amount = 10;

        expected = [
            {day: '2023-11-27', dailySubsidyRate: 45, dailyRate: 50, dailyCopayRate: 5},
            {day: '2023-11-28', dailySubsidyRate: 45, dailyRate: 50, dailyCopayRate: 5},
            {day: '2023-11-29', dailySubsidyRate: 45, dailyRate: 50, dailyCopayRate: 5},
            {day: '2023-11-30', dailySubsidyRate: 45, dailyRate: 50, dailyCopayRate: 5},
            {day: '2023-12-01', dailySubsidyRate: 50, dailyRate: 50, dailyCopayRate: 0}
        ]

        // Rate should be 45 because the discount is 10% and 10% * 50 = 5. The new month day should have a rate that is equal to dailyRate.
        result = InvoiceUtils.payerDistributionOfDiscount(options);
        expect(result).toEqual(expected);

        // standard amountType, no split but enrollment date is in the middle of the month.
        options.discount.originalAllocation.amountType = 'dollars';
        options.discount.originalAllocation.amount = 200;
        options.discount.coveredDays = [
            '2023-11-13',
            '2023-11-14',
            '2023-11-15',
            '2023-11-16',
            '2023-11-17'
        ];
        options.enrollmentDateValue = 1700000000000;

        expected = [
            {day: '2023-11-13', dailySubsidyRate: 50, dailyRate: 50, dailyCopayRate: 0},
            {day: '2023-11-14', dailySubsidyRate: 50, dailyRate: 50, dailyCopayRate: 0},
            {day: '2023-11-15', dailySubsidyRate: 50, dailyRate: 50, dailyCopayRate: 0},
            {day: '2023-11-16', dailySubsidyRate: 50, dailyRate: 50, dailyCopayRate: 0},
            {day: '2023-11-17', dailySubsidyRate: 50, dailyRate: 50, dailyCopayRate: 0}
        ]
        // All the rates should equal the dailyRate because the enrollment date is after the first day in the set. The math ends up being 50 * 5 - 0 / 5 = 50.
        result = InvoiceUtils.payerDistributionOfDiscount(options);
        expect(result).toEqual(expected);

        // percentage amountType, no split but enrollment date is in the middle of the month.
        options.discount.originalAllocation.amountType = 'percent';
        options.discount.originalAllocation.amount = 1;

        expected = [
            {day: '2023-11-13', dailySubsidyRate: 50, dailyRate: 50, dailyCopayRate: 0},
            {day: '2023-11-14', dailySubsidyRate: 50, dailyRate: 50, dailyCopayRate: 0},
            {day: '2023-11-15', dailySubsidyRate: 50, dailyRate: 50, dailyCopayRate: 0},
            {day: '2023-11-16', dailySubsidyRate: 50, dailyRate: 50, dailyCopayRate: 0},
            {day: '2023-11-17', dailySubsidyRate: 50, dailyRate: 50, dailyCopayRate: 0}
        ]
        // All the rates should equal the dailyRate because the enrollment date is after the first day in the set. This is logically consistent with dollar amount types.
        result = InvoiceUtils.payerDistributionOfDiscount(options);
        expect(result).toEqual(expected);

    });

    describe('BillingHelper.calculateMerchantFee', () => {
        it('should calculate the correct fee for bank accounts', () => {
            const accountType = 'bank_account';
            const plan = { calculatedAmount: '100.00' };
            const orgPaymentFees = { achFee: 0.40 };
            const result = InvoiceUtils.calculateMerchantFee(accountType, plan, orgPaymentFees);
            expect(result).toBe(0.40);
        });

        it('should calculate the correct fee for credit cards with default fees', () => {
            const accountType = 'card';
            const plan = { calculatedAmount: '100.00' };
            const orgPaymentFees = {};
            const result = InvoiceUtils.calculateMerchantFee(accountType, plan, orgPaymentFees);
            expect(result).toBe(3.20);
        });

        it('should calculate the correct fee for credit cards with custom fees', () => {
            const accountType = 'card';
            const plan = { calculatedAmount: '100.00' };
            const orgPaymentFees = { cardFee: 0.50, cardRate: 0.035 };
            const result = InvoiceUtils.calculateMerchantFee(accountType, plan, orgPaymentFees);
            expect(result).toBe(4.00);
        });

        it('should handle plans with non-numeric calculated amounts gracefully', () => {
            const accountType = 'card';
            const plan = { calculatedAmount: 'abc' };
            const orgPaymentFees = { cardFee: 0.30, cardRate: 0.029 };
            const result = InvoiceUtils.calculateMerchantFee(accountType, plan, orgPaymentFees);
            expect(result).toBeNaN();
        });

        it('should use default ACH fee if not provided', () => {
            const accountType = 'bank_account';
            const plan = { calculatedAmount: '100.00' };
            const orgPaymentFees = {};
            const result = InvoiceUtils.calculateMerchantFee(accountType, plan, orgPaymentFees);
            expect(result).toBe(0.40);
        });

        it('should calculate the correct fee for zero amount', () => {
            const accountType = 'card';
            const plan = { calculatedAmount: '0.00' };
            const orgPaymentFees = {};
            const result = InvoiceUtils.calculateMerchantFee(accountType, plan, orgPaymentFees);
            expect(result).toBe(0.30);
        });

        it('should calculate the correct fee for negative amount', () => {
            const accountType = 'card';
            const plan = { calculatedAmount: '-100.00' };
            const orgPaymentFees = {};
            const result = InvoiceUtils.calculateMerchantFee(accountType, plan, orgPaymentFees);
            expect(result).toBe(-2.60);
        });

        it('should calculate the correct fee for large amount', () => {
            const accountType = 'card';
            const plan = { calculatedAmount: '1000000.00' };
            const orgPaymentFees = {};
            const result = InvoiceUtils.calculateMerchantFee(accountType, plan, orgPaymentFees);
            expect(result).toBe(29000.30);
        });

        it('should calculate the correct fee for small positive amount', () => {
            const accountType = 'card';
            const plan = { calculatedAmount: '0.01' };
            const orgPaymentFees = {};
            const result = InvoiceUtils.calculateMerchantFee(accountType, plan, orgPaymentFees);
            expect(result).toBe(0.30);
        });

        it('should handle missing calculated amount in plan gracefully', () => {
            const accountType = 'card';
            const plan = {};
            const orgPaymentFees = { cardFee: 0.30, cardRate: 0.029 };
            const result = InvoiceUtils.calculateMerchantFee(accountType, plan, orgPaymentFees);
            expect(result).toBeNaN();
        });
    });
    
    describe('getPlanAmountTotal', () => {
        it('should return the amount when there are no discounts', () => {
            const lineItem = {
                amount: 100.00,
                appliedDiscounts: []
            };
            const total = InvoiceUtils.getPlanAmountTotal(lineItem);
            expect(total).toBe(100.00);
        });
    
        it('should return the amount minus the discount when there is one discount', () => {
            const lineItem = {
                amount: 100.00,
                appliedDiscounts: [
                    { amount: 10.00 }
                ]
            };
            const total = InvoiceUtils.getPlanAmountTotal(lineItem);
            expect(total).toBe(90.00);
        });
    
        it('should return the amount minus the total discounts when there are multiple discounts', () => {
            const lineItem = {
                amount: 100.00,
                appliedDiscounts: [
                    { amount: 10.00 },
                    { amount: 5.00 }
                ]
            };
            const total = InvoiceUtils.getPlanAmountTotal(lineItem);
            expect(total).toBe(85.00);
        });
    
        it('should return the amount when appliedDiscounts is undefined', () => {
            const lineItem = {
                amount: 100.00
            };
            const total = InvoiceUtils.getPlanAmountTotal(lineItem);
            expect(total).toBe(100.00);
        });
    
        it('should return the amount when appliedDiscounts is null', () => {
            const lineItem = {
                amount: 100.00,
                appliedDiscounts: null
            };
            const total = InvoiceUtils.getPlanAmountTotal(lineItem);
            expect(total).toBe(100.00);
        });
    
        it('should return the amount when appliedDiscounts is not an array', () => {
            const lineItem = {
                amount: 100.00,
                appliedDiscounts: {}
            };
            const total = InvoiceUtils.getPlanAmountTotal(lineItem);
            expect(total).toBe(100.00);
        });
    });
});

describe("InvoiceUtils.creditsForFamilyMember", () => {
    const familyPersonId = "person123";

    const baseInvoice = {
        credits: [
            {
                type: "payment",
                amount: 100,
                paidBy: "person123"
            },
            {
                type: "payment",
                amount: 50,
                paidBy: "someoneElse"
            },
            {
                type: "credit",
                amount: 25,
                creditReason: "manual_payment"
            },
            {
                type: "credit",
                amount: 75,
                paidBy: "person123"
            },
            {
                type: "credit",
                amount: 30,
                paidBy: "someoneElse"
            },
            {
                type: "refund",
                amount: 20,
                paidBy: "person123"
            },
            {
                type: "refund",
                amount: 15,
                paidBy: "someoneElse"
            }
        ]
    };

    beforeEach(() => {
        jest.spyOn(InvoiceUtils, "getFamilySplits").mockReturnValue(null); // default: full share
    });

    afterEach(() => {
        jest.restoreAllMocks();
    });

    it("includes all credit types (payment, credit, refund) when familyPersonShare is 1.0 and includeRefunds is true", () => {
        const result = InvoiceUtils.creditsForFamilyMember(baseInvoice, familyPersonId, true);
        expect(result).toHaveLength(7); // all entries
    });

    it("excludes refunds when includeRefunds is false", () => {
        const result = InvoiceUtils.creditsForFamilyMember(baseInvoice, familyPersonId, false);
        expect(result).toHaveLength(5);
        expect(result.every(c => c.type !== "refund")).toBe(true);
    });

    it("only includes credits paid by familyPersonId when share is less than 1", () => {
        jest.spyOn(InvoiceUtils, "getFamilySplits").mockReturnValue({ [familyPersonId]: 50 });

        const result = InvoiceUtils.creditsForFamilyMember(baseInvoice, familyPersonId, true);

        expect(result).toEqual(
            expect.arrayContaining([
                expect.objectContaining({ type: "payment", paidBy: "person123" }),
                expect.objectContaining({ type: "credit", paidBy: "person123" }),
                expect.objectContaining({ type: "refund", paidBy: "person123" }),
            ])
        );
        // Should not include credits or refunds paid by others
        expect(result.some(c => c.paidBy === "someoneElse")).toBe(false);
        // Should also not include manual_payment credit with no paidBy if share < 1
        expect(result.some(c => !c.paidBy && c.creditReason === "manual_payment")).toBe(false);
    });

    it("includes manual payment credit with no paidBy only if familyPersonShare is 1", () => {
        // full share
        jest.spyOn(InvoiceUtils, "getFamilySplits").mockReturnValue({ [familyPersonId]: 100 });
        const result = InvoiceUtils.creditsForFamilyMember(baseInvoice, familyPersonId, false);
        expect(result.some(c => c.type === "credit" && c.creditReason === "manual_payment" && !("paidBy" in c))).toBe(true);

        // partial share
        jest.spyOn(InvoiceUtils, "getFamilySplits").mockReturnValue({ [familyPersonId]: 50 });
        const resultPartial = InvoiceUtils.creditsForFamilyMember(baseInvoice, familyPersonId, false);
        expect(resultPartial.some(c => c.creditReason === "manual_payment" && !c.paidBy)).toBe(false);
    });

    it("returns an empty array if there are no credits", () => {
        const result = InvoiceUtils.creditsForFamilyMember({ credits: [] }, familyPersonId, true);
        expect(result).toEqual([]);
    });

    it("returns empty array if no credits match filtering conditions", () => {
        const invoice = {
            credits: [
                { type: "credit", paidBy: "not-this-person" },
                { type: "refund", paidBy: "not-this-person" },
            ]
        };
        jest.spyOn(InvoiceUtils, "getFamilySplits").mockReturnValue({ [familyPersonId]: 50 });
        const result = InvoiceUtils.creditsForFamilyMember(invoice, familyPersonId, false);
        expect(result).toEqual([]);
    });
});
describe("InvoiceUtils.amountPaidByFamilyMember", () => {
    const familyPersonId = "person123";
    const timestamp = 1740000000000;

    beforeEach(() => {
        jest.clearAllMocks();
    });

    it("returns 0 if no credits exist", () => {
        jest.spyOn(InvoiceUtils, "creditsForFamilyMember").mockReturnValue([]);
        const invoice = { credits: [] };

        const result = InvoiceUtils.amountPaidByFamilyMember(invoice, familyPersonId, null);
        expect(result).toBe(0);
    });

    it("sums all family member payments when there are no refunds or chargebacks", () => {
        const credits = [
            { type: "payment", amount: 100, createdAt: timestamp },
            { type: "payment", amount: 50, createdAt: timestamp }
        ];
        const invoice = { credits };

        jest.spyOn(InvoiceUtils, "creditsForFamilyMember").mockReturnValue(credits);

        const result = InvoiceUtils.amountPaidByFamilyMember(invoice, familyPersonId, null);
        expect(result).toBe(150);
    });

    it("filters out payments after the throughDateStamp", () => {
        const credits = [
            { type: "payment", amount: 100, createdAt: timestamp },
            { type: "payment", amount: 50, createdAt: timestamp + 100000 } // beyond throughDate
        ];
        const invoice = { credits };

        jest.spyOn(InvoiceUtils, "creditsForFamilyMember").mockReturnValue(credits);

        const result = InvoiceUtils.amountPaidByFamilyMember(invoice, familyPersonId, timestamp + 1);
        expect(result).toBe(100);
    });

    it("subtracts refunds that match adyenInfo.additionalData.merchantReference", () => {
        const credits = [
            { type: "payment", amount: 200, adyenInfo: { merchantReference: "mr-abc" }, createdAt: timestamp }
        ];
        const invoice = {
            credits: [
                ...credits,
                { type: "refund", amount: 100, adyenInfo: { additionalData: { merchantReference: "mr-abc" } } }
            ]
        };

        jest.spyOn(InvoiceUtils, "creditsForFamilyMember").mockReturnValue(credits);

        const result = InvoiceUtils.amountPaidByFamilyMember(invoice, familyPersonId, null);
        expect(result).toBe(100);
    });

    it("subtracts chargebacks that match adyenInfo.originalReference", () => {
        const credits = [
            { type: "payment", amount: 100, adyenInfo: { pspReference: "psp-abc" }, createdAt: timestamp }
        ];
        const invoice = {
            credits: [
                ...credits,
                { type: "chargeback", amount: 40, adyenInfo: { originalReference: "psp-abc" } }
            ]
        };

        jest.spyOn(InvoiceUtils, "creditsForFamilyMember").mockReturnValue(credits);

        const result = InvoiceUtils.amountPaidByFamilyMember(invoice, familyPersonId, null);
        expect(result).toBe(60);
    });

    it("handles refunds and chargebacks together (Adyen only)", () => {
        const credits = [
            {
                type: "payment",
                amount: 300,
                adyenInfo: {
                    pspReference: "psp123",           // for chargeback matching
                    reference: "adyen-ref-456"        // new-style refund matching
                },
                createdAt: timestamp
            }
        ];

        const invoice = {
            credits: [
                ...credits,
                {
                    type: "refund",
                    amount: 50,
                    adyenInfo: {
                        reference: "adyen-ref-456"      // should match the payment's reference
                    }
                },
                {
                    type: "chargeback",
                    amount: 75,
                    adyenInfo: {
                        originalReference: "psp123"     // should match the payment's pspReference
                    }
                }
            ]
        };

        jest.spyOn(InvoiceUtils, "creditsForFamilyMember").mockReturnValue(credits);

        const result = InvoiceUtils.amountPaidByFamilyMember(invoice, familyPersonId, null);
        expect(result).toBe(175); // 300 - 50 (refund) - 75 (chargeback)
    });
    it("matches refunds using adyenInfo.reference (new Adyen Balance platform)", () => {
        const credits = [
            {
                type: "payment",
                amount: 120,
                adyenInfo: {
                    reference: "adyen-new-ref-999"
                },
                createdAt: timestamp
            }
        ];

        const invoice = {
            credits: [
                ...credits,
                {
                    type: "refund",
                    amount: 20,
                    adyenInfo: {
                        reference: "adyen-new-ref-999" // Matches the payment above
                    }
                }
            ]
        };

        jest.spyOn(InvoiceUtils, "creditsForFamilyMember").mockReturnValue(credits);

        const result = InvoiceUtils.amountPaidByFamilyMember(invoice, familyPersonId, null);
        expect(result).toBe(100); // 120 - 20 refund
    });

    it("does not subtract refund when adyenInfo.reference does not match", () => {
        const credits = [
            {
                type: "payment",
                amount: 150,
                adyenInfo: {
                    reference: "ref-a"
                },
                createdAt: timestamp
            }
        ];

        const invoice = {
            credits: [
                ...credits,
                {
                    type: "refund",
                    amount: 40,
                    adyenInfo: {
                        reference: "ref-b" // Does not match the payment
                    }
                }
            ]
        };

        jest.spyOn(InvoiceUtils, "creditsForFamilyMember").mockReturnValue(credits);

        const result = InvoiceUtils.amountPaidByFamilyMember(invoice, familyPersonId, null);
        expect(result).toBe(150); // refund ignored
    });

    it("matches legacy refunds using adyenInfo.additionalData.merchantReference", () => {
        const credits = [
            {
                type: "payment",
                amount: 200,
                adyenInfo: {
                    merchantReference: "legacy-ref-xyz"
                },
                createdAt: timestamp
            }
        ];

        const invoice = {
            credits: [
                ...credits,
                {
                    type: "refund",
                    amount: 75,
                    adyenInfo: {
                        additionalData: {
                            merchantReference: "legacy-ref-xyz" // Legacy match
                        }
                    }
                }
            ]
        };

        jest.spyOn(InvoiceUtils, "creditsForFamilyMember").mockReturnValue(credits);

        const result = InvoiceUtils.amountPaidByFamilyMember(invoice, familyPersonId, null);
        expect(result).toBe(125); // 200 - 75
    });
});