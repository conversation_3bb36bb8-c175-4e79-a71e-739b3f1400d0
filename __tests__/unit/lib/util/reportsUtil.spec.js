import { Reservations } from "../../../../lib/collections/reservations";
import { ReportsUtilLib } from "../../../../lib/util/reportsUtil";
import { mockCollection } from "../../../helpers/collectionMock";
import moment from 'moment-timezone';

const reservationsMock = Reservations;
describe('ReportsUtilLib', () => {
    describe('checkForAbsenceInAttendance method', () => {
        beforeEach(() => {
            jest.clearAllMocks();
        });

        const mockReservation = {
            _id: 'reservation123',
            recurringExceptions: [1673740800000]
        };

        const mockPerson = {
            _id: 'person123'
        };

        const mockTimezone = 'America/New_York';

        it('should return undefined when no recurring exceptions exist', async () => {
            const reservation = { ...mockReservation, recurringExceptions: [] };
            const startDate = moment.tz('01/14/2023', 'MM/DD/YYYY', mockTimezone);
            
            const result = await ReportsUtilLib.checkForAbsenceInAttendance(
                reservation,
                startDate,
                mockTimezone,
                mockPerson
            );
            
            expect(result).toBeUndefined();
        });

        it('should return cancellation reason when exception date matches', async () => {
            const mockCancelledReservation = {
                cancellationReason: 'Sick Leave'
            };

            reservationsMock.findOneAsync.mockResolvedValueOnce(mockCancelledReservation);
            
            const startDate = moment.tz('01/14/2023', 'MM/DD/YYYY', mockTimezone);
            
            const result = await ReportsUtilLib.checkForAbsenceInAttendance(
                mockReservation,
                startDate,
                mockTimezone,
                mockPerson
            );
            
            expect(result).toBe('Sick Leave');
            expect(reservationsMock.findOneAsync).toHaveBeenCalledWith({
                scheduledDate: mockReservation.recurringExceptions[0],
                selectedPerson: mockPerson._id,
                cancellationOriginalReservationId: mockReservation._id,
                cancellationReason: { $exists: true }
            });
        });

        it('should return undefined when exception dates do not match', async () => {
            const startDate = moment.tz('01/15/2023', 'MM/DD/YYYY', mockTimezone);
            
            const result = await ReportsUtilLib.checkForAbsenceInAttendance(
                mockReservation,
                startDate,
                mockTimezone,
                mockPerson
            );
            
            expect(result).toBeUndefined();
        });

        it('should handle multiple recurring exceptions', async () => {
            const reservation = {
                ...mockReservation,
                recurringExceptions: [
                    '2023-01-15T00:00:00.000Z',
                    '2023-01-16T00:00:00.000Z'
                ]
            };

            const mockCancelledReservation = {
                cancellationReason: 'Vacation'
            };

            reservationsMock.findOneAsync.mockResolvedValueOnce(mockCancelledReservation);
            
            const startDate = moment.tz('01/15/2023', 'MM/DD/YYYY', mockTimezone);
            
            const result = await ReportsUtilLib.checkForAbsenceInAttendance(
                reservation,
                startDate,
                mockTimezone,
                mockPerson
            );
            
            expect(result).toBe('Vacation');
        });

        it('should handle no cancellation reason', async () => {
            const reservation = {
                ...mockReservation,
                recurringExceptions: [
                    '2023-01-15T00:00:00.000Z',
                    '2023-01-16T00:00:00.000Z'
                ]
            };
            const startDate = moment.tz('01/15/2023', 'MM/DD/YYYY', mockTimezone);
            const result = await ReportsUtilLib.checkForAbsenceInAttendance(
                reservation,
                startDate,
                mockTimezone,
                mockPerson
            );
            expect(result).toBeUndefined();
        });

        it('should handle mobile absent reasons and skip web app reason', async () => {
            const person = {
                ...mockPerson,
                familyCheckIn: {
                    absentReason: 'Mobile',
                    absent: true,
                    checkInTime: moment.tz('01/15/2023', 'MM/DD/YYYY', mockTimezone)
                }
            };
            const reservation = {
                ...mockReservation,
                recurringExceptions: [
                    '2023-01-15T00:00:00.000Z',
                    '2023-01-16T00:00:00.000Z'
                ]
            };
            const startDate = moment.tz('01/15/2023', 'MM/DD/YYYY', mockTimezone);
            const result = await ReportsUtilLib.checkForAbsenceInAttendance(
                reservation,
                startDate,
                mockTimezone,
                person
            );
            expect(result).toBe('Mobile');
        });

        it('should return cancellation reason for non-recurring cancelled reservation', async () => {
            const reservation = {
                ...mockReservation,
                recurringExceptions: [],
                cancellationReason: 'Family Emergency'
            };
            const startDate = moment.tz('01/15/2023', 'MM/DD/YYYY', mockTimezone);
            
            const result = await ReportsUtilLib.checkForAbsenceInAttendance(
                reservation,
                startDate,
                mockTimezone,
                mockPerson
            );
            
            expect(result).toBe('Family Emergency');
        });
        
        it('should return undefined when non-recurring reservation has no cancellation reason', async () => {
            const reservation = {
                ...mockReservation,
                recurringExceptions: []
            };
            const startDate = moment.tz('01/15/2023', 'MM/DD/YYYY', mockTimezone);
            
            const result = await ReportsUtilLib.checkForAbsenceInAttendance(
                reservation,
                startDate,
                mockTimezone,
                mockPerson,
            );
            
            expect(result).toBeUndefined();
        });

        it('should return undefined when recurring exceptions is null', async () => {
            const reservation = {
                ...mockReservation,
                recurringExceptions: null
            };
            const startDate = moment.tz('01/14/2023', 'MM/DD/YYYY', mockTimezone);
            
            const result = await ReportsUtilLib.checkForAbsenceInAttendance(
                reservation,
                startDate,
                mockTimezone,
                mockPerson
            );
            
            expect(result).toBeUndefined();
        });
        
        it('should return undefined when cancelled reservation lookup fails', async () => {
            reservationsMock.findOneAsync.mockResolvedValueOnce(null);
            
            const startDate = moment.tz('01/14/2023', 'MM/DD/YYYY', mockTimezone);
            
            const result = await ReportsUtilLib.checkForAbsenceInAttendance(
                mockReservation,
                startDate,
                mockTimezone,
                mockPerson
            );
            
            expect(result).toBeUndefined();
        });
        
        it('should return undefined when person object is invalid', async () => {
            const invalidPerson = null;
            const startDate = moment.tz('01/14/2023', 'MM/DD/YYYY', mockTimezone);
            
            await expect(ReportsUtilLib.checkForAbsenceInAttendance(
                mockReservation,
                startDate,
                mockTimezone,
                invalidPerson
            )).rejects.toThrow('Invalid parameters');
        });
        
        it('should return undefined when familyCheckIn data is invalid', async () => {
            const personWithInvalidCheckIn = {
                ...mockPerson,
                familyCheckIn: {
                    absent: true,
                    absentReason: 'Mobile',
                    checkInTime: 'invalid-date'
                }
            };
            const startDate = moment.tz('01/15/2023', 'MM/DD/YYYY', mockTimezone);
            
            const result = await ReportsUtilLib.checkForAbsenceInAttendance(
                mockReservation,
                startDate,
                mockTimezone,
                personWithInvalidCheckIn
            );
            
            expect(result).toBeUndefined();
        });
        
        it('should throw error when reservation is invalid', async () => {
            const startDate = moment.tz('01/14/2023', 'MM/DD/YYYY', mockTimezone);
            
            await expect(ReportsUtilLib.checkForAbsenceInAttendance(
                null,
                startDate,
                mockTimezone,
                mockPerson
            )).rejects.toThrow('Invalid parameters');
        });
        
        it('should throw error when startDateMoment is invalid', async () => {
            await expect(ReportsUtilLib.checkForAbsenceInAttendance(
                mockReservation,
                null,
                mockTimezone,
                mockPerson
            )).rejects.toThrow('Invalid parameters');
        });
        
        it('should throw error when timezone is invalid', async () => {
            const startDate = moment.tz('01/14/2023', 'MM/DD/YYYY', mockTimezone);
            
            await expect(ReportsUtilLib.checkForAbsenceInAttendance(
                mockReservation,
                startDate,
                null,
                mockPerson
            )).rejects.toThrow('Invalid parameters');
        });

        it('should return cancellationReason when reservation has originalScheduledDate matching report date', async () => {
            const testDate = moment.tz('01/14/2023', 'MM/DD/YYYY', mockTimezone);
            const reservation = {
                _id: 'reservation123',
                originalScheduledDate: testDate.valueOf(),
                cancellationReason: 'Vacation'
            };
            
            const result = await ReportsUtilLib.checkForAbsenceInAttendance(
                reservation,
                testDate,
                mockTimezone,
                mockPerson
            );
            
            expect(result).toBe('Vacation');
        });
        
        it('should use reservation cancellationReason as fallback when no cancelled reservation found', async () => {
            const testDate = moment.tz('01/14/2023', 'MM/DD/YYYY', mockTimezone);
            const reservation = {
                _id: 'reservation123',
                recurringExceptions: [testDate.valueOf()],
                cancellationReason: 'Family Emergency'
            };
            
            const result = await ReportsUtilLib.checkForAbsenceInAttendance(
                reservation,
                testDate,
                mockTimezone,
                mockPerson
            );
            
            expect(result).toBe('Family Emergency');
        });
        
        it('should reproduce the original bug scenario (fixed by the changes)', async () => {        
            const testDate = moment.tz('01/14/2023', 'MM/DD/YYYY', mockTimezone);
            const reservation = {
                _id: 'reservation123',
                recurringExceptions: [testDate.valueOf()],
                cancellationReason: 'Absent - Vacation'
            };
            
            const result = await ReportsUtilLib.checkForAbsenceInAttendance(
                reservation,
                testDate,
                mockTimezone,
                mockPerson
            );
            
            expect(result).toBe('Absent - Vacation');
        });

        it('should not return cancellationReason when originalScheduledDate does not match report date but should return it for non-recurring reservations', async () => {
            const testDate = moment.tz('01/14/2023', 'MM/DD/YYYY', mockTimezone);
            const reservation = {
                _id: 'reservation123',
                originalScheduledDate: moment.tz('01/15/2023', 'MM/DD/YYYY', mockTimezone).valueOf(),
                cancellationReason: 'Vacation',
                recurringExceptions: []
            };
            
            reservationsMock.findOneAsync.mockReset();
            reservationsMock.findOneAsync.mockResolvedValue(null);
            
            const result = await ReportsUtilLib.checkForAbsenceInAttendance(
                reservation,
                testDate,
                mockTimezone,
                mockPerson
            );
            
            expect(result).toBe('Vacation');
        });

        it('should handle complex scenario with multiple cancellation possibilities', async () => {
            const testDate = moment.tz('01/14/2023', 'MM/DD/YYYY', mockTimezone);
            const reservation = {
                _id: 'reservation123',
                originalScheduledDate: testDate.valueOf(),
                recurringExceptions: [testDate.valueOf()],
                cancellationReason: 'Original Cancellation Reason'
            };
            
            reservationsMock.findOneAsync.mockReset();
            reservationsMock.findOneAsync.mockResolvedValue({
                cancellationReason: 'Separate Cancellation Record'
            });
            
            const result = await ReportsUtilLib.checkForAbsenceInAttendance(
                reservation,
                testDate,
                mockTimezone,
                mockPerson
            );
            
            expect(result).toBe('Original Cancellation Reason');
        });

        it('should prioritize familyCheckIn absence reason over originalScheduledDate cancellation reason', async () => {
            const testDate = moment.tz('01/14/2023', 'MM/DD/YYYY', mockTimezone);
            const personWithFamilyCheckIn = {
                ...mockPerson,
                familyCheckIn: {
                    absent: true,
                    absentReason: 'Mobile App Absence',
                    checkInTime: testDate.valueOf()
                }
            };
            
            const reservation = {
                _id: 'reservation123',
                originalScheduledDate: testDate.valueOf(),
                cancellationReason: 'Web Cancellation Reason'
            };
            
            const result = await ReportsUtilLib.checkForAbsenceInAttendance(
                reservation,
                testDate,
                mockTimezone,
                personWithFamilyCheckIn
            );
            
            expect(result).toBe('Mobile App Absence');
        });

        it('should check for cancelled reservation when no originalScheduledDate match but recurringExceptions match', async () => {
            const testDate = moment.tz('01/14/2023', 'MM/DD/YYYY', mockTimezone);
            const reservation = {
                _id: 'reservation123',
                recurringExceptions: [testDate.valueOf()],
                cancellationReason: 'Fallback Reason'
            };
            
            reservationsMock.findOneAsync.mockReset();
            reservationsMock.findOneAsync.mockResolvedValue({
                cancellationReason: 'Exception Cancellation Reason'
            });
            
            const result = await ReportsUtilLib.checkForAbsenceInAttendance(
                reservation,
                testDate,
                mockTimezone,
                mockPerson
            );
            
            expect(result).toBe('Exception Cancellation Reason');
        });
    });
});

