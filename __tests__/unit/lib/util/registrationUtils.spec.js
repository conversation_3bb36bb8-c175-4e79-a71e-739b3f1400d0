import { Meteor } from 'meteor/meteor';
import { RegistrationUtils } from "../../../../lib/util/registrationUtils";
import { jest, beforeEach } from '@jest/globals';
import expect from 'expect';
import { MiscUtils } from '../../../../lib/util/miscUtils';
import { mockCollection } from '../../../helpers/collectionMock';
import { DiscountTypes } from '../../../../lib/discountTypes';
import { parentSource } from '../../../../lib/constants/registrationConstants';
import { ITEM_TYPE, PLAN_TYPE, PUNCH_CARD_TYPE, SCALED_WEEKLY_PLAN } from '../../../../lib/constants/billingConstants';
import { Orgs } from "../../../../lib/collections/orgs";
import { People } from "../../../../lib/collections/people";
import { Invoices } from "../../../../lib/collections/invoices";
import moment from "moment-timezone";
import { Reservations } from "../../../../lib/collections/reservations";
import { Session } from '../../../mocks/meteor/session';

describe('RegistrationUtils', () => {
    let instance;
    
    beforeEach(() => {
        instance = {
            currentPlans: {
                get: jest.fn()
            },
            currentBundle: {
                get: jest.fn()
            },
            data: {
                selectedPlans: {
                    set: jest.fn(),
                    get: jest.fn()
                }
            },
            allChildPlans: [],
            regularPrice: {
                set: jest.fn(),
                get: jest.fn(() => 0)
            },
            bundledPrice: {
                set: jest.fn(),
                get: jest.fn(() => 0)
            },
            bundleSavings: {
                set: jest.fn()
            }
        };
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    it('correctly counts multiple invoices with the same time period', async () => {
        const org = {
            _id: 'orgId',
            billing: {
                regFeeConfig: {
                    enabled: true,
                    feeId: 'feeId',
                    perChild: false,
                    enableMaxPerFamily: false,
                    maxPerFamily: 1
                    },
                }
            };

        const regData = {
            children: [
                { _id: 'childId1' }
            ],
            plans: [
                [
                    {_id: 'plan1', details: { timePeriod: 'timePeriod1' }, regFeeExempt: false}
                ]
            ]
        };
        const registrationFee = 50;
        const updatedOrg = {
            ...org,
            billing: {
                regFeeConfig: {
                    ...org.billing.regFeeConfig,
                    perChild: true,
                    enableMaxPerFamily: true,
                    maxPerFamily: 2
                }
            }
        };

        const orgFindOneMock = Orgs.findOneAsync;
        orgFindOneMock.mockImplementationOnce(() => updatedOrg);

        const peopleFindMock = People.findOneAsync;
        peopleFindMock.mockImplementationOnce(() => ({
            _id: 'personId',
            findInheritedRelationshipsAsArray: jest.fn(() => ({
                mapAsync: jest.fn(() => [
                    'childId1'
                ]),
            }))
        }));

        
        Invoices.find.mockImplementationOnce(() => {
            return ({
                fetchAsync:jest.fn().mockImplementation(()=>[
                    {
                        _id: 'iId1',
                        personId: 'childId2',
                        lineItems: [{ originalItem: { _id: 'feeId' }, timePeriodIds: ['timePeriod1'] }]
                    },
                    {
                        _id: 'iId2',
                        personId: 'childId3',
                        lineItems: [{ originalItem: { _id: 'feeId' }, timePeriodIds: ['timePeriod1'] }]
                    }
                ])
            })
        });

        const result = await RegistrationUtils.totalRegistrationFees(regData, registrationFee, false, 'personId', 'orgId');
        // Two invoices for the same time period should count towards the family max.
        expect(result.fee).toBe(0);
        expect(result.timePeriodIds).toEqual([]);

    });

    it('checks invoices for other children in the family to get the list of time periods that have already been registered for', async () => {
        let peopleFindMock;
        let orgFindOneMock;
        const formattedCurrencySpy = jest.spyOn(MiscUtils, 'formatCurrency');
        const org = {
            _id: 'orgId',
            billing: {
                regFeeConfig: {
                    enabled: true,
                    feeId: 'feeId',
                    perChild: false,
                    enableMaxPerFamily: false,
                    maxPerFamily: 1
                }
            }
        };
        let data = {
            children: [
                {
                    _id: 'childId1'
                }
            ],
            plans: [
                [
                    {
                        _id: "planId1",
                        description: "Other Scaled Weekly Spring 2024",
                        details: {
                            timePeriod: "timePeriod1"
                        },
                        regFeeExempt: false
                    }
                ]
            ]
        };

        orgFindOneMock = Orgs.findOneAsync;
        orgFindOneMock.mockImplementation(() => org);

        peopleFindMock = People.findOneAsync;
        peopleFindMock.mockImplementationOnce(() => ({
            _id: 'personId',
            findInheritedRelationshipsAsArray: jest.fn(() => ({
                mapAsync: jest.fn(() => [
                    'childId1' 
                    ,'childId2' 
                ]),
            })),
        }));

        const registrationFee = 50;
        Invoices.find.mockImplementation(() =>{
            return ({
                fetchAsync : jest.fn().mockImplementation(()=> [
                    {
                        _id: 'iId1',
                        personId: 'childId1',
                        lineItems: [{ originalItem: { _id: 'feeId' }, timePeriodIds: ['timePeriod1'] }]
                    },
                    { _id: 'iId2', personId: 'childId2', lineItems: [{ originalItem: { _id: 'feeId' }, timePeriodIds: ['otherTPId'] }] }
                ])
            }) 
        });

        const result = await RegistrationUtils.totalRegistrationFees(data, registrationFee, false, 'personId', 'orgId', true);

        expect(result.totalFee).toBe(0);
        expect(result.childrenFees).toEqual([{
            childIndex: 0,
            fee: 0,
            timePeriodIds: []
        }]);
        expect(peopleFindMock.mock.calls.length).toBe(1);
        expect(peopleFindMock.mock.calls[0]).toStrictEqual([{ _id: 'personId' }]);
        expect(Invoices.find.mock.calls.length).toBe(1);
        expect(Invoices.find.mock.calls[0]).toStrictEqual([
            {
                orgId: 'orgId',
                voided: { $ne: true },
                personId: { $in: ['childId1', 'childId2'] },
                lineItems: { $elemMatch: { 'originalItem._id': 'feeId' } }
            }
        ]);
    });

    it('totalRegistrationFees calculates correctly', async () => {
        const org = {
            _id: 'orgId',
            billing: {
                regFeeConfig: {
                    enabled: true,
                    feeId: 'feeId',
                    perChild: false,
                    enableMaxPerFamily: false,
                    maxPerFamily: 1
                },
            }
        };
        const orgFindOneMock = Orgs.findOneAsync;
        orgFindOneMock.mockImplementation(() => org);
        const fee = 11;
        const data1 = {
            children: [
                { _id: 'child1' }
            ],
            plans: [
                [
                    {
                        details: { timePeriod: 'abc'},
                        regFeeExempt: false,
                    },
                    {
                        details: { timePeriod: 'cde'},
                        regFeeExempt: true,
                    }
                ]
                ]
        };
        expect(await RegistrationUtils.totalRegistrationFees(data1, fee, false, null, 'orgId')).toStrictEqual({ fee: 11, timePeriodIds: ['abc']});
        const data2 = {
            children: [
                { _id: 'child1' }
            ],
            plans: [
                [
                    {
                        details: { timePeriod: 'abc'},
                        regFeeExempt: false,
                    },
                    {
                        details: { timePeriod: 'cde'},
                        regFeeExempt: false,
                    }
                ]
                ]
        };
        expect(await RegistrationUtils.totalRegistrationFees(data2, fee, false, null, 'orgId')).toStrictEqual({fee: 22, timePeriodIds: ['abc', 'cde']});
        const data3 = {
            children: [
                { _id: 'child1' },
                { _id: 'child2' }
            ],
            plans: [
                [
                    {
                        details: {timePeriod: 'abc'},
                        regFeeExempt: false,
                        enrollmentDate: '01/01/2023',
                    },
                    {
                        details: {timePeriod: 'cde'},
                        enrollmentDate: '01/01/2023',
                        regFeeExempt: false,
                    }
                ],
                [
                    {
                        details: {timePeriod: 'abc'},
                        regFeeExempt: false,
                    },
                    {
                        details: {timePeriod: 'cde'},
                        regFeeExempt: false,
                    }
                ]
            ]
        }
        const peopleFindMock = People.findOneAsync;
        peopleFindMock.mockImplementation(() => ({
            _id: 'personId',
            findInheritedRelationshipsAsArray: jest.fn(() => ({
                mapAsync: jest.fn(() => [
                    'childId1'
                    ,'childId2' 
                ]),
            }))
        }));

        const invoicesMock = Invoices;
        // const invoicesCursorMock = invoicesMock.cursorMock;
        // const invoicesFindMock = invoicesMock.findMock;
        invoicesMock.find.mockImplementation(() => {
            return ({fetchAsync : jest.fn().mockImplementation(()=>([
                    {
                        _id: 'iId1',
                        personId: 'childId1',
                        lineItems: [{ originalItem: { _id: 'feeId' }, timePeriodIds: ['abc', 'cde'] }]
                    },
                ]))
            })
        });

        expect(await RegistrationUtils.totalRegistrationFees(data3, fee, false, 'personId', 'orgId')).toStrictEqual({fee: 0, timePeriodIds: []});
        const data4 = {
            children: [
                {_id: 'child1'},
                {_id: 'child2'}
            ],
            plans: [
                [
                    {
                        details: {timePeriod: 'abc'},
                        regFeeExempt: false,
                        enrollmentDate: '01/01/2023',
                    },
                    {
                        details: {timePeriod: 'cde'},
                        enrollmentDate: '01/01/2023',
                        regFeeExempt: false,
                    }
                ],
                [
                    {
                        details: {timePeriod: 'abc'},
                        regFeeExempt: false,
                    },
                    {
                        regFeeExempt: false,
                    }
                ]
            ]
        };
        expect(await RegistrationUtils.totalRegistrationFees(data4, fee, false, 'personId', 'orgId')).toStrictEqual({fee: 11, timePeriodIds: []});
    });
    it('singleChildRegistrationFee calculates correctly', () => {
        const fee = 12;
        const plans1 = [
            [
                {
                    details: { timePeriod: 'abc'},
                    regFeeExempt: false,
                },
                {
                    details: { timePeriod: 'cde'},
                    regFeeExempt: true,
                }
            ]
        ];
        expect(RegistrationUtils.singleChildRegistrationFee(plans1, 0, fee)).toStrictEqual({ fee: 12, timePeriodIds: ['abc'] });
        const plans2 = [
            [
                {
                    details: { timePeriod: 'abc'},
                    regFeeExempt: false,
                },
                {
                    details: { timePeriod: 'cde'},
                    regFeeExempt: false,
                }
            ]
        ];
        expect(RegistrationUtils.singleChildRegistrationFee(plans2, 0, fee)).toStrictEqual({ fee: 24, timePeriodIds: ['abc', 'cde'] });
        const plans3 = [
            [
                {
                    details: { timePeriod: 'abc'},
                    regFeeExempt: false,
                    enrollmentDate: '01/01/2023',
                },
                {
                    details: { timePeriod: 'cde'},
                    enrollmentDate: '01/01/2023',
                    regFeeExempt: false,
                }
            ],
            [
                {
                    details: { timePeriod: 'abc'},
                    regFeeExempt: false,
                },
                {
                    details: { timePeriod: 'cde'},
                    regFeeExempt: false,
                }
            ]
        ];
        expect(RegistrationUtils.singleChildRegistrationFee(plans3, 0, fee)).toStrictEqual({ fee: 0, timePeriodIds: [] });
        expect(RegistrationUtils.singleChildRegistrationFee(plans3, 1, fee)).toStrictEqual({ fee: 0, timePeriodIds: [] });
        const plans4 = [
            [
                {
                    details: { timePeriod: 'abc'},
                    regFeeExempt: false,
                    enrollmentDate: '01/01/2023',
                },
                {
                    details: { timePeriod: 'cde'},
                    enrollmentDate: '01/01/2023',
                    regFeeExempt: false,
                }
            ],
            [
                {
                    details: { timePeriod: 'abc'},
                    regFeeExempt: false,
                },
                {
                    regFeeExempt: false,
                }
            ]
        ];
        expect(RegistrationUtils.singleChildRegistrationFee(plans4, 0, fee)).toStrictEqual({ fee: 0, timePeriodIds: [] });
        expect(RegistrationUtils.singleChildRegistrationFee(plans4, 1, fee)).toStrictEqual({ fee: 12, timePeriodIds: [expect.any(String)] });
    });
    describe('add allocation coupon discount to plans', () => {
        let plans
        let morePlans
        let children
        const firstChild = 0
        const firstPlan = 0
        const secondPlan = 1
        const thirdPlan = 2
        beforeEach(() => {
            plans = [[
                {
                    "_id": "1",
                    "description": "First Plan",
                    "type": "plan",
                    "category": "tuition",
                    "amount": 50,
                    "bundlePlanId": "UIO",
                    "allocations": [
                        {
                            "allocationType": "discount",
                            "amount": 25,
                            "amountType": "dollars",
                            "discountType": "bundle",
                            "allocationDescription": "Bundle: Another Random Plan and Open Plan",
                            "discountAmount": 25
                        }
                    ],
                    "planTotal": 0
                },
                {
                    "_id": "2",
                    "description": "Second Plan",
                    "type": "plan",
                    "category": "tuition",
                    "amount": 100,
                    "bundlePlanId": "UIO",
                    "allocations": [
                        {
                            "allocationType": "discount",
                            "amount": 25,
                            "amountType": "dollars",
                            "discountType": "bundle",
                            "allocationDescription": "Bundle: Another Random Plan and Open Plan",
                            "discountAmount": 25
                        }
                    ],
                    "planTotal": 25
                },
                {
                    "_id": "4",
                    "description": "Non-Bundled plan",
                    "amount": 150,
                    "type": "plan",
                    "allocations": [],
                    "planTotal": 150,
                    "regularPrice": 110
                }
            ]]
            morePlans = [[
                {
                    "_id": "3",
                    "description": "Third plan",
                    "amount": 50,
                    "type": "plan",
                    "allocations": [
                        {
                            "allocationType": "discount",
                            "amount": -40,
                            "amountType": "dollars",
                            "discountType": "bundle",
                            "allocationDescription": "Bundle: Third plan and Fourth plan"
                        }
                    ],
                    "planTotal": 50,
                    "bundlePlanId": "WER",
                    "regularPrice": 110
                },
                {
                    "_id": "4",
                    "description": "Fourth plan",
                    "amount": 150,
                    "type": "plan",
                    "allocations": [
                        {
                            "allocationType": "discount",
                            "amount": -40,
                            "amountType": "dollars",
                            "discountType": "bundle",
                            "allocationDescription": "Bundle: Third plan and Fourth plan"
                        }
                    ],
                    "planTotal": 150,
                    "bundlePlanId": "WER",
                    "regularPrice": 110
                },
                {
                    "_id": "4",
                    "description": "Fifth plan",
                    "amount": 150,
                    "type": "plan",
                    "allocations": [],
                    "planTotal": 150,
                    "regularPrice": 110
                }
            ]]
            children = [
                {
                    "firstName": "Younger",
                    "lastName": "Child",
                    "birthday": "08/02/2023",
                    "studentGrade": "Kindergarten",
                    "gender": "Female",
                    "registrationConditionals": {
                        "yes": [],
                        "no": []
                    }
                },
                {
                    "firstName": "Older",
                    "lastName": "Child",
                    "birthday": "08/01/2023",
                    "studentGrade": "Kindergarten",
                    "gender": "Female",
                    "registrationConditionals": {
                        "yes": [],
                        "no": []
                    }
                }
            ]
        });

        let coupon = {
            "_id": "e8gRRzife8ot4wsHP",
            "code": "THIS IS A COUPON CODE",
            "description": "",
            "amountType": "dollars",
            "amount": 100,
            "regStartDate": 1690430400000,
            "usedWithOtherCoupons": false,
            "usedWithDiscounts": false,
            "useCouponInBundles": "yes-all"
        };
        const allocation = {
            "id": "FmfdkC9cd8SJz9Gbk",
            "allocationType": "discount",
            "amount": 100,
            "amountType": "dollars",
            "discountType": "coupon",
            "code": "THIS IS A COUPON CODE",
            "allocationDescription": "Discount: Coupon Code THIS IS A COUPON CODE"
        };

        describe('fallback to youngest child tests', () => {
            const PLAN_TYPE = 'plan';
            let testChildren;
            let baseAllocation;
            let baseCoupon;

            beforeEach(() => {
                testChildren = [
                    {
                        "firstName": "Younger",
                        "lastName": "Child",
                        "birthday": "08/02/2023",
                        "index": 0,
                        "registrationConditionals": {
                            "yes": [],
                            "no": []
                        }
                    },
                    {
                        "firstName": "Older",
                        "lastName": "Child",
                        "birthday": "08/01/2023",
                        "index": 1,
                        "registrationConditionals": {
                            "yes": [],
                            "no": []
                        }
                    }
                ];

                baseAllocation = {
                    ...allocation,
                    isSingleUse: true,
                    used: false,
                    allocationType: "discount",
                    discountType: "coupon",
                    code: "THIS IS A COUPON CODE"
                };

                baseCoupon = {
                    ...coupon,
                    isSingleInstallmentCoupon: true,
                    usedWithDiscounts: false,
                    billingPlans: ["1", "2"],
                    code: "THIS IS A COUPON CODE"
                };
            });

            it('should apply to youngest child when no children qualify for initial application', () => {
                const plansWithDiscounts = [
                    [ // First child (youngest)
                        {
                            "_id": "1",
                            "type": PLAN_TYPE,
                            "description": "First Plan",
                            "amount": 50,
                            "allocations": [
                                {
                                    "allocationType": "discount",
                                    "discountType": "otherDiscount"
                                }
                            ]
                        }
                    ],
                    [ // Second child (older)
                        {
                            "_id": "2",
                            "type": PLAN_TYPE,
                            "description": "Second Plan",
                            "amount": 100,
                            "allocations": [
                                {
                                    "allocationType": "discount",
                                    "discountType": "otherDiscount"
                                }
                            ]
                        }
                    ]
                ];

                RegistrationUtils.addAllocationCouponDiscount(testChildren, plansWithDiscounts, baseCoupon, baseAllocation);

                expect(plansWithDiscounts[0][0].allocations).toHaveLength(2);
                expect(plansWithDiscounts[0][0].allocations.some(a => a.code === "THIS IS A COUPON CODE")).toBe(true);
            });

            it('should skip plans with enrollment date for youngest child', () => {
                const plansWithEnrollment = [
                    [ // First child (youngest)
                        {
                            "_id": "1",
                            "type": PLAN_TYPE,
                            "description": "First Plan",
                            "amount": 50,
                            "enrollmentDate": "2024-01-01",
                            "allocations": [
                                {
                                    "allocationType": "discount",
                                    "discountType": "otherDiscount"
                                }
                            ]
                        },
                        {
                            "_id": "2",
                            "type": PLAN_TYPE,
                            "description": "Second Plan",
                            "amount": 100,
                            "allocations": [
                                {
                                    "allocationType": "discount",
                                    "discountType": "otherDiscount"
                                }
                            ]
                        }
                    ]
                ];

                RegistrationUtils.addAllocationCouponDiscount(testChildren, plansWithEnrollment, baseCoupon, baseAllocation);

                // First plan should be skipped due to enrollment date
                expect(plansWithEnrollment[0][0].allocations).toHaveLength(1);
                // Second plan should get the coupon
                expect(plansWithEnrollment[0][1].allocations).toHaveLength(2);
                expect(plansWithEnrollment[0][1].allocations.some(a => a.code === "THIS IS A COUPON CODE")).toBe(true);
            });

            it('should respect billing plans and one-time charges restrictions for youngest child', () => {
                const plansWithRestrictedTypes = [
                    [ // First child (youngest)
                        {
                            "_id": "1",
                            "type": "item",  // Different type, but should still be considered if in oneTimeCharges
                            "description": "First Plan",
                            "amount": 50,
                            "allocations": [
                                {
                                    "allocationType": "discount",
                                    "discountType": "otherDiscount"
                                }
                            ]
                        }
                    ]
                ];

                const restrictedCoupon = {
                    ...baseCoupon,
                    oneTimeCharges: ["1"]  // Allow item type through oneTimeCharges
                };

                RegistrationUtils.addAllocationCouponDiscount(testChildren, plansWithRestrictedTypes, restrictedCoupon, baseAllocation);

                // Plan should get coupon because it's in oneTimeCharges, regardless of type
                expect(plansWithRestrictedTypes[0][0].allocations).toHaveLength(2);
                expect(plansWithRestrictedTypes[0][0].allocations.some(a => a.code === "THIS IS A COUPON CODE")).toBe(true);
            });

            it('should apply to non-plan types if they pass billing/one-time charge restrictions', () => {
                const plansWithDifferentTypes = [
                    [ // First child (youngest)
                        {
                            "_id": "1",
                            "type": "punchCard",
                            "description": "First Plan",
                            "amount": 50,
                            "allocations": [
                                {
                                    "allocationType": "discount",
                                    "discountType": "otherDiscount"
                                }
                            ]
                        }
                    ]
                ];

                const mixedCoupon = {
                    ...baseCoupon,
                    oneTimeCharges: ["1"]  // Include punch card in one-time charges
                };

                RegistrationUtils.addAllocationCouponDiscount(testChildren, plansWithDifferentTypes, mixedCoupon, baseAllocation);

                // Plan should get coupon because it's in oneTimeCharges
                expect(plansWithDifferentTypes[0][0].allocations).toHaveLength(2);
                expect(plansWithDifferentTypes[0][0].allocations.some(a => a.code === "THIS IS A COUPON CODE")).toBe(true);
            });
        });

        it('should handle empty or invalid inputs', () => {
            const plan = { _id: "1" };

            // Test undefined/null cases
            expect(RegistrationUtils.isPlanNotInCouponsBillingPlansOrOneTimeCharges(null, null)).toBe(false);
            expect(RegistrationUtils.isPlanNotInCouponsBillingPlansOrOneTimeCharges(plan, null)).toBe(false);
            expect(RegistrationUtils.isPlanNotInCouponsBillingPlansOrOneTimeCharges(plan, {})).toBe(false);
            expect(RegistrationUtils.isPlanNotInCouponsBillingPlansOrOneTimeCharges(plan, { billingPlans: null })).toBe(false);
            expect(RegistrationUtils.isPlanNotInCouponsBillingPlansOrOneTimeCharges(plan, { oneTimeCharges: null })).toBe(false);
            expect(RegistrationUtils.isPlanNotInCouponsBillingPlansOrOneTimeCharges(plan, { billingPlans: [] })).toBe(false);
            expect(RegistrationUtils.isPlanNotInCouponsBillingPlansOrOneTimeCharges(plan, { oneTimeCharges: [] })).toBe(false);
        });

        it('should return true only when plan is not in non-empty restriction lists', () => {
            const plan = { _id: "1" };

            // Should return true - plan not in either list
            expect(RegistrationUtils.isPlanNotInCouponsBillingPlansOrOneTimeCharges(
              plan,
              { billingPlans: ["2", "3"], oneTimeCharges: ["4", "5"] }
            )).toBe(true);

            // Should return false - plan in billingPlans
            expect(RegistrationUtils.isPlanNotInCouponsBillingPlansOrOneTimeCharges(
              plan,
              { billingPlans: ["1", "2"], oneTimeCharges: ["3", "4"] }
            )).toBe(false);

            // Should return false - plan in oneTimeCharges
            expect(RegistrationUtils.isPlanNotInCouponsBillingPlansOrOneTimeCharges(
              plan,
              { billingPlans: ["2", "3"], oneTimeCharges: ["1", "4"] }
            )).toBe(false);
        });

        it('should handle mixed empty and populated lists', () => {
            const plan = { _id: "1" };

            // Empty billingPlans, populated oneTimeCharges
            expect(RegistrationUtils.isPlanNotInCouponsBillingPlansOrOneTimeCharges(
              plan,
              { billingPlans: [], oneTimeCharges: ["2", "3"] }
            )).toBe(false);

            // Populated billingPlans, empty oneTimeCharges
            expect(RegistrationUtils.isPlanNotInCouponsBillingPlansOrOneTimeCharges(
              plan,
              { billingPlans: ["2", "3"], oneTimeCharges: [] }
            )).toBe(false);

            // Undefined billingPlans, populated oneTimeCharges
            expect(RegistrationUtils.isPlanNotInCouponsBillingPlansOrOneTimeCharges(
              plan,
              { oneTimeCharges: ["2", "3"] }
            )).toBe(false);

            // Populated billingPlans, undefined oneTimeCharges
            expect(RegistrationUtils.isPlanNotInCouponsBillingPlansOrOneTimeCharges(
              plan,
              { billingPlans: ["2", "3"] }
            )).toBe(false);
        });

        it('all bundled plans get coupon', () => {
            RegistrationUtils.addAllocationCouponDiscount(children, plans, coupon, allocation)
            expect(plans[firstChild][firstPlan].allocations).toHaveLength(2)
            expect(plans[firstChild][secondPlan].allocations).toHaveLength(2)
            expect(plans[firstChild][thirdPlan].allocations).toHaveLength(1) // Non-bundled plan gets coupon
        });

        it('most expensive bundled plan gets coupon', () => {
            let coupon_most = { ...coupon, "useCouponInBundles": "yes-most" }
            RegistrationUtils.addAllocationCouponDiscount(children, plans, coupon_most, allocation)
            expect(plans[firstChild][firstPlan].allocations).toHaveLength(1)
            expect(plans[firstChild][secondPlan].allocations).toHaveLength(2)
            expect(plans[firstChild][thirdPlan].allocations).toHaveLength(1) // Non-bundled plan gets coupon
        });

        it('least expensive bundled plan gets coupon', () => {
            let coupon_least = { ...coupon, "useCouponInBundles": "yes-least" }
            RegistrationUtils.addAllocationCouponDiscount(children, plans, coupon_least, allocation)
            expect(plans[firstChild][firstPlan].allocations).toHaveLength(2)
            expect(plans[firstChild][secondPlan].allocations).toHaveLength(1)
            expect(plans[firstChild][thirdPlan].allocations).toHaveLength(1) // Non-bundled plan gets coupon
        });

        it('no bundled plan gets coupon', () => {
            let coupon_none = { ...coupon, "useCouponInBundles": "none" }
            RegistrationUtils.addAllocationCouponDiscount(children, plans, coupon_none, allocation)
            expect(plans[firstChild][firstPlan].allocations).toHaveLength(1)
            expect(plans[firstChild][secondPlan].allocations).toHaveLength(1)
            expect(plans[firstChild][thirdPlan].allocations).toHaveLength(1) // Non-bundled plan gets coupon
        });

        it('update plans with new plans', () => {
            let coupon_most_update = { ...coupon, "useCouponInBundles": "yes-most" }
            RegistrationUtils.addAllocationCouponDiscount(children, morePlans, coupon_most_update, allocation, true)
            expect(morePlans[firstChild][firstPlan].allocations).toHaveLength(1)
            expect(morePlans[firstChild][secondPlan].allocations).toHaveLength(2)
            expect(morePlans[firstChild][thirdPlan].allocations).toHaveLength(1) // Non-bundled plan gets coupon
        });
        it('should apply coupon to punch card plan when included in oneTimeCharges', () => {
            const allocation = {
                id: "allocation1",
                allocationType: "discount",
                amount: 100,
                amountType: "dollars",
                discountType: "coupon",
                code: "TEST_COUPON"
            };

            const plan = {
                _id: "punch1",
                type: "punchCard",
                allocations: []
            };

            const coupon = {
                oneTimeCharges: ["punch1"],
                code: "TEST_COUPON"
            };

            RegistrationUtils.addCouponAllocationToPlanOrItem(plan, [], coupon, allocation);
            expect(plan.allocations).toHaveLength(1);
            expect(plan.allocations[0]).toEqual(allocation);
        });

        it('should not apply coupon to punch card plan when not included in oneTimeCharges', () => {
            const allocation = {
                id: "allocation1",
                allocationType: "discount",
                amount: 100,
                amountType: "dollars",
                discountType: "coupon",
                code: "TEST_COUPON"
            };

            const plan = {
                _id: "punch1",
                type: PUNCH_CARD_TYPE,
                allocations: []
            };

            const coupon = {
                oneTimeCharges: ["differentId"],
                code: "TEST_COUPON"
            };

            RegistrationUtils.addCouponAllocationToPlanOrItem(plan, [], coupon, allocation);
            expect(plan.allocations).toHaveLength(0);
        });

        it('should handle both item and punch card plans with oneTimeCharges', () => {
            const allocation = {
                id: "allocation1",
                allocationType: "discount",
                amount: 100,
                amountType: "dollars",
                discountType: "coupon",
                code: "TEST_COUPON"
            };

            const punchCardPlan = {
                _id: "punch1",
                type: "punchCard",
                allocations: []
            };

            const itemPlan = {
                _id: "item1",
                type: "item",
                allocations: []
            };

            const coupon = {
                oneTimeCharges: ["punch1", "item1"],
                code: "TEST_COUPON"
            };

            RegistrationUtils.addCouponAllocationToPlanOrItem(punchCardPlan, [], coupon, allocation);
            RegistrationUtils.addCouponAllocationToPlanOrItem(itemPlan, [], coupon, allocation);

            expect(punchCardPlan.allocations).toHaveLength(1);
            expect(itemPlan.allocations).toHaveLength(1);
            expect(punchCardPlan.allocations[0]).toEqual(allocation);
            expect(itemPlan.allocations[0]).toEqual(allocation);
        });
    });
    describe('getAvailableSelectiveWeeks', () => {
        const timezone = 'America/Chicago';
        it('returns an empty array if plan is not selective week plan', () => {
            const plan = { details: {} };
            const enrolledPlans = [];

            const result = RegistrationUtils.getAvailableSelectiveWeeks(plan, enrolledPlans, timezone);

            expect(result).toEqual([]);
        });

        it('returns available unmatched weeks after today for selective week plan', () => {
            const plan = {
                _id: 'plan1Id',
                details: {
                    selectiveWeeks: [
                        ['01/01/2024', '01/05/2024'],
                        ['01/08/2024', '01/12/2024'],
                        ['01/15/2024', '01/19/2024']
                    ]
                },
            };

            // Mock current date to be 01/10/2024
            jest.spyOn(moment, 'tz').mockReturnValueOnce(moment.tz('01/10/2024', 'MM/DD/YYYY', timezone));

            const result = RegistrationUtils.getAvailableSelectiveWeeks(plan, [], timezone);
            expect(result).toStrictEqual([['01/15/2024', '01/19/2024']]);

            moment.tz.mockRestore();
        });

        it('returns available unmatched weeks after today for selective week plan when there are existing plans', () => {
            const plan = {
                _id: 'plan1Id',
                details: {
                    selectiveWeeks: [
                        ['01/01/2024', '01/05/2024'],
                        ['01/08/2024', '01/12/2024'],
                        ['01/15/2024', '01/19/2024']
                    ]
                },
            };

            const enrolledPlans = [
                { _id: 'plan1Id', enrollmentDateFormatted: '01/01/2024' },
            ];

            // Mock current date to be 12/20/2023
            jest.spyOn(moment, 'tz').mockReturnValueOnce(moment.tz('12/20/2023', 'MM/DD/YYYY', timezone));

            let result = RegistrationUtils.getAvailableSelectiveWeeks(plan, enrolledPlans, timezone);
            expect(result).toStrictEqual([['01/08/2024', '01/12/2024'], ['01/15/2024', '01/19/2024']]);

            // Mock current date to be 12/20/2024
            jest.spyOn(moment, 'tz').mockReturnValueOnce(moment.tz('12/20/2024', 'MM/DD/YYYY', timezone));

            result = RegistrationUtils.getAvailableSelectiveWeeks(plan, enrolledPlans, timezone);
            expect(result).toStrictEqual([]);

            moment.tz.mockRestore();
        });
    });
    describe('totalRegistrationFees', () => {
        const formattedCurrencySpy = jest.spyOn(MiscUtils, 'formatCurrency');
        const org = {
            _id: 'orgId',
            billing: {
                regFeeConfig: {
                    enabled: true,
                    feeId: 'feeId',
                    perChild: false,
                    enableMaxPerFamily: false,
                    maxPerFamily: 1
                    },
                }
            }
        let data = {
            children: [
                {}
            ],
            plans: [
                [
                    {
                        "_id": "AxWDPgc53oNmjWLYt",
                        "description": "Other Scaled Weekly Spring 2024",
                        "type": "plan",
                        "program": "jbzmAcwwyEcGsYFKa",
                        "frequency": "scaledWeekly",
                        "category": "tuition",
                        "amount": 45,
                        "scaledAmounts": [
                            25,
                            30,
                            35,
                            40,
                            45
                        ],
                        "expires": *************,
                        "ledgerAccountName": "741266",
                        "details": {
                            "startTime": "5:30 pm",
                            "endTime": "7:00 pm",
                            "regStartDate": *************,
                            "regEndDate": *************,
                            "scheduleType": "5ATwY9i6nPmKP8Fac",
                            "dateType": "timePeriod",
                            "timePeriod": "J4DxrJ8GR9RxyvWFr"
                        },
                        "serviceDates": {
                            "response": "03/25/2024-06/21/2024",
                            "startDate": "03/25/2024",
                            "endDate": "06/21/2024"
                        },
                        "selectedDays": [
                            "monday",
                            "tuesday",
                            "wednesday",
                            "thursday",
                            "friday"
                        ],
                        "allocations": [
                            {
                                "allocationType": "discount",
                                "amount": 14.5,
                                "amountType": "dollars",
                                "discountType": "bundle",
                                "allocationDescription": "Bundle: Scaled Weekly Spring 2024 and Other Scaled Weekly Spring 2024",
                                "discountAmount": 14.5
                            }
                        ],
                        "serviceStartDate": 1711342800000,
                        "serviceEndDate": 1718946000000,
                        "bundlePlanId": "ktHuvc6We9qE2vuJ3",
                        "bundlePlanPrice": 166,
                        "regularPrice": 195,
                        "startDate": "03/25/2024",
                        "planTotal": 30.5
                    }
                ]
            ]
        }

        afterEach(() => {
            jest.clearAllMocks();
        });

        it('returns zero fee and empty timePeriodIds when plans are not provided', async () => {
            const result = await RegistrationUtils.totalRegistrationFees({ plans: null });

            expect(result.fee).toBe('$0.00');
            expect(result.timePeriodIds).toEqual([]);
            expect(formattedCurrencySpy).toHaveBeenCalledWith(0);
        });
        it('returns zero fee and empty timePeriodIds when plans array is empty', async () => {
            const result = await RegistrationUtils.totalRegistrationFees({ plans: [] });
            const orgFindOneMock = Orgs.findOneAsync;
            orgFindOneMock.mockImplementation(() => org);
            expect(result.fee).toBe("$0.00");
            expect(result.timePeriodIds).toEqual([]);
            expect(formattedCurrencySpy).toHaveBeenCalled();
        });
        it('returns zero fee and empty timePeriodIds when registrationFee is zero', async () => {
            const result = await  RegistrationUtils.totalRegistrationFees(data, 0, true, null, 'orgId');
            const orgFindOneMock = Orgs.findOneAsync;
            orgFindOneMock.mockImplementation(() => org);
            expect(result.fee).toBe(0);
            expect(result.timePeriodIds).toEqual(['J4DxrJ8GR9RxyvWFr']);
            expect(formattedCurrencySpy).not.toHaveBeenCalled();
        });
        it('calls formatCurrency with correct arguments when fee is not zero', async () => {
            const registrationFee = 5000;
            const orgFindOneMock = Orgs.findOneAsync;
            orgFindOneMock.mockImplementation(() => org);
            let result = await RegistrationUtils.totalRegistrationFees(data, registrationFee, true, null, 'orgId');

            expect(result.fee).toBe('$5,000.00');
            expect(result.timePeriodIds).toEqual(['J4DxrJ8GR9RxyvWFr']);
            expect(formattedCurrencySpy).toHaveBeenCalledWith(5000);

            result = await RegistrationUtils.totalRegistrationFees(data, 25.2, true, null, 'orgId');

            expect(result.fee).toBe('$25.20');
        });
        it('does not call formatCurrency with correct arguments when fee is not zero', async () => {
            const registrationFee = 50;
            const orgFindOneMock = Orgs.findOneAsync;
            orgFindOneMock.mockImplementation(() => org);
            const result = await RegistrationUtils.totalRegistrationFees(data, registrationFee, false, null, 'orgId');

            expect(result.fee).toBe(50);
            expect(result.timePeriodIds).toEqual(['J4DxrJ8GR9RxyvWFr']);
            expect(formattedCurrencySpy).not.toHaveBeenCalled();
        });
        it('checks invoices for other children in the family to get the list of time periods that have already been registered for', async () => {
            const registrationFee = 50;

            const peopleFindMock = People.findOneAsync;
            const orgFindOneMock = Orgs.findOneAsync;
            orgFindOneMock.mockImplementationOnce(() => org);
            peopleFindMock.mockImplementationOnce(() => ({
                _id: 'personId',
                findInheritedRelationshipsAsArray: jest.fn(() => ({
                    mapAsync: jest.fn(() => [
                        'childId1' 
                        ,'childId2'
                    ]),
                })),
            }));

            Invoices.find.mockImplementation(() =>{
                return ({
                    fetchAsync: jest.fn().mockImplementation(()=>[
                        {
                            _id: 'iId1',
                            personId: 'childId1',
                            lineItems: [{ originalItem: { _id: 'feeId' }, timePeriodIds: ['J4DxrJ8GR9RxyvWFr'] }]
                        },
                        { _id: 'iId2', personId: 'childId2', lineItems: [{ originalItem: { _id: 'feeId' }, timePeriodIds: ['otherTPId'] }] }
                    ])
                })
            });

            const result = await RegistrationUtils.totalRegistrationFees(data, registrationFee, false, 'personId', 'orgId');

            expect(result.fee).toBe(0);
            expect(result.timePeriodIds).toEqual([]);
            expect(peopleFindMock.mock.calls.length).toBe(1);
            expect(peopleFindMock.mock.calls[0]).toStrictEqual([{ _id: 'personId' }]);
            expect(Invoices.find.mock.calls.length).toBe(1);
            expect(Invoices.find.mock.calls[0]).toStrictEqual([
                {
                    orgId: 'orgId',
                    voided: { $ne: true },
                    personId: { $in: ['childId1', 'childId2'] },
                    lineItems: { $elemMatch: { 'originalItem._id': 'feeId' } }
                }
            ]);
        });
        it('returns correct registration fee when family and org are provided but no invoices found', async () => {
            const registrationFee = 50;

            const orgFindOneMock = Orgs.findOneAsync;
            orgFindOneMock.mockImplementationOnce(() => org);

            const peopleFindMock = People.findOneAsync;
            peopleFindMock.mockImplementationOnce(() => ({
                _id: 'personId',
                findInheritedRelationshipsAsArray: jest.fn(() => ({
                    mapAsync: jest.fn(() => [
                        'childId1' 
                        ,'childId2' 
                    ]),
                }))
            }));

            Invoices.find.mockImplementationOnce(() => {
                return ({
                    fetchAsync:jest.fn().mockImplementation(()=>[])
                })
            });

            const result = await RegistrationUtils.totalRegistrationFees(data, registrationFee, false, 'personId', 'orgId');

            expect(result.fee).toBe(50);
            expect(result.timePeriodIds).toEqual(['J4DxrJ8GR9RxyvWFr']);
            expect(peopleFindMock.mock.calls.length).toBe(1);
            expect(peopleFindMock.mock.calls[0]).toStrictEqual([{ _id: 'personId' }]);
            expect(Invoices.find.mock.calls.length).toBe(1);
            expect(Invoices.find.mock.calls[0]).toStrictEqual([
                {
                    orgId: 'orgId',
                    voided: { $ne: true },
                    personId: { $in: ['childId1', 'childId2'] },
                    lineItems: { $elemMatch: { 'originalItem._id': 'feeId' } }
                }
            ]);
        });
        it('returns correct registration fee when perChild is true, no invoices found', async () => {
            const registrationFee = 50;

            const regData = {
                children: [
                    {_id: 'childId1'},
                    {_id: 'childId2'}
                ],
                plans: [
                    [
                        {_id: 'plan1', details: {timePeriod: 'timePeriod1'}, regFeeExempt: false},
                        {_id: 'plan2', details: {timePeriod: 'timePeriod2'}, regFeeExempt: false}
                    ],
                    [
                        {_id: 'plan3', details: {timePeriod: 'timePeriod1'}, regFeeExempt: false},
                        {_id: 'plan4', details: {timePeriod: 'timePeriod2'}, regFeeExempt: false}
                    ]
                ]
            };

            let updatedOrg = {
                ...org,
                billing: {
                    regFeeConfig: {
                        ...org.billing.regFeeConfig,
                        perChild: true
                    }
                }
            };

            const orgFindOneMock = Orgs.findOneAsync;
            orgFindOneMock.mockImplementationOnce(() => updatedOrg);

            const peopleFindMock = People.findOneAsync;
            peopleFindMock.mockImplementationOnce(() => ({
                _id: 'personId',
                findInheritedRelationshipsAsArray: jest.fn(() => ({
                    mapAsync: jest.fn(() => ['childId1','childId2']),
                }))
            }));

            const invoicesMock = Invoices;
            invoicesMock.find.mockImplementationOnce(() => {
                return ({
                    fetchAsync:jest.fn().mockImplementation(()=>[])
                })
            });

            const result = await RegistrationUtils.totalRegistrationFees(regData, registrationFee, false, 'personId', 'orgId');
            // perChild = true. 2 children, 2 time periods, 50 fee per child per time period = 200.
            expect(result.fee).toBe(200);
            expect(result.timePeriodIds).toEqual(['timePeriod1', 'timePeriod2']);
        });
        it('returns correct registration fee when perChild is true, invoices found', async () => {
            const registrationFee = 50;

            const regData = {
                children: [
                    {_id: 'childId1'},
                    {_id: 'childId2'}
                ],
                plans: [
                    [
                        {_id: 'plan1', details: {timePeriod: 'timePeriod1'}, regFeeExempt: false},
                        {_id: 'plan2', details: {timePeriod: 'timePeriod2'}, regFeeExempt: false}
                    ],
                    [
                        {_id: 'plan3', details: {timePeriod: 'timePeriod1'}, regFeeExempt: false},
                        {_id: 'plan4', details: {timePeriod: 'timePeriod2'}, regFeeExempt: false}
                    ]
                ]
            };

            const updatedOrg = {
                ...org,
                billing: {
                    regFeeConfig: {
                        ...org.billing.regFeeConfig,
                        perChild: true
                    }
                }
            };

            const orgFindOneMock = Orgs.findOneAsync;
            orgFindOneMock.mockImplementationOnce(() => updatedOrg);

            const peopleFindMock = People.findOneAsync;
            peopleFindMock.mockImplementationOnce(() => ({
                _id: 'personId',
                findInheritedRelationshipsAsArray: jest.fn(() => ({
                    mapAsync: jest.fn(() => [
                        'childId1' 
                        ,'childId2'
                    ]),
                }))
            }));

            const invoicesMock = Invoices;
            const invoicesCursorMock = invoicesMock.cursorMock;
            const invoicesFindMock = invoicesMock.findMock;
            invoicesMock.find.mockImplementationOnce(() => {
                return ({
                    fetchAsync:jest.fn().mockImplementation(()=>[
                        {
                            _id: 'iId1',
                            personId: 'childId1',
                            lineItems: [{ originalItem: { _id: 'feeId' }, timePeriodIds: ['timePeriod1'] }]
                        },
                        {
                            _id: 'iId2',
                            personId: 'childId2',
                            lineItems: [{ originalItem: { _id: 'feeId' }, timePeriodIds: ['timePeriod2'] }]
                        }
                    ])
                })
            });

            const result = await RegistrationUtils.totalRegistrationFees(regData, registrationFee, false, 'personId', 'orgId');
            // perChild = true. 2 children, 2 time periods, but each child has already paid for 1 time period, 50 fee per child per time period they have not paid = 100.
            expect(result.fee).toBe(100);
            expect(result.timePeriodIds).toEqual(['timePeriod2', 'timePeriod1']);
        });
        it('returns correct registration fee when enableMaxPerFamily is true, no invoices found', async () => {
            const regData = {
                children: [
                    {_id: 'childId1'},
                    {_id: 'childId2'}
                ],
                plans: [
                    [
                        {_id: 'plan1', details: {timePeriod: 'timePeriod1'}, regFeeExempt: false},
                        {_id: 'plan2', details: {timePeriod: 'timePeriod2'}, regFeeExempt: false}
                    ],
                    [
                        {_id: 'plan3', details: {timePeriod: 'timePeriod1'}, regFeeExempt: false},
                        {_id: 'plan4', details: {timePeriod: 'timePeriod2'}, regFeeExempt: false}
                    ]
                ]
            };
            const registrationFee = 50;
            const updatedOrg = {
                ...org,
                billing: {
                    regFeeConfig: {
                        ...org.billing.regFeeConfig,
                        perChild: true,
                        enableMaxPerFamily: true,
                        maxPerFamily: 1
                    }
                }
            };

            const orgFindOneMock = Orgs.findOneAsync;
            orgFindOneMock.mockImplementationOnce(() => updatedOrg);

            const peopleFindMock = People.findOneAsync;
            peopleFindMock.mockImplementationOnce(() => ({
                _id: 'personId',
                findInheritedRelationshipsAsArray: jest.fn(() => ({
                    mapAsync: jest.fn(() => [
                        'childId1' 
                        ,'childId2'
                    ]),
                }))
            }));

            const invoicesMock = Invoices;
            const invoicesCursorMock = invoicesMock.cursorMock;
            const invoicesFindMock = invoicesMock.findMock;
            invoicesMock.find.mockImplementationOnce(() => {
                return ({
                    fetchAsync : jest.fn().mockImplementation(()=> [])
                })
            });

            const result = await RegistrationUtils.totalRegistrationFees(regData, registrationFee, false, 'personId', 'orgId');
            // perChild = true. 2 children, 2 time periods, maxPerFamily of 1 per time period; 50 fee max per family for 2 time periods = 100.
            expect(result.fee).toBe(100);
            expect(result.timePeriodIds).toEqual(['timePeriod1','timePeriod2']);
        });
        it('returns correct registration fee when enableMaxPerFamily is true, invoices found', async () => {
            const regData = {
                children: [
                    {_id: 'childId1'},
                    {_id: 'childId2'}
                ],
                plans: [
                    [
                        {_id: 'plan1', details: {timePeriod: 'timePeriod1'}, regFeeExempt: false},
                        {_id: 'plan2', details: {timePeriod: 'timePeriod2'}, regFeeExempt: false}
                    ],
                    [
                        {_id: 'plan3', details: {timePeriod: 'timePeriod1'}, regFeeExempt: false},
                        {_id: 'plan4', details: {timePeriod: 'timePeriod2'}, regFeeExempt: false}
                    ]
                ]
            };
            const registrationFee = 50;
            const updatedOrg = {
                ...org,
                billing: {
                    regFeeConfig: {
                        ...org.billing.regFeeConfig,
                        perChild: true,
                        enableMaxPerFamily: true,
                        maxPerFamily: 1
                    }
                }
            };

            const orgFindOneMock = Orgs.findOneAsync;
            orgFindOneMock.mockImplementationOnce(() => updatedOrg);

            const peopleFindMock = People.findOneAsync;
            peopleFindMock.mockImplementationOnce(() => ({
                _id: 'personId',
                findInheritedRelationshipsAsArray: jest.fn(() => ({
                    mapAsync: jest.fn(() => [
                        'childId1' 
                        ,'childId2'
                    ]),
                }))
            }));

            const invoicesMock = Invoices;
            const invoicesCursorMock = invoicesMock.cursorMock;
            const invoicesFindMock = invoicesMock.findMock;
            invoicesMock.find.mockImplementationOnce(() => {
                return ({
                    fetchAsync : jest.fn().mockImplementation(()=> [
                        {
                            _id: 'iId1',
                            personId: 'childId1',
                            lineItems: [{ originalItem: { _id: 'feeId' }, timePeriodIds: ['timePeriod1'] }]
                        }
                    ])
                })
            });

            const result = await RegistrationUtils.totalRegistrationFees(regData, registrationFee, false, 'personId', 'orgId');
            // perChild = true. 2 children, 2 time periods, maxPerFamily of 1 per time period; 50 fee max per family for 2 time periods, but one has already been paid for = 50.
            expect(result.fee).toBe(50);
            expect(result.timePeriodIds).toEqual(['timePeriod2']);
        });
        it('returns correct registration fee when maxPerFamily is > 1, no invoices found', async () => {
            const regData = {
                children: [
                    {_id: 'childId1'},
                    {_id: 'childId2'},
                    {_id: 'childId3'},
                    {_id: 'childId4'}
                ],
                plans: [
                    [
                        {_id: 'plan1', details: {timePeriod: 'timePeriod1'}, regFeeExempt: false},
                        {_id: 'plan2', details: {timePeriod: 'timePeriod2'}, regFeeExempt: false}
                    ],
                    [
                        {_id: 'plan3', details: {timePeriod: 'timePeriod1'}, regFeeExempt: false},
                        {_id: 'plan4', details: {timePeriod: 'timePeriod2'}, regFeeExempt: false}
                    ],
                    [
                        {_id: 'plan5', details: {timePeriod: 'timePeriod1'}, regFeeExempt: false},
                        {_id: 'plan6', details: {timePeriod: 'timePeriod2'}, regFeeExempt: false}
                    ],
[
                        {_id: 'plan7', details: {timePeriod: 'timePeriod3'}, regFeeExempt: false},
                        {_id: 'plan8', details: {timePeriod: 'timePeriod3'}, regFeeExempt: false}
                    ]
                ]
            };
            const registrationFee = 50;
            const updatedOrg = {
                ...org,
                billing: {
                    regFeeConfig: {
                        ...org.billing.regFeeConfig,
                        perChild: true,
                        enableMaxPerFamily: true,
                        maxPerFamily: 2
                    }
                }
            };

            const orgFindOneMock = Orgs.findOneAsync;
            orgFindOneMock.mockImplementationOnce(() => updatedOrg);

            const peopleFindMock = People.findOneAsync;
            peopleFindMock.mockImplementationOnce(() => ({
                _id: 'personId',
                findInheritedRelationshipsAsArray: jest.fn(() => ({
                    mapAsync: jest.fn(() => [
                        'childId1' 
                        ,'childId2' 
                    ]),
                }))
            }));

            const invoicesMock = Invoices;
            invoicesMock.find.mockImplementationOnce(() => {
                return ({
                    fetchAsync : jest.fn().mockImplementation(()=> [])
                })
            });

            const result = await RegistrationUtils.totalRegistrationFees(regData, registrationFee, false, 'personId', 'orgId');
            // child 1 has 2 plans; 2 time periods = 100
            // child 2 has 2 plans; 2 time periods = 100
            // child 3 has 2 plans; 2 time periods, but exceeds maxPerFamily = 0
            // child 4 has 2 plans with same time period = 50
            expect(result.fee).toBe(250);
            expect(result.timePeriodIds).toEqual(['timePeriod1', 'timePeriod2', 'timePeriod3']);
        });
        it('returns correct registration fee when maxPerFamily is > 1, invoices found', async () => {
            const regData = {
                children: [
                    {_id: 'childId1'},
                    {_id: 'childId2'},
                    {_id: 'childId3'},
                    {_id: 'childId4'}
                ],
                plans: [
                    [
                        {_id: 'plan1', details: {timePeriod: 'timePeriod1'}, regFeeExempt: false},
                        {_id: 'plan2', details: {timePeriod: 'timePeriod2'}, regFeeExempt: false}
                    ],
                    [
                        {_id: 'plan3', details: {timePeriod: 'timePeriod1'}, regFeeExempt: false},
                        {_id: 'plan4', details: {timePeriod: 'timePeriod2'}, regFeeExempt: false}
                    ],
                    [
                        {_id: 'plan5', details: {timePeriod: 'timePeriod1'}, regFeeExempt: false},
                        {_id: 'plan6', details: {timePeriod: 'timePeriod2'}, regFeeExempt: false}
                    ],
                    [
                        {_id: 'plan7', details: {timePeriod: 'timePeriod3'}, regFeeExempt: false},
                        {_id: 'plan8', details: {timePeriod: 'timePeriod3'}, regFeeExempt: false}
                    ]
                ]
            };
            const registrationFee = 50;
            const updatedOrg = {
                ...org,
                billing: {
                    regFeeConfig: {
                        ...org.billing.regFeeConfig,
                        perChild: true,
                        enableMaxPerFamily: true,
                        maxPerFamily: 2
                    }
                }
            };

            const orgFindOneMock = Orgs.findOneAsync;
            orgFindOneMock.mockImplementationOnce(() => updatedOrg);

            const peopleFindMock = People.findOneAsync;
            peopleFindMock.mockImplementationOnce(() => ({
                _id: 'personId',
                findInheritedRelationshipsAsArray: jest.fn(() => ({
                    mapAsync: jest.fn(() => [
                        'childId1' 
                        ,'childId2' 
                    ]),
                }))
            }));

            const invoicesMock = Invoices;
            const invoicesCursorMock = invoicesMock.cursorMock;
            const invoicesFindMock = invoicesMock.findMock;
            invoicesMock.find.mockImplementationOnce(() => {
                return ({
                    fetchAsync : jest.fn().mockImplementation(()=> [
                        {
                            _id: 'iId1',
                            personId: 'childId5',
                            lineItems: [{ originalItem: { _id: 'feeId' }, timePeriodIds: ['timePeriod2'] }]
                        },
                        {
                            _id: 'iId2',
                            personId: 'childId1',
                            lineItems: [{ originalItem: { _id: 'feeId' }, timePeriodIds: ['timePeriod3'] }]
                        }
                    ])
                })
            });

            const result = await RegistrationUtils.totalRegistrationFees(regData, registrationFee, false, 'personId', 'orgId');
            // child 1 has 2 plans; 2 time periods = 100
            // child 2 has 2 plans; 2 time periods, 1 time period is at maxPerFamily = 50
            // child 3 has 2 plans; 2 time periods, but exceeds maxPerFamily = 0
            // child 4 has 2 plans with same time period = 50
            expect(result.fee).toBe(200);
            expect(result.timePeriodIds).toEqual(["timePeriod1","timePeriod2","timePeriod3"]);
        });
        it('returns correct registration fee with regFeeExempt plans', async () => {
            const regData = {
                children: [
                    {_id: 'childId1'}
                ],
                plans: [
                    [
                        {_id: 'plan1', details: {timePeriod: 'timePeriod1'}, regFeeExempt: true},
                        {_id: 'plan2', details: {timePeriod: 'timePeriod2'}, regFeeExempt: false}
                    ]
                ]
            };
            const registrationFee = 50;
            const updatedOrg = {
                ...org,
                billing: {
                    regFeeConfig: {
                        ...org.billing.regFeeConfig,
                        perChild: true
                    }
                }
            };

            const orgFindOneMock = Orgs.findOneAsync;
            orgFindOneMock.mockImplementationOnce(() => updatedOrg);

            const peopleFindMock = People.findOneAsync;
            peopleFindMock.mockImplementationOnce(() => ({
                _id: 'personId',
                findInheritedRelationshipsAsArray: jest.fn(() => ({
                    mapAsync: jest.fn(() => [
                        'childId1' 
                    ]),
                }))
            }));

            const invoicesMock = Invoices;
            const invoicesCursorMock = invoicesMock.cursorMock;
            const invoicesFindMock = invoicesMock.findMock;
            invoicesMock.find.mockImplementationOnce(() => {
                return ({
                    fetchAsync : jest.fn().mockImplementation(()=> [])
                })
            });

            const result = await RegistrationUtils.totalRegistrationFees(regData, registrationFee, false, 'personId', 'orgId');
            // regFeeExempt = true for one plan; only one plan should be charged = 50.
            expect(result.fee).toBe(50);
            expect(result.timePeriodIds).toEqual(['timePeriod2']);
        });
        it('returns correct registration fee when no children are provided', async () => {
            const regData = {
                plans: [
                    [
                        {_id: 'plan1', details: {timePeriod: 'timePeriod1'}, regFeeExempt: false},
                        {_id: 'plan2', details: {timePeriod: 'timePeriod2'}, regFeeExempt: false}
                    ]
                ]
            };
            const registrationFee = 50;
            const updatedOrg = {
                ...org,
                billing: {
                    regFeeConfig: {
                        ...org.billing.regFeeConfig,
                        perChild: true
                    }
                }
            };

            const orgFindOneMock = Orgs.findOneAsync;
            orgFindOneMock.mockImplementationOnce(() => updatedOrg);

            const peopleFindMock = People.findOneAsync;
            peopleFindMock.mockImplementationOnce(() => ({
                _id: 'personId',
                findInheritedRelationshipsAsArray: jest.fn(() => ({
                    mapAsync: jest.fn(() => [
                        'childId1' 
                    ]),
                }))
            }));

            const invoicesMock = Invoices;
            invoicesMock.find.mockImplementationOnce(() => {
                return ({
                    fetchAsync : jest.fn().mockImplementation(()=> [])
                })
            });

            const result = await RegistrationUtils.totalRegistrationFees(regData, registrationFee, false, 'personId', 'orgId');
            // No children array provided, should still assess fees based on plans.
            expect(result.fee).toBe(100);
            expect(result.timePeriodIds).toEqual(['timePeriod1', 'timePeriod2']);
        });
        it('returns correct registration fee for children without ids', async () => {
            const regData = {
                children: [
                    {},
                    {_id: 'childId1'}
                ],
                plans: [
                    [
                        {_id: 'plan1', details: {timePeriod: 'timePeriod1'}, regFeeExempt: false},
                        {_id: 'plan2', details: {timePeriod: 'timePeriod2'}, regFeeExempt: false}
                    ],
                    [
                        {_id: 'plan3', details: {timePeriod: 'timePeriod1'}, regFeeExempt: false},
                        {_id: 'plan4', details: {timePeriod: 'timePeriod2'}, regFeeExempt: false}
                    ]
                ]
            };
            const registrationFee = 50;
            const updatedOrg = {
                ...org,
                billing: {
                    regFeeConfig: {
                        ...org.billing.regFeeConfig,
                        perChild: true
                    }
                }
            };

            const orgFindOneMock = Orgs.findOneAsync;
            orgFindOneMock.mockImplementationOnce(() => updatedOrg);

            const peopleFindMock = People.findOneAsync;
            peopleFindMock.mockImplementationOnce(() => ({
                _id: 'personId',
                findInheritedRelationshipsAsArray: jest.fn(() => ({
                    mapAsync: jest.fn(() => [
                        'childId1' 
                    ]),
                }))
            }));

            const invoicesMock = Invoices;
            const invoicesCursorMock = invoicesMock.cursorMock;
            const invoicesFindMock = invoicesMock.findMock;
            invoicesMock.find.mockImplementationOnce(() => {
                return ({
                    fetchAsync : jest.fn().mockImplementation(()=> [])
                })
            });

            const result = await RegistrationUtils.totalRegistrationFees(regData, registrationFee, false, 'personId', 'orgId');
            // Child without id and child with id, should assess fee for both.
            expect(result.fee).toBe(200);
            expect(result.timePeriodIds).toEqual(['timePeriod1', 'timePeriod2']);
        });
        it('returns zero fee as number when formatted is false', async () => {
            const result = await RegistrationUtils.totalRegistrationFees(null, 0, false);

            expect(result.fee).toBe(0);
            expect(result.timePeriodIds).toEqual([]);
            expect(formattedCurrencySpy).not.toHaveBeenCalled();
        });
        it('returns zero fee when registration fees are disabled', async () => {
            const regData = {
                children: [
                    {_id: 'childId1'}
                ],
                plans: [
                    [
                        {_id: 'plan1', details: {timePeriod: 'timePeriod1'}, regFeeExempt: false},
                        {_id: 'plan2', details: {timePeriod: 'timePeriod2'}, regFeeExempt: false}
                    ]
                ]
            };
            const registrationFee = 50;
            const updatedOrg = {
                ...org,
                billing: {
                    regFeeConfig: {
                        ...org.billing.regFeeConfig,
                        enabled: false
                    }
                }
            };

            const orgFindOneMock = Orgs.findOneAsync;
            orgFindOneMock.mockImplementationOnce(() => updatedOrg);

            const peopleFindMock = People.findOneAsync;
            peopleFindMock.mockImplementationOnce(() => ({
                _id: 'personId',
                findInheritedRelationshipsAsArray: jest.fn(() => ({
                    mapAsync: jest.fn(() => [
                        'childId1'
                    ]),
                }))
            }));

            const invoicesMock = Invoices;
            invoicesMock.find.mockImplementationOnce(() => {
                return ({
                    fetchAsync : jest.fn().mockImplementation(()=> [])
                })
            });

            const result = await RegistrationUtils.totalRegistrationFees(regData, registrationFee, false, 'personId', 'orgId');
            // Registration fees are disabled, should return zero fee.
            expect(result.fee).toBe(0);
            expect(result.timePeriodIds).toEqual([]);
        });
        it('doesnt add timeperiods not currently being registered to the list of timePeriodIds', async () => {
            const regData = {
                children: [
                    {_id: 'childId1'}
                ],
                plans: [
                    [
                        {_id: 'plan1', details: {timePeriod: 'timePeriod1'}, regFeeExempt: false},
                        {_id: 'plan2', details: {timePeriod: 'timePeriod2'}, regFeeExempt: false}
                    ]
                ]
            };
            const registrationFee = 50;
            const updatedOrg = {
                ...org,
                billing: {
                    regFeeConfig: {
                        ...org.billing.regFeeConfig,
                        perChild: true
                    }
                }
            };

            const orgFindOneMock = Orgs.findOneAsync;
            orgFindOneMock.mockImplementationOnce(() => updatedOrg);

            const peopleFindMock = People.findOneAsync;
            peopleFindMock.mockImplementationOnce(() => ({
                _id: 'personId',
                findInheritedRelationshipsAsArray: jest.fn(() => ({
                    mapAsync: jest.fn(() => [
                        'childId1'
                    ]),
                })),
            }));

            Invoices.find.mockImplementationOnce(() => {
                return ({
                    fetchAsync:jest.fn().mockImplementation(()=>[
                        {
                            _id: 'iId1',
                            personId: 'childId1',
                            lineItems: [{ originalItem: { _id: 'feeId' }, timePeriodIds: ['timePeriod5'] }]
                        }
                    ])
                })
            });

            const result = await RegistrationUtils.totalRegistrationFees(regData, registrationFee, false, 'personId', 'orgId');
            // Only timeperiod1 and timeperiod2 are being registered for so we do not want to record timeperiod5 from the invoices.
            expect(result.fee).toBe(100);
            expect(result.timePeriodIds).toEqual(['timePeriod1', 'timePeriod2']);
        });
                
    });
    describe('totalRegistrationFees - Detailed Path', () => {
        let peopleFindMock;
        let orgFindOneMock;
        const formattedCurrencySpy = jest.spyOn(MiscUtils, 'formatCurrency');
        const org = {
            _id: 'orgId',
            billing: {
                regFeeConfig: {
                    enabled: true,
                    feeId: 'feeId',
                    perChild: false,
                    enableMaxPerFamily: false,
                    maxPerFamily: 1
                }
            }
        };
    
        let data = {
            children: [
                {
                    _id: 'childId1'
                }
            ],
            plans: [
                [
                    {
                        _id: "planId1",
                        description: "Other Scaled Weekly Spring 2024",
                        details: {
                            timePeriod: "timePeriod1"
                        },
                        regFeeExempt: false
                    }
                ]
            ]
        };
    
    
        beforeEach(() => {
            orgFindOneMock = Orgs.findOneAsync;
            orgFindOneMock.mockImplementation(() => org);
    
            peopleFindMock = People.findOneAsync;
            peopleFindMock.mockImplementationOnce(() => ({
                _id: 'personId',
                findInheritedRelationshipsAsArray: jest.fn(() => ({
                    mapAsync: jest.fn(() => [
                        'childId1' 
                        ,'childId2' 
                    ]),
                })),
            }));
        });
    
        afterEach(() => {
            jest.clearAllMocks();
        });
        it('returns totalFee and childrenFees with zero fee and empty timePeriodIds when plans are not provided', async () => {
            const result = await RegistrationUtils.totalRegistrationFees(null, 100, true, null, 'orgId', true);
    
            expect(result.totalFee).toBe('$100.00');
            expect(result.childrenFees).toEqual([]);
            expect(formattedCurrencySpy).toHaveBeenCalledWith(100);
        });
        it('returns totalFee and childrenFees with zero fee and empty timePeriodIds when plans array is empty', async () => {
            const result = await RegistrationUtils.totalRegistrationFees({ plans:[] }, 100, true, null, 'orgId', true);
    
            expect(result.totalFee).toBe(0);
            expect(result.childrenFees).toEqual([]);
            expect(formattedCurrencySpy).not.toHaveBeenCalled();
        });
        it('returns totalFee and childrenFees with zero fee and empty timePeriodIds when registrationFee is zero', async () => {
            const result = await RegistrationUtils.totalRegistrationFees(data, 0, false, null, 'orgId', true);
    
            expect(result.totalFee).toBe(0);
            expect(result.childrenFees).toEqual([{
                childIndex: 0,
                fee: 0,
                timePeriodIds: ['timePeriod1']
            }]);
            expect(formattedCurrencySpy).not.toHaveBeenCalled();
        });
        it('calls formatCurrency with correct arguments when fee is not zero', async () => {
            const registrationFee = 5000;
    
            let result = await RegistrationUtils.totalRegistrationFees(data, registrationFee, true, null, 'orgId', true);
    
            expect(result.totalFee).toBe('$5,000.00');
            expect(result.childrenFees).toEqual([{
                childIndex: 0,
                fee: '$5,000.00',
                timePeriodIds: ['timePeriod1']
            }]);
            expect(formattedCurrencySpy).toHaveBeenCalledWith(5000);
    
            result = await RegistrationUtils.totalRegistrationFees(data, 25.2, true, null, 'orgId', true);
    
            expect(result.totalFee).toBe('$25.20');
        });
        it('does not call formatCurrency when formatted is false', async () => {
            const registrationFee = 50;
    
            const result = await RegistrationUtils.totalRegistrationFees(data, registrationFee, false, null, 'orgId', true);
    
            expect(result.totalFee).toBe(50);
            expect(result.childrenFees).toEqual([{
                childIndex: 0,
                fee: 50,
                timePeriodIds: ['timePeriod1']
            }]);
            expect(formattedCurrencySpy).not.toHaveBeenCalled();
        });
        
        it('returns correct registration fee when perChild is true, no invoices found', async () => {
            const registrationFee = 50;
    
            const regData = {
                children: [
                    {_id: 'childId1'},
                    {_id: 'childId2'}
                ],
                plans: [
                    [
                        {_id: 'plan1', details: {timePeriod: 'timePeriod1'}, regFeeExempt: false},
                        {_id: 'plan2', details: {timePeriod: 'timePeriod2'}, regFeeExempt: false}
                    ],
                    [
                        {_id: 'plan3', details: {timePeriod: 'timePeriod1'}, regFeeExempt: false},
                        {_id: 'plan4', details: {timePeriod: 'timePeriod2'}, regFeeExempt: false}
                    ]
                ]
            };
    
            let updatedOrg = {
                ...org,
                billing: {
                    regFeeConfig: {
                        ...org.billing.regFeeConfig,
                        perChild: true
                    }
                }
            };
    
            const orgFindOneMock = Orgs.findOneAsync;
            orgFindOneMock.mockImplementationOnce(() => updatedOrg);
    
    
    
            const invoicesMock = Invoices;
            invoicesMock.find.mockImplementation(() => {
                return ({
                    fetchAsync : jest.fn().mockImplementation(()=> [])
                }) 
            });
    
            const result = await RegistrationUtils.totalRegistrationFees(regData, registrationFee, false, 'personId', 'orgId', true);
            // perChild = true. 2 children, 2 time periods, 50 fee per child per time period = 200.
            expect(result.totalFee).toBe(200);
            expect(result.childrenFees).toEqual([
                { childIndex: 0, fee: 100, timePeriodIds: ['timePeriod1', 'timePeriod2'] },
                { childIndex: 1, fee: 100, timePeriodIds: ['timePeriod1', 'timePeriod2'] }
            ]);
        });
        it('returns correct registration fee when enableMaxPerFamily is true, invoices found', async () => {
            const regData = {
                children: [
                    {_id: 'childId1'},
                    {_id: 'childId2'}
                ],
                plans: [
                    [
                        {_id: 'plan1', details: {timePeriod: 'timePeriod1'}, regFeeExempt: false},
                        {_id: 'plan2', details: {timePeriod: 'timePeriod2'}, regFeeExempt: false}
                    ],
                    [
                        {_id: 'plan3', details: {timePeriod: 'timePeriod1'}, regFeeExempt: false},
                        {_id: 'plan4', details: {timePeriod: 'timePeriod2'}, regFeeExempt: false}
                    ]
                ]
            };
            const registrationFee = 50;
            const updatedOrg = {
                ...org,
                billing: {
                    regFeeConfig: {
                        ...org.billing.regFeeConfig,
                        perChild: true,
                        enableMaxPerFamily: true,
                        maxPerFamily: 1
                    }
                }
            };
    
            const orgFindOneMock = Orgs.findOneAsync;
            orgFindOneMock.mockImplementationOnce(() => updatedOrg);
    
    
    
            const invoicesMock = Invoices;
            const invoicesCursorMock = invoicesMock.cursorMock;
            const invoicesFindMock = invoicesMock.findMock;
            invoicesMock.find.mockImplementation(() => {
                return ({
                    fetchAsync : jest.fn().mockImplementation(()=> [
                        {
                            _id: 'iId1',
                            personId: 'childId1',
                            lineItems: [{ originalItem: { _id: 'feeId' }, timePeriodIds: ['timePeriod1'] }]
                        }
                    ])
                }) 
            });
    
            const result = await RegistrationUtils.totalRegistrationFees(regData, registrationFee, false, 'personId', 'orgId', true);
            // Child 1 has 2 time periods, but one has already been paid for = 50
            // Child 2 has 2 time periods, but both have already hit maxPerFamily of 1. = 0
            expect(result.totalFee).toBe(50);
            expect(result.childrenFees).toEqual([
                { childIndex: 0, fee: 50, timePeriodIds: ['timePeriod2'] },
                { childIndex: 1, fee: 0, timePeriodIds: [] }
            ]);
        });
        it('returns correct registration fee when maxPerFamily is > 1, no invoices found', async () => {
            const regData = {
                children: [
                    {_id: 'childId1'},
                    {_id: 'childId2'},
                    {_id: 'childId3'},
                    {_id: 'childId4'}
                ],
                plans: [
                    [
                        {_id: 'plan1', details: {timePeriod: 'timePeriod1'}, regFeeExempt: false},
                        {_id: 'plan2', details: {timePeriod: 'timePeriod2'}, regFeeExempt: false}
                    ],
                    [
                        {_id: 'plan3', details: {timePeriod: 'timePeriod1'}, regFeeExempt: false},
                        {_id: 'plan4', details: {timePeriod: 'timePeriod2'}, regFeeExempt: false}
                    ],
                    [
                        {_id: 'plan5', details: {timePeriod: 'timePeriod1'}, regFeeExempt: false},
                        {_id: 'plan6', details: {timePeriod: 'timePeriod2'}, regFeeExempt: false}
                    ],
                    [
                        {_id: 'plan7', details: {timePeriod: 'timePeriod3'}, regFeeExempt: false},
                        {_id: 'plan8', details: {timePeriod: 'timePeriod3'}, regFeeExempt: false}
                    ]
                ]
            };
            const registrationFee = 50;
            const updatedOrg = {
                ...org,
                billing: {
                    regFeeConfig: {
                        ...org.billing.regFeeConfig,
                        perChild: true,
                        enableMaxPerFamily: true,
                        maxPerFamily: 2
                    }
                }
            };
    
            const orgFindOneMock = Orgs.findOneAsync;
            orgFindOneMock.mockImplementationOnce(() => updatedOrg);
    
            const invoicesMock = Invoices;
            invoicesMock.find.mockImplementation(() => {
                return ({
                    fetchAsync : jest.fn().mockImplementation(()=> [])
                }) 
            });
    
            const result = await RegistrationUtils.totalRegistrationFees(regData, registrationFee, false, 'personId', 'orgId', true);
            // child 1 has 2 plans; 2 time periods = 100
            // child 2 has 2 plans; 2 time periods = 100
            // child 3 has 2 plans; 2 time periods, but exceeds maxPerFamily = 0
            // child 4 has 2 plans with same time period = 50
            expect(result.totalFee).toBe(250);
            expect(result.childrenFees).toEqual([
                { childIndex: 0, fee: 100, timePeriodIds: ['timePeriod1', 'timePeriod2'] },
                { childIndex: 1, fee: 100, timePeriodIds: ['timePeriod1', 'timePeriod2'] },
                { childIndex: 2, fee: 0, timePeriodIds: [] },
                { childIndex: 3, fee: 50, timePeriodIds: ['timePeriod3'] }
            ]);
        });
        it('returns correct registration fee when maxPerFamily is > 1, invoices found', async () => {
            const regData = {
                children: [
                    {_id: 'childId1'},
                    {_id: 'childId2'},
                    {_id: 'childId3'},
                    {_id: 'childId4'}
                ],
                plans: [
                    [
                        {_id: 'plan1', details: {timePeriod: 'timePeriod1'}, regFeeExempt: false},
                        {_id: 'plan2', details: {timePeriod: 'timePeriod2'}, regFeeExempt: false}
                    ],
                    [
                        {_id: 'plan3', details: {timePeriod: 'timePeriod1'}, regFeeExempt: false},
                        {_id: 'plan4', details: {timePeriod: 'timePeriod2'}, regFeeExempt: false}
                    ],
                    [
                        {_id: 'plan5', details: {timePeriod: 'timePeriod1'}, regFeeExempt: false},
                        {_id: 'plan6', details: {timePeriod: 'timePeriod2'}, regFeeExempt: false}
                    ],
                    [
                        {_id: 'plan7', details: {timePeriod: 'timePeriod3'}, regFeeExempt: false},
                        {_id: 'plan8', details: {timePeriod: 'timePeriod3'}, regFeeExempt: false}
                    ]
                ]
            };
            const registrationFee = 50;
            const updatedOrg = {
                ...org,
                billing: {
                    regFeeConfig: {
                        ...org.billing.regFeeConfig,
                        perChild: true,
                        enableMaxPerFamily: true,
                        maxPerFamily: 2
                    }
                }
            };
    
            const orgFindOneMock = Orgs.findOneAsync;
            orgFindOneMock.mockImplementationOnce(() => updatedOrg);
    
    
    
            const invoicesMock = Invoices;
            invoicesMock.find.mockImplementation(() => {
                return ({
                    fetchAsync : jest.fn().mockImplementation(()=> [
                        {
                            _id: 'iId1',
                            personId: 'childId5',
                            lineItems: [{ originalItem: { _id: 'feeId' }, timePeriodIds: ['timePeriod2'] }]
                        },
                        {
                            _id: 'iId2',
                            personId: 'childId1',
                            lineItems: [{ originalItem: { _id: 'feeId' }, timePeriodIds: ['timePeriod3'] }]
                        }
                    ])
                }) 
            });
    
            const result = await RegistrationUtils.totalRegistrationFees(regData, registrationFee, false, 'personId', 'orgId', true);
            // child 1 has 2 plans; 2 time periods = 100
            // child 2 has 2 plans; 2 time periods, 1 time period is at maxPerFamily = 50
            // child 3 has 2 plans; 2 time periods, but exceeds maxPerFamily = 0
            // child 4 has 2 plans with same time period = 50
            expect(result.totalFee).toBe(200);
            expect(result.childrenFees).toEqual([
                { childIndex: 0, fee: 100, timePeriodIds: ['timePeriod1', 'timePeriod2'] },
                { childIndex: 1, fee: 50, timePeriodIds: ['timePeriod1'] },
                { childIndex: 2, fee: 0, timePeriodIds: [] },
                { childIndex: 3, fee: 50, timePeriodIds: ['timePeriod3'] }
            ]);
        });
        it('doesnt add timeperiods not currently being registered to the list of timePeriodIds', async () => {
            const regData = {
                children: [
                    {_id: 'childId1'}
                ],
                plans: [
                    [
                        {_id: 'plan1', details: {timePeriod: 'timePeriod1'}, regFeeExempt: false},
                        {_id: 'plan2', details: {timePeriod: 'timePeriod2'}, regFeeExempt: false}
                    ]
                ]
            };
            const registrationFee = 50;
            const updatedOrg = {
                ...org,
                billing: {
                    regFeeConfig: {
                        ...org.billing.regFeeConfig,
                        perChild: true
                    }
                }
            };
    
            const orgFindOneMock = Orgs.findOneAsync;
            orgFindOneMock.mockImplementationOnce(() => updatedOrg);
    
            const peopleFindMock = People.findOneAsync;
            peopleFindMock.mockImplementationOnce(() => ({
                _id: 'personId',
                findInheritedRelationshipsAsArray: jest.fn(() => ({
                    mapAsync: jest.fn(() => [
                        'childId1' 
                    ]),
                }))
            }));
    
            const invoicesMock = Invoices;
            const invoicesCursorMock = invoicesMock.cursorMock;
            const invoicesFindMock = invoicesMock.findMock;
            invoicesMock.find.mockImplementationOnce(() => {
                return ({
                    fetchAsync : jest.fn().mockImplementation(()=> [
                        {
                            _id: 'iId1',
                            personId: 'childId1',
                            lineItems: [{ originalItem: { _id: 'feeId' }, timePeriodIds: ['timePeriod5'] }]
                        }
                    ])
                }) 
            });
    
            const result = await RegistrationUtils.totalRegistrationFees(regData, registrationFee, false, 'personId', 'orgId', true);
            // Only timeperiod1 and timeperiod2 are being registered for so we do not want to record timeperiod5 from the invoices.
            expect(result.totalFee).toBe(100);
            expect(result.childrenFees).toEqual([
                { childIndex: 0, fee: 100, timePeriodIds: ['timePeriod1', 'timePeriod2'] }
            ]);
        });
        it('correctly counts multiple invoices with the same time period', async () => {
            const regData = {
                children: [
                    { _id: 'childId1' }
                ],
                plans: [
                    [
                        {_id: 'plan1', details: { timePeriod: 'timePeriod1' }, regFeeExempt: false}
                    ]
                ]
            };
            const registrationFee = 50;
            const updatedOrg = {
                ...org,
                billing: {
                    regFeeConfig: {
                        ...org.billing.regFeeConfig,
                        perChild: true,
                        enableMaxPerFamily: true,
                        maxPerFamily: 2
                    }
                }
            };
    
            const orgFindOneMock = Orgs.findOneAsync;
            orgFindOneMock.mockImplementationOnce(() => updatedOrg);
    
            const peopleFindMock = People.findOneAsync;
            peopleFindMock.mockImplementationOnce(() => ({
                _id: 'personId',
                findInheritedRelationshipsAsArray: jest.fn(() => ({
                    mapAsync: jest.fn(() => [
                        'childId1' 
                    ]),
                }))
            }));
    
            const invoicesMock = Invoices;
            invoicesMock.find.mockImplementation(() => {
                return ({
                    fetchAsync : jest.fn().mockImplementation(()=> [
                        {
                            _id: 'iId1',
                            personId: 'childId2',
                            lineItems: [{ originalItem: { _id: 'feeId' }, timePeriodIds: ['timePeriod1'] }]
                        },
                        {
                            _id: 'iId2',
                            personId: 'childId3',
                            lineItems: [{ originalItem: { _id: 'feeId' }, timePeriodIds: ['timePeriod1'] }]
                        }
                    ])
                }) 
            });
    
            const result = await RegistrationUtils.totalRegistrationFees(regData, registrationFee, false, 'personId', 'orgId', true);
            // Two invoices for the same time period should count towards the family max.
            expect(result.totalFee).toBe(0);
            expect(result.childrenFees).toEqual([
                { childIndex: 0, fee: 0, timePeriodIds: [] }
            ]);
        });
    
    });
    describe('wasCouponApplied', () => {
        test('should return true if the coupon was applied to any of the plans', () => {
            const data = {
                plans: [
                    [
                        { allocations: [{ code: 'DISCOUNT10' }, { code: 'SAVE20' }] },
                        { allocations: [{ code: 'SUMMER20' }] },
                    ],
                    [
                        { allocations: [{ code: 'FALL50' }] },
                    ],
                ],
            };

            const coupon = { code: 'SAVE20' };

            const result = RegistrationUtils.wasCouponApplied(data, coupon);
            expect(result).toBe(true);
        });

        test('should return true if the coupon was applied to the registration fee', () => {
            const data = {
                plans: [
                    [{ allocations: [{ code: 'DISCOUNT10' }] }],
                ],
                registrationFee: {
                    allocations: [{ code: 'REGFEE10' }, { code: 'SAVE20' }],
                },
            };

            const coupon = { code: 'SAVE20' };

            const result = RegistrationUtils.wasCouponApplied(data, coupon);
            expect(result).toBe(true);
        });

        test('should return false if the coupon was not applied to any of the plans or the registration fee', () => {
            const data = {
                plans: [
                    [{ allocations: [{ code: 'DISCOUNT10' }] }],
                ],
                registrationFee: {
                    allocations: [{ code: 'REGFEE10' }],
                },
            };

            const coupon = { code: 'SAVE20' };

            const result = RegistrationUtils.wasCouponApplied(data, coupon);
            expect(result).toBe(false);
        });

        test('should return false if the plans array is empty and the coupon was not applied to the registration fee', () => {
            const data = {
                plans: [],
                registrationFee: {
                    allocations: [{ code: 'REGFEE10' }],
                },
            };

            const coupon = { code: 'SAVE20' };

            const result = RegistrationUtils.wasCouponApplied(data, coupon);
            expect(result).toBe(false);
        });

        test('should return true if the plans array is empty but the coupon was applied to the registration fee', () => {
            const data = {
                plans: [],
                registrationFee: {
                    allocations: [{ code: 'SAVE20' }],
                },
            };

            const coupon = { code: 'SAVE20' };

            const result = RegistrationUtils.wasCouponApplied(data, coupon);
            expect(result).toBe(true);
        });

        test('should return false if there are no allocations in any plan or registration fee', () => {
            const data = {
                plans: [
                    [{ allocations: [] }],
                ],
                registrationFee: {
                    allocations: [],
                },
            };

            const coupon = { code: 'SAVE20' };

            const result = RegistrationUtils.wasCouponApplied(data, coupon);
            expect(result).toBe(false);
        });

        test('should handle undefined allocations gracefully in both plans and registration fee', () => {
            const data = {
                plans: [
                    [{ allocations: undefined }],
                ],
                registrationFee: {
                    allocations: undefined,
                },
            };

            const coupon = { code: 'FALL50' };

            const result = RegistrationUtils.wasCouponApplied(data, coupon);
            expect(result).toBe(false);
        });

        test('should handle missing registrationFee object gracefully', () => {
            const data = {
                plans: [
                    [{ allocations: [{ code: 'DISCOUNT10' }] }],
                ],
            };

            const coupon = { code: 'SAVE20' };

            const result = RegistrationUtils.wasCouponApplied(data, coupon);
            expect(result).toBe(false);
        });
    });
    describe('applyCouponToRegistrationFee', () => {
        test('should apply the coupon to the registration fee when not a single installment coupon and no one-time charges', () => {
            const registrationFee = {
                _id: 'regFee1',
                allocations: []
            };
            const coupon = {
                isSingleInstallmentCoupon: false,
                oneTimeCharges: []
            };
            const allocation = { id: 'alloc1', amount: 100 };

            RegistrationUtils.applyCouponToRegistrationFee(registrationFee, coupon, allocation);

            expect(registrationFee.allocations).toHaveLength(1);
            expect(registrationFee.allocations[0]).toEqual(allocation);
        });

        test('should apply the coupon to the registration fee if it matches one-time charges', () => {
            const registrationFee = {
                _id: 'regFee1',
                allocations: []
            };
            const coupon = {
                isSingleInstallmentCoupon: false,
                oneTimeCharges: ['regFee1']
            };
            const allocation = { id: 'alloc1', amount: 100 };

            RegistrationUtils.applyCouponToRegistrationFee(registrationFee, coupon, allocation);

            expect(registrationFee.allocations).toHaveLength(1);
            expect(registrationFee.allocations[0]).toEqual(allocation);
        });

        test('should not apply the coupon to the registration fee if it does not match one-time charges', () => {
            const registrationFee = {
                _id: 'regFee1',
                allocations: []
            };
            const coupon = {
                isSingleInstallmentCoupon: false,
                oneTimeCharges: ['regFee2']
            };
            const allocation = { id: 'alloc1', amount: 100 };

            RegistrationUtils.applyCouponToRegistrationFee(registrationFee, coupon, allocation);

            expect(registrationFee.allocations).toHaveLength(0);
        });

        test('should not apply the coupon if it is a single installment coupon', () => {
            const registrationFee = {
                _id: 'regFee1',
                allocations: []
            };
            const coupon = {
                isSingleInstallmentCoupon: true
            };
            const allocation = { id: 'alloc1', amount: 100 };

            RegistrationUtils.applyCouponToRegistrationFee(registrationFee, coupon, allocation);

            expect(registrationFee.allocations).toHaveLength(0);
        });

        test('should initialize allocations array if it does not exist', () => {
            const registrationFee = {
                _id: 'regFee1'
            };
            const coupon = {
                isSingleInstallmentCoupon: false
            };
            const allocation = { id: 'alloc1', amount: 100 };

            RegistrationUtils.applyCouponToRegistrationFee(registrationFee, coupon, allocation);

            expect(registrationFee.allocations).toHaveLength(1);
            expect(registrationFee.allocations[0]).toEqual(allocation);
        });
    });
    describe('createAllocationObjectFromCoupon', () => {
        beforeEach(() => {
           global.Random = {
               id: jest.fn().mockImplementation(() => `randomId-${Math.random().toString(36).substr(2, 9)}`)
           };
        });

        afterEach(() => {
            delete global.Random;
        });

        test('should create a valid allocation object with all properties', () => {
            const coupon = {
                amount: 50,
                amountType: 'percentage',
                code: 'SAVE50',
                description: '50% off',
                expirationDate: new Date('2025-01-01')
            };

            const allocation = RegistrationUtils.createAllocationObjectFromCoupon(coupon);

            expect(allocation).toHaveProperty('id');
            expect(allocation.allocationType).toBe('discount');
            expect(allocation.amount).toBe(50);
            expect(allocation.amountType).toBe('percentage');
            expect(allocation.discountType).toBe(DiscountTypes.COUPON);
            expect(allocation.code).toBe('SAVE50');
            expect(allocation.allocationDescription).toBe('50% off');
            expect(allocation.discountExpires).toEqual(new Date('2025-01-01'));
        });

        test('should create a valid allocation object without an expiration date', () => {
            const coupon = {
                amount: 50,
                amountType: 'percentage',
                code: 'SAVE50',
                description: '50% off'
            };

            const allocation = RegistrationUtils.createAllocationObjectFromCoupon(coupon);

            expect(allocation).toHaveProperty('id');
            expect(allocation.allocationType).toBe('discount');
            expect(allocation.amount).toBe(50);
            expect(allocation.amountType).toBe('percentage');
            expect(allocation.discountType).toBe(DiscountTypes.COUPON);
            expect(allocation.code).toBe('SAVE50');
            expect(allocation.allocationDescription).toBe('50% off');
            expect(allocation).not.toHaveProperty('discountExpires');
        });

        test('should create a valid allocation object with a default description', () => {
            const coupon = {
                amount: 50,
                amountType: 'percentage',
                code: 'SAVE50'
            };

            const allocation = RegistrationUtils.createAllocationObjectFromCoupon(coupon);

            expect(allocation.allocationDescription).toBe('Discount: Coupon Code SAVE50');
        });

        test('should generate a unique id for the allocation', () => {
            const coupon = {
                amount: 50,
                amountType: 'percentage',
                code: 'SAVE50'
            };

            const allocation1 = RegistrationUtils.createAllocationObjectFromCoupon(coupon);
            const allocation2 = RegistrationUtils.createAllocationObjectFromCoupon(coupon);

            expect(allocation1.id).not.toBe(allocation2.id);
        });
    });
    describe('showSavingsForBundles', () => {
        const currentPlans = require('../../../fixtures/lib/util/registrationUtils/currentPlans.json');
        const bundles = require('../../../fixtures/lib/util/registrationUtils/bundles.json');
        const availablePlans = require('../../../fixtures/lib/util/registrationUtils/availablePlans.json');
        const allChildPlans = require('../../../fixtures/lib/util/registrationUtils/allChildPlans.json');
    
        it('should return the bundle and true for showing savings when all plans are currently selected', () => {
            // Only use currentPlans
            const result = RegistrationUtils.showSavingsForBundles(currentPlans, allChildPlans, bundles, availablePlans);
            expect(result).toEqual({
                bundle: bundles[6],
                showSavings: true
            });
        });
    
        it('should return the bundle and true for showing savings when at least one plan is currently selected', () => {
            // Use only a subset of current plans
            const partialCurrentPlans = [currentPlans[0]];
            const result = RegistrationUtils.showSavingsForBundles(partialCurrentPlans, allChildPlans, bundles, availablePlans);
            expect(result).toEqual({
                bundle: bundles[6],
                showSavings: true
            });
        });
    
        it('should not return the bundle and false for showing savings when no matching bundle exists', () => {
            // Use current plans but with a limited bundle list that doesn't match
            const result = RegistrationUtils.showSavingsForBundles(currentPlans, allChildPlans, [bundles[0]], availablePlans);
            expect(result).toEqual({
                bundle: null,
                showSavings: false
            });
        });
    
        it('should handle empty current plans array', () => {
            const result = RegistrationUtils.showSavingsForBundles([], allChildPlans, bundles, availablePlans);
            expect(result).toEqual({
                bundle: null,
                showSavings: false
            });
        });
    
        it('should return the bundle with the lowest maximum amount when multiple bundles match', () => {
            // Create a scenario with multiple matching bundles
            const customBundles = [
                {
                    _id: 'bundle1',
                    plans: [currentPlans[0]._id, currentPlans[1]._id],
                    scaledAmounts: [[10, 20], [30, 40]] // Lower maximum amount
                },
                {
                    _id: 'bundle2',
                    plans: [currentPlans[0]._id, currentPlans[1]._id],
                    scaledAmounts: [[50, 60], [70, 80]] // Higher maximum amount
                }
            ];
            
            const result = RegistrationUtils.showSavingsForBundles(currentPlans, allChildPlans, customBundles, availablePlans);
            expect(result.bundle).toEqual(customBundles[0]); // Should select bundle1 with lower maximum amount
            expect(result.showSavings).toBe(true);
        });
    
        it('should handle null or undefined inputs gracefully', () => {
            // Mock MiscUtils.twoMatchedPlans to prevent errors
            const originalTwoMatchedPlans = MiscUtils.twoMatchedPlans;
            MiscUtils.twoMatchedPlans = jest.fn().mockReturnValue([]);
            
            expect(RegistrationUtils.showSavingsForBundles(null, allChildPlans, bundles, availablePlans)).toEqual({
                bundle: null,
                showSavings: false
            });
            
            expect(RegistrationUtils.showSavingsForBundles(currentPlans, null, bundles, availablePlans)).toEqual({
                bundle: null,
                showSavings: false
            });
            
            expect(RegistrationUtils.showSavingsForBundles(currentPlans, allChildPlans, null, availablePlans)).toEqual({
                bundle: null,
                showSavings: false
            });
            
            expect(RegistrationUtils.showSavingsForBundles(currentPlans, allChildPlans, bundles, null)).toEqual({
                bundle: null,
                showSavings: false
            });
            
            // Restore the original function
            MiscUtils.twoMatchedPlans = originalTwoMatchedPlans;
        });
    
        it('should show bundle savings when completing a partial bundle', () => {
            // Mock MiscUtils.twoMatchedPlans to return our partial bundle
            const originalTwoMatchedPlans = MiscUtils.twoMatchedPlans;
            MiscUtils.twoMatchedPlans = jest.fn().mockReturnValue([{
                _id: 'partialBundle',
                plans: ['plan1', 'plan2'],
                scaledAmounts: [[10, 20], [30, 40]]
            }]);
            
            // Scenario: Child already has one plan from a bundle enrolled, parent is selecting the second plan
            const enrolledPlan = { _id: 'plan1' };
            const selectedPlan = { _id: 'plan2' };
            const partialBundle = {
                _id: 'partialBundle',
                plans: ['plan1', 'plan2'],
                scaledAmounts: [[10, 20], [30, 40]]
            };
            
            const result = RegistrationUtils.showSavingsForBundles([selectedPlan], [enrolledPlan], [partialBundle], availablePlans);
            
            // Restore the original function
            MiscUtils.twoMatchedPlans = originalTwoMatchedPlans;
            
            expect(result.bundle).toEqual(partialBundle);
            expect(result.showSavings).toBe(true);
        });
    
        it('should not show bundle savings for bundles exclusively in enrolled plans', () => {
            // Scenario: Child already has all plans from a bundle enrolled, parent is selecting unrelated plans
            const enrolledPlans = [{ _id: 'plan1' }, { _id: 'plan2' }];
            const selectedPlan = { _id: 'unrelatedPlan' };
            const enrolledBundle = {
                _id: 'enrolledBundle',
                plans: ['plan1', 'plan2'],
                scaledAmounts: [[10, 20], [30, 40]]
            };
            
            const result = RegistrationUtils.showSavingsForBundles([selectedPlan], enrolledPlans, [enrolledBundle], availablePlans);
            expect(result).toEqual({
                bundle: null,
                showSavings: false
            });
        });
    });
    describe('calculateAndSetBundledPrice', () => {
        it('should calculate and set the bundled price for a valid bundle', () => {
            instance.currentBundle.get.mockReturnValue({
                plans: ['plan1', 'plan2'],
                scaledAmounts: [
                    [1, 2, 3, 4, 5],
                    [2, 3, 4, 5, 6],
                    [3, 4, 5, 6, 7],
                    [4, 5, 6, 7, 8],
                    [5, 6, 7, 8, 9]
                ]
            });
            instance.currentPlans.get.mockReturnValue([{ _id: 'plan1', scaledAmounts: [1, 2, 3, 4, 5] }, { _id: 'plan2', scaledAmounts: [2, 3, 4, 5, 6]  }]);
            RegistrationUtils.getDaysCountForPlan = jest.fn().mockReturnValue(3);

            RegistrationUtils.calculateAndSetBundledPrice(instance, parentSource.INITIAL_REGISTRATION);

            expect(instance.bundledPrice.set).toHaveBeenCalledWith(5);
            expect(instance.bundleSavings.set).toHaveBeenCalledWith(0); // Assuming regular price was 0
        });

        it('should handle empty or undefined bundle gracefully', () => {
            instance.currentBundle.get.mockReturnValue(null);
            RegistrationUtils.calculateAndSetBundledPrice(instance, parentSource.INITIAL_REGISTRATION);
            expect(instance.bundledPrice.set).not.toHaveBeenCalled();
            expect(instance.bundleSavings.set).not.toHaveBeenCalled();
        });
    });
    describe('findExistingPlanDayCount', () => {
        it('should return the count of selected days for a given plan', () => {
            const plans = [{ _id: 'plan1', selectedDays: ['mon', 'tue', 'wed'] }];
            const result = RegistrationUtils.findExistingPlanDayCount(plans, 'plan1');
            expect(result).toBe(3);
        });

        it('should return 0 if the plan is not found or has no selected days', () => {
            const plans = [{ _id: 'plan1', selectedDays: [] }];
            const result = RegistrationUtils.findExistingPlanDayCount(plans, 'plan2');
            expect(result).toBe(0);
        });
    });
    describe('calculateAndSetRegularPrice', () => {
        it('should handle plans with no scaled amounts gracefully', () => {
            instance.currentBundle.get.mockReturnValue({ plans: ['plan1', 'plan2'] });
            const plans = [{ _id: 'plan1', scaledAmounts: [] }];
            RegistrationUtils.calculateAndSetRegularPrice(instance, plans);
            expect(instance.regularPrice.set).toHaveBeenCalledWith(0);
        });

        it('should skip plans that are not part of the current bundle', () => {
            instance.currentBundle.get.mockReturnValue({ plans: ['plan1'] });
            const plans = [{ _id: 'plan2', scaledAmounts: [100, 150, 200] }];
            RegistrationUtils.calculateAndSetRegularPrice(instance, plans);
            expect(instance.regularPrice.set).toHaveBeenCalledWith(0);
        });
    });
    describe('getRegularPriceForBundleSavings', () => {
        it('should calculate regular price for INITIAL_REGISTRATION source', () => {
            instance.currentPlans.get.mockReturnValue([{ _id: 'plan1' }, { _id: 'plan2' }]);
            RegistrationUtils.calculateAndSetRegularPrice = jest.fn();

            RegistrationUtils.getRegularPriceForBundleSavings(instance, parentSource.INITIAL_REGISTRATION);

            expect(RegistrationUtils.calculateAndSetRegularPrice).toHaveBeenCalledWith(instance, [{ _id: 'plan1' }, { _id: 'plan2' }]);
        });

        it('should calculate regular price for ADD_PROGRAM source', () => {
            instance.allChildPlans = [{ _id: 'plan1' }];
            instance.data.selectedPlans.get.mockReturnValue([{ _id: 'plan2' }]);
            RegistrationUtils.calculateAndSetRegularPrice = jest.fn();

            RegistrationUtils.getRegularPriceForBundleSavings(instance, parentSource.ADD_PROGRAM);

            expect(RegistrationUtils.calculateAndSetRegularPrice).toHaveBeenCalledWith(instance, expect.arrayContaining([{ _id: 'plan1' }, { _id: 'plan2' }]));
        });

        it('should handle empty plan lists gracefully', () => {
            instance.allChildPlans = [];
            instance.data.selectedPlans.get.mockReturnValue([]);
            RegistrationUtils.calculateAndSetRegularPrice = jest.fn();

            RegistrationUtils.getRegularPriceForBundleSavings(instance, parentSource.ADD_PROGRAM);

            expect(RegistrationUtils.calculateAndSetRegularPrice).toHaveBeenCalledWith(instance, []);
        });
    });

    describe('formatNumberWithCommas', () => {
        it('should format numbers with commas and two decimal places', () => {
            const result = RegistrationUtils.formatNumberWithCommas(1234567.89);
            expect(result).toBe('1,234,567.89');
        });

        it('should handle small numbers correctly', () => {
            const result = RegistrationUtils.formatNumberWithCommas(10);
            expect(result).toBe('10.00');
        });

        it('should format large numbers with commas', () => {
            const result = RegistrationUtils.formatNumberWithCommas(1000000);
            expect(result).toBe('1,000,000.00');
        });

        it('should handle zero correctly', () => {
            const result = RegistrationUtils.formatNumberWithCommas(0);
            expect(result).toBe('0.00');
        });
    });

    describe('calculateCartTotalsAndDiscountSavings', () => {
        it('should calculate the grand total and total savings for multiple plans', () => {
            const cartPlansList = [
                [{ amount: 100, planTotal: 80 }, { amount: 200, planTotal: 150 }],
                [{ amount: 300, planTotal: 250 }],
            ];
            const result = RegistrationUtils.calculateCartTotalsAndDiscountSavings(cartPlansList, 'new');
            expect(result).toEqual({
                newGrandTotal: '480.00',
                newTotalSavings: '120.00',
                newCartPlansList: cartPlansList
            });
        });

        it('should handle plans with missing planTotal or amount gracefully (current)', () => {
            const cartPlansList = [
                [{ amount: 100, planTotal: 80 }, {}],
                [{ planTotal: 250 }],
            ];
            const result = RegistrationUtils.calculateCartTotalsAndDiscountSavings(cartPlansList, 'current');
            expect(result).toEqual({
                currentGrandTotal: '330.00',
                currentTotalSavings: '20.00',
                currentCartPlansList: cartPlansList
            });
        });

        it('should return 0 for both new grand total and total savings if cartPlansList is empty', () => {
            const cartPlansList = [];
            const result = RegistrationUtils.calculateCartTotalsAndDiscountSavings(cartPlansList, 'new');
            expect(result).toEqual({
                newGrandTotal: '0.00',
                newTotalSavings: '0.00',
                newCartPlansList: cartPlansList
            });
        });

        it('should return 0 savings if no discounts are applied (current)', () => {
            const cartPlansList = [
                [{ amount: 100, planTotal: 100 }, { amount: 200, planTotal: 200 }],
            ];
            const result = RegistrationUtils.calculateCartTotalsAndDiscountSavings(cartPlansList, 'current');
            expect(result).toEqual({
                currentGrandTotal: '300.00',
                currentTotalSavings: '0.00',
                currentCartPlansList: cartPlansList
            });
        });

        it('should format large new totals and savings with commas', () => {
            const cartPlansList = [
                [{ amount: 1000000, planTotal: 800000 }, { amount: 2000000, planTotal: 1500000 }],
            ];
            const result = RegistrationUtils.calculateCartTotalsAndDiscountSavings(cartPlansList, 'new');
            expect(result).toEqual({
                newGrandTotal: '2,300,000.00',  
                newTotalSavings: '700,000.00',  
                newCartPlansList: cartPlansList
            });
        });

        it('should handle a mix of discounted and non-discounted plans correctly (current)', () => {
            const cartPlansList = [
                [{ amount: 100, planTotal: 80 }, { amount: 200, planTotal: 200 }],
                [{ amount: 300, planTotal: 250 }],
            ];
            const result = RegistrationUtils.calculateCartTotalsAndDiscountSavings(cartPlansList, 'current');
            expect(result).toEqual({
                currentGrandTotal: '530.00',
                currentTotalSavings: '70.00',
                currentCartPlansList: cartPlansList
            });
        });

        it('should handle negative savings gracefully for current plans', () => {
            const cartPlansList = [
                [{ amount: 100, planTotal: 120 }],
                [{ amount: 200, planTotal: 180 }],
            ];
            const result = RegistrationUtils.calculateCartTotalsAndDiscountSavings(cartPlansList, 'current');
            expect(result).toEqual({
                currentGrandTotal: '300.00',
                currentTotalSavings: '0.00',
                currentCartPlansList: cartPlansList
            });
        });

        it('should calculate correct new totals and savings for simple plans', () => {
            const cartPlansList = [
                [{ amount: 100, planTotal: 80 }],
                [{ amount: 200, planTotal: 150 }],
            ];
            const result = RegistrationUtils.calculateCartTotalsAndDiscountSavings(cartPlansList, 'new');
            expect(result).toEqual({
                newGrandTotal: '230.00',
                newTotalSavings: '70.00',
                newCartPlansList: cartPlansList
            });
        });

        it('should calculate current totals and savings when all plans have equal amounts and planTotals', () => {
            const cartPlansList = [
                [{ amount: 100, planTotal: 100 }],
                [{ amount: 200, planTotal: 200 }],
            ];
            const result = RegistrationUtils.calculateCartTotalsAndDiscountSavings(cartPlansList, 'current');
            expect(result).toEqual({
                currentGrandTotal: '300.00',
                currentTotalSavings: '0.00',
                currentCartPlansList: cartPlansList
            });
        });

        it('should handle a mix of plans with discounts and plans with no discounts for new plans', () => {
            const cartPlansList = [
                [{ amount: 100, planTotal: 80 }],
                [{ amount: 300, planTotal: 300 }],
            ];
            const result = RegistrationUtils.calculateCartTotalsAndDiscountSavings(cartPlansList, 'new');
            expect(result).toEqual({
                newGrandTotal: '380.00',
                newTotalSavings: '20.00',
                newCartPlansList: cartPlansList
            });
        });

        it('should calculate correct totals when all new plans have discounts', () => {
            const cartPlansList = [
                [{ amount: 500, planTotal: 450 }],
                [{ amount: 200, planTotal: 150 }],
            ];
            const result = RegistrationUtils.calculateCartTotalsAndDiscountSavings(cartPlansList, 'new');
            expect(result).toEqual({
                newGrandTotal: '600.00',
                newTotalSavings: '100.00',
                newCartPlansList: cartPlansList
            });
        });

        it('should calculate correct new totals and savings when no discounts are applied', () => {
            const cartPlansList = [
                [{ amount: 100, planTotal: 100 }],
                [{ amount: 200, planTotal: 200 }],
                [{ amount: 300, planTotal: 300 }],
            ];
            const result = RegistrationUtils.calculateCartTotalsAndDiscountSavings(cartPlansList, 'new');
            expect(result).toEqual({
                newGrandTotal: '600.00',
                newTotalSavings: '0.00',
                newCartPlansList: cartPlansList
            });
        });

        it('should handle multiple new plans for a single child correctly', () => {
            const cartPlansList = [
                [
                    { amount: 150, planTotal: 130 },
                    { amount: 200, planTotal: 180 },
                ],
                [{ amount: 100, planTotal: 90 }],
            ];
            const result = RegistrationUtils.calculateCartTotalsAndDiscountSavings(cartPlansList, 'new');
            expect(result).toEqual({
                newGrandTotal: '400.00',
                newTotalSavings: '50.00',
                newCartPlansList: cartPlansList
            });
        });

        it('should calculate correctly when some new plans have no discounts', () => {
            const cartPlansList = [
                [{ amount: 120, planTotal: 120 }],
                [{ amount: 300, planTotal: 250 }],
                [{ amount: 150, planTotal: 150 }],
            ];
            const result = RegistrationUtils.calculateCartTotalsAndDiscountSavings(cartPlansList, 'new');
            expect(result).toEqual({
                newGrandTotal: '520.00',
                newTotalSavings: '50.00',
                newCartPlansList: cartPlansList
            });
        });

        it('should calculate totals and savings for multiple new children with multiple plans each', () => {
            const cartPlansList = [
                [
                    { amount: 100, planTotal: 80 },
                    { amount: 200, planTotal: 180 }
                ],
                [
                    { amount: 150, planTotal: 140 },
                    { amount: 250, planTotal: 200 }
                ]
            ];
            const result = RegistrationUtils.calculateCartTotalsAndDiscountSavings(cartPlansList, 'new');
            expect(result).toEqual({
                newGrandTotal: '600.00',
                newTotalSavings: '100.00',
                newCartPlansList: cartPlansList
            });
        });

        it('should calculate correctly when one new child has no discounts but the other does', () => {
            const cartPlansList = [
                [
                    { amount: 100, planTotal: 100 },
                    { amount: 200, planTotal: 200 }
                ],
                [
                    { amount: 150, planTotal: 140 },
                    { amount: 250, planTotal: 200 }
                ]
            ];
            const result = RegistrationUtils.calculateCartTotalsAndDiscountSavings(cartPlansList, 'new');
            expect(result).toEqual({
                newGrandTotal: '640.00',
                newTotalSavings: '60.00',
                newCartPlansList: cartPlansList
            });
        });

        it('should handle a mix of full-priced new plans and discounted plans for multiple children', () => {
            const cartPlansList = [
                [
                    { amount: 300, planTotal: 300 },
                    { amount: 400, planTotal: 350 }
                ],
                [
                    { amount: 200, planTotal: 180 },
                    { amount: 150, planTotal: 150 }
                ]
            ];
            const result = RegistrationUtils.calculateCartTotalsAndDiscountSavings(cartPlansList, 'new');
            expect(result).toEqual({
                newGrandTotal: '980.00',
                newTotalSavings: '70.00',
                newCartPlansList: cartPlansList
            });
        });

        it('should calculate correctly when all new children have multiple discounts applied', () => {
            const cartPlansList = [
                [
                    { amount: 500, planTotal: 450 },
                    { amount: 600, planTotal: 550 }
                ],
                [
                    { amount: 300, planTotal: 270 },
                    { amount: 400, planTotal: 350 }
                ]
            ];
            const result = RegistrationUtils.calculateCartTotalsAndDiscountSavings(cartPlansList, 'new');
            expect(result).toEqual({
                newGrandTotal: '1,620.00',
                newTotalSavings: '180.00',
                newCartPlansList: cartPlansList
            });
        });

        it('should calculate correctly when one new child has discounts and the other has none', () => {
            const cartPlansList = [
                [
                    { amount: 500, planTotal: 450 },
                    { amount: 400, planTotal: 400 }
                ],
                [
                    { amount: 300, planTotal: 300 },
                    { amount: 200, planTotal: 200 }
                ]
            ];
            const result = RegistrationUtils.calculateCartTotalsAndDiscountSavings(cartPlansList, 'new');
            expect(result).toEqual({
                newGrandTotal: '1,350.00',
                newTotalSavings: '50.00',
                newCartPlansList: cartPlansList
            });
        });
    });

    describe('getScaledAmountIndexFromDaysCount', () => {
        it('should return -1 if the plan has no scaled amounts', () => {
            const plan = { scaledAmounts: [] };
            const result = RegistrationUtils.getScaledAmountIndexFromDaysCount(3, plan);
            expect(result).toBe(-1);
        });

        it('should return the correct index if the days count is within the scaled amounts length', () => {
            const plan = { scaledAmounts: [100, 200, 300, 400] };
            const result = RegistrationUtils.getScaledAmountIndexFromDaysCount(3, plan);
            expect(result).toBe(2); // Index should be daysCount - 1
        });

        it('should return the last index if the days count exceeds the scaled amounts length', () => {
            const plan = { scaledAmounts: [100, 200, 300, 400] };
            const result = RegistrationUtils.getScaledAmountIndexFromDaysCount(6, plan);
            expect(result).toBe(3); // The last index (length - 1)
        });

        it('should handle days count equal to the scaled amounts length', () => {
            const plan = { scaledAmounts: [100, 200, 300, 400] };
            const result = RegistrationUtils.getScaledAmountIndexFromDaysCount(4, plan);
            expect(result).toBe(3); // Index should be daysCount - 1
        });

        it('should handle a days count of 1 properly', () => {
            const plan = { scaledAmounts: [100, 200, 300, 400] };
            const result = RegistrationUtils.getScaledAmountIndexFromDaysCount(1, plan);
            expect(result).toBe(0); // Index should be 0 for daysCount of 1
        });

        it('should handle cases when days count is 0 or negative gracefully', () => {
            const plan = { scaledAmounts: [100, 200, 300, 400] };
            const result = RegistrationUtils.getScaledAmountIndexFromDaysCount(0, plan);
            expect(result).toBe(0);
        });

        it('should handle cases when days count is undefined', () => {
            const plan = { scaledAmounts: [100, 200, 300, 400] };
            const result = RegistrationUtils.getScaledAmountIndexFromDaysCount(undefined, plan);
            expect(result).toBe(-1); // Invalid daysCount should not match any index
        });
    });

    describe('updateTotalAmountForScaledAmounts', () => {
        let instance;

        beforeEach(() => {
            instance = {
                plansWithScaledAmounts: {
                    get: jest.fn(),
                    set: jest.fn()
                }
            };
        });

        it('should update the amount for a plan with the correct scaled amount based on checked days', () => {
            const plans = [
                {
                    _id: 'plan1',
                    scaledAmounts: [100, 200, 300, 400]
                },
                {
                    _id: 'plan2',
                    scaledAmounts: [150, 250, 350, 450]
                }
            ];
            instance.plansWithScaledAmounts.get.mockReturnValue(plans);

            RegistrationUtils.updateTotalAmountForScaledAmounts(instance, 'plan1', 3);

            expect(instance.plansWithScaledAmounts.set).toHaveBeenCalledWith([
                {
                    _id: 'plan1',
                    scaledAmounts: [100, 200, 300, 400],
                    amount: 300 // Expecting the amount to be updated to the value at index 2 (daysCount - 1)
                },
                {
                    _id: 'plan2',
                    scaledAmounts: [150, 250, 350, 450]
                }
            ]);
        });

        it('should not update the amount if the plan is not found', () => {
            const plans = [
                {
                    _id: 'plan1',
                    scaledAmounts: [100, 200, 300, 400]
                }
            ];
            instance.plansWithScaledAmounts.get.mockReturnValue(plans);

            RegistrationUtils.updateTotalAmountForScaledAmounts(instance, 'nonexistent-plan', 3);

            expect(instance.plansWithScaledAmounts.set).not.toHaveBeenCalled();
        });

        it('should update the amount to the highest available scaled amount if days count exceeds available range', () => {
            const plans = [
                {
                    _id: 'plan1',
                    scaledAmounts: [100, 200, 300, 400]
                }
            ];
            instance.plansWithScaledAmounts.get.mockReturnValue(plans);

            RegistrationUtils.updateTotalAmountForScaledAmounts(instance, 'plan1', 10); // Days count exceeds the available range

            expect(instance.plansWithScaledAmounts.set).toHaveBeenCalledWith([
                {
                    _id: 'plan1',
                    scaledAmounts: [100, 200, 300, 400],
                    amount: 400 // Expecting the amount to be set to the last available value
                }
            ]);
        });

        it('should handle plans with no scaled amounts gracefully', () => {
            const plans = [
                {
                    _id: 'plan1',
                    scaledAmounts: []
                }
            ];
            instance.plansWithScaledAmounts.get.mockReturnValue(plans);

            RegistrationUtils.updateTotalAmountForScaledAmounts(instance, 'plan1', 3);

            expect(instance.plansWithScaledAmounts.set).not.toHaveBeenCalled(); // Expect no update when scaledAmounts is empty
        });

        it('should handle cases where numCheckedDays is 0 correctly', () => {
            const plans = [
                {
                    _id: 'plan1',
                    scaledAmounts: [100, 200, 300, 400]
                }
            ];
            instance.plansWithScaledAmounts.get.mockReturnValue(plans);

            RegistrationUtils.updateTotalAmountForScaledAmounts(instance, 'plan1', 0); // Days count is 0

            expect(instance.plansWithScaledAmounts.set).toHaveBeenCalledWith([
                {
                    _id: 'plan1',
                    scaledAmounts: [100, 200, 300, 400],
                    amount: 100 // Expecting the amount to be set to the first available value (minimum)
                }
            ]);
        });
    });

    describe('getBundleAmount', () => {
        const reservationsMock = Reservations.findOne;
        const mockBundlePlan = {
            plans: ['plan1', 'plan2'],
            scaledAmounts: [
                [100, 150, 200],
                [150, 200, null],
                [200, 250, 300]
            ]
        };

        afterEach(() => {
           jest.clearAllMocks();
        });

        it('should return the correct discounted amount when secondPlan is the first in the bundle', () => {
            const secondPlan = { _id: 'plan1' };
            const numCheckedDays = 2;
            const secondPlanNumCheckedDays = 3;
            const result = RegistrationUtils.getBundleAmount(mockBundlePlan, secondPlan, numCheckedDays, secondPlanNumCheckedDays);
            expect(result).toBe(250); // Amount from scaledAmounts[2][1] because secondPlan is first in the list
        });

        it('should return the correct discounted amount when secondPlan is the second in the bundle', () => {
            const secondPlan = { _id: 'plan2' };
            const numCheckedDays = 2;
            const secondPlanNumCheckedDays = 3;
            const result = RegistrationUtils.getBundleAmount(mockBundlePlan, secondPlan, numCheckedDays, secondPlanNumCheckedDays);
            expect(result).toBe(0); // Amount from scaledAmounts[1][2] because secondPlan is second in the list
        });

        it('should return the discounted amount using reservation details if secondPlanNumCheckedDays is not provided', () => {
            const mockReservation = { recurringDays: ['monday', 'tuesday', 'wednesday'] };
            reservationsMock.mockReturnValue(mockReservation);

            const secondPlan = { _id: 'plan1', reservationId: 'reservation1' };
            const numCheckedDays = 2;
            const result = RegistrationUtils.getBundleAmount(mockBundlePlan, secondPlan, numCheckedDays);
            expect(result).toBe(250); // Amount from scaledAmounts[2][1] based on recurringDays length
        });

        it('should return 0 if secondPlan is not found in the bundle plans', () => {
            const secondPlan = { _id: 'plan3' }; // Plan not in bundle
            const numCheckedDays = 2;
            const secondPlanNumCheckedDays = 3;
            const result = RegistrationUtils.getBundleAmount(mockBundlePlan, secondPlan, numCheckedDays, secondPlanNumCheckedDays);
            expect(result).toBe(0); // Should return 0 if secondPlan is not found in the bundle
        });

        it('should return 0 if no valid values are found in reservation', () => {
            reservationsMock.mockReturnValue(null); // Mock reservation not found

            const secondPlan = { _id: 'plan1', reservationId: 'reservation1' };
            const numCheckedDays = 2;
            const result = RegistrationUtils.getBundleAmount(mockBundlePlan, secondPlan, numCheckedDays);
            expect(result).toBe(0); // No valid data, should return 0
        });

        it('should handle cases where both plans have minimal day counts properly', () => {
            const secondPlan = { _id: 'plan2' };
            const numCheckedDays = 1;
            const secondPlanNumCheckedDays = 1;
            const result = RegistrationUtils.getBundleAmount(mockBundlePlan, secondPlan, numCheckedDays, secondPlanNumCheckedDays);
            expect(result).toBe(100); // Minimal day count from scaledAmounts[0][0]
        });
    });

    describe('validateIndicesAndGetAmount', () => {
        it('should return the correct amount for valid indices', () => {
            const bundlePlan = {
                scaledAmounts: [
                    [10, 20],
                    [30, 40],
                ],
            };
            const amount = RegistrationUtils.validateIndicesAndGetAmount(bundlePlan, 1, 1);
            expect(amount).toBe(40);
        });

        it('should return 0 if first index is out of bounds', () => {
            const bundlePlan = {
                scaledAmounts: [
                    [10, 20],
                    [30, 40],
                ],
            };
            const amount = RegistrationUtils.validateIndicesAndGetAmount(bundlePlan, 2, 0); // Out of bounds
            expect(amount).toBe(0);
        });

        it('should return 0 if second index is out of bounds', () => {
            const bundlePlan = {
                scaledAmounts: [
                    [10, 20],
                    [30, 40],
                ],
            };
            const amount = RegistrationUtils.validateIndicesAndGetAmount(bundlePlan, 0, 2); // Out of bounds
            expect(amount).toBe(0);
        });

        it('should return 0 if scaledAmounts is undefined', () => {
            const bundlePlan = {}; // No scaledAmounts property
            const amount = RegistrationUtils.validateIndicesAndGetAmount(bundlePlan, 0, 0);
            expect(amount).toBe(0);
        });

        it('should return 0 if scaledAmounts is an empty array', () => {
            const bundlePlan = { scaledAmounts: [] }; // Empty array
            const amount = RegistrationUtils.validateIndicesAndGetAmount(bundlePlan, 0, 0);
            expect(amount).toBe(0);
        });
    });
    describe('getPlansExcludingSelectiveWeeks', () => {
        it('should return only plans of type PLAN_TYPE without selective weeks', () => {
            const plansAndItems = [
                { type: PLAN_TYPE, details: { selectiveWeeks: [1, 2, 3] } },
                { type: PLAN_TYPE, details: {} },
                { type: ITEM_TYPE },
                { type: PUNCH_CARD_TYPE }
            ];

            const result = RegistrationUtils.getPlansExcludingSelectiveWeeks(plansAndItems);

            expect(result).toHaveLength(1);
            expect(result).toContainEqual(plansAndItems[1]);
        });

        it('should handle null or undefined input', () => {
            expect(RegistrationUtils.getPlansExcludingSelectiveWeeks(null)).toEqual([]);
            expect(RegistrationUtils.getPlansExcludingSelectiveWeeks(undefined)).toEqual([]);
        });

        it('should handle plans with undefined details', () => {
            const plansAndItems = [
                { type: PLAN_TYPE },
                { type: PLAN_TYPE, details: undefined }
            ];

            const result = RegistrationUtils.getPlansExcludingSelectiveWeeks(plansAndItems);

            expect(result).toHaveLength(2);
            expect(result).toEqual(plansAndItems);
        });
    });
});

describe('labelSelectedWeeks', () => {
    it('prepares registration plans for registration receipt of selected weeks', () => {
        const plans = [
            {
                _id: 'nonSel',
                foo: 'bar',
            },
            {
                _id: 'camp1',
                description: 'Summer Camp',
                selectedWeeks: [1, 3, 10]
            },
            {
                _id: 'camp1',
                description: 'Summer Camp',
                selectedWeeks: [1, 3, 10]
            },
            {
                _id: 'camp1',
                description: 'Summer Camp',
                selectedWeeks: [1, 3, 10]
            },
            {
                _id: 'camp2',
                description: 'Winter Camp',
                selectedWeeks: [0, 2]
            },
            {
                _id: 'camp2',
                description: 'Winter Camp',
                selectedWeeks: [0, 2]
            },
        ]
        const result = RegistrationUtils.labelSelectedWeeks(plans);
        expect(result).toStrictEqual([
            {
                _id: 'nonSel',
                foo: 'bar',
            },
            {
                _id: 'camp1',
                description: 'Summer Camp - Week 2',
                selectedWeeks: [1, 3, 10],
                isSelectedWeeks: true
            },
            {
                _id: 'camp1',
                description: 'Summer Camp - Week 4',
                isSelectedWeeks: true,
                selectedWeeks: [1, 3, 10]
            },
            {
                _id: 'camp1',
                description: 'Summer Camp - Week 11',
                isSelectedWeeks: true,
                selectedWeeks: [1, 3, 10]
            },
            {
                _id: 'camp2',
                description: 'Winter Camp - Week 1',
                isSelectedWeeks: true,
                selectedWeeks: [0, 2]
            },
            {
                _id: 'camp2',
                description: 'Winter Camp - Week 3',
                isSelectedWeeks: true,
                selectedWeeks: [0, 2]
            },
        ]);
    })
});

describe('isSimplePlanType', () => {
    it('should return true for ITEM_TYPE plan', () => {
        const plan = { type: ITEM_TYPE };
        expect(RegistrationUtils.isSimplePlanType(plan)).toBe(true);
    });

    it('should return true for PUNCH_CARD_TYPE plan', () => {
        const plan = { type: PUNCH_CARD_TYPE };
        expect(RegistrationUtils.isSimplePlanType(plan)).toBe(true);
    });

    it('should return false for scaled frequency plan', () => {
        const plan = { frequency: SCALED_WEEKLY_PLAN };
        expect(RegistrationUtils.isSimplePlanType(plan)).toBe(false);
    });
});

describe('getSimplePlanPriceRange', () => {
    let formatCurrencyMock;

    beforeEach(() => {
        formatCurrencyMock = jest.spyOn(MiscUtils, 'formatCurrency').mockImplementation(amount => `$${amount.toFixed(2)}`);
    });

    afterEach(() => {
        jest.restoreAllMocks();
    });

    it('should return the price for a specific week if index is valid', () => {
        const plan = { details: { selectiveWeekAmounts: [50, 75, 100] } };
        const result = RegistrationUtils.getSimplePlanPriceRange(plan, 1);
        expect(result).toBe('$75.00');
    });

    it('should return a single price if all selective week amounts are the same', () => {
        const plan = { details: { selectiveWeekAmounts: [50, 50, 50] } };
        const result = RegistrationUtils.getSimplePlanPriceRange(plan);
        expect(result).toBe('$50.00');
    });

    it('should return a price range if selective week amounts vary', () => {
        const plan = { details: { selectiveWeekAmounts: [50, 75, 100] } };
        const result = RegistrationUtils.getSimplePlanPriceRange(plan);
        expect(result).toBe('$50.00 - $100.00');
    });

    it('should return the plan amount if no selective week amounts are available', () => {
        const plan = { amount: 150 };
        const result = RegistrationUtils.getSimplePlanPriceRange(plan);
        expect(result).toBe('$150.00');
    });
});

describe('getScaledPlanRange', () => {
    let formatCurrencyMock;

    beforeEach(() => {
        formatCurrencyMock = jest.spyOn(MiscUtils, 'formatCurrency').mockImplementation(amount => `$${amount.toFixed(2)}`);
    });

    afterEach(() => {
        jest.restoreAllMocks();
    });

    it('should return the scaled amount range for a plan with valid values', () => {
        const plan = { scaledAmounts: [100, 150, 200, 250, 300, 350, 400] };
        const result = RegistrationUtils.getScaledPlanRange(plan);
        expect(result).toBe('$100.00 - $400.00');
    });

    it('should ignore null values and return the correct range', () => {
        const plan = { scaledAmounts: [null, 150, 200, null, 250, null, 300] };
        const result = RegistrationUtils.getScaledPlanRange(plan);
        expect(result).toBe('$150.00 - $300.00');
    });

    it('should return the plan amount if all scaled amounts are null', () => {
        const plan = { scaledAmounts: [null, null, null], amount: 200 };
        const result = RegistrationUtils.getScaledPlanRange(plan);
        expect(result).toBe('$200.00');
    });
});

describe('getBundlePriceRange', () => {
    let formatCurrencyMock;

    beforeEach(() => {
        formatCurrencyMock = jest.spyOn(MiscUtils, 'formatCurrency').mockImplementation(amount => `$${amount.toFixed(2)}`);
    });

    afterEach(() => {
        jest.restoreAllMocks();
    });

    it('should return the price range for a bundle with valid scaled amounts', () => {
        const bundles = [{
            scaledAmounts: [
                [null, 200, 300],
                [400, null, 500],
                [600, 700, 800]
            ],
            plans: ['plan1', 'plan2']
        }];
        const plan = { _id: 'plan1' };
        const selectedPlanIds = ['plan2'];
        const result = RegistrationUtils.getBundlePriceRange(bundles, plan, selectedPlanIds);
        expect(result).toBe('$200.00 - $800.00');
    });

    it('should return null if no valid scaled amounts are found in the bundle', () => {
        const bundles = [{
            scaledAmounts: [
                [null, null, null],
                [null, null, null]
            ],
            plans: ['plan1', 'plan2']
        }];
        const plan = { _id: 'plan1' };
        const selectedPlanIds = ['plan2'];
        const result = RegistrationUtils.getBundlePriceRange(bundles, plan, selectedPlanIds);
        expect(result).toBeNull();
    });
});

describe('isPlanInBundle', () => {
    it('should return true if the plan is in the bundle and matches another selected plan', () => {
        const bundle = { plans: ['plan1', 'plan2'] };
        const plan = { _id: 'plan1' };
        const selectedPlanIds = ['plan2'];
        expect(RegistrationUtils.isPlanInBundle(bundle, plan, selectedPlanIds)).toBe(true);
    });

    it('should return false if the plan is not in the bundle or no match is found', () => {
        const bundle = { plans: ['plan1', 'plan3'] };
        const plan = { _id: 'plan2' };
        const selectedPlanIds = ['plan3'];
        expect(RegistrationUtils.isPlanInBundle(bundle, plan, selectedPlanIds)).toBe(false);
    });
});

describe('setDefaultSelectedDaysByPlanConfig', () => {
    const DEFAULT_PROGRAM_DAYS = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'];

    it('should return the default program days when no specific configuration is provided', () => {
        const plan = {};
        const result = RegistrationUtils.setDefaultSelectedDaysByPlanConfig(plan);
        expect(result).toEqual(DEFAULT_PROGRAM_DAYS);
    });

    it('should return the programOfferedOn days if they are provided by the plan', () => {
        const plan = { programOfferedOn: ['monday', 'wednesday'] };
        const result = RegistrationUtils.setDefaultSelectedDaysByPlanConfig(plan);
        expect(result).toEqual(['monday', 'wednesday']);
    });

    it('should slice the selected days based on requiredEnrollmentMax when specified', () => {
        const plan = {
            programOfferedOn: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
            requiredEnrollmentMax: 3
        };
        const result = RegistrationUtils.setDefaultSelectedDaysByPlanConfig(plan);
        expect(result).toEqual(['monday', 'tuesday', 'wednesday']);
    });

    it('should handle cases where requiredEnrollmentMax is greater than available days', () => {
        const plan = {
            programOfferedOn: ['monday', 'wednesday'],
            requiredEnrollmentMax: 5
        };
        const result = RegistrationUtils.setDefaultSelectedDaysByPlanConfig(plan);
        expect(result).toEqual(['monday', 'wednesday']);
    });

    it('should fall back to the default days if requiredEnrollmentMax is not provided', () => {
        const plan = { programOfferedOn: ['tuesday', 'thursday'] };
        const result = RegistrationUtils.setDefaultSelectedDaysByPlanConfig(plan);
        expect(result).toEqual(['tuesday', 'thursday']);
    });

    it('should return the default days if plan has no specific restrictions or maximum', () => {
        const plan = { selectedDays: [] };
        const result = RegistrationUtils.setDefaultSelectedDaysByPlanConfig(plan);
        expect(result).toEqual(DEFAULT_PROGRAM_DAYS);
    });
});

describe('doesSessionHavePrimaryContacts', () => {
    beforeEach(() => {
        Session.get.mockClear();
        Session.set.mockClear();
        Session.equals.mockClear();
    });

    it('should return true when there is a primary contact', () => {
        Session.get.mockReturnValue({
            contacts: [
                { primaryCaregiver: 'Yes', name: 'John' },
                { primaryCaregiver: 'No', name: 'Jane' }
            ]
        });
        expect(RegistrationUtils.doesSessionHavePrimaryContacts()).toBe(true);
    });

    it('should return false when there are no primary contacts', () => {
        Session.get.mockReturnValue({
            contacts: [
                { primaryCaregiver: 'No', name: 'John' },
                { primaryCaregiver: 'No', name: 'Jane' }
            ]
        });
        expect(RegistrationUtils.doesSessionHavePrimaryContacts()).toBe(false);
    });

    it('should return undefined when contacts array is empty', () => {
        Session.get.mockReturnValue({
            contacts: []
        });
        expect(RegistrationUtils.doesSessionHavePrimaryContacts()).toBe(false);
    });

    it('should return undefined when registrationFlowData has no contacts property', () => {
        Session.get.mockReturnValue({});
        expect(RegistrationUtils.doesSessionHavePrimaryContacts()).toBe(false);
    });

    it('should handle multiple primary contacts correctly', () => {
        Session.get.mockReturnValue({
            contacts: [
                { primaryCaregiver: 'Yes', name: 'John' },
                { primaryCaregiver: 'Yes', name: 'Jane' }
            ]
        });
        expect(RegistrationUtils.doesSessionHavePrimaryContacts()).toBe(true);
    });
  });

describe('calculateTotalAmountWithSelectiveWeek', () => {
    test('should return 0 if plans is null or not an array', () => {
        expect(RegistrationUtils.calculateTotalAmountWithSelectiveWeek(null)).toBe(0);
        expect(RegistrationUtils.calculateTotalAmountWithSelectiveWeek(undefined)).toBe(0);
        expect(RegistrationUtils.calculateTotalAmountWithSelectiveWeek({})).toBe(0);
        expect(RegistrationUtils.calculateTotalAmountWithSelectiveWeek(123)).toBe(0);
    });

    test('should return 0 if plans is an empty array', () => {
        expect(RegistrationUtils.calculateTotalAmountWithSelectiveWeek([])).toBe(0);
    });

    test('should calculate total amount based on selectiveWeeks if no startDate exists', () => {
        const plans = [
            {
                details: {
                    selectiveWeeks: [
                        { start: '01/27/2025', end: '01/31/2025' },
                        { start: '02/03/2025', end: '02/07/2025' },
                        { start: '02/10/2025', end: '02/14/2025' }
                    ],
                    selectiveWeekAmounts: [50, 100, 150]
                },
                selectedWeeks: [1, 2]
            }
        ];
        expect(RegistrationUtils.calculateTotalAmountWithSelectiveWeek(plans)).toBe(100);
    });

    test('should calculate total amount based on planTotal if startDate exists', () => {
        const plans = [
            {
                startDate: '2025-01-01',
                planTotal: 200
            },
            {
                details: {
                    selectiveWeeks: [{ start: '01/27/2025', end: '01/31/2025' }],
                    selectiveWeekAmounts: [100]
                },
                selectedWeeks: [0]
            }
        ];
        expect(RegistrationUtils.calculateTotalAmountWithSelectiveWeek(plans)).toBe(300);
    });

    test('should handle mixed cases with startDate and selectiveWeeks correctly', () => {
        const plans = [
            {
                startDate: '2025-01-01',
                planTotal: 200
            },
            {
                details: {
                    selectiveWeeks: [
                        { start: '01/27/2025', end: '01/31/2025' },
                        { start: '02/03/2025', end: '02/07/2025' },
                        { start: '02/10/2025', end: '02/14/2025' },
                        { start: '02/17/2025', end: '02/21/2025' },
                        { start: '02/24/2025', end: '02/28/2025' },
                    ],
                    selectiveWeekAmounts: [10, 20, 300, 30, 400]
                },
                selectedWeeks: [2, 4]
            }
        ];
        expect(RegistrationUtils.calculateTotalAmountWithSelectiveWeek(plans)).toBe(500);
    });
});

describe('filterBundleDiscountsWithoutRequiredPlans', () => {
    it('should validate bundle discount for a single plan', () => {
        const plan = {
            allocations: [{
                discountType: 'bundle'
            }],
            bundlePlanId: 'bundle1'
        };
        const plans = [plan];
        const bundles = [{
            _id: 'bundle1',
            plans: ['plan1', 'plan2']
        }];

        RegistrationUtils.filterBundleDiscountsWithoutRequiredPlans(plans, plan, bundles);
        
        expect(plan.bundlePlanId).toBeUndefined();
        expect(plan.allocations).toHaveLength(0);
    });

    it('should keep bundle discount when all plans are selected', () => {
        const plan = {
            allocations: [{
                discountType: 'bundle'
            }],
            bundlePlanId: 'bundle1'
        };
        const plans = [
            { _id: 'plan1' },
            { _id: 'plan2' }
        ];
        const bundles = [{
            _id: 'bundle1',
            plans: ['plan1', 'plan2']
        }];

        RegistrationUtils.filterBundleDiscountsWithoutRequiredPlans(plans, plan, bundles);

        expect(plan.bundlePlanId).toBe('bundle1');
        expect(plan.allocations[0].discountType).toBe('bundle');
    });

    it('should skip validation for plan without bundle allocations', () => {
        const plan = {
            allocations: [{
                discountType: 'other'
            }],
            bundlePlanId: 'bundle1'
        };
        const plans = [{ _id: 'plan1' }];
        const bundles = [{
            _id: 'bundle1',
            plans: ['plan1', 'plan2']
        }];

        RegistrationUtils.filterBundleDiscountsWithoutRequiredPlans(plans, plan, bundles);
        expect(plan.bundlePlanId).toBe('bundle1');
        expect(plan.allocations[0].discountType).toBe('other');
    });

    it('should handle non-existent bundle', () => {
        const plan = {
            allocations: [{
                discountType: 'bundle'
            }],
            bundlePlanId: 'nonexistent'
        };
        const plans = [{ _id: 'plan1' }];
        const bundles = [{
            _id: 'bundle1',
            plans: ['plan1', 'plan2']
        }];

        RegistrationUtils.filterBundleDiscountsWithoutRequiredPlans(plans, plan, bundles);
        expect(plan.bundlePlanId).toBeUndefined();
        expect(plan.allocations).toHaveLength(0);
    });

    it('should handle empty bundles array', () => {
        const plan = {
            allocations: [{
                discountType: 'bundle'
            }],
            bundlePlanId: 'bundle1'
        };
        const plans = [{ _id: 'plan1' }];

        RegistrationUtils.filterBundleDiscountsWithoutRequiredPlans(plans, plan, []);
        expect(plan.bundlePlanId).toBeUndefined();
        expect(plan.allocations).toHaveLength(0);
    });
});

describe('RegistrationUtils.validateAndPushBundleDiscount', () => {
    const formData = [
        { name: 'monday', value: 'on' },
        { name: 'tuesday', value: 'on' },
        { name: 'startDate', value: '2025-04-30' },
    ];

    const bundle = {
        _id: 'bundle-1',
        plans: ['plan-1', 'plan-2'],
        description: 'Bundle 1 and 2'
    };

    const plan = {
        _id: 'plan-1',
        bundlePlanId: 'bundle-1',
        regularPrice: 1600,
        bundlePlanPrice: 1400,
        allocations: [],
        selectedDays: [],
        scaledAmounts: [200, 300, 400, 500, 600, 700, 800]
    };

    const currentData = {
        plans: {
            0: [
                { _id: 'plan-1', bundlePlanId: 'bundle-1' },
                { _id: 'plan-2', bundlePlanId: 'bundle-1' }
            ]
        }
    };

    it('should apply a bundle discount if all plans in the bundle are selected and allocation is missing', () => {
        const planClone = JSON.parse(JSON.stringify(plan));
        const bundlesApplied = [];

        RegistrationUtils.validateAndPushBundleDiscount(
            currentData,
            planClone,
            [bundle],
            formData,
            bundlesApplied,
            0
        );

        expect(planClone.allocations).toHaveLength(1);
        expect(planClone.allocations[0]).toMatchObject({
            discountType: DiscountTypes.BUNDLE,
            amount: 100,
            allocationDescription: 'Bundle: Bundle 1 and 2'
        });

        expect(bundlesApplied).toContain('bundle-1');
        expect(planClone.selectedDays).toEqual(['monday', 'tuesday']);
        expect(planClone.startDate).toBe('2025-04-30');
    });

    it('should not apply duplicate bundle discounts if one already exists', () => {
        const planWithExisting = {
            ...JSON.parse(JSON.stringify(plan)),
            allocations: [{
                allocationType: 'discount',
                amount: 50, // old value
                amountType: 'dollars',
                discountType: DiscountTypes.BUNDLE,
                allocationDescription: 'Old bundle'
            }]
        };
        const bundlesApplied = [];

        RegistrationUtils.validateAndPushBundleDiscount(
            currentData,
            planWithExisting,
            [bundle],
            formData,
            bundlesApplied,
            0
        );

        const bundleAlloc = planWithExisting.allocations.find(a => a.discountType === DiscountTypes.BUNDLE);
        expect(planWithExisting.allocations).toHaveLength(1);
        expect(bundleAlloc.amount).toBe(100); // Updated amount
        expect(bundleAlloc.allocationDescription).toBe('Old bundle');
        expect(bundlesApplied).toContain('bundle-1');
    });
});