import { jest } from '@jest/globals';
import { ChildcareCrmUtil } from '../../../../../server/childcareCrmUtil';
import { ChildcareCRM } from '../../../../../server/childcareCrmService';
import { People } from '../../../../../lib/collections/people';
import { ChildcareCrmAccounts } from '../../../../../lib/collections/childcareCrmAccounts';

jest.mock('../../../../../server/childcareCrmUtil');
jest.mock('../../../../../lib/collections/people');
jest.mock('../../../../../lib/collections/childcareCrmAccounts');

describe('ChildStatusSyncService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('syncAllChildStatuses should call syncChildStatusesForOrg sequentially for each org', async () => {
    const mockOrgs = ['123e4567-e89b-12d3-a456-************', '987fbc97-4bed-5078-b1f3-273e02a22d0a'];
    ChildcareCrmUtil.getAllChildcareCrmOrgs.mockResolvedValue(mockOrgs);
    ChildcareCRM.syncChildStatusesForOrg = jest.fn().mockResolvedValue();

    await ChildcareCRM.syncAllChildStatuses();

    expect(ChildcareCrmUtil.getAllChildcareCrmOrgs).toHaveBeenCalled();
    expect(ChildcareCRM.syncChildStatusesForOrg).toHaveBeenCalledTimes(mockOrgs.length);
    expect(ChildcareCRM.syncChildStatusesForOrg).toHaveBeenNthCalledWith(1, '123e4567-e89b-12d3-a456-************');
    expect(ChildcareCRM.syncChildStatusesForOrg).toHaveBeenNthCalledWith(2, '987fbc97-4bed-5078-b1f3-273e02a22d0a');
  });

  test('syncChildStatusesForOrg should throw an error if orgId is missing', async () => {
    ChildcareCRM.syncChildStatusesForOrg = jest.fn().mockImplementation(async (orgId) => {
      if (!orgId) {
        throw new Error('orgId is required to sync child statuses.');
      }
    });

    await expect(ChildcareCRM.syncChildStatusesForOrg())
      .rejects
      .toThrow('orgId is required to sync child statuses.');
  });

  test('syncChildStatusesForOrg should throw an error if orgId is invalid', async () => {
    ChildcareCrmUtil.getChildcareCrmAccount.mockResolvedValue(null);

    ChildcareCRM.syncChildStatusesForOrg = jest.fn().mockImplementation(async (orgId) => {
      if (!orgId) {
        throw new Error('orgId is required to sync child statuses.');
      }

      const orgExists = await ChildcareCrmUtil.getChildcareCrmAccount(orgId);
      if (!orgExists) {
        throw new Error(`Invalid orgId: ${orgId}. Organization not found.`);
      }
    });

    await expect(ChildcareCRM.syncChildStatusesForOrg('invalidOrg'))
      .rejects
      .toThrow('Invalid orgId: invalidOrg. Organization not found.');
  });

  test('syncChildStatusesForOrg should process children one by one', async () => {
    const mockChildren = [{ _id: 'child1' }, { _id: 'child2' }];

    ChildcareCrmUtil.getChildcareCrmAccount.mockResolvedValue(true);
    People.find.mockReturnValue({
      async *[Symbol.asyncIterator]() {
        yield* mockChildren;
      }
    });

    const syncSpy = jest.spyOn(ChildcareCRM, 'syncChildStatusFromSchedules').mockResolvedValue();

    ChildcareCRM.syncChildStatusesForOrg = async (orgId) => {
      if (!orgId) {
        throw new Error("orgId is required to sync child statuses.");
      }

      const orgExists = await ChildcareCrmUtil.getChildcareCrmAccount(orgId);
      if (!orgExists) {
        throw new Error(`Invalid orgId: ${orgId}. Organization not found.`);
      }

      const childrenCursor = People.find({ orgId, type: 'person' }, { fields: { _id: 1 } });

      for await (const child of childrenCursor) {
        try {
          await ChildcareCRM.syncChildStatusFromSchedules(child._id);
        } catch (ex) {
          console.error(`Child status sync error for child ${child._id}:`, ex);
        }
      }
    };

    await ChildcareCRM.syncChildStatusesForOrg('validOrg');

    expect(ChildcareCrmUtil.getChildcareCrmAccount).toHaveBeenCalledWith('validOrg');
    expect(syncSpy).toHaveBeenCalledTimes(mockChildren.length);
    expect(syncSpy).toHaveBeenNthCalledWith(1, 'child1');
    expect(syncSpy).toHaveBeenNthCalledWith(2, 'child2');

    syncSpy.mockRestore();
  });

  test('syncChildStatusesForOrg should handle errors while syncing child statuses', async () => {
    expect.assertions(1);

    const mockChildren = [{ _id: 'child1' }, { _id: 'child2' }];
    ChildcareCrmUtil.getChildcareCrmAccount.mockResolvedValue(true);
    People.find.mockReturnValue({
      async *[Symbol.asyncIterator]() {
        yield* mockChildren;
      }
    });

    ChildcareCRM.syncChildStatusFromSchedules = jest
      .fn()
      .mockResolvedValueOnce()
      .mockRejectedValueOnce(new Error('Sync failed'));

    console.error = jest.fn();

    await ChildcareCRM.syncChildStatusesForOrg('validOrg');

    expect(console.error).toHaveBeenCalledWith(
      'Child status sync error for child child2:',
      expect.any(Error)
    );
  });

  test('syncChildStatusFromSchedules should skip if person is not found', async () => {
    People.findOneAsync.mockResolvedValue(null);

    ChildcareCRM.syncChildStatusFromSchedules = jest.fn().mockImplementation(async (personId) => {
      const person = await People.findOneAsync({ _id: personId });
      if (!person) {
        return;
      }
    });

    await ChildcareCRM.syncChildStatusFromSchedules('nonexistentChild');

    expect(People.findOneAsync).toHaveBeenCalledWith({ _id: 'nonexistentChild' });

    expect(ChildcareCrmUtil.calculateAutomaticStatus).not.toHaveBeenCalled();
  });

  test('syncChildStatusFromSchedules should skip if person is on wait list', async () => {
    const waitListedPerson = {
      _id: 'waitListedChild',
      designations: ['Wait List'],
      childcareCrm: { childId: 'child1', familyId: 'family1' },
      orgId: 'validOrg'
    };
    People.findOneAsync.mockResolvedValue(waitListedPerson);

    await ChildcareCRM.syncChildStatusFromSchedules('waitListedChild');

    expect(ChildcareCrmUtil.calculateAutomaticStatus).not.toHaveBeenCalled();
  });

  test('syncChildStatusFromSchedules should skip if CRM account is not found', async () => {
    const person = {
      _id: 'validChild',
      childcareCrm: { childId: 'child1', familyId: 'family1' },
      orgId: 'validOrg'
    };

    People.findOneAsync.mockResolvedValue(person);

    ChildcareCrmAccounts.findOneAsync.mockResolvedValue(null);

    ChildcareCRM.syncChildStatusFromSchedules = jest.fn().mockImplementation(async (personId) => {
      const person = await People.findOneAsync({ _id: personId });
      if (!person || !person.childcareCrm || !person.childcareCrm.childId || !person.childcareCrm.familyId) {
        return;
      }

      const crmAccount = await ChildcareCrmAccounts.findOneAsync({ 'centers.orgId': person.orgId });
      if (!crmAccount) {
        return;
      }

    });

    await ChildcareCRM.syncChildStatusFromSchedules('validChild');

    expect(ChildcareCrmAccounts.findOneAsync).toHaveBeenCalledWith({ 'centers.orgId': 'validOrg' });

    expect(ChildcareCrmUtil.calculateAutomaticStatus).not.toHaveBeenCalled();
  });

  test('syncChildStatusFromSchedules should skip if status is WITHDRAWN', async () => {
    const person = {
      _id: 'withdrawnChild',
      childcareCrm: { childId: 'child1', familyId: 'family1' },
      orgId: 'validOrg'
    };

    People.findOneAsync.mockResolvedValue(person);

    ChildcareCrmAccounts.findOneAsync.mockResolvedValue({});

    ChildcareCrmUtil.calculateAutomaticStatus.mockResolvedValue({ status: 'WITHDRAWN', statusDate: '2023-01-01' });

    ChildcareCRM.syncChildStatusFromSchedules = jest.fn().mockImplementation(async (personId) => {
      const person = await People.findOneAsync({ _id: personId });
      if (!person || !person.childcareCrm || !person.childcareCrm.childId || !person.childcareCrm.familyId) {
        return;
      }

      const crmAccount = await ChildcareCrmAccounts.findOneAsync({ 'centers.orgId': person.orgId });
      if (!crmAccount) {
        return;
      }

      const { status, statusDate } = await ChildcareCrmUtil.calculateAutomaticStatus(person._id);
      if (status === 'WITHDRAWN') {
        return;
      }

    });

    const doStatusUpdateSpy = jest.spyOn(ChildcareCRM, 'doStatusUpdate').mockImplementation(() => { });

    await ChildcareCRM.syncChildStatusFromSchedules('withdrawnChild');

    expect(ChildcareCrmUtil.calculateAutomaticStatus).toHaveBeenCalledWith('withdrawnChild');

    expect(doStatusUpdateSpy).not.toHaveBeenCalled();

    doStatusUpdateSpy.mockRestore();
  });
});