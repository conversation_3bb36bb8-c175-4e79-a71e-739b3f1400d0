<template name="old_aside" >
<!--begin::Aside-->
				<div class="aside aside-left d-flex flex-column" id="kt_aside">

					<!--begin::Brand-->
					<div class="aside-brand d-flex flex-column align-items-center flex-column-auto pt-5 pt-lg-18 pb-4">

						<!--begin::Logo-->
						<div class="btn btn-hover-icon-primary p-0" id="kt_aside_mobile_toggle">
							<img alt="Logo" src="/media/svg/icons/icon.svg" class="logo-default max-h-30px" style="width:24px;" />
						</div>

						<!--end::Logo-->
					</div>

					<!--end::Brand-->

					<!--begin::Nav Wrapper-->
					<div class="aside-nav d-flex flex-column align-items-center flex-column-fluid pb-10">

						<!--begin::Nav-->
						<ul class="nav flex-column">
							
							<li class="nav-item mobile-only" data-toggle="tooltip" data-placement="right" data-container="body" data-boundary="window" title="Navigation">
								<span class="nav-link justify-content-center" id="kt_header_mobile_toggle">
									<i class="fad fad-regular fa-bars fad-primary-h"></i>
								</span>
							</li>
							
							<!-- <button class="btn rounded-0 p-0 burger-icon burger-icon-left mobile-only" id="kt_header_mobile_toggle">
								<span></span>
							</button>  -->
							
							<li class="nav-item" data-toggle="tooltip" data-placement="right" data-container="body" data-boundary="window" title="My Profile">
								<span type="button" class="nav-link justify-content-center">
									<i class="fad fad-regular fa-user fad-primary-h"></i>
								</span>
							</li>

							<!--begin::Item-->
							<li class="nav-item" data-toggle="tooltip" data-placement="right" data-container="body" data-boundary="window" title="People">
								<span type="button" class="nav-link justify-content-center">
									<i class="fad fad-regular fa-users fad-primary-h"></i>
								</span>
							</li>

							<!--end::Item-->

							<!--begin::Item-->
							<li class="nav-item" data-toggle="tooltip" data-placement="right" data-container="body" data-boundary="window" title="Calendar">
								<span type="button" class="nav-link justify-content-center">
									<i class="fad fad-regular fa-calendar-alt fad-primary-h"></i>
								</span>
							</li>

							<!--end::Item-->

							<!--begin::Item-->
							<li class="nav-item" data-toggle="tooltip" data-placement="right" data-container="body" data-boundary="window" title="Groups">
								<span type="button" class="nav-link justify-content-center">
									<i class="fad fad-regular fa-layer-group fad-primary-h"></i>
								</span>
							</li>

							<!--end::Item-->

							<!--begin::Item-->
							<li class="nav-item" data-toggle="tooltip" data-placement="right" data-container="body" data-boundary="window" title="Announcements">
								<span type="button" class="nav-link justify-content-center">
									<i class="fad fad-regular fa-bullhorn fad-primary-h"></i>
								</span>
							</li>

							<!--end::Item-->

							<!--begin::Item-->
							<li class="nav-item" data-toggle="tooltip" data-placement="right" data-container="body" data-boundary="window" title="Activities">
								<span type="button" class="nav-link justify-content-center">
									<i class="fad fad-regular fa-pencil fad-primary-h"></i>
								</span>
							</li>

							<!--end::Item-->

							<!--begin::Item-->
							<li class="nav-item" data-toggle="tooltip" data-placement="right" data-container="body" data-boundary="window" title="Food">
								<span type="button" class="nav-link justify-content-center">
									<i class="fad fad-regular fa-utensils fad-primary-h"></i>
								</span>
							</li>

							<!--end::Item-->
							
							<!--begin::Item-->
							<li class="nav-item" data-toggle="tooltip" data-placement="right" data-container="body" data-boundary="window" title="Scheduling">
								<span type="button" class="nav-link justify-content-center">
									<i class="fad fad-regular fa-calendar-check fad-primary-h"></i>
								</span>
							</li>

							<!--end::Item-->
							<!--begin::Item-->
							<li class="nav-item" data-toggle="tooltip" data-placement="right" data-container="body" data-boundary="window" title="Reports">
								<span type="button" class="nav-link justify-content-center">
									<i class="fad fad-regular fa-folder-open fad-primary-h"></i>
								</span>
							</li>

							<!--end::Item-->
							<!--begin::Item-->
							<li class="nav-item" data-toggle="tooltip" data-placement="right" data-container="body" data-boundary="window" title="Billing">
								<span type="button" class="nav-link justify-content-center">
									<i class="fad fad-regular fa-dollar-sign fad-primary-h"></i>
								</span>
							</li>

							<!--end::Item-->
							<!--begin::Item-->
							<li class="nav-item" data-toggle="tooltip" data-placement="right" data-container="body" data-boundary="window" title="Admin/Account">
								<span type="button" class="nav-link justify-content-center">
									<i class="fad fad-regular fa-key fad-primary-h"></i>
								</span>
							</li>

							<!--end::Item-->
						</ul>

						<!--end::Nav-->
					</div>

					<!--end::Nav Wrapper-->

					<!--begin::Footer-->
					<div class="aside-footer d-flex flex-column align-items-center flex-column-auto py-8">

						<!--begin::Notifications-->
						<a href="#" class="btn btn-icon btn-hover-text-primary btn-lg mb-1 position-relative" id="kt_quick_notifications_toggle" data-toggle="tooltip" data-placement="right" data-container="body" data-boundary="window" title="Notifications">
							<span class="svg-icon svg-icon-xxl">

								<!--begin::Svg Icon | path:assets/media/svg/icons/Design/Layers.svg-->
								<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
									<g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
										<polygon points="0 0 24 0 24 24 0 24" />
										<path d="M12.9336061,16.072447 L19.36,10.9564761 L19.5181585,10.8312381 C20.1676248,10.3169571 20.2772143,9.3735535 19.7629333,8.72408713 C19.6917232,8.63415859 19.6104327,8.55269514 19.5206557,8.48129411 L12.9336854,3.24257445 C12.3871201,2.80788259 11.6128799,2.80788259 11.0663146,3.24257445 L4.47482784,8.48488609 C3.82645598,9.00054628 3.71887192,9.94418071 4.23453211,10.5925526 C4.30500305,10.6811601 4.38527899,10.7615046 4.47382636,10.8320511 L4.63,10.9564761 L11.0659024,16.0730648 C11.6126744,16.5077525 12.3871218,16.5074963 12.9336061,16.072447 Z" fill="#000000" fill-rule="nonzero" />
										<path d="M11.0563554,18.6706981 L5.33593024,14.122919 C4.94553994,13.8125559 4.37746707,13.8774308 4.06710397,14.2678211 C4.06471678,14.2708238 4.06234874,14.2738418 4.06,14.2768747 L4.06,14.2768747 C3.75257288,14.6738539 3.82516916,15.244888 4.22214834,15.5523151 C4.22358765,15.5534297 4.2250303,15.55454 4.22647627,15.555646 L11.0872776,20.8031356 C11.6250734,21.2144692 12.371757,21.2145375 12.909628,20.8033023 L19.7677785,15.559828 C20.1693192,15.2528257 20.2459576,14.6784381 19.9389553,14.2768974 C19.9376429,14.2751809 19.9363245,14.2734691 19.935,14.2717619 L19.935,14.2717619 C19.6266937,13.8743807 19.0546209,13.8021712 18.6572397,14.1104775 C18.654352,14.112718 18.6514778,14.1149757 18.6486172,14.1172508 L12.9235044,18.6705218 C12.377022,19.1051477 11.6029199,19.1052208 11.0563554,18.6706981 Z" fill="#000000" opacity="0.3" />
									</g>
								</svg>

								<!--end::Svg Icon-->
							</span>
							<span class="label label-sm label-light-danger label-rounded font-weight-bolder position-absolute top-0 right-0 mt-1 mr-1">3</span>
						</a>

						<!--end::Notifications-->

						<!--begin::Quick Actions-->
						<a href="#" class="btn btn-icon btn-hover-text-primary btn-lg mb-1" id="kt_quick_actions_toggle" data-toggle="tooltip" data-placement="right" data-container="body" data-boundary="window" title="Quick Actions">
							<span class="svg-icon svg-icon-xxl">

								<!--begin::Svg Icon | path:assets/media/svg/icons/Media/Equalizer.svg-->
								<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
									<g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
										<rect x="0" y="0" width="24" height="24" />
										<rect fill="#000000" opacity="0.3" x="13" y="4" width="3" height="16" rx="1.5" />
										<rect fill="#000000" x="8" y="9" width="3" height="11" rx="1.5" />
										<rect fill="#000000" x="18" y="11" width="3" height="9" rx="1.5" />
										<rect fill="#000000" x="3" y="13" width="3" height="7" rx="1.5" />
									</g>
								</svg>

								<!--end::Svg Icon-->
							</span>
						</a>

						<!--end::Quick Actions-->

						<!--begin::Quick Panel-->
						<a href="#" class="btn btn-icon btn-hover-text-primary btn-lg mb-1" id="kt_quick_panel_toggle" data-toggle="tooltip" data-placement="right" data-container="body" data-boundary="window" title="Quick Panel">
							<span class="svg-icon svg-icon-xxl">

								<!--begin::Svg Icon | path:assets/media/svg/icons/Layout/Layout-4-blocks.svg-->
								<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
									<g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
										<rect x="0" y="0" width="24" height="24" />
										<rect fill="#000000" x="4" y="4" width="7" height="7" rx="1.5" />
										<path d="M5.5,13 L9.5,13 C10.3284271,13 11,13.6715729 11,14.5 L11,18.5 C11,19.3284271 10.3284271,20 9.5,20 L5.5,20 C4.67157288,20 4,19.3284271 4,18.5 L4,14.5 C4,13.6715729 4.67157288,13 5.5,13 Z M14.5,4 L18.5,4 C19.3284271,4 20,4.67157288 20,5.5 L20,9.5 C20,10.3284271 19.3284271,11 18.5,11 L14.5,11 C13.6715729,11 13,10.3284271 13,9.5 L13,5.5 C13,4.67157288 13.6715729,4 14.5,4 Z M14.5,13 L18.5,13 C19.3284271,13 20,13.6715729 20,14.5 L20,18.5 C20,19.3284271 19.3284271,20 18.5,20 L14.5,20 C13.6715729,20 13,19.3284271 13,18.5 L13,14.5 C13,13.6715729 13.6715729,13 14.5,13 Z" fill="#000000" opacity="0.3" />
									</g>
								</svg>

								<!--end::Svg Icon-->
							</span>
						</a>

						<!--end::Quick Panel-->
					</div>

					<!--end::Footer-->
				</div>

				<!--end::Aside-->
</template>
