import $ from "jquery";
import './_announcementOrgsField.html';

import { Template } from 'meteor/templating';
import { ReactiveVar } from 'meteor/reactive-var';
import { Meteor } from 'meteor/meteor';
import { Orgs } from '../../../lib/collections/orgs';

Template.announcementOrgsField.helpers({
	availableOrgs() {
		return Template.instance().orgs.get();
	},
	selectedOrgOptions() {
		return Template.instance().selectedOrgs.get();
	},
	isLoading() {
		return !Template.instance().loaded.get();
	}
});

Template.announcementOrgsField.onCreated( function() {
	var self = this;
	var soIds = self.data && self.data.orgIds
  	self.selectedOrgs = new ReactiveVar(soIds || []);
	self.orgs = new ReactiveVar();
	self.loaded = new ReactiveVar();
  const currentOrgId = Orgs.current() && Orgs.current()._id;
	if (this.data && this.data.childOnly) {
		Meteor.callAsync('getChildOrgs').then((result) => {
			self.orgs.set(result);
      if (self.data.preselectAll) {
        self.selectedOrgs.set(result.map(org => org._id));
      } else if (!soIds || soIds.length === 0) {
        if (currentOrgId) {
          if (result.some(org => org._id === currentOrgId)) {
            self.selectedOrgs.set([currentOrgId]);
          }
        }
      }
			self.loaded.set(true);
		});
	} else {
		Meteor.callAsync('getSwitchableSites').then((result) => {
			self.orgs.set(result);
			if (self.data.preselectAll) {
				self.selectedOrgs.set(result.map(org => org._id));
			} else if (!soIds || soIds.length === 0) {
        if (currentOrgId) {
          if (result.some(org => org._id === currentOrgId)) {
            self.selectedOrgs.set([currentOrgId]);
          }
        }
      }
			self.loaded.set(true);
		});
	}
});

Template.announcementOrgsFieldDropdown.onCreated( function() {
	var self = this;
  	self.selectedOrgs = new ReactiveVar(self.data.selectedOrgs || []);
});

Template.announcementOrgsFieldDropdown.onRendered( function() {
	const self = this;
	$("#announcementOrgs").multiselect({
		includeSelectAllOption: true,
		enableFiltering: true,
		nonSelectedText: "Current Org Only",
		maxHeight: 300,
		onChange: function(option, checked, select) { 
			const orgs = self.data.orgs;
			const event = new Event('orgChanged');
			const body = document.getElementsByTagName('body')[0];
			checkAllBelow($(option).val(), orgs, checked);
			body.dispatchEvent(event);
		},
	});
});

Template.announcementOrgsFieldDropdown.helpers({
	selectedOrgOptions() {
		return Template.instance().selectedOrgs.get();
	},
});

function checkAllBelow(orgId, orgs, checked) {
	const isSelected = checked ? 'select' : 'deselect';
	// if selected org is brand check all that apply
	if(orgs.find(org => org._id === orgId).isBrand){
		orgs.filter( o => o.selectedBrand === orgId).forEach(o => {
			$("#announcementOrgs").multiselect(isSelected, o._id);
		});
	}

	orgs.filter( o => o.parentOrgId == orgId).forEach(o => {
		$("#announcementOrgs").multiselect(isSelected, o._id);
		checkAllBelow(o._id, orgs);
	});
}