<template name="messageList">
	<!--begin::List-->
	<div class="flex-row-fluid ml-lg-8 d-block" id="kt_inbox_list">
        <!--begin::Card-->
        <div class="card card-custom card-stretch">
          <!--begin::Header-->
          <div class="card-header row row-marginless align-items-center flex-wrap py-5 h-auto">
            <!--begin::Toolbar-->
            <div class="col-12 col-sm-6 col-xxl-4 order-2 order-xxl-1 d-flex flex-wrap align-items-center">
              <div class="d-flex align-items-center mr-1 my-2">
                <label data-inbox="group-select" class="checkbox checkbox-inline checkbox-primary mr-3">
                  <input data-cy="select-all-messages" type="checkbox" id="checkbox-select-all">
                  <span class="symbol-label"></span>
                </label>
                
              </div>
              <div class="d-flex align-items-center mr-1 my-2">
                <span data-cy="archive-all-messages" class="btn btn-default btn-icon btn-sm mr-2" data-toggle="tooltip" id="btn-archive-all" title="" data-original-title="Archive">
                  <span class="svg-icon svg-icon-md">
                    <!--begin::Svg Icon | path:/metronic/theme/html/demo13/dist/assets/media/svg/icons/Communication/Mail-opened.svg-->
                    <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                      <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                        <rect x="0" y="0" width="24" height="24"></rect>
                        <path d="M6,2 L18,2 C18.5522847,2 19,2.44771525 19,3 L19,12 C19,12.5522847 18.5522847,13 18,13 L6,13 C5.44771525,13 5,12.5522847 5,12 L5,3 C5,2.44771525 5.44771525,2 6,2 Z M7.5,5 C7.22385763,5 7,5.22385763 7,5.5 C7,5.77614237 7.22385763,6 7.5,6 L13.5,6 C13.7761424,6 14,5.77614237 14,5.5 C14,5.22385763 13.7761424,5 13.5,5 L7.5,5 Z M7.5,7 C7.22385763,7 7,7.22385763 7,7.5 C7,7.77614237 7.22385763,8 7.5,8 L10.5,8 C10.7761424,8 11,7.77614237 11,7.5 C11,7.22385763 10.7761424,7 10.5,7 L7.5,7 Z" fill="#000000" opacity="0.3"></path>
                        <path d="M3.79274528,6.57253826 L12,12.5 L20.2072547,6.57253826 C20.4311176,6.4108595 20.7436609,6.46126971 20.9053396,6.68513259 C20.9668779,6.77033951 21,6.87277228 21,6.97787787 L21,17 C21,18.1045695 20.1045695,19 19,19 L5,19 C3.8954305,19 3,18.1045695 3,17 L3,6.97787787 C3,6.70173549 3.22385763,6.47787787 3.5,6.47787787 C3.60510559,6.47787787 3.70753836,6.51099993 3.79274528,6.57253826 Z" fill="#000000"></path>
                      </g>
                    </svg>
                    <!--end::Svg Icon-->
                  </span>
                </span>
                
              </div>
            </div>
            <!--end::Toolbar-->
            <!--begin::Search-->
            <div class="col-xxl-3 d-flex order-1 order-xxl-2 align-items-center justify-content-center">
              <div class="input-group input-group-lg input-group-solid my-2">
                <input data-cy="search-term" type="text" class="form-control pl-4" placeholder="Search..." name="search-term">
                <div class="input-group-append">
                  <span class="input-group-text pr-3">
                    <span class="svg-icon svg-icon-lg">
                      <!--begin::Svg Icon | path:/metronic/theme/html/demo13/dist/assets/media/svg/icons/General/Search.svg-->
                      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                        <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                          <rect x="0" y="0" width="24" height="24"></rect>
                          <path d="M14.2928932,16.7071068 C13.9023689,16.3165825 13.9023689,15.6834175 14.2928932,15.2928932 C14.6834175,14.9023689 15.3165825,14.9023689 15.7071068,15.2928932 L19.7071068,19.2928932 C20.0976311,19.6834175 20.0976311,20.3165825 19.7071068,20.7071068 C19.3165825,21.0976311 18.6834175,21.0976311 18.2928932,20.7071068 L14.2928932,16.7071068 Z" fill="#000000" fill-rule="nonzero" opacity="0.3"></path>
                          <path d="M11,16 C13.7614237,16 16,13.7614237 16,11 C16,8.23857625 13.7614237,6 11,6 C8.23857625,6 6,8.23857625 6,11 C6,13.7614237 8.23857625,16 11,16 Z M11,18 C7.13400675,18 4,14.8659932 4,11 C4,7.13400675 7.13400675,4 11,4 C14.8659932,4 18,7.13400675 18,11 C18,14.8659932 14.8659932,18 11,18 Z" fill="#000000" fill-rule="nonzero"></path>
                        </g>
                      </svg>
                      <!--end::Svg Icon-->
                    </span>
                  </span>
                </div>
              </div>
            </div>
            <!--end::Search-->
            <!--begin::Pagination-->
            <div class="col-12 col-sm-6 col-xxl-4 order-2 order-xxl-3 d-flex align-items-center justify-content-sm-end text-right my-2">
              <!--begin::Per Page Dropdown-->
              <div data-cy="records-per-page" class="d-flex align-items-center mr-2" data-toggle="tooltip" title="" data-original-title="Records per page">
                  <span class="text-muted font-weight-bold mr-2">{{indexPosition}} - {{lastDisplayPosition}} of {{threadCount}}</span>
              </div>
              <!--end::Per Page Dropdown-->
              <!--begin::Arrow Buttons-->
              <span data-cy="previous-page" class="btn btn-default btn-icon btn-sm mr-2" id="btn-messagelist-previous" data-toggle="tooltip" title="" data-original-title="Previous page">
                <i class="ki ki-bold-arrow-back icon-sm"></i>
              </span>
              <span data-cy="next-page" class="btn btn-default btn-icon btn-sm mr-2" id="btn-messagelist-next" data-toggle="tooltip" title="" data-original-title="Next page">
                <i class="ki ki-bold-arrow-next icon-sm"></i>
              </span>
              <!--end::Arrow Buttons-->
              <!--begin::Sort Dropdown-->
              <!--<div class="dropdown mr-2" data-toggle="tooltip" title="" data-original-title="Sort">
                <span class="btn btn-default btn-icon btn-sm" data-toggle="dropdown">
                  <i class="flaticon2-console icon-1x"></i>
                </span>
                <div class="dropdown-menu dropdown-menu-right p-0 m-0 dropdown-menu-sm">
                  <ul class="navi py-3">
                    <li class="navi-item">
                      <a href="#" class="navi-link active">
                        <span class="navi-text">Newest</span>
                      </a>
                    </li>
                    <li class="navi-item">
                      <a href="#" class="navi-link">
                        <span class="navi-text">Olders</span>
                      </a>
                    </li>
                    
                  </ul>
                </div>
              </div>
              -->
              <!--end::Sort Dropdown-->
              
            </div>
            <!--end::Pagination-->
          </div>
          <!--end::Header-->
          <!--begin::Body-->
            <div class="card-body table-responsive px-0">
                {{# if isListLoading }} {{> loading containerClasses=getLoadingClass }} {{/ if }}
            <!--begin::Items-->
            <div class="list list-hover min-w-500px" data-inbox="list">
              {{#each threads}}
              <!--begin::Item-->
              <div class="d-flex align-items-start list-item card-spacer-x py-3" data-inbox="message" data-message-id="{{_id}}">
                <!--begin::Toolbar-->
                <div class="d-flex align-items-center">
                  <!--begin::Actions-->
                  <div class="d-flex align-items-center mr-3" data-inbox="actions">
                    <label class="checkbox checkbox-inline checkbox-primary flex-shrink-0 mr-3">
                      <input data-cy="message-checkbox" type="checkbox" class="message-select-checkbox" data-message-id="{{_id}}">
                      <span></span>
                    </label>
                  </div>
                  <!--end::Actions-->
                  <!--begin::Author-->
                  <div class="d-flex align-items-center flex-wrap w-xxl-200px mr-3" data-toggle="view">
                    <div class="symbol symbol-light-danger symbol-35 mr-3">
                      <span data-cy="initials" class="symbol-label font-weight-bolder">{{threadInitials}}</span>
                    </div>
                    <a data-cy="message-person" href="#" class="{{#if threadUnread}}font-weight-bold{{/if}} text-dark-75 text-hover-primary">
                      {{#if trueIfEq messageView "admin"}}
                        {{fullRecipientDescription}}
                      {{else}}
                        {{threadRecipients}}
                      {{/if}}
                    </a>
                  </div>
                  <!--end::Author-->
                </div>
                <!--end::Toolbar-->
                <!--begin::Info-->
                <div class="flex-grow-1 mt-2 mr-2" data-toggle="view">
                  <div>
                    <span data-cy="message-title" class="{{#if threadUnread}}font-weight-bolder{{/if}} font-size-lg mr-2">{{threadTitle}} -</span>
                    <span data-cy="message-summary" class="text-muted">{{threadSummary}}</span>
                  </div>
                  <div class="mt-2">
                    <!--<span class="label label-light-primary font-weight-bold label-inline mr-1">inbox</span>
                    <span class="label label-light-danger font-weight-bold label-inline">task</span>-->
                  </div>
                </div>
                <!--end::Info-->
                <!--begin::Datetime-->
                <div data-cy="message-time" class="mt-2 mr-3 {{#if threadUnread}}font-weight-bolder{{/if}} w-80px text-right" data-toggle="view">{{formatDate mostRecentStamp "M/DD/YYYY"}}<br/>{{formatDate mostRecentStamp "h:mm a"}}</div>
                <!--end::Datetime-->
              </div>
              <!--end::Item-->
              {{/each}}
            </div>
            <!--end::Items-->
          </div>
          <!--end::Body-->
        </div>
        <!--end::Card-->
      </div>
      <!--end::List-->
</template>