<template name="_curriculumBuilderUpdateThemeDetailsPanel">
  <div class="d-flex flex-column-fluid">
    <div class="container">
      <div class="card card-custom gutter-b">
        <div class="card-body">
          <div class="d-flex flex-row align-items-center justify-content-end my-4">
            <div data-cy="btn-delete-theme" class="btn btn-danger font-weight-bolder btn-text-white" id="btnDeleteTheme">
              <i class="fad fa-swap-opacity fa-minus mr-2"></i>Delete
            </div>
          </div>
          <form id="updateThemeForm">
            <div class="form-group row">
              <label class="col-xl-3 col-lg-3 text-right col-form-label">Theme Name</label>
              <div class="col-lg-9 col-xl-6">
                <input data-cy="theme-name" class="form-control form-control-lg form-control-solid" type="text" name="theme-name" value="{{theme.name}}" />
              </div>
            </div>
            <div class="form-group row">
              <label class="col-xl-3 col-lg-3 text-right col-form-label">Description</label>
              <div class="col-lg-9 col-xl-6">
                <textarea data-cy="description-theme" class="form-control form-control-lg form-control-solid" name="theme-description">{{theme.description}}</textarea>
              </div>
            </div>
            <div class="form-group row">
              <label class="col-xl-3 col-lg-3 text-right col-form-label">Group(s)</label>
              <div class="col-lg-9 col-xl-6">
                <select data-cy="select-groups" multiple class="form-control form-control-lg form-control-solid" id="theme-groups">
                  {{#each groups}}
                    <option value="{{_id}}" selected={{isGroupSelected _id }}>{{name}}</option>
                  {{/each}}
                </select>
              </div>
            </div>
          </form>
          <div class="d-flex flex-row align-items-center justify-content-center mt-4">
            <div data-cy="btn-update-theme" class="btn btn-primary font-weight-bolder btn-text-white" id="btn-update-theme">
              Update
            </div>
          </div>
        </div>
      </div>
      {{#unless days}}
        <div class="d-flex flex-row align-items-center justify-content-center mt-12">
          <div class="alert alert-custom alert-notice alert-light-info fade show max-w-1000px" id="no-themedays-alert" role="alert">
            <div class="alert-icon"><i class="fad fa-bullhorn"></i></div>
            <div class="alert-text">
              <h4>Your theme currently doesn't have any scheduled days. Add day(s) below to start building your theme.</h4>
            </div>
            <div class="alert-close">
              <button type="button" class="close" data-dismiss="alert" aria-label="Close" id="no-themedays-close-alert">
                <span aria-hidden="true"><i class="icon-2x fad fa-times"></i></span>
              </button>
            </div>
          </div>
        </div>
      {{else}}
        {{#each day in days}}
          <div class="card card-custom gutter-b">
            <div class="card-body">
              <div class="d-flex flex-row align-items-center justify-content-between mb-4">
                <div>
                  <span data-cy="theme-date" class="font-size-h3 text-primary font-weight-bold">{{formatDate day "dddd MMMM Do, YYYY"}}</span>
                </div>
                <div data-cy="remove-day" class="btn btn-danger font-weight-bolder btn-text-white remove-day" data-day="{{day}}">
                  <i class="fad fa-swap-opacity fa-minus mr-2"></i>Remove Day
                </div>
              </div>
              <div class="separator separator-dashed my-8"></div>
              <div id="activity-{{ @index }}" class="sortable-activities-list">
              {{#each activity in (dayActivities day)}}
                <div class="sortable-activity" data-id="{{ activity._id }}">
                    <div class="row">
                      <div class="col-sm-1 my-auto text-center sort-handle">
                        <i class="fad fad-primary fa-arrows-up-down"></i>
                      </div>
                      <div class="col-sm-4">
                        <a data-cy="activity-scheduled-name" href="/activities/{{activity._id}}" class="font-size-h4 font-weight-bolder">{{activity.headline}}</a>
                        <p class="font-size-base">{{activity.message}}</p>
                        {{#if activity.homework}}
                          <div class="font-size-lg font-weight-bold">Homework:</div>
                          <p data-cy="activivy-homework" class="font-size-base">{{activity.homework}}</p>
                        {{/if}}
                        {{#if activity.materials}}
                          <div class="font-size-lg font-weight-bold">Materials:</div>
                          <p data-cy="activity-materials" class="font-size-base">{{activity.materials}}</p>
                        {{/if}}
                        {{#if activity.teacherNotes}}
                          <div class="font-size-lg font-weight-bold">Teacher Notes:</div>
                          <p data-cy="activity-teacher-notes" class="font-size-base">{{activity.teacherNotes}}</p>
                        {{/if}}
                        {{#if activity.internalNotes}}
                          <div class="font-size-lg font-weight-bold">Internal Notes:</div>
                          <p data-cy="activity-internal-notes" class="font-size-base">{{activity.internalNotes}}</p>
                        {{/if}}
                        {{#if activity.internalLink}}
                          <div class="font-size-lg font-weight-bold">Internal Link:</div>
                          <a data-cy="activity-internal-link" href="{{activity.internalLink}}" target="_blank" class="font-size-base">View Internal Link</a>
                        {{/if}}
                      </div>
                      <div class="col-sm-5">
                        {{#if activity.selectedTypes}}
                          <div class="font-size-lg font-weight-bold">Tag(s):</div>
                          {{formatSelectedTypes activity.selectedTypes}}<br/><br/>
                        {{/if}}
                        {{#if activity.selectedStandards}}
                          <div class="font-size-lg font-weight-bold">Standard(s):</div>
                        <!--{{formatSelectedStandards activity.selectedStandards}}<br/>-->
                          {{#each standard in activity.findStandards}}
                            {{standard}}<br/>
                          {{/each}}
                          <br/>
                        {{/if}}
                        {{#if activity.mediaFiles}}
                          <div class="font-size-lg font-weight-bold">Attachment(s):</div>
                          {{#each activity.mediaFiles}}
                            {{mediaName}} <a href="#" onclick="window.open('{{attachmentHandler mediaUrl}}', '_blank');" style="font-weight:normal;font-size:14px"> (View PDF)</a><br/>
                          {{/each}}
                        {{/if}}
                      </div>
                      <div class="d-flex justify-content-end align-items-center col-2">
                        <div class="d-flex flex-row align-items-center justify-content-end">
                          <div class="dropdown">
                            <div data-cy="dropdown-scheduled-day" class="btn btn-icon btn-clean" data-toggle="dropdown" >
                              <span class="svg-icon svg-icon-primary fad-regular fad-primary fad fa-ellipsis-h"></span>
                            </div>
                            <div class="dropdown-menu dropdown-menu-right" aria-labelledby="dropdownMenuButton">
                              <span data-cy="option-move" class="dropdown-item clickable-row btn-move-activity" data-id="{{activity._id}}" >Move</span>
                              <span data-cy="option-edit" class="dropdown-item clickable-row btn-edit-activity" data-id="{{activity._id}}" >Edit</span>
                              <span data-cy="option-delete" class="dropdown-item clickable-row btn-delete-activity" data-id="{{activity._id}}" >Delete</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="separator separator-dashed my-8"></div>
                </div>
              {{/each}}
              </div>
              <div class="d-flex flex-row align-items-center justify-content-center mt-4">
                <div data-cy="add-activity-new" class="btn btn-primary font-weight-bolder btn-text-white btn-add-activity" data-day="{{day}}">
                  <i class="fad fa-swap-opacity fa-plus mr-2"></i>Add Activity
                </div>
              </div>
            </div>
          </div>
        {{/each}}
      {{/unless}}
      <div class="d-flex flex-row align-items-center justify-content-center mt-4" id="show-add-days-container">
        <div data-cy="add-day" class="btn btn-primary font-weight-bolder btn-text-white" id="btn-show-add-days">
          <i class="fad fa-swap-opacity fa-plus mr-2"></i>Add Day(s)
        </div>
      </div>
      <div class="card card-custom gutter-b" id="section-add-days" hidden>
        <div class="card-body">
          <div class="row">
            <div class="d-flex flex-column col-6 align-items-center justify-content-center">
              <div class="mb-4"><span class="font-size-h4 text-primary font-weight-bold">Add selected day(s)</span></div>
              <div id="days-picker" class="mb-4" style="width:280px;"></div>
              <div data-cy="btn-add-selected-days" class="btn btn-primary font-weight-bolder btn-text-white" id="btnAddSelectedDays">
                <i class="fad fa-swap-opacity fa-plus mr-2"></i>Add Selection(s)
              </div>
            </div>
            <div class="d-flex flex-column col-6 align-items-center justify-content-center">
              <span class="font-size-h4 text-primary font-weight-bold">Next weekday: {{nextWeekdayLabel}}</span>
              <div data-cy="add-next-weekday" class="btn btn-primary font-weight-bolder btn-text-white mt-4" id="btnAddNextDay">
                <i class="fad fa-swap-opacity fa-plus mr-2"></i>Add Next Weekday
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
