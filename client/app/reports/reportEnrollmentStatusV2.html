<template name="reportEnrollmentStatusV2">
	<div class="card card-custom">
		<div class="card-header flex-wrap border-0 pt-6 pb-0">
			<div class="card-title">
				<h3 class="card-label">Enrollment Status
				<span class="text-muted pt-2 font-size-sm d-block">View changes in enrollment classifications for specified dates.</span></h3>
			</div>
			<div class="card-toolbar">
				<!--begin::Button-->

				<div data-cy="export-btn" class="btn btn-secondary font-weight-bolder btn-text-secondary mr-4" id="btnPrint">
					<i class="fad-regular fad fa-download fa-swap-opacity mr-2 text-secondary"></i>Export CSV
				</div>
				{{#unless isSavedMode }}
				<div data-cy="update-btn" class="btn btn-primary font-weight-bolder btn-text-white" id="btnUpdate">
					{{updateLabel}}
				</div>
				{{/unless}}
				
				<!--end::Button-->
			</div>
		</div>
		<div class="card-body">
			<div class="box">
				
				
				
				<div class="box-body">
					<div class="row" id="dvData">
						<div class="col-12">
							{{#if isSavedMode}}
							<div class="row font-weight-bolder text-dark">
								<div class="col">
									{{{ savedHeader }}}
								</div>
							</div>
							{{/if}}
							{{#unless isSavedMode}}
							<div class="row">
								<div class="col-sm-3">
									<div class="form-group">
										<label>Start Date</label>
										<div class="input-group">
											
											<input data-cy="start-date-input" type="text" class="form-control pull-right" id="reportStartDate">
										</div>
									</div>
								</div>
								<div class="col-sm-3">
									<div class="form-group">
										<label>End Date</label>
										<div class="input-group">
											
											<input data-cy="end-date-input" type="text" class="form-control pull-right" id="reportEndDate">
										</div>
									</div>
								</div>
								{{#if showOrgSelection}}
								<div class="col-sm-3">
									<div class="form-group">
										<label>Select Orgs</label>
										<div data-cy="select-org" class="input-group">
											{{> announcementOrgsField }}
										</div>
									</div>
								</div>
								{{/if}}
								<div class="col-sm-3">
									<div class="form-group">
										<label>Select Person Type(s)</label>
										<div class="input-group">
											<select data-cy="select-person-type" name='reportPersonType' id='reportPersonType' multiple="multiple" data-actions-box="true" class="form-control selectpicker">
												<option value="person">{{ getEntityTypePerson }}</option>
												<option value="family">{{ getEntityTypeFamily }}</option>
												<option value="staff">{{ getEntityTypeStaff }}</option>
												<option value="admin">{{ getEntityTypeAdmin }}</option>
											</select>
										</div>
									</div>
								</div>
							</div>
							<div class="row">
								<div class="col-sm-3">
									<div class="form-group">
										<label>Include Only</label>
										<div class="input-group">
											<select data-cy="include-only-types" name='reportIncludeOnly' id='reportIncludeOnly' data-actions-box="true" class="form-control selectpicker">
												<option value="">All</option>
												<option value="enrollees">New Enrollees</option>
												<option value="waitlistees">New Waitlistees</option>
												<option value="withdrawn">Withdrawn</option>
											</select>
										</div>
									</div>
								</div>
                                <div class="d-flex px-4">
                                    {{> reportQueueCheckbox }}
                                </div>
							</div>
							{{/unless}}
							<br/>
							
						
							{{#if reportRows}}
								<h4 class="text-center">Enrollment Status Report</h4>
								<h5 class="text-center">{{dateRangeLabel}}</h5>
								<br/>
								<table data-cy="enrollment-status-report-table" class="table" style="display:block;overflow-x:auto;padding-bottom:10px;">
									<tr>
										<td></td>
										<td class='text-right' style="padding-right:15px"><label>Total Waitlistees</label></td>
										<td class='text-right' style="padding-right:15px"><label>Total Enrolled</label></td>
										<td class='text-right' style="padding-right:15px"><label>Total Withdrawn</label></td>
										{{#each breakdownLabel in withdrawalBreakdownLabels}}
											<td class='text-right' style="padding-left:15px;padding-right:15px">{{breakdownLabel}}</td>
										{{/each}}
										<td class='text-right' style="padding-right:15px"><label>Total on Report</label></td>
									</tr>
									{{#each breakdown in breakdowns}}
										<tr data-cy="enrollment-status-all-stats"
												class="{{ rollupClass breakdown.isRollup }} {{ rollupIndent breakdown.displayDepth}}"
												data-parent-id="{{ breakdown.displayParentId }}"
												data-state="closed"
												data-display-id="{{ breakdown.displayId }}"
												{{ rollupHidden breakdown.displayParentId }}
										>
											<td data-cy="org-on-report" >
												{{#if rollupHasChildren breakdown.displayChildrenCount }}
												<a href="javascript:void(0)" class="rollup-control" data-control-id="{{breakdown.displayId}}" onclick="rollupToggle(this)">
													<i class="fa fa-caret-down d-none"></i>
													<i class="fa fa-caret-right "></i>
												</a>
												{{/if}}
												{{breakdown.locationName}}
											</td>
											<td data-cy="total-waitlistees" class='text-right'>{{breakdown.waitlistees}}</td>
											<td data-cy="total-enrolled" class='text-right'>{{breakdown.enrolled}}</td>
											<td data-cy="total-withdrawn" class='text-right'>{{breakdown.withdrawn}}</td>
											{{#each breakdownLabel in withdrawalBreakdownLabels}}	
												<td data-cy="never-enrolled" class='text-right'>{{amountFor breakdownLabel breakdown}}</td>
											{{/each}}
											<td data-cy="total-on-report" class='text-right'>{{breakdown.total}}</td>
										</tr>
									{{/each}}
								</table>
								{{#if showDetails}}
									<table class="table" id="subsidy-report-table" style="display:block;overflow-x:auto;padding-bottom:10px;">
										<tr>
											<th class='no-wrap'>Name</th>
											<th class='no-wrap'>Type</th>
											<th class='no-wrap'>Waitlist Date</th>
											<th class='no-wrap'>Enrollment Date</th>
											<th class='no-wrap'>Withdraw Date</th>
											<th class='no-wrap'>Center</th>
											<th class='no-wrap'>Group</th>
											<th class='no-wrap'>Deactivated</th>
											<th class='no-wrap'>Deactivated Reason</th>
											{{#if hasCustomization "report/classList/enabled"}}
												{{#if showTuition}}
												<th class='no-wrap text-right'>Tuition</th>
												{{/if}}
												<th class='no-wrap text-right'>FTE Total</th>
												<th class='no-wrap text-right'>Lost Tuition</th>
											{{/if}}
										</tr>
										{{#each reportRows}}
											<tr>
												<td data-cy="enrolled-child-name" class='no-wrap'>{{firstName}} {{lastName}}</td>
												<td data-cy="enrolled-child-type" class='no-wrap'>{{type}}</td>
												<td data-cy="enrolled-waitlist-date" class='no-wrap'>{{waitlistDate}}</td>
												<td data-cy="enrollment-date" class='no-wrap'>{{enrollmentDate}}</td>
												<td data-cy="withdraw-date" class='no-wrap'>{{withdrawDate}}</td>
												
												<td data-cy="org-name" class='no-wrap'>{{orgName}}</td>
												<td data-cy="group-name" class='no-wrap'>{{groupName}}</td>
												<td data-cy="deactivated-tag" class='no-wrap'>{{deactivated}}</td>
												<td data-cy="deactivated-reason" class='no-wrap'>{{deactivationReason}}</td>
												{{#if hasCustomization "report/classList/enabled"}}
													{{#if showTuition}}
													<td data-cy="tuition" class='no-wrap text-right'>{{formatCurrency tuition}}</td>
													{{/if}}
													<td data-cy="fte-total" class='no-wrap text-right'>{{fteTotal}}</td>
													<td data-cy="lost-tuition" class='no-wrap text-right'>{{#if withdrawDate}}{{formatCurrency lostTuition}}{{/if}}</td>
												{{/if}}
											</tr>
										{{/each}}
									</table>
									{{#if showWithdrawnChildrenOnly}}
									<table class="table" style="display:block;overflow-x:auto;padding-bottom:10px;">
										<tr>
											<td style="padding-right:15px"><label>Total FTE Lost</label></td>
		
											<td data-cy="total-fte-lost" class='text-right'>{{withdrawnFteSummary.fteTotal}}</td>
										</tr>
										<tr>
											<td style="padding-right:15px"><label>Total Tuition Lost</label></td>
		
											<td data-cy="total-tuition-lost" class='text-right'>{{formatCurrency withdrawnFteSummary.tuition}}</td>
										</tr>
									</table>
									{{/if}}
								{{else}}
									<div class='text-center'>
										<button daya-cy="show-detail-btn" class='btn btn-primary' id="btnShowDetail">Show Detail</button>
									</div>
								{{/if}}
							{{/if}}
						</div>
					</div>
				</div>

			</div>
		</div>
	</div>
</template>
