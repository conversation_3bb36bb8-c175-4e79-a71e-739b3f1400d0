<template name="_timeCardHistoryModal">
  <div id="_timeCardHistoryModal" class="modal fade">
    <div class="modal-dialog modal-dialog-scrollable modal-lg" style="width: 100%;">
      <div class="modal-content" style="height:auto;min-height: 100%;">
        <div class="modal-header">
          <h5 class="modal-title" id="exampleModalLabel">Time Card History</h5>
          <div class="d-flex align-items-center">
            <div class="btn btn-icon btn-clean btn-lg" data-dismiss="modal" aria-label="Close" >
              <span class="fad-regular fad-primary fad fa-times"></span>
            </div>
          </div>
        </div>
        {{#if loaded}}
          <div class="modal-body">
            {{#if hasOriginalCheckInMoment}}
              <div>
                <h4 data-cy="checkin-name-history">{{formatName}}: Original CheckIn Moment Details</h4>
              </div>
              <div class="row mb-1">
                <span class="col-xl-3 col-lg-3 text-right font-weight-bold">CheckIn Date and Time</span>
                <span data-cy="checkin-date-time" class="col-lg-6 col-md-9 col-sm-12">{{timeCard.originalCheckInMoment.date}} {{timeCard.originalCheckInMoment.time}}</span>
              </div>
              <div class="row mb-1">
                <span class="col-xl-3 col-lg-3 text-right font-weight-bold">Selected Pay Type</span>
                <span data-cy=selected-pay-type class="col-lg-6 col-md-9 col-sm-12">{{checkInSelectedPay}}</span>
              </div>
              <div class="row">
                <span class="col-xl-3 col-lg-3 text-right font-weight-bold">Created By</span>
                <span data-cy="created-by-checkin" class="col-lg-6 col-md-9 col-sm-12">{{getOriginalCheckInCreator}} on {{getOriginalCheckInCreateDateTime}}</span>
              </div>
            {{/if}}
            {{#if hasOriginalCheckOutMoment}}
              <div class="separator separator-dashed my-8"></div>
              <div>
                <h4 data-cy="checkout-name-history">{{formatName}}: Original CheckOut Moment Details</h4>
              </div>
              <div class="row mb-1">
                <span class="col-xl-3 col-lg-3 text-right font-weight-bold">CheckOut Date and Time</span>
                <span data-cy="checkout-date-time" class="col-lg-6 col-md-9 col-sm-12">{{timeCard.originalCheckOutMoment.date}} {{timeCard.originalCheckOutMoment.time}}</span>
              </div>
              <div class="row mb">
                <span class="col-xl-3 col-lg-3 text-right font-weight-bold">Created By</span>
                <span data-cy="created-by-checkout" class="col-lg-6 col-md-9 col-sm-12">{{getOriginalCheckOutCreator}} on {{getOriginalCheckOutCreateDateTime}}</span>
              </div>
            {{/if}}
            {{#if hasUpdateLog}}
              <div class="separator separator-dashed my-8"></div>
              <div>
                <h4>Time Card Update Log</h4>
              </div>
              {{#each uLog in getUpdateLogs}}
                <div class="row mb-1">
                  <span class="col-xl-3 col-lg-3 text-right font-weight-bold">Modified Description</span>
                  <span data-cy="modified-log" class="col-lg-6 col-md-9 col-sm-12">{{getModifiedElement uLog}}</span>
                </div>
                {{#if getDateUpdate uLog}}
                  <div class="row mb-1">
                    <span class="col-xl-3 col-lg-3 text-right font-weight-bold">Date Field</span>
                    <span data-cy="modified-date" class="col-lg-6 col-md-9 col-sm-12">{{getDateUpdate uLog}}</span>
                  </div>
                {{/if}}
                {{#if getTimeUpdate uLog}}
                  <div class="row mb-1">
                    <span class="col-xl-3 col-lg-3 text-right font-weight-bold">Time Field</span>
                    <span data-cy="modified-time" class="col-lg-6 col-md-9 col-sm-12">{{getTimeUpdate uLog}}</span>
                  </div>
                {{/if}}
                {{#if getSelectedPayUpdate uLog}}
                  <div class="row mb-1">
                    <span class="col-xl-3 col-lg-3 text-right font-weight-bold">Selected Pay Field</span>
                    <span data-cy="modified-pay-field" class="col-lg-6 col-md-9 col-sm-12">{{getSelectedPayUpdate uLog}}</span>
                  </div>
                {{/if}}
              {{/each}}
            {{/if}}
            {{#if isVoided}}
              <div class="separator separator-dashed my-8"></div>
              <div>
                  <h4>{{isVoidedBy}}: Voided Time Card</h4>
              </div>
              <div class="row mb-1">
                  <span class="col-xl-3 col-lg-3 text-right font-weight-bold">Void Reason</span>
                  <span class="col-lg-6 col-md-9 col-sm-12">{{getVoidReason}}</span>
              </div>
              <div class="row mb-1">
                  <span class="col-xl-3 col-lg-3 text-right font-weight-bold">Voided By</span>
                  <span class="col-lg-6 col-md-9 col-sm-12">{{getVoidedByPersonName}} on {{getVoidTimeAndDate}}</span>
              </div>
            {{/if}}
          </div>
        {{/if}}
        <div class="modal-footer">
          <button data-cy="close-history-btn" type="button" class="btn btn-secondary font-weight-bolder" data-dismiss="modal">Close</button>
        </div>
      </div>
    </div>
  </div>
</template>





<template name="timeCardHistoryModal">
				<div class="modal" id="timeCardHistoryModal">
					<div class="modal-dialog">
						<div class="modal-content">
						
							<div class="modal-header">
								<button type="button" class="close" data-dismiss="modal" aria-label="Close">
      						<span aria-hidden="true">×</span>
      					</button>
      					<h4 class="modal-title">Time Card History</h4>
							</div>
              {{#if loaded}}
							<div class="modal-body">
                
                {{#if hasOriginalCheckInMoment}}
	                <div>
	                  <h4>{{formatName}}: Original CheckIn Moment Details</h4>
									</div>
	                <div>
	                  <label>CheckIn Date and Time</label>
	                  <span>{{timeCard.originalCheckInMoment.date}} {{timeCard.originalCheckInMoment.time}}</span>
	                </div>
									<div>
										<label>Selected Pay Type</label>
										<span>{{checkInSelectedPay}}</span>
									</div>
	                <div>
	                  <label>Created By</label>
	                  <span>{{getOriginalCheckInCreator}} on {{getOriginalCheckInCreateDateTime}}</span>
	                </div>
                {{/if}}
								{{#if hasOriginalCheckOutMoment}}
									<div class="separator separator-dashed" style="margin:12px;"></div>
									<div>
										<h4>{{formatName}}: Original CheckOut Moment Details</h4>
									</div>
									<div>
										<label>CheckOut Date and Time</label>
										<span>{{timeCard.originalCheckOutMoment.date}} {{timeCard.originalCheckOutMoment.time}}</span>
									</div>
									<div>
										<label>Created By</label>
										<span>{{getOriginalCheckOutCreator}} on {{getOriginalCheckOutCreateDateTime}}</span>
									</div>
								{{/if}}
								
								{{#if hasUpdateLog}}
									<div class="separator separator-dashed" style="margin:12px;"></div>
									<div>
										<h4>Time Card Update Log</h4>
									</div>
									{{#each uLog in getUpdateLogs}}
										<div>
											<label>Modified Description</label>
											<span>{{getModifiedElement uLog}}</span>
										</div>
										{{#if getDateUpdate uLog}}
											<div>
												<label>Date Field</label>
												<span>{{getDateUpdate uLog}}</span>
											</div>
										{{/if}}
										{{#if getTimeUpdate uLog}}
											<div>
												<label>Time Field</label>
												<span>{{getTimeUpdate uLog}}</span>
											</div>
										{{/if}}
										{{#if getSelectedPayUpdate uLog}}
											<div>
												<label>Selected Pay Field</label>
												<span>{{getSelectedPayUpdate uLog}}</span>
											</div>
										{{/if}}
										<div class="separator separator-dashed" style="margin-bottom:4px;"></div>
									{{/each}}
								{{/if}}

							</div>
              {{/if}}
						</div>
					</div>
				</div>
			
</template>
