import { Template } from 'meteor/templating';
import { Meteor } from 'meteor/meteor';
import { ReactiveVar } from 'meteor/reactive-var';
import _ from '../../../lib/util/underscore';
import './registrationFlowStep4.html';
import { MiscUtils } from '../../../lib/util/miscUtils';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';

import { AVAILABILITIES_PLACEHOLDER } from '../../../lib/constants/enrollmentConstants';
import { Log } from '../../../lib/util/log';
import { RegistrationUtils } from '../../../lib/util/registrationUtils';
import { parentSource } from '../../../lib/constants/registrationConstants';
import moment from 'moment-timezone';
import { selectedPlans, validNext, checkValidity } from './registrationFlow'
import logger from '../../../imports/winston/index';
import './_planDescription';
import './_daySelection';
import './_effectiveDate';
import { EnrollmentUtils } from '../../../lib/util/enrollmentUtils';

Template.registrationFlowStep4.created = function () {
    this.currentStep = new ReactiveVar(this.data.currentStep);
    this.bundles = new ReactiveVar([]);
    this.availablePlans = new ReactiveVar([]);
    this.availableTimePeriods = new ReactiveVar([])
    this.availableBillingFrequencies = new ReactiveVar([]);
    this.planToAdd = new ReactiveVar({});
    this.planAdded = new ReactiveVar(false);
    this.currentPlans = new ReactiveVar(this.data.savedData.plans[this.data.currentChildIndex] || []);
    this.currentBundle = new ReactiveVar(null);
    this.regularPrice = new ReactiveVar(0);
    this.bundledPrice = new ReactiveVar(0);
    this.bundleSavings = new ReactiveVar(0);
    this.plansWithScaledAmounts = new ReactiveVar(this.currentPlans.get().filter(plan => plan.scaledAmounts.length));
    this.orgTimezone = new ReactiveVar('America/New_York');
    this.showWeekends = new ReactiveVar(false);
    this.designation = FlowRouter.getQueryParam('designation') || null;
    this.wasValid = new ReactiveVar(false);
    this.showSavings = new ReactiveVar(false);
    this.currentBundle = new ReactiveVar(false);

    const orgId = FlowRouter.getQueryParam('orgId');
    Meteor.callAsync('getOrgInformation', orgId, true, this.designation).then((result) => {
        logger.info('getOrgInformation > registrationFlowStep4', { 'getOrgInformation result': result });
        if (result) {
            const {plans, frequencies, timePeriods, timezone, bundles, showWeekends} = result;
            this.bundles.set(bundles);
            this.availablePlans.set(plans);
            this.availableTimePeriods.set(timePeriods);
            this.orgTimezone.set(timezone);
            this.availableBillingFrequencies.set(frequencies);
            this.showWeekends.set(showWeekends);
        }
    }).catch((error) => {
        mpSwal.fire('Error', error.reason || error.message, 'error');
        logger.error('getOrgInformation > registrationFlowStep4', { 'Error reason': error.reason, 'Error message': error.message });
        Log.error(error);
    });

    this.autorun(() => {
        const showSavingsObject = RegistrationUtils.showSavingsForBundles(
            this.currentPlans.get(),
            [],
            this.bundles.get(),
            this.availablePlans.get()
        );
        this.showSavings.set(showSavingsObject.showSavings);
        this.currentBundle.set(showSavingsObject.bundle);
    });
};

Template.registrationFlowStep4.rendered = function () {
    validNext.set(checkValidity(this));
    this.wasValid.set(validNext.get());
};

Template.registrationFlowStep4.helpers({
    planAmount: (planId) => {
        const plans = Template.instance().plansWithScaledAmounts.get();
        const planIndex = plans.findIndex(obj => planId === obj._id);
        return plans[planIndex]["amount"]?.toFixed(2) ?? '0.00';
    },
    isScaledAmount : (planId) => {
        const plansInBundle = Template.instance().currentBundle.get()?.plans ?? [];
        const plans = Template.instance().plansWithScaledAmounts.get();
        const scaledPlans = plans.filter((plan) => {if(!plansInBundle.includes(plan._id)) return plan})
        const planIndex = scaledPlans.findIndex(obj => planId === obj._id);
        return planIndex >= 0;
    },
    isAllDaysSelected: (planId) => {
        const plans = Template.instance().plansWithScaledAmounts.get();
        const totalNumberOfDays = plans.find(plan => plan._id === planId).scaledAmounts.length;
        return !(RegistrationUtils.numOfCheckedBoxesInPlan(planId) >= totalNumberOfDays);
    },
    validationMessage: () => {
        return Template.instance().validationMessage.get();
    },
    validationClass: () => {
        return Template.instance().validationClass.get();
    },
    plans: () => {
        const timezone = Template.instance().orgTimezone.get();
        const availableTimePeriods = Template.instance().availableTimePeriods.get();
        const orgHasWeekends = Template.instance().showWeekends.get();
        const instance = Template.instance();

        if (!timezone || !availableTimePeriods.length) {
            return [];
        }
        const plans = _.clone(RegistrationUtils.getPlansForDaySelect(Template.instance().currentPlans.get()));
        _.each(plans, plan => {
            const startDate = plan.isRequiredAdvanceNotice ? moment().tz(timezone).add(7, 'days') : moment().tz(timezone);
            plan.serviceStartDate = startDate.valueOf();
            plan.serviceEndDate = null;

            if (plan.details && plan.details.timePeriod) {
                const planTimePeriod = availableTimePeriods.find(tp => tp._id === plan.details.timePeriod);
                if (planTimePeriod && planTimePeriod.startDate && planTimePeriod.endDate) {
                    const timePeriodStart = moment.tz(planTimePeriod.startDate, timezone);
                    plan.serviceStartDate = startDate.isSameOrBefore(timePeriodStart) ? timePeriodStart.valueOf() : startDate.valueOf();
                    plan.serviceEndDate = moment.tz(planTimePeriod.endDate, timezone).valueOf();
                }
            }
        });

        let pendingCalls = plans.length;

        const checkAllCallsCompleted = () => {
            pendingCalls--;
            if (pendingCalls === 0) {
                instance.findAll('.select-multi-group').forEach((group) => {
                    const firstCheckbox = group.querySelector('.day-checkbox');
                    if (firstCheckbox) {
                        firstCheckbox.dispatchEvent(new Event('revalidate', { bubbles: true }));
                    }
                });
                // All calls are complete, restore 'validNext'
                validNext.set(instance.wasValid.get() || checkValidity(instance));
            }
        };

        const calculateAvailability = (plan, startDate) => {
            validNext.set(false);
            Meteor.callAsync('getOrgScheduleTypesAvailabilities', FlowRouter.getQueryParam('orgId'), startDate, plan.details?.timePeriod ?? null).then((result) => {
                if (result) {
                    const availabilities = result[plan.details?.scheduleType ?? '0'] ?? EnrollmentUtils.getDefaultAvailabilities(orgHasWeekends);

                    logger.info('registrationFlowStep4 > getOrgScheduleTypesAvailabilities result', { 'Plan ID': plan._id, 'Result': result });

                    if (instance?.data?.savedData?.children?.length > 1 && instance?.data?.savedData?.plans?.length) {
                        const childIndex = instance.data.currentChildIndex;
                        for (let i = 0; i < instance.data.savedData.plans.length; i++) {
                            if (i !== childIndex) {
                                for (let j = 0; j < instance.data.savedData.plans[i].length; j++) {
                                    const savedPlan = instance.data.savedData.plans[i][j];
                                    if (savedPlan.details?.scheduleType === plan.details?.scheduleType) {
                                        EnrollmentUtils.adjustAvailabilitiesBasedOnSelectedDays(savedPlan.selectedDays, availabilities, orgHasWeekends);
                                    }
                                }
                            }
                        }
                    }

                    const elements = document.querySelectorAll('[data-type="days"][data-id="' + plan._id + '"]');
                    const numberOfDays = orgHasWeekends ? 7 : 5;
                    const indexOffset = orgHasWeekends ? 0 : 1;
                    for (let i = 0; i < numberOfDays; i++) {
                        // If already disabled because of plan offering, don't re-enable it due to availability.
                        const disabledDueToOffer = elements[i] && elements[i].disabled && plan.programOfferedOn && !plan.programOfferedOn.includes(elements[i].name);

                        if (!elements[i] || disabledDueToOffer) {
                            continue;
                        }

                        if (availabilities[i + indexOffset] <= 0) {
                            elements[i].checked = false;
                            // Trigger a custom event to notify the child template
                            elements[i].dispatchEvent(new Event('revalidate', { bubbles: true }));
                        }

                        elements[i].disabled = availabilities[i + indexOffset] <= 0;
                        const note = document.querySelectorAll('[data-type="unavailable-note"][data-id="' + plan._id + '"]')[i];
                        if (elements[i].disabled && note) {
                            note.classList.remove('d-none');
                        } else if (note) {
                            note.classList.add('d-none');
                        }
                    }
                }
            }).catch((error) => {
                console.log("error in getGenderField", error);
            }).finally(() => {
                checkAllCallsCompleted();
            });
        };

        _.each(plans, plan => {
            setTimeout(() => {
                const startDateValue = $(`.date-picker[data-id=${plan._id}]`).val();
                const startDate = startDateValue ?? moment.tz(plan.serviceStartDate, timezone).format('MM/DD/YYYY');
                const startDatePicker = $(`.date-picker[data-id=${ plan._id }]`).datepicker({
                    autoclose: true,
                    startDate: startDate,
                    endDate: plan.serviceEndDate ? moment.tz(plan.serviceEndDate, timezone).format('MM/DD/YYYY') : null
                });
                startDatePicker.on('changeDate', (ev) => {
                    ev.target.dispatchEvent(new Event('input', { 'bubbles': true }));
                    calculateAvailability(plan, ev.target.value);
                });
                startDatePicker.on('hide', (ev) => {
                    ev.target.dispatchEvent(new Event('input', {'bubbles': true}));
                });
                calculateAvailability(plan, startDate);
            }, 400);
        });

        const bundlePlansToBottom = moveBundlePlansToBottom();
        logger.info('registrationFlowStep4 > moveBundlePlansToBottom | Selected Plans', { 'bundlePlansToBottom': bundlePlansToBottom });
        return bundlePlansToBottom;
    },
    showSavings: () => {
        return Template.instance().showSavings.get();
    },
    getPlanToAdd: () => {
        if (Template.instance().planToAdd.get()?._id) {
            logger.info('registrationFlowStep4 > getPlanToAdd: Returning existing planToAdd', { 'planToAdd': Template.instance().planToAdd.get() });
            return Template.instance().planToAdd.get();
        }
        const currentPlans = Template.instance().currentPlans.get();
        const currentPlanIds = currentPlans.map(p => p._id);
        const availablePlans = Template.instance().availablePlans.get();
        const bundles = Template.instance().bundles.get();
        if (!availablePlans.length || !bundles.length || twoMatchedPlans(currentPlanIds, bundles, availablePlans).length) {
            logger.info('registrationFlowStep4 > getPlanToAdd: No available plans or bundles or matched plans found', { availablePlans, bundles, currentPlanIds });
            return null;
        }

        const possibleBundles = oneMatchedPlan(currentPlanIds, bundles, availablePlans);
        if (!possibleBundles.length) {
            logger.info('registrationFlowStep4 > getPlanToAdd: No possible bundles found', { currentPlanIds });
            return null;
        }

        possibleBundles.sort((a, b) => a.scaledAmounts[4][4] - b.scaledAmounts[4][4]);

        const planToAdd = possibleBundles[0].plans.map(id => availablePlans.find(p => p._id === id)).filter(p => !currentPlanIds.includes(p._id))[0];
        const otherPlanInBundle = possibleBundles[0].plans.map(id => availablePlans.find(p => p._id === id)).filter(p => currentPlanIds.includes(p._id))[0];
        planToAdd.selectedDays = RegistrationUtils.setDefaultSelectedDaysByPlanConfig(planToAdd);
        planToAdd.otherPlanDescription = otherPlanInBundle.description;

        logger.info('registrationFlowStep4 > getPlanToAdd: New planToAdd determined', { 'planToAdd': planToAdd });
        Template.instance().planToAdd.set(planToAdd);
        return Template.instance().planToAdd.get();
    },
    showLinkBundle: () => {
        const plans = Template.instance().currentPlans.get();
        const planIds = plans.map(p => p._id);
        const bundles = Template.instance().bundles.get();
        const availablePlans = Template.instance().availablePlans.get();
        return oneMatchedPlan(planIds, bundles, availablePlans).length > 0 && !twoMatchedPlans(planIds, bundles, availablePlans).length;
    },
    planAdded: () => {
        return Template.instance().planAdded.get();
    },
    regularPrice: () => {
        RegistrationUtils.getRegularPriceForBundleSavings(Template.instance(), parentSource.INITIAL_REGISTRATION);
        const regularPrice = Template.instance().regularPrice.get();
        logger.info('registrationFlowStep4 > regularPrice: Regular price calculated', { 'regularPrice': regularPrice });
        return regularPrice;
    },
    bundledPrice: () => {
        RegistrationUtils.calculateAndSetBundledPrice(Template.instance(), parentSource.INITIAL_REGISTRATION, selectedPlans);
        const bundledPrice = Template.instance().bundledPrice.get();
        logger.info('registrationFlowStep4 > bundledPrice: Bundled price calculated', { 'bundledPrice': bundledPrice });
        return bundledPrice;
    },
    bundleSavings: () => {
        return Template.instance().bundleSavings.get();
    },
    whiteLabelLogoOverride: function() {
        const host = window.location.host.split(".")[0];
        const enabledSites = Meteor.settings.public.whitelabel && Meteor.settings.public.whitelabel.enabled_sites;
        const meteorUser = Meteor.user(), currentOrg = meteorUser && meteorUser.fetchOrg();
        if (currentOrg && _.deep(currentOrg, "whiteLabel.assets.appLogo")) {
            return {
                small: currentOrg.whiteLabel.assets.appLogoSmall,
                large: currentOrg.whiteLabel.assets.appLogo
            };
        } else if (enabledSites && enabledSites.indexOf(host) > -1) {
            return {
                small: Meteor.settings.public.whitelabel[host].small_logo,
                large: Meteor.settings.public.whitelabel[host].large_logo
            }
        }
    },
    parentData: () => {
        // Object to pass to child templates so that they can access the reactive vars from this template.
        return {
            selectedPlans: selectedPlans.get(),
            availablePlans: Template.instance().availablePlans.get(),
            bundles: Template.instance().bundles.get(),
            orgTimezone: Template.instance().orgTimezone.get(),
            availableTimePeriods: Template.instance().availableTimePeriods.get(),
            availableBillingFrequencies: Template.instance().availableBillingFrequencies.get(),
            showWeekends: Template.instance().showWeekends.get(),
        }
    },
    validNext: () => {
        return validNext;
    }
});

Template.registrationFlowStep4.events({
    'submit .registration-card': function(e) {
        e.preventDefault();
    },
    'input [name="startDate"]': (e, i) => {
        validateDateInput(e, i);
    },
    'click #btnAddMissingPlan': function(e, i) {
        e.preventDefault();
        // Add the plan to the current plans
        const currentPlans = i.currentPlans.get();
        currentPlans.push(i.planToAdd.get());
        i.currentPlans.set(currentPlans);
        // Mark a plan as added
        i.planAdded.set(true);
        logger.info('registrationFlowStep4 > click #btnAddMissingPlan: Added plan to current plans', { 'New plans after adding the plan': currentPlans });
        selectedPlans.set(currentPlans);
        revalidate(i);
    },
    'click #btnRemovePlan': function(e, i) {
        e.preventDefault();
        // Remove the plan from the current plans and remove bundlePlanId from all plans
        let currentPlans = i.currentPlans.get();
        currentPlans = currentPlans.map(p => {
            delete p.bundlePlanId;
            return p;
        });
        currentPlans = currentPlans.filter(p => p._id !== i.planToAdd.get()._id);
        i.currentPlans.set(currentPlans);
        // Mark a plan as not added
        i.planAdded.set(false);
        logger.info('registrationFlowStep4 > click #btnRemovePlan: Plan removed from current plans', { 'New plans after removing the plan': currentPlans });
        selectedPlans.set(currentPlans);
        revalidate(i);
    },
    'click .day-checkbox': function (e, i) {
        const planId = e.currentTarget.dataset.id;
        RegistrationUtils.updateTotalAmountForScaledAmounts(i, planId, RegistrationUtils.numOfCheckedBoxesInPlan(planId));
        if (i.currentBundle.get()) {
            RegistrationUtils.getRegularPriceForBundleSavings(i, parentSource.INITIAL_REGISTRATION);
            RegistrationUtils.calculateAndSetBundledPrice(i, parentSource.INITIAL_REGISTRATION, selectedPlans);
            logger.info('registrationFlowStep4 > click .day-checkbox: Updated total amounts for scaled amounts', { planId });
        }
    },
    'focus input': (e) => {
        MiscUtils.sendHeight('xl')
        validateInput(e);
    },
    'focus [name="startDate"]': (e, i) => {
        validateDateInput(e, i);
    }
});

/**
 * Moves bundle plans to the bottom of the plans list.
 *
 * This function retrieves the current bundle plans and the available plans for the day.
 * It then reorders the plans such that the plans included in the current bundle
 * are moved to the end of the plans list, maintaining the order of other plans.
 *
 * @function moveBundlePlansToBottom
 * @returns {Array} The reordered array of plans with bundle plans moved to the bottom.
 */
function moveBundlePlansToBottom() {
    const plansInBundle = Template.instance().currentBundle.get()?.plans ?? [];
    const plansTemp = RegistrationUtils.getPlansForDaySelect(Template.instance().currentPlans.get());

    plansInBundle.forEach((planId) => {
        plansTemp.push(plansTemp.splice(plansTemp.findIndex(plan => plan._id === planId), 1)[0]);
    });

    return plansTemp;
}


/**
 * Finds bundles that contain exactly one matched plan from the provided plan IDs
 * and checks if those plans are valid based on their registration dates.
 *
 * This function filters the provided bundles to find those that have exactly one plan
 * matching the given plan IDs. It further checks if the plans are valid based on
 * their registration start and end dates considering the organization's timezone.
 *
 * @function oneMatchedPlan
 * @param {Array<string>} planIds - An array of plan IDs to match against.
 * @param {Array<Object>} bundles - An array of bundle objects, each containing a list of plan IDs.
 * @param {Array<Object>} availablePlans - An array of available plan objects, each containing details with registration dates.
 * @returns {Array<Object>} An array of matched bundles that contain exactly one valid plan.
 */
function oneMatchedPlan(planIds, bundles, availablePlans) {
    const matchedBundles = _.filter(bundles, b => {
        const matchedPlans = b.plans.filter(p => planIds.includes(p));
        return matchedPlans.length === 1;
    });

    return _.filter(matchedBundles, b => {
        const validPlanIds = b.plans.filter(id => {
            const plan = availablePlans.find(p => p._id === id);
            if (!plan) {
                return false;
            }
            const regEndDate = plan.details?.regEndDate;
            const regStartDate = plan.details?.regStartDate;
            const todayDate = new Date();

            const timezone = Template.instance().orgTimezone.get();
            const timezoneTodayDate = startOfDayInTimezone(todayDate, timezone);
            const timezoneRegEndDate = regEndDate ? new Date(new Date(regEndDate).toLocaleString("en-US", { timeZone: timezone })) : null;
            const timezoneRegStartDate = regStartDate ? new Date(new Date(regStartDate).toLocaleString("en-US", { timeZone: timezone })) : null;

            return (!timezoneRegEndDate || timezoneRegEndDate >= timezoneTodayDate) && (!timezoneRegStartDate || timezoneRegStartDate <= timezoneTodayDate);
        });
        return validPlanIds.length === 2;
    });
}

/**
 * Finds bundles that contain exactly two matched plans from the provided plan IDs.
 *
 * This function filters the provided bundles to find those that have exactly two plans
 * matching the given plan IDs. It further checks if those plans are valid based on their
 * existence in the available plans list.
 *
 * @function twoMatchedPlans
 * @param {Array<string>} planIds - An array of plan IDs to match against.
 * @param {Array<Object>} bundles - An array of bundle objects, each containing a list of plan IDs.
 * @param {Array<Object>} availablePlans - An array of available plan objects, each containing an ID.
 * @returns {Array<Object>} An array of matched bundles that contain exactly two valid plans.
 */
function twoMatchedPlans(planIds, bundles, availablePlans) {
    const matchedBundles = _.filter(bundles, b => {
        const matchedPlans = b.plans.filter(p => planIds.includes(p));
        return matchedPlans.length === 2;
    });

    return _.filter(matchedBundles, b => {
        const validPlanIds = b.plans.filter(id => availablePlans.find(p => p._id === id) !== undefined);
        return validPlanIds.length === 2;
    });
}

/**
 * Revalidates the provided object after a short delay.
 *
 * This function uses a timeout to delay the validation process by 200 milliseconds.
 * It checks the validity of the object `i` and updates the relevant state
 * properties accordingly. Specifically, it sets `validNext` to the result of
 * `checkValidity(i)` and then updates `i.wasValid` to reflect the new validity status.
 *
 * @function revalidate
 * @param {Object} i - An object containing state properties related to validation.
 * @param {Function} i.wasValid.set - Function to set the validity status of the object.
 * @returns {void} This function does not return a value; it modifies the state in place.
 */
function revalidate(i) {
    setTimeout(function() {
        validNext.set(checkValidity(i));
        i.wasValid.set(validNext.get());
    }, 200, validNext);
}

/**
 * Validates the input from an event and displays or hides validation messages accordingly.
 *
 * This function retrieves the ID and type of the input element from the event,
 * then uses a utility function to validate the input. Based on the validation result,
 * it shows or hides the corresponding validation message element.
 *
 * @function validateInput
 * @param {Event} event - The event object triggered by the input element.
 * @returns {void} This function does not return a value; it modifies the visibility of validation messages.
 */
function validateInput(event) {
    const id = $(event.currentTarget).data('id');
    const type = $(event.currentTarget).data('type');
    const validation = MiscUtils.validateInput(event);
    if (!validation.valid) {
        $(`#validate-${type}-${id}`).show();
    } else {
        $(`#validate-${type}-${id}`).hide();
    }
}

/**
 * Validates the date input from an event and updates the current plans accordingly.
 *
 * This function retrieves the timezone from the instance and validates the date input
 * from the event. It checks if the date is valid, whether it falls within the service
 * dates of the selected time period, and ensures that the start date is appropriate.
 * If the validation fails, it sets an error message and shows the corresponding
 * validation message element; otherwise, it hides the message and updates the plans.
 *
 * @param {Event} event - The event object triggered by the date input element.
 * @param {Object} instance - An object representing the current instance containing
 *                            necessary state and methods.
 * @returns {void} This function does not return a value; it modifies the state of the
 *                 current plans in place and updates the visibility of validation messages.
 */
export function validateDateInput(event, instance) {
    let timezone = null;
    try {
        timezone = instance.orgTimezone.get();
    } catch (e) {
        timezone = instance.orgTimezone;
    }
    const id = $(event.currentTarget).data('id');
    const type = $(event.currentTarget).data('type');
    const value = $(event.currentTarget).val();
    const validation = MiscUtils.validateInput(event);
    const plans = instance.currentPlans.get();
    const index = plans.findIndex(plan => plan._id === id);
    if (index === -1) {
        return;
    }
    if (!validation.valid) {
        plans[index].dateErrorMessage = validation.message;
        instance.currentPlans.set(plans);
        $(`#validate-${type}-${id}`).show();
        return;
    }
    const momentValue = moment.tz(value, 'MM/DD/YYYY', timezone).valueOf();
    if (plans[index].details && plans[index].details.timePeriod) {
        let timePeriods = [];
        try {
            timePeriods = instance.availableTimePeriods.get();
        } catch (e) {
            timePeriods = instance.availableTimePeriods;
        }

        const foundTimePeriod = timePeriods.find(tp => tp._id === plans[index].details.timePeriod);

        if (foundTimePeriod && (momentValue < foundTimePeriod.startDate || momentValue > foundTimePeriod.endDate)) {
            plans[index].dateErrorMessage = 'Start date must be between the service dates.';
            instance.currentPlans.set(plans);
            $(`#validate-${type}-${id}`).show();
            return;
        }
    } else {
        const defaultStartDate = plans[index].isRequiredAdvanceNotice
            ? moment().tz(timezone).startOf('day').add(7, 'd').valueOf()
            : moment().tz(timezone).startOf('day').valueOf();

        if (momentValue < defaultStartDate) {
            plans[index].dateErrorMessage = plans[index].isRequiredAdvanceNotice
                ? 'Start date must be 7 days from today.'
                : 'Start date cannot be in the past.';
            instance.currentPlans.set(plans);
            $(`#validate-${type}-${id}`).show();
            return;
        }
    }
    delete plans[index].dateErrorMessage;
    instance.currentPlans.set(plans);
    $(`#validate-${type}-${id}`).hide();
}

/**
 * Returns a Date object representing the start of the day (midnight)
 * in the specified timezone for a given date.
 *
 * This function converts the provided date to the specified timezone
 * and sets the time to midnight (00:00:00) for that date.
 * This can be useful for date comparisons and calculations that
 * are sensitive to time zones.
 *
 * @function startOfDayInTimezone
 * @param {Date} date - The date object for which to find the start of the day.
 * @param {string} timezone - A string representing the time zone (e.g., 'America/New_York').
 * @returns {Date} A new Date object set to midnight of the specified date in the given timezone.
 */
export function startOfDayInTimezone(date, timezone) {
    const dateString = date.toLocaleString("en-US", { timeZone: timezone });
    const midnightDate = new Date(dateString);
    midnightDate.setHours(0, 0, 0, 0);
    return midnightDate;
}