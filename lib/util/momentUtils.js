import { Meteor } from 'meteor/meteor';
import moment from 'moment-timezone';
import { AvailableCustomizations } from '../customizations';
import { DefaultCheckedOutMomentTypes, ExpandedCheckedOutMomentTypes, LateCheckInEarlyDropOffModes } from '../constants/momentTypeConstants';
import { People } from '../collections/people';
import { Orgs } from '../collections/orgs';
import { MomentDefinitions } from '../collections/momentDefinitions';
import { Reservations } from '../collections/reservations';

export class MomentUtils {
    /**
     * Processes the people tagged in a moment, based on the tagging settings, media requirements, and organization settings.
     *
     * @param {Object} momentData - The data related to the moment being processed.
     * @param {boolean} momentData.tagAllInGroup - Whether to tag all members in a group.
     * @param {boolean} momentData.tagEntireOrg - Whether to tag everyone in the organization.
     * @param {string[]} momentData.taggedPeople - List of tagged people, groups, roles, or designations.
     * @param {string} momentData.momentType - The type of moment (e.g., "alert").
     * @param {boolean} momentData.tagOnlyCheckins - Whether to only tag people who have checked in.
     * @param {string} momentData.tagGroupId - The ID of the group to tag.
     * @param {Object} momentData.mediaFiles - Media files attached to the moment.
     * @param {Object} momentData.attachedMedia - Media attached to the moment.
     * @param {boolean} editMomentStatus - Indicates if the moment is being edited.
     * @param {Object} commonData - Common data, including sorting and timestamp.
     * @param {number} commonData.sortStamp - A timestamp used for sorting moments.
     * @param {boolean} checkMediaRequirements - Whether to enforce media requirements.
     * @returns {Object} - An object containing the list of tagged people and whether individual media review is required.
     * @throws {Meteor.Error} - Throws an error if no one is tagged, media requirements are violated, or people are not checked in when required.
     */
    static async processTaggedPeople(momentData, editMomentStatus, commonData, checkMediaRequirements) {
        const org = await Orgs.current();
        const timezone = org.getTimezone();
        const mediaReviewOrgWide = org.mediaRequirement?.mediaReviewRequired;
        const taggedPeople = [];

        if (momentData.tagAllInGroup) {
            momentData.taggedPeople = [`group|${momentData.tagGroupId}`];
        }

        if (momentData.tagEntireOrg) {
            momentData.taggedPeople = ['org|org'];
        }

        if (!momentData.taggedPeople) {
            throw new Meteor.Error("At least one person must be tagged.");
        }

        if (momentData.momentType !== "alert") {
            momentData.tagOnlyCheckins = true;
        }

        let mediaNotAllowed = "";
        let individualMediaReviewRequired = false;

        const processPerson = (person) => {
            if (!person) {
                return;
            }

            if (person?.mediaRequirements?.noMediaAllowed === "Yes" || person?.profileData?.mediaRequirements?.noMediaAllowed === "Yes") {
                mediaNotAllowed += ` ${person.firstName} ${person.lastName}`;
            }

            if (mediaReviewOrgWide === "Yes" || person?.mediaRequirements?.mediaReviewRequired === "Yes" || person?.profileData?.mediaRequirements?.mediaReviewRequired === "Yes") {
                individualMediaReviewRequired = true;
            }

            taggedPeople.push(person._id);
        };

        const handleTagGroup = async (tagValue) => {
            const peepQuery = {
                type: momentData.momentType === "alert" ? { "$in": ["person", "staff"] } : "person",
                inActive: { $ne: true },
                orgId: org._id,
                [momentData.tagAllInGroup || momentData.momentType !== "alert" ? "checkInGroupId" : "defaultGroupId"]: tagValue,
                ...(momentData.tagOnlyCheckins && { checkedIn: true }),
            };

            const peeps = await People.find(peepQuery).fetchAsync();
            peeps.forEach(processPerson);
        };

        const handleTagOrg = async () => {
            const designations = org?.valueOverrides?.designations ?? [];
            const peepQuery = {
                type: { "$in": ["person", "admin", "staff"] },
                inActive: { $ne: true },
                orgId: org._id,
                ...(designations.length > 0 && { designations: { $nin: ["Wait List"] } }),
                ...(momentData.tagOnlyCheckins && { checkedIn: true }),
            };

            const peeps = await People.find(peepQuery).fetchAsync();
            peeps.forEach(processPerson);
        };

        const handleTagRole = async (tagValue) => {
            if (tagValue === "all_staff") {
                const peepQuery = {
                    type: { $in: ["staff"] },
                    inActive: { $ne: true },
                    orgId: org._id,
                    ...(momentData.tagOnlyCheckins && { checkedIn: true }),
                };

                const peeps = await People.find(peepQuery).fetchAsync();
                peeps.forEach(processPerson);
            } else {
                throw new Meteor.Error("At least one person must be tagged.");
            }
        };

        const handleTagDesignation = async (tagValue) => {
            const designations = org?.valueOverrides?.designations ?? [];
            const foundDesignation = designations.find(d => d.replace(/\s/g, '') === tagValue);

            if (foundDesignation) {
                const peepQuery = {
                    type: { $in: ["person"] },
                    designations: { $in: [foundDesignation] },
                    inActive: { $ne: true },
                    orgId: org._id,
                };

                const peeps = await People.find(peepQuery).fetchAsync();
                peeps.forEach(processPerson);
            } else {
                throw new Meteor.Error("At least one person must be tagged for designations.");
            }
        };

        const handleTagPerson = async (tagValue) => {
            const person = await People.findOneAsync({ orgId: org._id, _id: tagValue });
            const allowMomentsWhenCheckedOut = org.hasCustomization(AvailableCustomizations.ALLOW_MOMENTS_WHEN_CHECKED_OUT);
            const momentsAvailableWhenCheckedOut = allowMomentsWhenCheckedOut ? ExpandedCheckedOutMomentTypes : DefaultCheckedOutMomentTypes;

            if (!editMomentStatus && commonData.sortStamp && commonData.sortStamp > new moment().tz(timezone).startOf("day").valueOf() && person && person.type !== "prospect" && !person.checkedIn && !momentsAvailableWhenCheckedOut.includes(momentData.momentType)) {
                throw new Meteor.Error(`${person.firstName} ${person.lastName} must be checked in to post a moment for today.`);
            }

            processPerson(person);
        };
        for(const tag of momentData.taggedPeople) {
            let tagType
            let tagValue
            if (typeof tag === "string") {
                [tagType, tagValue] = tag.split("|");
            } else {
                tagType = tag?.tagType;
                tagValue = tag?.tagValue ?? tag?.tagId;
            }

            switch (tagType) {
                case "group":
                    await handleTagGroup(tagValue);
                    break;
                case "org":
                    await handleTagOrg();
                    break;
                case "role":
                    await handleTagRole(tagValue);
                    break;
                case "designation":
                    await handleTagDesignation(tagValue);
                    break;
                default:
                    await handleTagPerson(tagValue);
                    break;
            }
        };

        if (taggedPeople.length === 0) {
            throw new Meteor.Error("At least one person must be tagged.");
        }

        if (checkMediaRequirements && momentData.momentType !== "alert" && mediaNotAllowed.length > 0 && (momentData?.mediaFiles?.length > 0 || momentData?.attachedMedia?.length > 0)) {
            throw new Meteor.Error(`No media files are allowed for ${mediaNotAllowed}`);
        }

        return { taggedPeople, individualMediaReviewRequired };
    }

    /**
     * Check if selected time is between a enrolled child's schedule type's start/end time. Should only be used on client side
     *
     * @param {String} personId Id of the child
     * @param {org} org Org object
     * @param {String} timezone Timezone of the org
     * @param {Number} selectedTime Time to check against
     * @param {string} mode Mode of check in/check out
     * @param {Array} reservations Reservations data if subscription data is not available
     */
    static isEarlyOrLateCheckInCheckOut(personId, org, timezone, selectedTime, mode, reservations) {
        const startOfDay = moment.tz(timezone).startOf('day').valueOf();
		const endOfDay = moment.tz(timezone).endOf('day').valueOf();
        let orgMinuteWindow = org.earlyPickDropMinute;
        if (!org.earlyPickDropMinute) { // Default to 0 if no early pick up/drop off window is set
            orgMinuteWindow = 0;
        }
        // Find reservations today to match with org schedule types
        if (!reservations) {
            reservations = Reservations.findWithRecurrence({
                query: {
                    selectedPerson: personId,
                    cancellationReason: null,
                    orgId: org._id
                },
                startDateValue: startOfDay,
                endDateValue: endOfDay,
            })
        }
        const scheduleTypes = org.getScheduleTypes();
        // Check each reservation to see if the selected time is within the schedule type's start/end time

        for (const reservation of reservations) {
            const matchingScheduleType = scheduleTypes.find(type => type._id === reservation.scheduleType);
            if (matchingScheduleType) {
                if (!matchingScheduleType.startTime || !matchingScheduleType.endTime) {
                    continue; // Skip entry if start/end time is not set
                }
                const startTime = moment.tz(matchingScheduleType.startTime, "h:mm A", timezone);
                const endTime = moment.tz(matchingScheduleType.endTime, "h:mm A", timezone);
                const selectedTimeIsWithinSchedule = selectedTime >= startTime.valueOf() && selectedTime <= endTime.valueOf();
                if (mode === LateCheckInEarlyDropOffModes.LATE_DROP_OFF && selectedTimeIsWithinSchedule) {
                    const lateDropOffWindow = startTime.clone().add(orgMinuteWindow, 'minutes').valueOf()
                    if (selectedTime > lateDropOffWindow) {
                        return true;
                    }
                } else if (mode === LateCheckInEarlyDropOffModes.EARLY_PICK_UP && selectedTimeIsWithinSchedule) {
                    const earlyPickUpWindow = endTime.clone().subtract(orgMinuteWindow, 'minutes').valueOf()
                    if (selectedTime < earlyPickUpWindow) {
                        return true;
                    }
                }
            } else {
                continue; // Skip entry if schedule type is not found
            }
        }
        // Default response should be false
        return false;
    }

    /**
     * Sets the dynamic moment type data.
     *
     * @param momentType
     * @param momentData
     * @param commonData
     * @returns {Promise<void>}
     */
    static async setDynamicMomentTypeData(momentType, momentData, commonData) {
        const momentDefinition = await MomentDefinitions.findOneAsync({ momentType: momentType });
        if (momentData.isDynamic && momentDefinition) {
            commonData.momentTypePretty = momentDefinition.momentTypePretty;
            commonData.isDynamic = true;
            commonData.momentDefinitionId = momentDefinition._id;
            commonData.dynamicFieldValues = {};
            for (const momentField of momentDefinition.momentFields) {
                commonData.dynamicFieldValues[momentField.dataId] = momentData.dynamicFieldValues[momentField.dataId];
            }
            if (momentDefinition.internalOnly) {
                commonData.adminOnly = momentData.adminOnly;
            }
        } else {
            throw new Meteor.Error(500, "Invalid moment type");
        }
    }

    /**
     * Check in / Check out util to save early/late reasons to send to server. Should only be used on client side
     *
     * @param {Object} checkData Check in/Check out data
     * @param {Object} selectedReason Selected reason id from user input
     * @param {Boolean} isDropdownVisible Determines if the dropdown is visible or not
     */
    static saveEarlyLateReason(checkData, selectedReason, isDropdownVisible) {
        try {
            if (isDropdownVisible) {
                if (!selectedReason || selectedReason === '') {
                    const errorMessage = "Please select a reason for early/late checkout";
                    throw new Error(errorMessage);
                }
                checkData["earlyLateReason"] = selectedReason;
            }
        } catch (e) {
            throw e
        }
    }
}
