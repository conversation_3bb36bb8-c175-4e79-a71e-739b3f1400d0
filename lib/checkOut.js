import moment from "moment-timezone";
import { Cache } from "./util/cacheUtils";
import { CACHE_KEYS } from "./constants/cacheConstants";
import { AvailableCustomizations } from './customizations';
import { People } from "./collections/people";
import { processPermissions } from "./permissions";
import { Orgs } from "./collections/orgs";
import { processSummaryMail2021 } from '../server/emails/v2021/processSummaryMail2021';
import { recalculateGroupMediaDesignation } from "./momentHelpers";
import { Moments } from "./collections/moments";
import { Meteor } from 'meteor/meteor';
import {Log} from "./util/log";

const checkOut = async (checkOutData, dontUpdatePersonCollection = false) => {
  const currentUser = checkOutData?.currentUser ?? await Meteor.userAsync();
  const currentPerson = checkOutData?.currentPerson ?? await currentUser?.fetchPerson();
  const createdByUser = checkOutData.isKioskMode ? await Meteor.users.findOneAsync({ personId: checkOutData.checkedOutById }) : currentUser;
  const createdByPersonId = checkOutData.isKioskMode ? checkOutData.checkedOutById : currentUser.personId;
  const userPersonIsStaff = currentPerson.type === "staff";
  let pinCode = currentPerson.pinCode;
  let usedPin = checkOutData.usedPin;
  // Try to get the org from checkOutData, currentPerson, or Orgs.current()
  let org = null;

  if (checkOutData.currentOrg) {
    org = checkOutData.currentOrg;
  } else if (checkOutData.currentPerson?.orgId) {
    org = await Orgs.findOneAsync(checkOutData.currentPerson.orgId);
  } else if (currentUser?.orgId) {
    org = await Orgs.findOneAsync(currentUser.orgId);
  } else {
    org = await Orgs.current();
  }

  if (!org) {
    Log.error('Could not find organization');
    throw new Meteor.Error(500, 'Organization not found');
  }

  Log.info(`Found org: ${org._id}, name: ${org.name}, hasCustomization method: ${typeof org.hasCustomization === 'function'}`);
  Log.info(`CheckOutData sources: currentOrg: ${!!checkOutData.currentOrg}, currentPerson.orgId: ${!!checkOutData.currentPerson?.orgId}, currentUser.orgId: ${!!currentUser?.orgId}`);


  // Check if the org object has the hasCustomization method
  if (typeof org.hasCustomization !== 'function') {
    Log.info('hasCustomization method not found on org object, fetching from database');
    // Fetch the org from the database to ensure it has all methods
    try {
      const dbOrg = await Orgs.findOneAsync(org._id);
      if (dbOrg) {
        org = dbOrg;
      }
    } catch (error) {
      console.error('Error fetching org from database:', error);
      // Continue with the original org object
    }
  }

  if (currentPerson?.pinCodeSupplemental?.length > 0) {
    pinCode = pinCode + currentPerson.pinCodeSupplemental;
  }

  if (currentPerson.type === "family" && checkOutData.pinCode) {
    if (pinCode !== checkOutData.pinCode) {
      throw new Meteor.Error(403, "Access denied");
    }
  } else {
    Log.info(`Current Person Type: ${currentPerson.type}`);
    Log.info(`Current org: ${JSON.stringify({
      _id: org._id,
      name: org.name,
      hasCustomization: typeof org.hasCustomization === 'function'
    })}`);
    if (!Cache.has(`${currentPerson._id}-${CACHE_KEYS.PROCESSPERMISSIONS_PEOPLE_MOVEMENT_EDIT}`)) {
      Log.info(`Cache empty for ${currentPerson._id}-${CACHE_KEYS.PROCESSPERMISSIONS_PEOPLE_MOVEMENT_EDIT}`);
      const getProcessPermissions = await processPermissions({
        assertions: [{ context: "people/movement", action: "edit" }],
        evaluator: (thisPerson) => thisPerson.type === "admin" || thisPerson.type === "staff",
        throwError: true,
        currentOrg: org,
        currentPerson,
      });

      Cache.set(`${currentPerson._id}-${CACHE_KEYS.PROCESSPERMISSIONS_PEOPLE_MOVEMENT_EDIT}`, getProcessPermissions);
    }

    const processPermissionsResult = Cache.get(`${currentPerson._id}-${CACHE_KEYS.PROCESSPERMISSIONS_PEOPLE_MOVEMENT_EDIT}`);
    Log.info(`Process Permissions Result: ${processPermissionsResult}`);
  }

  const targetPerson = await People.findOneAsync(checkOutData.personId);

  if (!(targetPerson["orgId"] === currentUser["orgId"])) {
    Log.info(`Target person orgId: ${targetPerson["orgId"]}, currentUser orgId: ${currentUser["orgId"]}`);
    throw new Meteor.Error(403, "Access denied", "Person is not in your organization.");
  }

  if (targetPerson.inActive) {
    throw new Meteor.Error(500, "That person is inactive and cannot be checked out.");
  }

  if (!targetPerson.checkedIn && !dontUpdatePersonCollection) {
    throw new Meteor.Error(500, "Person already checked out");
  }

  if (!Cache.has(`${org._id}-${CACHE_KEYS.ORGS_CUSTOMIZATION_MOMENTS_CHECKIN_STAFFLOCKDOWN}`)) {
    let getStaffLockdown = false;

    if (typeof org.hasCustomization === 'function') {
      getStaffLockdown = org.hasCustomization("moments/checkin/staffLockdown");
    } else if (org.customizations) {
      // Fallback implementation if the method doesn't exist
      getStaffLockdown = !!org.customizations["moments/checkin/staffLockdown"];
    }

    Cache.set(`${org._id}-${CACHE_KEYS.ORGS_CUSTOMIZATION_MOMENTS_CHECKIN_STAFFLOCKDOWN}`, getStaffLockdown);
  }

  const staffLockdown = Cache.get(`${org._id}-${CACHE_KEYS.ORGS_CUSTOMIZATION_MOMENTS_CHECKIN_STAFFLOCKDOWN}`);

  if (userPersonIsStaff && staffLockdown
    && (targetPerson.type === "staff" || targetPerson.type === "admin") && currentUser.personId !== checkOutData.personId) {
    throw new Meteor.Error(403, "Access denied -- staff can only checkout themselves");
  }

  if (!Cache.has(`${org._id}-${CACHE_KEYS.ORGS_CUSTOMIZATION_PEOPLE_STAFFREQUIREDPINCODECHECKIN_ENABLED}`)) {
    let getStaffRequiredPinCodeCheckin_enabled = false;

    if (typeof org.hasCustomization === 'function') {
      getStaffRequiredPinCodeCheckin_enabled = org.hasCustomization("people/staffRequiredPinCodeCheckin/enabled");
    } else if (org.customizations) {
      // Fallback implementation if the method doesn't exist
      getStaffRequiredPinCodeCheckin_enabled = !!org.customizations["people/staffRequiredPinCodeCheckin/enabled"];
    }

    Cache.set(`${org._id}-${CACHE_KEYS.ORGS_CUSTOMIZATION_PEOPLE_STAFFREQUIREDPINCODECHECKIN_ENABLED}`, getStaffRequiredPinCodeCheckin_enabled);
  }
  const staffRequiredPinCodeCheckin_enabled = Cache.get(`${org._id}-${CACHE_KEYS.ORGS_CUSTOMIZATION_PEOPLE_STAFFREQUIREDPINCODECHECKIN_ENABLED}`);

  if (userPersonIsStaff && targetPerson.type !== "person" && staffRequiredPinCodeCheckin_enabled && checkOutData.prettyTime) {
    throw new Meteor.Error(403, "Access denied -- staff are required to use pin code checkout");
  }

  const timezone = org.getTimezone();
  const createdAt = new Date().valueOf();
  let sortStamp;

  if (!checkOutData.prettyTime || (targetPerson && targetPerson.type === "staff" && staffLockdown)) {
    checkOutData.prettyTime = new moment().tz(timezone).format("h:mm a");
  }

  if (checkOutData.date && checkOutData.prettyTime) {
    sortStamp = new moment.tz(checkOutData.date + " " + checkOutData.prettyTime, "MM/DD/YYYY h:mm a", timezone).valueOf();
  } else {
    sortStamp = createdAt;
  }

  const currentDay = (sortStamp > new moment().tz(timezone).startOf('day'));
  const previousCheckInGroupId = targetPerson.checkInGroupId;
  const previousCheckInGroupName = targetPerson.checkInGroupName;

  if (!dontUpdatePersonCollection) {
    Log.info("Updating person collection");
    await People.updateAsync(checkOutData.personId, {
      $set: { checkedIn: false, checkedInOutTime: sortStamp, checkInGroupId: null, checkInGroupName: null, previousClassroomGroupId: null, presenceLastGroupId: previousCheckInGroupId },
      $unset: { checkInReminders: 1, familyCheckOut: 1, checkInOutlook: 1 }
    });
  }

  const newMoment = {};
  newMoment["createdAt"] = createdAt;
  newMoment["sortStamp"] = sortStamp;
  newMoment["createdBy"] = createdByUser?._id ?? currentUser._id;
  newMoment["createdByPersonId"] = createdByPersonId;
  newMoment["usedPin"] = usedPin;

  if (previousCheckInGroupId) {
    newMoment["checkOutGroupId"] = previousCheckInGroupId;
    newMoment["checkOutGroupName"] = previousCheckInGroupName;
  }

  // Get orgId from available sources to prevent null reference errors
  if (org && org._id) {
    newMoment.orgId = org._id;
  } else if (targetPerson && targetPerson.orgId) {
    newMoment.orgId = targetPerson.orgId;
  } else if (currentUser && currentUser.orgId) {
    newMoment.orgId = currentUser.orgId;
  } else {
    const meteorUser = await Meteor.userAsync();
    if (meteorUser && meteorUser.orgId) {
      newMoment.orgId = meteorUser.orgId;
    } else {
      console.error("Could not determine orgId for checkout moment");
      throw new Meteor.Error(500, "Organization ID not found");
    }
  }

  if (targetPerson.checkInGroupId) {
    newMoment["comment"] = "Checked out of " + targetPerson.checkInGroupName;
  }

  if (checkOutData.comments) {
    check(checkOutData.comments, String);
    newMoment["comment"] = (newMoment["comment"] ? newMoment["comment"] + ". \n" : "") + checkOutData.comments;
  }

  newMoment["owner"] = checkOutData.personId;
  newMoment["taggedPeople"] = [checkOutData.personId];
  newMoment["momentType"] = "checkout";
  newMoment["momentTypePretty"] = "Check Out";
  newMoment["time"] = checkOutData.prettyTime;
  newMoment["date"] = checkOutData.date || new moment().tz(timezone).format("MM/DD/YYYY");
  newMoment["mood"] = checkOutData.mood;

  if (!Cache.has(`${org._id}-${CACHE_KEYS.ORGS_CUSTOMIZATION_MOMENTS_CHECKOUT_SHOWTRANSPORTATION}`)) {
    let showTransportation = false;

    if (typeof org.hasCustomization === 'function') {
      showTransportation = org.hasCustomization(AvailableCustomizations.SHOW_TRANSPORTATION);
    } else if (org.customizations) {
      // Fallback implementation if the method doesn't exist
      showTransportation = !!org.customizations[AvailableCustomizations.SHOW_TRANSPORTATION];
    }

    Cache.set(`${org._id}-${CACHE_KEYS.ORGS_CUSTOMIZATION_MOMENTS_CHECKOUT_SHOWTRANSPORTATION}`, showTransportation);
  }

  const checkOutShowTransportation = Cache.get(`${org._id}-${CACHE_KEYS.ORGS_CUSTOMIZATION_MOMENTS_CHECKOUT_SHOWTRANSPORTATION}`);

  if (checkOutShowTransportation) {
    newMoment["checkOutTransportation"] = checkOutData.transportation;
  }

  if (checkOutData.checkedOutById) {
    const checkOutPerson = await People.findOneAsync({ _id: checkOutData.checkedOutById });
    newMoment["checkedOutById"] = checkOutData.checkedOutById;
    const labelPrefix = "Picked up by ";
    newMoment["comment"] = (newMoment["comment"] ? newMoment["comment"] + ". " : "") + labelPrefix + checkOutPerson.firstName + " " + checkOutPerson.lastName + ".";
  }

  if (!Cache.has(`${org._id}-${CACHE_KEYS.ORGS_CUSTOMIZATION_MOMENTS_CHECKIN_SHOWHEALTHCHECK}`)) {
    let showHealthCheck = false;

    if (typeof org.hasCustomization === 'function') {
      showHealthCheck = org.hasCustomization("moments/checkin/showHealthCheck");
    } else if (org.customizations) {
      // Fallback implementation if the method doesn't exist
      showHealthCheck = !!org.customizations["moments/checkin/showHealthCheck"];
    }

    Cache.set(`${org._id}-${CACHE_KEYS.ORGS_CUSTOMIZATION_MOMENTS_CHECKIN_SHOWHEALTHCHECK}`, showHealthCheck);
  }

  const showHealthCheck = Cache.get(`${org._id}-${CACHE_KEYS.ORGS_CUSTOMIZATION_MOMENTS_CHECKIN_SHOWHEALTHCHECK}`);

  if (showHealthCheck) {
    newMoment["checkOutHealthChecks"] = checkOutData.healthChecks;
  }

  // Check early late reason and add to moment
  let hasPickDropReasonEnabled = false;

  if (typeof org.hasCustomization === 'function') {
    hasPickDropReasonEnabled = org.hasCustomization(AvailableCustomizations.PICK_DROP_REASON_ENABLED);
  } else if (org.customizations) {
    // Fallback implementation if the method doesn't exist
    hasPickDropReasonEnabled = !!org.customizations[AvailableCustomizations.PICK_DROP_REASON_ENABLED];
  }

  if (hasPickDropReasonEnabled) {
    newMoment["reason"] = checkOutData.earlyLateReason;
  }
  Log.info("Inserting Checkout Moment", JSON.stringify(newMoment));
  try {
    const momentId = await Moments.insertAsync(newMoment);
    Log.info(`Successfully inserted checkout moment with ID: ${momentId}`);
  } catch (e) {
    console.error(`Error inserting checkout moment: ${e}`);
    console.error(`Checkout data: ${JSON.stringify(checkOutData)}`);
    throw new Meteor.Error(500, `Error inserting moment: ${e}`);
  }

  if (Meteor.isServer && currentDay && !checkOutData.multipleCheckoutEmail) {
    Meteor.defer(async function () {
      processSummaryMail2021(checkOutData.personId, "");
    })
  }

  if (Meteor.isServer) {
    if (previousCheckInGroupId) {
      await recalculateGroupMediaDesignation(previousCheckInGroupId);
    }
  }
};

export {
  checkOut
}
