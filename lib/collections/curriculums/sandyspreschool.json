[{"ageGroup": "Frogs", "category": "Social and Emotional Development", "benchmark": "Express a Variety of Emotions through facial expressions, gestures, movement, and sounds.", "standardId": "SE1a.1"}, {"ageGroup": "Frogs", "category": "Social and Emotional Development", "benchmark": "Show awareness of own emotions and uses verbal and nonverbal ways to express complex emotions.", "standardId": "SE1a.2"}, {"ageGroup": "Frogs", "category": "Social and Emotional Development", "benchmark": "Comfort self in simple ways and communicate needs for help through vocalizations and gestures.", "standardId": "SE1b.1"}, {"ageGroup": "Frogs", "category": "Social and Emotional Development", "benchmark": "Comfort self in a variety of ways.", "standardId": "SE1b.2"}, {"ageGroup": "Frogs", "category": "Social and Emotional Development", "benchmark": "Expresses and acts on impulses.", "standardId": "SE1c.1"}, {"ageGroup": "Frogs", "category": "Social and Emotional Development", "benchmark": "Respond positively to limits and choices offered by adults to guide behavior.", "standardId": "SE1c.2"}, {"ageGroup": "Frogs", "category": "Social and Emotional Development", "benchmark": "With modeling and support, manage actions and emotional expressions.", "standardId": "SE1c.3"}, {"ageGroup": "Frogs", "category": "Social and Emotional Development", "benchmark": "Act in ways to make things happen.", "standardId": "SE1d.1"}, {"ageGroup": "Frogs", "category": "Social and Emotional Development", "benchmark": "Show a sense of satisfaction when making things happen", "standardId": "SE1d.2"}, {"ageGroup": "Frogs", "category": "Social and Emotional Development", "benchmark": "Initiate interactions and seeks close proximity to familiar adults who provide constant nurturing.", "standardId": "SE2a.1"}, {"ageGroup": "Frogs", "category": "Social and Emotional Development", "benchmark": "Explore the environment in the presence of familiar adults.", "standardId": "SE2a.2"}, {"ageGroup": "Frogs", "category": "Social and Emotional Development", "benchmark": "Seeks close proximity to familiar adults for security and support, especially when distressed.", "standardId": "SE2a.3"}, {"ageGroup": "Frogs", "category": "Social and Emotional Development", "benchmark": "Seek security and support from familiar adults when distressed.", "standardId": "SE2a.4"}, {"ageGroup": "Frogs", "category": "Social and Emotional Development", "benchmark": "Initiate and engage in reciprocal interactions with familiar adults.", "standardId": "SE2b.1"}, {"ageGroup": "Frogs", "category": "Social and Emotional Development", "benchmark": "Interact with familiar adults in a variety of ways.", "standardId": "SE2b.2"}, {"ageGroup": "Frogs", "category": "Social and Emotional Development", "benchmark": "Show interest in other children.", "standardId": "SE2c.1"}, {"ageGroup": "Frogs", "category": "Social and Emotional Development", "benchmark": "React to emotional expressions of others.", "standardId": "SE2d.1"}, {"ageGroup": "Frogs", "category": "Approaches toward Learning", "benchmark": "Show interest in people and objects.", "standardId": "AL3a.1"}, {"ageGroup": "Frogs", "category": "Approaches toward Learning", "benchmark": "Explore the environment through a variety of sensory motor activities.", "standardId": "AL3a.2"}, {"ageGroup": "Frogs", "category": "Approaches toward Learning", "benchmark": "Respond to people and objects in their immediate environment based on past experience.", "standardId": "AL3b.1"}, {"ageGroup": "Frogs", "category": "Approaches toward Learning", "benchmark": "Demonstrate awareness of happenings in surroundings.", "standardId": "AL4a.1"}, {"ageGroup": "Frogs", "category": "Approaches toward Learning", "benchmark": "Focus on an activity, but is easily distracted.", "standardId": "AL4a.2"}, {"ageGroup": "Frogs", "category": "Approaches toward Learning", "benchmark": "Make discoveries about self, others, and the environment.", "standardId": "AL5a.1"}, {"ageGroup": "Frogs", "category": "Approaches toward Learning", "benchmark": "Repeat actions intentionally to achieve goal.", "standardId": "AL5a.2"}, {"ageGroup": "Frogs", "category": "Approaches toward Learning", "benchmark": "Engage in self-initiated activities for sustained periods of time.", "standardId": "AL5a.3"}, {"ageGroup": "Frogs", "category": "Physical Development", "benchmark": "Demonstrate strength and control of head, arms, legs, and trunk using purposeful movements.", "standardId": "PE6a.1"}, {"ageGroup": "Frogs", "category": "Physical Development", "benchmark": "Move with increasing coordination and balance, with or without adult support and/or assistive device.", "standardId": "PE6a.2"}, {"ageGroup": "Frogs", "category": "Physical Development", "benchmark": "Use locomotor skills with increasing coordination and balance.", "standardId": "PE6a.3"}, {"ageGroup": "Frogs", "category": "Physical Development", "benchmark": "Use a variety of non-locomotor body movements during play.", "standardId": "PE6a.4"}, {"ageGroup": "Frogs", "category": "Physical Development", "benchmark": "Transfer a toy from one hand to another by reaching, grasping and releasing.", "standardId": "PE6b.1"}, {"ageGroup": "Frogs", "category": "Physical Development", "benchmark": "Use both hands together to accomplish a task.", "standardId": "PE6b.2"}, {"ageGroup": "Frogs", "category": "Physical Development", "benchmark": "Coordinate the use of arms, hands, and fingers to accomplish tasks.", "standardId": "PE6b.3"}, {"ageGroup": "Frogs", "category": "Physical Development", "benchmark": "Use mouth and tongue to explore objects.", "standardId": "PE6c.1"}, {"ageGroup": "Frogs", "category": "Physical Development", "benchmark": "Open mouth to wait for food to enter and use upper lip to clean food off spoon during spoon feeding.", "standardId": "PE6c.2"}, {"ageGroup": "Frogs", "category": "Physical Development", "benchmark": "Take and chew small bites/pieces of finger food.", "standardId": "PE6c.3"}, {"ageGroup": "Frogs", "category": "Physical Development", "benchmark": "Take bites from whole foods and coordinate chewing and swallowing.", "standardId": "PE6c.4"}, {"ageGroup": "Frogs", "category": "Physical Development", "benchmark": "Use senses and movement to explore immediate surroundings.", "standardId": "PE6d.1"}, {"ageGroup": "Frogs", "category": "Physical Development", "benchmark": "Coordinate senses with movement.", "standardId": "PE6d.2"}, {"ageGroup": "Frogs", "category": "Physical Development", "benchmark": "Show awareness of own body.", "standardId": "PE7a.1"}, {"ageGroup": "Frogs", "category": "Physical Development", "benchmark": "Express when hungry or full.", "standardId": "PE7b.1"}, {"ageGroup": "Frogs", "category": "Physical Development", "benchmark": "Follow a regular eating routine.", "standardId": "PE7b.2"}, {"ageGroup": "Frogs", "category": "Physical Development", "benchmark": "Follow adult intervention/guidance regarding safety.", "standardId": "PE7c.1"}, {"ageGroup": "Frogs", "category": "Physical Development", "benchmark": "Cooperate and or/stop a behavior in response to a direction regarding safety.", "standardId": "PE7c.2"}, {"ageGroup": "Frogs", "category": "Physical Development", "benchmark": "Use adults as resources when needing help in potentially unsafe or dangerous situations.", "standardId": "Pe7c.3"}, {"ageGroup": "Frogs", "category": "Cognition and General Knowledge", "benchmark": "Exhibit differentiated responses to familiar and unfamiliar people, events, objects, and their features.", "standardId": "CGK8a.1"}, {"ageGroup": "Frogs", "category": "Cognition and General Knowledge", "benchmark": "Mirror simple actions and facial expressions of others previously experienced.", "standardId": "CGK8a.2"}, {"ageGroup": "Frogs", "category": "Cognition and General Knowledge", "benchmark": "Recall information over a period of time with contextual cues.", "standardId": "CGK8a.3"}, {"ageGroup": "Frogs", "category": "Cognition and General Knowledge", "benchmark": "Mirror and repeat something seen at an earlier time", "standardId": "CGK8a.4"}, {"ageGroup": "Frogs", "category": "Cognition and General Knowledge", "benchmark": "Recall information over a longer period of time without contextual cues.", "standardId": "CGK8a.5"}, {"ageGroup": "Frogs", "category": "Cognition and General Knowledge", "benchmark": "Anticipate routines.", "standardId": "CGK8a.6"}, {"ageGroup": "Frogs", "category": "Cognition and General Knowledge", "benchmark": "Explore real objects, people, and actions.", "standardId": "CGK8b.1"}, {"ageGroup": "Frogs", "category": "Cognition and General Knowledge", "benchmark": "Actively use the body to find out about the world.", "standardId": "CGK8c.1"}, {"ageGroup": "Frogs", "category": "Mathematics", "benchmark": "Explore objects and attend to events in the environment.", "standardId": "MATH9a.1"}, {"ageGroup": "Frogs", "category": "Mathematics", "benchmark": "Notice differences between familiar and unfamiliar people, objects, and places.", "standardId": "MATH10a.1"}, {"ageGroup": "Frogs", "category": "Mathematics", "benchmark": "Imitate repeated movements.", "standardId": "MATH10b.1"}, {"ageGroup": "Frogs", "category": "Mathematics", "benchmark": "Participate in adult-initiated movement patterns.", "standardId": "MATH10b.2"}, {"ageGroup": "Frogs", "category": "Mathematics", "benchmark": "Copy and anticipate a repeating pattern.", "standardId": "MATH10b.3"}, {"ageGroup": "Frogs", "category": "Mathematics", "benchmark": "Explore the properties of objects.", "standardId": "MATH11a.1"}, {"ageGroup": "Frogs", "category": "Social Studies", "benchmark": "Show awareness of self and awareness of other people.", "standardId": "SS12a.1"}, {"ageGroup": "Frogs", "category": "Social Studies", "benchmark": "Prefer familiar adults and recognize familiar actions and routines.", "standardId": "SS12a.2"}, {"ageGroup": "Frogs", "category": "Science", "benchmark": "Examine objects with lips and tongue.", "standardId": "SCI13a.1"}, {"ageGroup": "Frogs", "category": "Science", "benchmark": "Observe, hold, touch, and manipulate objects.", "standardId": "SCI13a.2"}, {"ageGroup": "Frogs", "category": "Science", "benchmark": "Try different things with objects to see what happens or how things work.", "standardId": "SCI13a.3"}, {"ageGroup": "Frogs", "category": "Science", "benchmark": "Observe the physical and natural world around them.", "standardId": "SCI13a.4"}, {"ageGroup": "Frogs", "category": "Science", "benchmark": "Use simple actions to make things happen.", "standardId": "SCI13b.1"}, {"ageGroup": "Frogs", "category": "Science", "benchmark": "Purposefully combine actions to make things happen.", "standardId": "SCI13b.2"}, {"ageGroup": "Frogs", "category": "Science", "benchmark": "Demonstrate understanding that events have a cause.", "standardId": "SCI13b.3"}, {"ageGroup": "Frogs", "category": "Language and Literacy Development", "benchmark": "Attends and responds to language and sounds.", "standardId": "LL14a.1"}, {"ageGroup": "Frogs", "category": "Language and Literacy Development", "benchmark": "Experiment intentionally with sound inflection and gestures in different ways to express wants, needs, or feelings.", "standardId": "LL14b.1"}, {"ageGroup": "Frogs", "category": "Language and Literacy Development", "benchmark": "Attempt to respond to basic forms of social communication with the appropriate facial expression, vocalization, and or gesture.", "standardId": "LL14c.1"}, {"ageGroup": "Frogs", "category": "Language and Literacy Development", "benchmark": "Show interest in books, pictures, songs, and rhymes.", "standardId": "LL15a.1"}, {"ageGroup": "Frogs 2", "category": "Social and Emotional Development", "benchmark": "Express a Variety of Emotions through facial expressions, gestures, movement, and sounds.", "standardId": "SE1a.1"}, {"ageGroup": "Frogs 2", "category": "Social and Emotional Development", "benchmark": "Communicate emotions purposefully and intentionally, nonverbally and with familiar words.", "standardId": "SE1a.2"}, {"ageGroup": "Frogs 2", "category": "Social and Emotional Development", "benchmark": "Show awareness of own emotions and uses verbal and nonverbal ways to express complex emotions.", "standardId": "SE1a.3"}, {"ageGroup": "Frogs 2", "category": "Social and Emotional Development", "benchmark": "Begin to understand self as a separate person from others.", "standardId": "SE1b.1"}, {"ageGroup": "Frogs 2", "category": "Social and Emotional Development", "benchmark": "Recognize self as a unique person with thoughts feelings and distinct characteristics.", "standardId": "SE1b.2"}, {"ageGroup": "Frogs 2", "category": "Social and Emotional Development", "benchmark": "Show awareness of self as belonging to one or more groups.", "standardId": "SE1b.3"}, {"ageGroup": "Frogs 2", "category": "Social and Emotional Development", "benchmark": "Identify feelings, needs, and interests.", "standardId": "SE1b.4"}, {"ageGroup": "Frogs 2", "category": "Social and Emotional Development", "benchmark": "Expresses and acts on impulses.", "standardId": "SE1c.1"}, {"ageGroup": "Frogs 2", "category": "Social and Emotional Development", "benchmark": "Respond positively to limits and choices offered by adults to guide behavior.", "standardId": "SE1c.2"}, {"ageGroup": "Frogs 2", "category": "Social and Emotional Development", "benchmark": "With modeling and support, manage actions and emotional expressions.", "standardId": "SE1c.3"}, {"ageGroup": "Frogs 2", "category": "Social and Emotional Development", "benchmark": "Act in ways to make things happen.", "standardId": "SE1d.1"}, {"ageGroup": "Frogs 2", "category": "Social and Emotional Development", "benchmark": "Show a sense of satisfaction when making things happen", "standardId": "SE1d.2"}, {"ageGroup": "Frogs 2", "category": "Social and Emotional Development", "benchmark": "Recognize own abilities and express satisfaction when demonstrating them to others.", "standardId": "SE1d.3"}, {"ageGroup": "Frogs 2", "category": "Social and Emotional Development", "benchmark": "Comfort self in simple ways and communicate needs for help through vocalizations and gestures.", "standardId": "SE1e.1"}, {"ageGroup": "Frogs 2", "category": "Social and Emotional Development", "benchmark": "Comfort self in a variety of ways.", "standardId": "SE1e.2"}, {"ageGroup": "Frogs 2", "category": "Social and Emotional Development", "benchmark": "Anticipate the need for comfort and try to prepare for changes in routine.", "standardId": "SE1e.3"}, {"ageGroup": "Frogs 2", "category": "Social and Emotional Development", "benchmark": "Initiate interactions and seeks close proximity to familiar adults who provide constant nurturing.", "standardId": "SE2a.1"}, {"ageGroup": "Frogs 2", "category": "Social and Emotional Development", "benchmark": "Explore the environment in the presence of familiar adults.", "standardId": "SE2a.2"}, {"ageGroup": "Frogs 2", "category": "Social and Emotional Development", "benchmark": "Seeks close proximity to familiar adults for security and support, especially when distressed.", "standardId": "SE2a.3"}, {"ageGroup": "Frogs 2", "category": "Social and Emotional Development", "benchmark": "Seek security and support from familiar adults when distressed.", "standardId": "SE2a.4"}, {"ageGroup": "Frogs 2", "category": "Social and Emotional Development", "benchmark": "Initiate play with familiar adults.", "standardId": "SE2a.5"}, {"ageGroup": "Frogs 2", "category": "Social and Emotional Development", "benchmark": "Display signs of comfort during play with familiar adults are nearby but not in the immediate area.", "standardId": "SE2a.6"}, {"ageGroup": "Frogs 2", "category": "Social and Emotional Development", "benchmark": "Seek security and support from familiar adults when distressed.", "standardId": "SE2a.7"}, {"ageGroup": "Frogs 2", "category": "Social and Emotional Development", "benchmark": "Initiate and engage in reciprocal interactions with familiar adults.", "standardId": "SE2b.1"}, {"ageGroup": "Frogs 2", "category": "Social and Emotional Development", "benchmark": "Interact with familiar adults in a variety of ways.", "standardId": "SE2b.2"}, {"ageGroup": "Frogs 2", "category": "Social and Emotional Development", "benchmark": "Interact with familiar adults in a variety of ways.", "standardId": "SE2b.3"}, {"ageGroup": "Frogs 2", "category": "Social and Emotional Development", "benchmark": "Seek assistance from familiar adults.", "standardId": "SE2b.4"}, {"ageGroup": "Frogs 2", "category": "Social and Emotional Development", "benchmark": "Demonstrate early signs of interest in unfamiliar adults.", "standardId": "SE2b.5"}, {"ageGroup": "Frogs 2", "category": "Social and Emotional Development", "benchmark": "Show interest in other children.", "standardId": "SE2c.1"}, {"ageGroup": "Frogs 2", "category": "Social and Emotional Development", "benchmark": "Repeat actions that elicit social responses from others.", "standardId": "SE2c.2"}, {"ageGroup": "Frogs 2", "category": "Social and Emotional Development", "benchmark": "Participate in simple back and forth interactions with peers.", "standardId": "SE2c.3"}, {"ageGroup": "Frogs 2", "category": "Social and Emotional Development", "benchmark": "Engage in associative play with peers.", "standardId": "SE2c.4"}, {"ageGroup": "Frogs 2", "category": "Social and Emotional Development", "benchmark": "With modeling and support, demonstrate socially competent behavior with peers.", "standardId": "SE2c.5"}, {"ageGroup": "Frogs 2", "category": "Social and Emotional Development", "benchmark": "React to emotional expressions of others.", "standardId": "SE2d.1"}, {"ageGroup": "Frogs 2", "category": "Social and Emotional Development", "benchmark": "Demonstrate awareness of the feelings expressed by others.", "standardId": "SE2d.2"}, {"ageGroup": "Frogs 2", "category": "Social and Emotional Development", "benchmark": "Demonstrate that others have feelings.", "standardId": "SE2d.3"}, {"ageGroup": "Frogs 2", "category": "Social and Emotional Development", "benchmark": "Respond in caring ways to another’s distress in some situations.", "standardId": "SE2d.4"}, {"ageGroup": "Frogs 2", "category": "Cognitive Development", "benchmark": "Show interest in people and objects.", "standardId": "AL3a.1"}, {"ageGroup": "Frogs 2", "category": "Cognitive Development", "benchmark": "Explore the environment through a variety of sensory motor activities.", "standardId": "AL3a.2"}, {"ageGroup": "Frogs 2", "category": "Cognitive Development", "benchmark": "Practice new skills with enthusiasm.", "standardId": "AL3a.3"}, {"ageGroup": "Frogs 2", "category": "Cognitive Development", "benchmark": "Demonstrate a willingness to try new activities.", "standardId": "AL3a.4"}, {"ageGroup": "Frogs 2", "category": "Cognitive Development", "benchmark": "Experiment in the environment with purpose.", "standardId": "AL3a.5"}, {"ageGroup": "Frogs 2", "category": "Cognitive Development", "benchmark": "Ask questions to gain information.", "standardId": "AL3a.6"}, {"ageGroup": "Frogs 2", "category": "Cognitive Development", "benchmark": "Respond to people and objects in their immediate environment based on past experience.", "standardId": "AL3b.1"}, {"ageGroup": "Frogs 2", "category": "Cognitive Development", "benchmark": "Respond to people and objects in their immediate environment based on past experience.", "standardId": "AL3b.2"}, {"ageGroup": "Frogs 2", "category": "Cognitive Development", "benchmark": "Use a variety of ways to meet simple goals.", "standardId": "AL3b.3"}, {"ageGroup": "Frogs 2", "category": "Cognitive Development", "benchmark": "Approach tasks with repeated trial and error.", "standardId": "AL3b.4"}, {"ageGroup": "Frogs 2", "category": "Cognitive Development", "benchmark": "Make choices to achieve a desired goal.", "standardId": "AL3b.5"}, {"ageGroup": "Frogs 2", "category": "Cognitive Development", "benchmark": "Use previous learning to inform new experiences with people and objects in their environment.", "standardId": "AL3b.6"}, {"ageGroup": "Frogs 2", "category": "Cognitive Development", "benchmark": "Demonstrate awareness of happenings in surroundings.", "standardId": "AL4a.1"}, {"ageGroup": "Frogs 2", "category": "Cognitive Development", "benchmark": "Focus on an activity, but is easily distracted.", "standardId": "AL4a.2"}, {"ageGroup": "Frogs 2", "category": "Cognitive Development", "benchmark": "Focus on an activity for short periods of time despite distractions.", "standardId": "AL4a.3"}, {"ageGroup": "Frogs 2", "category": "Cognitive Development", "benchmark": "Attempt to reproduce interesting and pleasurable affects and events.", "standardId": "AL4b.1"}, {"ageGroup": "Frogs 2", "category": "Cognitive Development", "benchmark": "Repeat actions intentionally to achieve goals.", "standardId": "AL4b.2"}, {"ageGroup": "Frogs 2", "category": "Cognitive Development", "benchmark": "Engage in self-initiated activities for sustained periods of time.", "standardId": "AL4b.3"}, {"ageGroup": "Frogs 2", "category": "Cognitive Development", "benchmark": "Make discoveries about self, others, and the environment.", "standardId": "AL5a.1"}, {"ageGroup": "Frogs 2", "category": "Cognitive Development", "benchmark": "Repeat actions intentionally to achieve goal.", "standardId": "AL5a.2"}, {"ageGroup": "Frogs 2", "category": "Cognitive Development", "benchmark": "Engage in self-initiated activities for sustained periods of time.", "standardId": "AL5a.3"}, {"ageGroup": "Frogs 2", "category": "Cognitive Development", "benchmark": "Demonstrate preferences, pleasure or displeasure when interacting with various media.", "standardId": "AL5b.1"}, {"ageGroup": "Frogs 2", "category": "Cognitive Development", "benchmark": "Seek out experiences with a variety of materials based on preferences and past experiences.", "standardId": "AL5b.2"}, {"ageGroup": "Frogs 2", "category": "Cognitive Development", "benchmark": "Use self-selected materials and media to express ideas and feelings.", "standardId": "AL5b.3"}, {"ageGroup": "Frogs 2", "category": "Physical Development", "benchmark": "Demonstrate strength and control of head, arms, legs, and trunk using purposeful movements.", "standardId": "PE6a.1"}, {"ageGroup": "Frogs 2", "category": "Physical Development", "benchmark": "Move with increasing coordination and balance, with or without adult support and/or assistive device.", "standardId": "PE6a.2"}, {"ageGroup": "Frogs 2", "category": "Physical Development", "benchmark": "Use locomotor skills with increasing coordination and balance.", "standardId": "PE6a.3"}, {"ageGroup": "Frogs 2", "category": "Physical Development", "benchmark": "Use a variety of non-locomotor body movements during play.", "standardId": "PE6a.4"}, {"ageGroup": "Frogs 2", "category": "Physical Development", "benchmark": "Transfer a toy from one hand to another by reaching, grasping and releasing.", "standardId": "PE6b.1"}, {"ageGroup": "Frogs 2", "category": "Physical Development", "benchmark": "Use both hands together to accomplish a task.", "standardId": "PE6b.2"}, {"ageGroup": "Frogs 2", "category": "Physical Development", "benchmark": "Coordinate the use of arms, hands, and fingers to accomplish tasks.", "standardId": "PE6b.3"}, {"ageGroup": "Frogs 2", "category": "Physical Development", "benchmark": "Take and chew small bites/pieces of finger food.", "standardId": "PE6c.1"}, {"ageGroup": "Frogs 2", "category": "Physical Development", "benchmark": "Take bites from whole foods and coordinate chewing and swallowing.", "standardId": "PE6c.2"}, {"ageGroup": "Frogs 2", "category": "Physical Development", "benchmark": "Use senses and movement to explore immediate surroundings.", "standardId": "PE6d.1"}, {"ageGroup": "Frogs 2", "category": "Physical Development", "benchmark": "Coordinate senses with movement.", "standardId": "PE6d.2"}, {"ageGroup": "Frogs 2", "category": "Physical Development", "benchmark": "Use sensory information to guide movement to accomplish tasks.", "standardId": "PE6d.3"}, {"ageGroup": "Frogs 2", "category": "Physical Development", "benchmark": "Show awareness of own body.", "standardId": "PE7a.1"}, {"ageGroup": "Frogs 2", "category": "Physical Development", "benchmark": "Point to basic body parts when asked.", "standardId": "PE7a.2"}, {"ageGroup": "Frogs 2", "category": "Physical Development", "benchmark": "Name, point to, and move body parts when asked.", "standardId": "PE7a.3"}, {"ageGroup": "Frogs 2", "category": "Physical Development", "benchmark": "Express when hungry or full.", "standardId": "PE7b.1"}, {"ageGroup": "Frogs 2", "category": "Physical Development", "benchmark": "Follow a regular eating routine.", "standardId": "PE7b.2"}, {"ageGroup": "Frogs 2", "category": "Physical Development", "benchmark": "Make simple food choices, has food preferences and demonstrate willingness to try new foods.", "standardId": "PE7b.3"}, {"ageGroup": "Frogs 2", "category": "Physical Development", "benchmark": "Follow adult intervention/guidance regarding safety.", "standardId": "PE7c.1"}, {"ageGroup": "Frogs 2", "category": "Physical Development", "benchmark": "Cooperate and or/stop a behavior in response to a direction regarding safety.", "standardId": "PE7c.2"}, {"ageGroup": "Frogs 2", "category": "Physical Development", "benchmark": "Use adults as resources when needing help in potentially unsafe or dangerous situations.", "standardId": "Pe7c.3"}, {"ageGroup": "Frogs 2", "category": "Physical Development", "benchmark": "Interact with adults in physical activities.", "standardId": "PE7d.1"}, {"ageGroup": "Frogs 2", "category": "Physical Development", "benchmark": "Using simple movement skills, participate in active physical play.", "standardId": "PE7d.2"}, {"ageGroup": "Frogs 2", "category": "Physical Development", "benchmark": "Participate in active physical play and structured activities requiring spontaneous and instructed body movements.", "standardId": "PE7d.3"}, {"ageGroup": "Frogs 2", "category": "Physical Development", "benchmark": "Demonstrate emerging participation in dressing.", "standardId": "PE7e.1"}, {"ageGroup": "Frogs 2", "category": "Physical Development", "benchmark": "With adult assistance, participate in personal care tasks.", "standardId": "PE7e.2"}, {"ageGroup": "Frogs 2", "category": "Physical Development", "benchmark": "With modeling and support, complete personal care tasks.", "standardId": "PE7e.3"}, {"ageGroup": "Frogs 2", "category": "Cognition and General Knowledge", "benchmark": "Exhibit differentiated responses to familiar and unfamiliar people, events, objects, and their features.", "standardId": "CGK8a.1"}, {"ageGroup": "Frogs 2", "category": "Cognition and General Knowledge", "benchmark": "Mirror simple actions and facial expressions of others previously experienced.", "standardId": "CGK8a.2"}, {"ageGroup": "Frogs 2", "category": "Cognition and General Knowledge", "benchmark": "Recall information over a period of time with contextual cues.", "standardId": "CGK8a.3"}, {"ageGroup": "Frogs 2", "category": "Cognition and General Knowledge", "benchmark": "Mirror and repeat something seen at an earlier time", "standardId": "CGK8a.4"}, {"ageGroup": "Frogs 2", "category": "Cognition and General Knowledge", "benchmark": "Recall information over a longer period of time without contextual cues.", "standardId": "CGK8a.5"}, {"ageGroup": "Frogs 2", "category": "Cognition and General Knowledge", "benchmark": "Anticipate routines.", "standardId": "CGK8a.6"}, {"ageGroup": "Frogs 2", "category": "Cognition and General Knowledge", "benchmark": "Recall information over a longer period of time without contextual cues.", "standardId": "CGK8a.7"}, {"ageGroup": "Frogs 2", "category": "Cognition and General Knowledge", "benchmark": "Reenact a sequence of events accomplished or observed at an earlier time.", "standardId": "CGK8a.8"}, {"ageGroup": "Frogs 2", "category": "Cognition and General Knowledge", "benchmark": "Link past and present activities.", "standardId": "CGK8a.9"}, {"ageGroup": "Frogs 2", "category": "Cognition and General Knowledge", "benchmark": "Explore real objects, people, and actions.", "standardId": "CGK8b.1"}, {"ageGroup": "Frogs 2", "category": "Cognition and General Knowledge", "benchmark": "Use one or two simple actions or objects to represent another in pretend play.", "standardId": "CGK8b.2"}, {"ageGroup": "Frogs 2", "category": "Cognition and General Knowledge", "benchmark": "Engage in pretend play involving several sequenced steps and assigned roles.", "standardId": "CGK8b.3"}, {"ageGroup": "Frogs 2", "category": "Cognition and General Knowledge", "benchmark": "Actively use the body to find out about the world.", "standardId": "CGK8c.1"}, {"ageGroup": "Frogs 2", "category": "Cognition and General Knowledge", "benchmark": "With modeling and support, use simple strategies to solve problems.", "standardId": "CGK8c.2"}, {"ageGroup": "Frogs 2", "category": "Cognition and General Knowledge", "benchmark": "In familiar situations, solve problems without having to try every possibility, while avoiding solutions that clearly will not work.", "standardId": "CGK8c.3"}, {"ageGroup": "Frogs 2", "category": "Mathematics", "benchmark": "Explore objects and attend to events in the environment.", "standardId": "MATH9a.1"}, {"ageGroup": "Frogs 2", "category": "Mathematics", "benchmark": "Explore objects and attend to events in the environment.", "standardId": "MATH9a.2"}, {"ageGroup": "Frogs 2", "category": "Mathematics", "benchmark": "Pay attention to quantities when interacting with objects.", "standardId": "MATH9a.3"}, {"ageGroup": "Frogs 2", "category": "Mathematics", "benchmark": "Show understanding that numbers represent quantity and demonstrate understanding of words that identify how much.", "standardId": "MATH9a.4"}, {"ageGroup": "Frogs 2", "category": "Mathematics", "benchmark": "Use number words to indicate the quantity in small sets of objects, and begin counting aloud.", "standardId": "MATH9a.5"}, {"ageGroup": "Frogs 2", "category": "Mathematics", "benchmark": "Demonstrating an understanding the adding to increases the number of objects in a group.", "standardId": "MATH9b.1"}, {"ageGroup": "Frogs 2", "category": "Mathematics", "benchmark": "Place objects in one-to-one correspondence relationships during play.", "standardId": "MATH9b.2"}, {"ageGroup": "Frogs 2", "category": "Mathematics", "benchmark": "Notice differences between familiar and unfamiliar people, objects, and places.", "standardId": "MATH10a.1"}, {"ageGroup": "Frogs 2", "category": "Mathematics", "benchmark": "Match two objects that are the same and select similar objects from a group.", "standardId": "MATH10a.2"}, {"ageGroup": "Frogs 2", "category": "Mathematics", "benchmark": "Sort objects into two or more groups by their properties and uses.", "standardId": "MATH10a.3"}, {"ageGroup": "Frogs 2", "category": "Mathematics", "benchmark": "Imitate repeated movements.", "standardId": "MATH10b.1"}, {"ageGroup": "Frogs 2", "category": "Mathematics", "benchmark": "Participate in adult-initiated movement patterns.", "standardId": "MATH10b.2"}, {"ageGroup": "Frogs 2", "category": "Mathematics", "benchmark": "Copy and anticipate a repeating pattern.", "standardId": "MATH10b.3"}, {"ageGroup": "Frogs 2", "category": "Mathematics", "benchmark": "Explore the properties of objects.", "standardId": "MATH11a.1"}, {"ageGroup": "Frogs 2", "category": "Mathematics", "benchmark": "Explore how things fit and move in space.", "standardId": "MATH11a.2"}, {"ageGroup": "Frogs 2", "category": "Mathematics", "benchmark": "Demonstrate how things fit together and or move in space with increasing accuracy.", "standardId": "MATH11a.3"}, {"ageGroup": "Frogs 2", "category": "Mathematics", "benchmark": "Explore properties of objects.", "standardId": "MATH11b.1"}, {"ageGroup": "Frogs 2", "category": "Mathematics", "benchmark": "Show awareness of the size of objects.", "standardId": "MATH11b.2"}, {"ageGroup": "Frogs 2", "category": "Mathematics", "benchmark": "Demonstrate awareness that objects can be compared by attributes, and begin to use words such as bigger, smaller, and longer.", "standardId": "MATH11b.3"}, {"ageGroup": "Frogs 2", "category": "Mathematics", "benchmark": "Recognize basic shapes.", "standardId": "MATH11b.1"}, {"ageGroup": "Frogs 2", "category": "Social Studies", "benchmark": "Show awareness of self and awareness of other people.", "standardId": "SS12a.1"}, {"ageGroup": "Frogs 2", "category": "Social Studies", "benchmark": "Prefer familiar adults and recognize familiar actions and routines.", "standardId": "SS12a.2"}, {"ageGroup": "Frogs 2", "category": "Social Studies", "benchmark": "Identify self and others as belonging to one or more groups by observable characteristics.", "standardId": "SS12a.3"}, {"ageGroup": "Frogs 2", "category": "Science", "benchmark": "Examine objects with lips and tongue.", "standardId": "SCI13a.1"}, {"ageGroup": "Frogs 2", "category": "Science", "benchmark": "Observe, hold, touch, and manipulate objects.", "standardId": "SCI13a.2"}, {"ageGroup": "Frogs 2", "category": "Science", "benchmark": "Try different things with objects to see what happens or how things work.", "standardId": "SCI13a.3"}, {"ageGroup": "Frogs 2", "category": "Science", "benchmark": "Observe the physical and natural world around them.", "standardId": "SCI13a.4"}, {"ageGroup": "Frogs 2", "category": "Science", "benchmark": "Engage in sustained and complex manipulation or objects.", "standardId": "SCI13a.4"}, {"ageGroup": "Frogs 2", "category": "Science", "benchmark": "Engage in focused observations or objects and events in the environment.", "standardId": "SCI13a.5"}, {"ageGroup": "Frogs 2", "category": "Science", "benchmark": "Ask questions about objects and events in the environment.", "standardId": "SCI13a.6"}, {"ageGroup": "Frogs 2", "category": "Science", "benchmark": "With modeling and support, use simple tools to explore the environment.", "standardId": "SCI13a.7"}, {"ageGroup": "Frogs 2", "category": "Science", "benchmark": "Use simple actions to make things happen.", "standardId": "SCI13b.1"}, {"ageGroup": "Frogs 2", "category": "Science", "benchmark": "Purposefully combine actions to make things happen.", "standardId": "SCI13b.2"}, {"ageGroup": "Frogs 2", "category": "Science", "benchmark": "Demonstrate understanding that events have a cause.", "standardId": "SCI13b.3"}, {"ageGroup": "Frogs 2", "category": "Science", "benchmark": "Make predictions.", "standardId": "SCI13b.4"}, {"ageGroup": "Frogs 2", "category": "Language and Literacy Development", "benchmark": "Attends and responds to language and sounds.", "standardId": "LL14a.1"}, {"ageGroup": "Frogs 2", "category": "Language and Literacy Development", "benchmark": "Show understanding of simple requests and statements referring to people and objects around him/her.", "standardId": "LL14a.2"}, {"ageGroup": "Frogs 2", "category": "Language and Literacy Development", "benchmark": "Show understanding of requests and statements referring to people, objects, ideas, and feelings.", "standardId": "LL14a.3"}, {"ageGroup": "Frogs 2", "category": "Language and Literacy Development", "benchmark": "Demonstrate interest in and use words that are new or unfamiliar in conversation and play.", "standardId": "LL14a.4"}, {"ageGroup": "Frogs 2", "category": "Language and Literacy Development", "benchmark": "Understand when words are used in unconventional ways.", "standardId": "LL14a.5"}, {"ageGroup": "Frogs 2", "category": "Language and Literacy Development", "benchmark": "Experiment intentionally with sound inflection and gestures in different ways to express wants, needs, or feelings.", "standardId": "LL14b.1"}, {"ageGroup": "Frogs 2", "category": "Language and Literacy Development", "benchmark": "Begin to use single words and conventional gestures to communicate with others.", "standardId": "LL14b.2"}, {"ageGroup": "Frogs 2", "category": "Language and Literacy Development", "benchmark": "Combine words to express more complex ideas, or requests.", "standardId": "LL14b.3"}, {"ageGroup": "Frogs 2", "category": "Language and Literacy Development", "benchmark": "With modeling and support, describe experiences with people, places, and things.", "standardId": "LL14b.4"}, {"ageGroup": "Frogs 2", "category": "Language and Literacy Development", "benchmark": "Use words that indicate position and direction.", "standardId": "LL14b.5"}, {"ageGroup": "Frogs 2", "category": "Language and Literacy Development", "benchmark": "Attempt to respond to basic forms of social communication with the appropriate facial expression, vocalization, and or gesture.", "standardId": "LL14c.1"}, {"ageGroup": "Frogs 2", "category": "Language and Literacy Development", "benchmark": "Participate in and often initiate basic communication with family members or familiar others.", "standardId": "LL14c.2"}, {"ageGroup": "Frogs 2", "category": "Language and Literacy Development", "benchmark": "Participate in and often initiate communication according to commonly accepted expectations with family members and in social groups.", "standardId": "LL14c.3"}, {"ageGroup": "Frogs 2", "category": "Language and Literacy Development", "benchmark": "Show interest in books, pictures, songs, and rhymes.", "standardId": "LL15a.1"}, {"ageGroup": "Frogs 2", "category": "Language and Literacy Development", "benchmark": "Actively participate in book reading, story-telling, and singing.", "standardId": "LL15a.2"}, {"ageGroup": "Frogs 2", "category": "Language and Literacy Development", "benchmark": "Show an appreciation for reading books, telling stories, and singing.", "standardId": "LL15b.3"}, {"ageGroup": "Owl", "category": "Social Emotional", "benchmark": "Uses adult support to calm self", "standardId": "SE1a.1"}, {"ageGroup": "Owl", "category": "Social Emotional", "benchmark": "Comforts self by seeking out special object/person", "standardId": "SE1a.2"}, {"ageGroup": "Owl", "category": "Social Emotional", "benchmark": "Controls strong emotions in an appropriate manner most of time", "standardId": "SE1a.3"}, {"ageGroup": "Owl", "category": "Social Emotional", "benchmark": "Manages strong emotions using known strategies", "standardId": "SE1a.4"}, {"ageGroup": "Owl", "category": "Social Emotional", "benchmark": "Responds to changes in adult’s tone/expression", "standardId": "SE1b.1"}, {"ageGroup": "Owl", "category": "Social Emotional", "benchmark": "Accepts redirection from adults", "standardId": "SE1b.2"}, {"ageGroup": "Owl", "category": "Social Emotional", "benchmark": "Manages classroom rules, routines, transitions with occasional reminders", "standardId": "SE1b.3"}, {"ageGroup": "Owl", "category": "Social Emotional", "benchmark": "Indicates needs and wants, participates as adult attends to needs", "standardId": "SE1c.1"}, {"ageGroup": "Owl", "category": "Social Emotional", "benchmark": "Seeks to do things for self", "standardId": "SE1c.2"}, {"ageGroup": "Owl", "category": "Social Emotional", "benchmark": "Confident in meeting own needs", "standardId": "SE1c.3"}, {"ageGroup": "Owl", "category": "Social Emotional", "benchmark": "Demonstrates a secure attachment to one or more adults", "standardId": "SE2a.1"}, {"ageGroup": "Owl", "category": "Social Emotional", "benchmark": "Uses trusted adult as a secure base from which to explore the world", "standardId": "SE2a.2"}, {"ageGroup": "Owl", "category": "Social Emotional", "benchmark": "Manages separations without distress/engages with trusted adults", "standardId": "SE2a.3"}, {"ageGroup": "Owl", "category": "Social Emotional", "benchmark": "Engages with trusted adults as resources and to share mutual interests", "standardId": "SE2a.4"}, {"ageGroup": "Owl", "category": "Social Emotional", "benchmark": "Reacts to others’ emotional expressions", "standardId": "SE2b.1"}, {"ageGroup": "Owl", "category": "Social Emotional", "benchmark": "Demonstrates concern about others feelings", "standardId": "SE2b.2"}, {"ageGroup": "Owl", "category": "Social Emotional", "benchmark": "Identifies basic emotional reactions of others and their causes", "standardId": "SE2b.3"}, {"ageGroup": "Owl", "category": "Social Emotional", "benchmark": "Plays near other children uses similar materials", "standardId": "SE2c.1"}, {"ageGroup": "Owl", "category": "Social Emotional", "benchmark": "Successful strategies for entering groups", "standardId": "SE2c.1"}, {"ageGroup": "Owl", "category": "Social Emotional", "benchmark": "Initiates joins in, and sustains positive interactions with small group of 44960", "standardId": "SE2c.2"}, {"ageGroup": "Owl", "category": "Social Emotional", "benchmark": "Seeks a preferred playmate", "standardId": "SE2d.1"}, {"ageGroup": "Owl", "category": "Social Emotional", "benchmark": "Plays with 44928 preferred playmates", "standardId": "SE2d.2"}, {"ageGroup": "Owl", "category": "Social Emotional", "benchmark": "Establishes special friendship with 1 other child (short time)", "standardId": "SE2d.3"}, {"ageGroup": "Owl", "category": "Social Emotional", "benchmark": "Responds appropriately to others’ expressions of wants", "standardId": "SE3a.1"}, {"ageGroup": "Owl", "category": "Social Emotional", "benchmark": "Takes Turns", "standardId": "SE3a.2"}, {"ageGroup": "Owl", "category": "Social Emotional", "benchmark": "Initiates the sharing of Materials", "standardId": "SE3a.3"}, {"ageGroup": "Owl", "category": "Social Emotional", "benchmark": "Cooperates and shares ideas and materials in socially acceptable way", "standardId": "SE3a.4"}, {"ageGroup": "Owl", "category": "Social Emotional", "benchmark": "Completes cooperative projects with other children", "standardId": "SE3a.5"}, {"ageGroup": "Owl", "category": "Social Emotional", "benchmark": "Expresses feelings during conflict", "standardId": "SE3b.1"}, {"ageGroup": "Owl", "category": "Social Emotional", "benchmark": "Seeks adult help to resolve social problems", "standardId": "SE3b.2"}, {"ageGroup": "Owl", "category": "Social Emotional", "benchmark": "Suggests solutions to social problems", "standardId": "SE3b.3"}, {"ageGroup": "Owl", "category": "Physical Development", "benchmark": "Walks across room", "standardId": "PE4.1"}, {"ageGroup": "Owl", "category": "Physical Development", "benchmark": "Uses Hurried Walk", "standardId": "PE4.2"}, {"ageGroup": "Owl", "category": "Physical Development", "benchmark": "Walks Backwards", "standardId": "PE4.3"}, {"ageGroup": "Owl", "category": "Physical Development", "benchmark": "Pushes Riding Toy with Feet", "standardId": "PE4.4"}, {"ageGroup": "Owl", "category": "Physical Development", "benchmark": "Marches", "standardId": "PE4.5"}, {"ageGroup": "Owl", "category": "Physical Development", "benchmark": "Runs", "standardId": "PE4.6"}, {"ageGroup": "Owl", "category": "Physical Development", "benchmark": "Squats to Pick Up Toys", "standardId": "PE5.1"}, {"ageGroup": "Owl", "category": "Physical Development", "benchmark": "Stands on tiptoes to reach something", "standardId": "PE5.2"}, {"ageGroup": "Owl", "category": "Physical Development", "benchmark": "Gets in and out of hhair", "standardId": "PE5.3"}, {"ageGroup": "Owl", "category": "Physical Development", "benchmark": "<PERSON><PERSON><PERSON> while playing", "standardId": "PE5.4"}, {"ageGroup": "Owl", "category": "Physical Development", "benchmark": "Sidesteps across beam", "standardId": "PE5.5"}, {"ageGroup": "Owl", "category": "Physical Development", "benchmark": "Carries a large ball while moving", "standardId": "PE6.1"}, {"ageGroup": "Owl", "category": "Physical Development", "benchmark": "Flings a beanbag", "standardId": "PE6.2"}, {"ageGroup": "Owl", "category": "Physical Development", "benchmark": "Throws a bal by pushing it with two hands", "standardId": "PE6.3"}, {"ageGroup": "Owl", "category": "Physical Development", "benchmark": "Catches a large bounced ball against body with straight arms", "standardId": "PE6.3"}, {"ageGroup": "Owl", "category": "Physical Development", "benchmark": "Places shapes in sorter", "standardId": "PE7a.1"}, {"ageGroup": "Owl", "category": "Physical Development", "benchmark": "Points at objects and pokes bubbles", "standardId": "PE7a.2"}, {"ageGroup": "Owl", "category": "Physical Development", "benchmark": "Uses spoon and sometimes a fork to feed self", "standardId": "PE7a.3"}, {"ageGroup": "Owl", "category": "Physical Development", "benchmark": "Dumbs sand into containers", "standardId": "PE7a.4"}, {"ageGroup": "Owl", "category": "Physical Development", "benchmark": "Pours water into containers", "standardId": "PE7a.5"}, {"ageGroup": "Owl", "category": "Physical Development", "benchmark": "Pounds, pokes, squeezes, rolls clay", "standardId": "PE7a.6"}, {"ageGroup": "Owl", "category": "Physical Development", "benchmark": "Tears Paper", "standardId": "PE7a.7"}, {"ageGroup": "Owl", "category": "Physical Development", "benchmark": "Strings large beads", "standardId": "PE7a.8"}, {"ageGroup": "Owl", "category": "Physical Development", "benchmark": "Grips drawing and writing tools with whole hand but may use whole arm movements to make marks -4", "standardId": "PE7b.1"}, {"ageGroup": "Owl", "category": "Language Development", "benchmark": "Identifies familiar people, animals, and objects when prompted", "standardId": "LANG8a.1"}, {"ageGroup": "Owl", "category": "Language Development", "benchmark": "Follows simple requests not accompanied by gestures", "standardId": "LANG8b.1"}, {"ageGroup": "Owl", "category": "Language Development", "benchmark": "Follows directions of two or more steps that relate to familiar objects and experiences", "standardId": "LANG8b.2"}, {"ageGroup": "Owl", "category": "Language Development", "benchmark": "Names familiar people, animals, and objects", "standardId": "LANG9a.1"}, {"ageGroup": "Owl", "category": "Language Development", "benchmark": "Describes and tells the use of many familiar items", "standardId": "LANG9a.2"}, {"ageGroup": "Owl", "category": "Language Development", "benchmark": "Uses some words and word-like sounds and is understood by most familiar people", "standardId": "LANG9b.1"}, {"ageGroup": "Owl", "category": "Language Development", "benchmark": "Is understood by most people; may mispronounce new, long, or unusual words", "standardId": "LANG9b.2"}, {"ageGroup": "Owl", "category": "Language Development", "benchmark": "Uses one or two word sentences or phrases", "standardId": "LANG9c.1"}, {"ageGroup": "Owl", "category": "Language Development", "benchmark": "Uses three to four word sentences; may omit some words or use some words incorrectly", "standardId": "LANG9c.2"}, {"ageGroup": "Owl", "category": "Language Development", "benchmark": "Makes simple statements about recent events and familiar people and objects that are not present", "standardId": "LANG9d.1"}, {"ageGroup": "Owl", "category": "Language Development", "benchmark": "Tells simple stories about objects, events, and people not present; lacks many details and a conventional beginning, middle, and end", "standardId": "LANG9d.2"}, {"ageGroup": "Owl", "category": "Language Development", "benchmark": "Initiates and attends to brief conversations", "standardId": "LANG10a.1"}, {"ageGroup": "Owl", "category": "Language Development", "benchmark": "Engages in conversations of at least three exchanges", "standardId": "LANG10a.2"}, {"ageGroup": "Owl", "category": "Language Development", "benchmark": "Responds to speech by looking toward the speaker; watches for signs of being understood when communicating", "standardId": "LANG10b.1"}, {"ageGroup": "Owl", "category": "Language Development", "benchmark": "Uses appropriate eye contact, pauses, and simple verbal prompts when communicating", "standardId": "LANG10b.2"}, {"ageGroup": "Owl", "category": "Language Development", "benchmark": "Uses acceptable language and basic social rules while communicating with others; may need reminders", "standardId": "LANG10b.3"}, {"ageGroup": "Owl", "category": "Cognitive Development", "benchmark": "Sustains interest in woring on a task especially when adults offer suggestions, questions and comments", "standardId": "AL11a.1"}, {"ageGroup": "Owl", "category": "Cognitive Development", "benchmark": "Sustains work on age-appropriate, interesting tasks; can ignore most distractions and interruptions", "standardId": "AL11a.2"}, {"ageGroup": "Owl", "category": "Cognitive Development", "benchmark": "Practices an activity many times until successful", "standardId": "AL11b.1"}, {"ageGroup": "Owl", "category": "Cognitive Development", "benchmark": "Observes and imitates how other people solve problems; asks for a solution and uses it", "standardId": "AL11c.1"}, {"ageGroup": "Owl", "category": "Cognitive Development", "benchmark": "Explores and investigates ways to make something happen", "standardId": "AL11d.1"}, {"ageGroup": "Owl", "category": "Cognitive Development", "benchmark": "Imitates others in using objects in new and/or unanticipated ways", "standardId": "AL11e.1"}, {"ageGroup": "Owl", "category": "Cognitive Development", "benchmark": "Uses creativity and imagination during play and routine tasks", "standardId": "AL11e.2"}, {"ageGroup": "Owl", "category": "Cognitive Development", "benchmark": "Recognizes familiar people, places and objects; looks for hidden object where it was last seen", "standardId": "AL12a.1"}, {"ageGroup": "Owl", "category": "Cognitive Development", "benchmark": "Recalls familiar people, places, objects, and actions from the pat (a few months before); recalls one or two items removed from view", "standardId": "AL12a.2"}, {"ageGroup": "Owl", "category": "Cognitive Development", "benchmark": "Looks for a familiar persons when they are named; relates objects to events", "standardId": "AL12b.1"}, {"ageGroup": "Owl", "category": "Cognitive Development", "benchmark": "Remembers the sequence of personal routines and experiences with teacher support", "standardId": "AL12b.2"}, {"ageGroup": "Owl", "category": "Cognitive Development", "benchmark": "Matches similar objects", "standardId": "AL13a.1"}, {"ageGroup": "Owl", "category": "Cognitive Development", "benchmark": "Places objects in two or more groups based on differences in a single characteristic (color, size, shape)", "standardId": "AL13a.2"}, {"ageGroup": "Owl", "category": "Cognitive Development", "benchmark": "Recognizes people, objects, and animals in pictures or photographs", "standardId": "AL14a.1"}, {"ageGroup": "Owl", "category": "Cognitive Development", "benchmark": "Draws or constructs, and then identifies what it is", "standardId": "AL14a.2"}, {"ageGroup": "Owl", "category": "Cognitive Development", "benchmark": "Imitates actions of others during play; uses real objects as props", "standardId": "AL14b.1"}, {"ageGroup": "Owl", "category": "Cognitive Development", "benchmark": "Acts out familiar or imaginary scenarios; may use props to stand for something else", "standardId": "AL14b.2"}, {"ageGroup": "Owl", "category": "Literacy", "benchmark": "Joins in rhyming songs and games", "standardId": "LIT15a.1"}, {"ageGroup": "Owl", "category": "Literacy", "benchmark": "Sings songs and recites rhymes and refrains with repeating initial sounds", "standardId": "LIT15b.1"}, {"ageGroup": "Owl", "category": "Literacy", "benchmark": "Shows awareness of separate words in sentences", "standardId": "LIT15c.1"}, {"ageGroup": "Owl", "category": "Literacy", "benchmark": "Recognizes and names a few letters in own name", "standardId": "LIT16a.1"}, {"ageGroup": "Owl", "category": "Literacy", "benchmark": "Shows interest in books", "standardId": "LIT17a.1"}, {"ageGroup": "Owl", "category": "Literacy", "benchmark": "Orients book correctly", "standardId": "LIT17a.2"}, {"ageGroup": "Owl", "category": "Literacy", "benchmark": "Turns pages from the front of the book t the back", "standardId": "LIT17a.3"}, {"ageGroup": "Owl", "category": "Literacy", "benchmark": "Recognizes familiar books by their covers", "standardId": "LIT17a.4"}, {"ageGroup": "Owl", "category": "Literacy", "benchmark": "Shows understanding that text is meaningful and can be read", "standardId": "LIT17b.1"}, {"ageGroup": "Owl", "category": "Literacy", "benchmark": "Contributes particular language from the book at the appropriate time", "standardId": "LIT18a.1"}, {"ageGroup": "Owl", "category": "Literacy", "benchmark": "Asks and answers questions about the text; refers to pictures", "standardId": "LIT18a.2"}, {"ageGroup": "Owl", "category": "Literacy", "benchmark": "Pretends to read a familiar book, treating each page as a separate unit", "standardId": "LIT18b.1"}, {"ageGroup": "Owl", "category": "Literacy", "benchmark": "Names and describes what is on each page, using pictures as clues", "standardId": "LIT18b.2"}, {"ageGroup": "Owl", "category": "Literacy", "benchmark": "Pretends to read using some of the language from the text", "standardId": "LIT18b.3"}, {"ageGroup": "Owl", "category": "Literacy", "benchmark": "Describes the action across the pages, using pictures to order the events (may need prompts from adult)", "standardId": "LIT18b.4"}, {"ageGroup": "Owl", "category": "Literacy", "benchmark": "Retells some events or information from a familiar story or other text with close adult prompting", "standardId": "LIT18c.1"}, {"ageGroup": "Owl", "category": "Literacy", "benchmark": "Makes scribbles or marks", "standardId": "LIT19a.1"}, {"ageGroup": "Owl", "category": "Literacy", "benchmark": "Makes controlled linear scribbles", "standardId": "LIT19a.2"}, {"ageGroup": "Owl", "category": "Literacy", "benchmark": "Uses drawing, dictation and controlled linear scribbles to convey a message", "standardId": "LIT19b.1"}, {"ageGroup": "Owl", "category": "Mathematics", "benchmark": "Verbally counts (not always in correct order)", "standardId": "MATH20a.1"}, {"ageGroup": "Owl", "category": "Mathematics", "benchmark": "Verbally counts to 10; counts up to five objects accurately, using one number name for each object", "standardId": "MATH20a.2"}, {"ageGroup": "Owl", "category": "Mathematics", "benchmark": "Demonstrates understanding of the concepts of one, two, and more", "standardId": "MATH20b.1"}, {"ageGroup": "Owl", "category": "Mathematics", "benchmark": "Recognizes and names the number of items in a small set (up to 5) instantly; combines and separates up to five objects and describes the parts", "standardId": "MATH20b.2"}, {"ageGroup": "Owl", "category": "Mathematics", "benchmark": "Recognizes and names a few numerals", "standardId": "MATH20c.1"}, {"ageGroup": "Owl", "category": "Mathematics", "benchmark": "Follows simple directions related to position (in, on, under, up, down)", "standardId": "MATH21a.1"}, {"ageGroup": "Owl", "category": "Mathematics", "benchmark": "Matches two identical shapes", "standardId": "MATH21b.1"}, {"ageGroup": "Owl", "category": "Mathematics", "benchmark": "Identifies a few basic shapes (circle, square, triangle)", "standardId": "MATH21b.2"}, {"ageGroup": "Owl", "category": "Mathematics", "benchmark": "Makes simple comparisons between two objects", "standardId": "MATH22a.1"}, {"ageGroup": "Owl", "category": "Mathematics", "benchmark": "Compares and orders a small set of objects as appropriate according to size, length, weight, area, or volume", "standardId": "MATH22a.2"}, {"ageGroup": "Owl", "category": "Mathematics", "benchmark": "Knows usual sequence of basic daily events", "standardId": "MATH22b.1"}, {"ageGroup": "Owl", "category": "Mathematics", "benchmark": "Relates time to daily routines and schedule", "standardId": "MATH22b.2"}, {"ageGroup": "Owl", "category": "Mathematics", "benchmark": "knows a few ordinal numbers", "standardId": "MATH22c.1"}, {"ageGroup": "Owl", "category": "Mathematics", "benchmark": "Shows interest in simple patterns in everyday life", "standardId": "MATH23a.1"}, {"ageGroup": "Owl", "category": "Mathematics", "benchmark": "Copies simple repeating patterns", "standardId": "MATH23a.2"}, {"ageGroup": "Owl", "category": "Science and Technology", "benchmark": "Observes and explores things in the environment", "standardId": "ST24a.1"}, {"ageGroup": "Owl", "category": "Science and Technology", "benchmark": "Reacts to changes", "standardId": "ST24a.2"}, {"ageGroup": "Owl", "category": "Science and Technology", "benchmark": "Manipulates objects to understand their properties", "standardId": "ST24a.3"}, {"ageGroup": "Owl", "category": "Science and Technology", "benchmark": "Connects new observations to what already knows", "standardId": "ST24a.4"}, {"ageGroup": "Owl", "category": "Science and Technology", "benchmark": "Shows a growing ability to classify living and nonliving things", "standardId": "ST25a.1"}, {"ageGroup": "Owl", "category": "Science and Technology", "benchmark": "Communicates about the characteristics of living things", "standardId": "ST25a.2"}, {"ageGroup": "Owl", "category": "Science and Technology", "benchmark": "Demonstrates understanding that living things grow, change, and reproduce", "standardId": "ST25a.3"}, {"ageGroup": "Owl", "category": "Science and Technology", "benchmark": "Examines, describes, and measures the observable features of objects", "standardId": "ST26a.1"}, {"ageGroup": "Owl", "category": "Science and Technology", "benchmark": "Demonstrates understanding that objects are made from one or more materials (metal, wood, plastic, or paper)", "standardId": "ST26a.2"}, {"ageGroup": "Owl", "category": "Science and Technology", "benchmark": "Communicates that the physical properties of objects and materials can change (when solid ice becomes liquid)", "standardId": "ST26a.3"}, {"ageGroup": "Owl", "category": "Science and Technology", "benchmark": "Demonstrates understanding that there are different kinds of weather and that weather changes", "standardId": "ST27a.1"}, {"ageGroup": "Owl", "category": "Science and Technology", "benchmark": "Describes and measures weather", "standardId": "ST27a.2"}, {"ageGroup": "Owl", "category": "Science and Technology", "benchmark": "Shows understanding that different tools and technology are used in different places for different purposes (finding information, communicating, and designing)", "standardId": "ST28a.1"}, {"ageGroup": "Owl", "category": "Science and Technology", "benchmark": "Demonstrates the appropriate use of various tools and other technology", "standardId": "ST28a.2"}, {"ageGroup": "Owl", "category": "Social Sudies", "benchmark": "Demonstrates understanding that each person has unique characteristics, ways of communicating, and ways of solving problems", "standardId": "SS29a.1"}, {"ageGroup": "Owl", "category": "Social Sudies", "benchmark": "Communicates that each person is part of a family that has unique characteristics", "standardId": "SS29a.2"}, {"ageGroup": "Owl", "category": "Social Sudies", "benchmark": "Shows awareness that each person has basic needs that must be met to stay healthy (food, shelter, clothing)", "standardId": "SS29a.3"}, {"ageGroup": "Owl", "category": "Social Sudies", "benchmark": "Shows awareness that there are similarities and differences among people and families", "standardId": "SS30a.1"}, {"ageGroup": "Owl", "category": "Social Sudies", "benchmark": "Demonstrates understanding of the various jobs of people in the community", "standardId": "SS30a.2"}, {"ageGroup": "Owl", "category": "Social Sudies", "benchmark": "Demonstrates understanding that people and things change over time", "standardId": "SS31a.1"}, {"ageGroup": "Owl", "category": "Social Sudies", "benchmark": "Demonstrates understanding that we are surrounded by geographical features (mountain, hill, desert, lake, river, creek, bayou)", "standardId": "SS32a.1"}, {"ageGroup": "Owl", "category": "The Arts", "benchmark": "Shows appreciation for various forms of visual art", "standardId": "ART33a.1"}, {"ageGroup": "Owl", "category": "The Arts", "benchmark": "Shows appreciation for the artwork of peers", "standardId": "ART33a.2"}, {"ageGroup": "Owl", "category": "The Arts", "benchmark": "Communicates what he sees and how it makes him feel", "standardId": "ART33a.3"}, {"ageGroup": "Owl", "category": "The Arts", "benchmark": "Uses and cares for art materials", "standardId": "ART33a.4"}, {"ageGroup": "Owl", "category": "The Arts", "benchmark": "Explores different materials, tools, and processes", "standardId": "ART33a.5"}, {"ageGroup": "Owl", "category": "The Arts", "benchmark": "Shows increasing awareness of color, line, form, texture, space, and design in her artwork or the work of others", "standardId": "ART33a.6"}, {"ageGroup": "Owl", "category": "The Arts", "benchmark": "Communicates about his artwork", "standardId": "ART33a.7"}, {"ageGroup": "Owl", "category": "The Arts", "benchmark": "Shows awareness and appreciation of different kinds of music", "standardId": "ART34a.1"}, {"ageGroup": "Owl", "category": "The Arts", "benchmark": "Expresses thoughts, feelings, and energy through music", "standardId": "ART34a.2"}, {"ageGroup": "Owl", "category": "The Arts", "benchmark": "Shows increasing awareness of various components of music: melody, pitch, rhythm, tempo, dynamics, and timbre", "standardId": "ART34a.3"}, {"ageGroup": "Owl", "category": "The Arts", "benchmark": "Communicates feelings and ideas through dance and movement", "standardId": "ART35a.1"}, {"ageGroup": "Owl", "category": "The Arts", "benchmark": "Demonstrates spatial awareness, location, directions, levels, and pathways", "standardId": "ART35a.2"}, {"ageGroup": "Owl", "category": "The Arts", "benchmark": "Shows that real-life roles can be enacted", "standardId": "ART36a.1"}, {"ageGroup": "Owl", "category": "The Arts", "benchmark": "Communicates a message or story through action and dialogue", "standardId": "ART36a.2"}, {"ageGroup": "Bear", "category": "Social Emotional", "benchmark": "Uses adult support to calm self", "standardId": "SE1a.1"}, {"ageGroup": "Bear", "category": "Social Emotional", "benchmark": "Comforts self by seeking out special object/person", "standardId": "SE1a.2"}, {"ageGroup": "Bear", "category": "Social Emotional", "benchmark": "Able to look at situation differently delay gratification", "standardId": "SE1a.3"}, {"ageGroup": "Bear", "category": "Social Emotional", "benchmark": "Controls strong emotions in appropriate manner most of time", "standardId": "SE1a.4"}, {"ageGroup": "Bear", "category": "Social Emotional", "benchmark": "Manages strong emotions using known strategies", "standardId": "SE1a.5"}, {"ageGroup": "Bear", "category": "Social Emotional", "benchmark": "Demonstrates patience with personal limitations", "standardId": "SE1a.6"}, {"ageGroup": "Bear", "category": "Social Emotional", "benchmark": "Responds to changes in adult’s tone/expression", "standardId": "SE1b.1"}, {"ageGroup": "Bear", "category": "Social Emotional", "benchmark": "Accepts redirection from adults", "standardId": "SE1b.2"}, {"ageGroup": "Bear", "category": "Social Emotional", "benchmark": "Manages classroom rules, routines, transitions with occasional reminders", "standardId": "SE1b.3"}, {"ageGroup": "Bear", "category": "Social Emotional", "benchmark": "Applies basic rules in new but similar situations", "standardId": "SE1b.4"}, {"ageGroup": "Bear", "category": "Social Emotional", "benchmark": "Understands and explains reasons for rules", "standardId": "SE1b.5"}, {"ageGroup": "Bear", "category": "Social Emotional", "benchmark": "Understands “big rule” concepts", "standardId": "SE1b.6"}, {"ageGroup": "Bear", "category": "Social Emotional", "benchmark": "Indicates needs and wants, participates as adult attends to needs", "standardId": "SE1c.1"}, {"ageGroup": "Bear", "category": "Social Emotional", "benchmark": "Seeks to do things for self", "standardId": "SE1c.2"}, {"ageGroup": "Bear", "category": "Social Emotional", "benchmark": "Confident in meeting own needs", "standardId": "SE1c.3"}, {"ageGroup": "Bear", "category": "Social Emotional", "benchmark": "Takes responsibility for own well-being", "standardId": "SE1c.4"}, {"ageGroup": "Bear", "category": "Social Emotional", "benchmark": "Practices skills to reach the desired level of personal achievement", "standardId": "SE1c.5"}, {"ageGroup": "Bear", "category": "Social Emotional", "benchmark": "Accurately identifies own strengths and challenges", "standardId": "SE1c.6"}, {"ageGroup": "Bear", "category": "Social Emotional", "benchmark": "Demonstrates a secure attachment to one or more adults", "standardId": "SE2a.1"}, {"ageGroup": "Bear", "category": "Social Emotional", "benchmark": "Uses trusted adult as a secure base from which to explore the world", "standardId": "SE2a.2"}, {"ageGroup": "Bear", "category": "Social Emotional", "benchmark": "Manages separations without distress/engages with trusted adults", "standardId": "SE2a.3"}, {"ageGroup": "Bear", "category": "Social Emotional", "benchmark": "Engages with trusted adults as resources and to share mutual interests", "standardId": "SE2a.4"}, {"ageGroup": "Bear", "category": "Social Emotional", "benchmark": "Respectfully engages adult with different viewpoint", "standardId": "SE2a.5"}, {"ageGroup": "Bear", "category": "Social Emotional", "benchmark": "Reacts to others’ emotional expressions", "standardId": "SE2b.1"}, {"ageGroup": "Bear", "category": "Social Emotional", "benchmark": "Demonstrates concern about others feelings", "standardId": "SE2b.2"}, {"ageGroup": "Bear", "category": "Social Emotional", "benchmark": "Identifies basic emotional reactions of others and their causes", "standardId": "SE2b.3"}, {"ageGroup": "Bear", "category": "Social Emotional", "benchmark": "Recognizes that others’ feelings about a situation might be different", "standardId": "SE2b.4"}, {"ageGroup": "Bear", "category": "Social Emotional", "benchmark": "Recognizes that people can experience more than one emotion at the same time", "standardId": "SE2b.5"}, {"ageGroup": "Bear", "category": "Social Emotional", "benchmark": "Uses situational context and past experience to interpret others’ feelings", "standardId": "SE2b.6"}, {"ageGroup": "Bear", "category": "Social Emotional", "benchmark": "Plays near other children uses similar materials", "standardId": "SE2c.1"}, {"ageGroup": "Bear", "category": "Social Emotional", "benchmark": "Successful strategies for entering groups", "standardId": "SE2c.1"}, {"ageGroup": "Bear", "category": "Social Emotional", "benchmark": "Initiates joins in, and sustains positive interactions with small group of 44960", "standardId": "SE2c.2"}, {"ageGroup": "Bear", "category": "Social Emotional", "benchmark": "Interacts cooperatively in groups of 45021", "standardId": "SE2c.3"}, {"ageGroup": "Bear", "category": "Social Emotional", "benchmark": "Works with a group toward a shared goal", "standardId": "SE2c.4"}, {"ageGroup": "Bear", "category": "Social Emotional", "benchmark": "Alternates between the roles of leader and follower to sustain play -12", "standardId": "SE2c.5"}, {"ageGroup": "Bear", "category": "Social Emotional", "benchmark": "Seeks a preferred playmate", "standardId": "SE2d.1"}, {"ageGroup": "Bear", "category": "Social Emotional", "benchmark": "Plays with 44928 preferred playmates", "standardId": "SE2d.2"}, {"ageGroup": "Bear", "category": "Social Emotional", "benchmark": "Establishes special friendship with 1 other child (short time)", "standardId": "SE2d.3"}, {"ageGroup": "Bear", "category": "Social Emotional", "benchmark": "Maintains friendships for several months and around similar interests -6", "standardId": "SE2d.4"}, {"ageGroup": "Bear", "category": "Social Emotional", "benchmark": "Forms friendships based on personal qualities", "standardId": "SE2d.5"}, {"ageGroup": "Bear", "category": "Social Emotional", "benchmark": "Forms friendships based on mutual trust and understands they can still exist through disagreements", "standardId": "SE2d.6"}, {"ageGroup": "Bear", "category": "Social Emotional", "benchmark": "Responds appropriately to others’ expressions of wants", "standardId": "SE3a.1"}, {"ageGroup": "Bear", "category": "Social Emotional", "benchmark": "Takes Turns", "standardId": "SE3a.2"}, {"ageGroup": "Bear", "category": "Social Emotional", "benchmark": "Initiates the sharing of Materials", "standardId": "SE3a.3"}, {"ageGroup": "Bear", "category": "Social Emotional", "benchmark": "Cooperates and shares ideas and materials in socially acceptable way", "standardId": "SE3a.4"}, {"ageGroup": "Bear", "category": "Social Emotional", "benchmark": "Completes cooperative projects with other children", "standardId": "SE3a.5"}, {"ageGroup": "Bear", "category": "Social Emotional", "benchmark": "A<PERSON>ura<PERSON>y completes self-assessment of role in group work", "standardId": "SE3a.6"}, {"ageGroup": "Bear", "category": "Social Emotional", "benchmark": "Expresses feelings during conflict", "standardId": "SE3b.1"}, {"ageGroup": "Bear", "category": "Social Emotional", "benchmark": "Seeks adult help to resolve social problems", "standardId": "SE3b.2"}, {"ageGroup": "Bear", "category": "Social Emotional", "benchmark": "Suggests solutions to social problems", "standardId": "SE3b.3"}, {"ageGroup": "Bear", "category": "Social Emotional", "benchmark": "Resolves social problems through negotiation or compromise", "standardId": "SE3b.4"}, {"ageGroup": "Bear", "category": "Social Emotional", "benchmark": "Seeks conflict resolution based on interest in maintaining relationship", "standardId": "SE3b.5"}, {"ageGroup": "Bear", "category": "Social Emotional", "benchmark": "Considers multiple viewpoints when solving conflicts", "standardId": "SE3b.6"}, {"ageGroup": "Bear", "category": "Physical Development", "benchmark": "Runs", "standardId": "PE4.1"}, {"ageGroup": "Bear", "category": "Physical Development", "benchmark": "Avoids obstacles and people while moving", "standardId": "PE4.2"}, {"ageGroup": "Bear", "category": "Physical Development", "benchmark": "Starts and stops if using wheelchair", "standardId": "PE4.3"}, {"ageGroup": "Bear", "category": "Physical Development", "benchmark": "Walks up and down stairs alternating feet", "standardId": "PE4.4"}, {"ageGroup": "Bear", "category": "Physical Development", "benchmark": "Climbs up and down playground equipment", "standardId": "PE4.5"}, {"ageGroup": "Bear", "category": "Physical Development", "benchmark": "Rides tricycle using pedals", "standardId": "PE4.6"}, {"ageGroup": "Bear", "category": "Physical Development", "benchmark": "Gallops but not smoothly", "standardId": "PE4.7"}, {"ageGroup": "Bear", "category": "Physical Development", "benchmark": "Runs smoothly and quickly, changes directions, stops and starts", "standardId": "PE4.8"}, {"ageGroup": "Bear", "category": "Physical Development", "benchmark": "Steers wheelchair into small playground spaces", "standardId": "PE4.9"}, {"ageGroup": "Bear", "category": "Physical Development", "benchmark": "Jumps and spins", "standardId": "PE4.1"}, {"ageGroup": "Bear", "category": "Physical Development", "benchmark": "Moves through obstacle course", "standardId": "PE4.11"}, {"ageGroup": "Bear", "category": "Physical Development", "benchmark": "Gallops and skips with ease", "standardId": "PE4.12"}, {"ageGroup": "Bear", "category": "Physical Development", "benchmark": "Plays Follow The Leader using a variety of movements", "standardId": "PE4.13"}, {"ageGroup": "Bear", "category": "Physical Development", "benchmark": "Sidesteps across beam", "standardId": "PE5.1"}, {"ageGroup": "Bear", "category": "Physical Development", "benchmark": "Walks forward along sandbox edge, watching feet", "standardId": "PE5.2"}, {"ageGroup": "Bear", "category": "Physical Development", "benchmark": "Jumps off low step, landing on 2 feet", "standardId": "PE5.3"}, {"ageGroup": "Bear", "category": "Physical Development", "benchmark": "Jumps over small object", "standardId": "PE5.4"}, {"ageGroup": "Bear", "category": "Physical Development", "benchmark": "Holds body upright while moving wheelchair forward", "standardId": "PE5.5"}, {"ageGroup": "Bear", "category": "Physical Development", "benchmark": "Hops across the playground", "standardId": "PE5.6"}, {"ageGroup": "Bear", "category": "Physical Development", "benchmark": "Hops on one foot and then the other", "standardId": "PE5.7"}, {"ageGroup": "Bear", "category": "Physical Development", "benchmark": "Walks across beam or sandbox edge forward and backwards", "standardId": "PE5.8"}, {"ageGroup": "Bear", "category": "Physical Development", "benchmark": "Attempts to jump rope", "standardId": "PE5.9"}, {"ageGroup": "Bear", "category": "Physical Development", "benchmark": "Throws a ball or other object", "standardId": "PE6.1"}, {"ageGroup": "Bear", "category": "Physical Development", "benchmark": "Traps thrown ball against body", "standardId": "PE6.2"}, {"ageGroup": "Bear", "category": "Physical Development", "benchmark": "Tosses a beanbag into a basket", "standardId": "PE6.3"}, {"ageGroup": "Bear", "category": "Physical Development", "benchmark": "Strikes a balloon with large paddle", "standardId": "PE6.3"}, {"ageGroup": "Bear", "category": "Physical Development", "benchmark": "Kicks ball forward by stepping or running to it", "standardId": "PE6.4"}, {"ageGroup": "Bear", "category": "Physical Development", "benchmark": "Steps forward to throw ball and follows through", "standardId": "PE6.5"}, {"ageGroup": "Bear", "category": "Physical Development", "benchmark": "Catches large ball with both hands", "standardId": "PE6.6"}, {"ageGroup": "Bear", "category": "Physical Development", "benchmark": "Strikes stationary ball", "standardId": "PE6.7"}, {"ageGroup": "Bear", "category": "Physical Development", "benchmark": "Bounces and catches ball", "standardId": "PE6.8"}, {"ageGroup": "Bear", "category": "Physical Development", "benchmark": "Kicks moving ball while running", "standardId": "PE6.9"}, {"ageGroup": "Bear", "category": "Physical Development", "benchmark": "Tears paper", "standardId": "PE7a.1"}, {"ageGroup": "Bear", "category": "Physical Development", "benchmark": "Squeezes and releases tongs, baster, squirt toy", "standardId": "PE7a.2"}, {"ageGroup": "Bear", "category": "Physical Development", "benchmark": "Snips with scissors", "standardId": "PE7a.3"}, {"ageGroup": "Bear", "category": "Physical Development", "benchmark": "Strings large beads", "standardId": "PE7a.4"}, {"ageGroup": "Bear", "category": "Physical Development", "benchmark": "Pours water into containers", "standardId": "PE7a.5"}, {"ageGroup": "Bear", "category": "Physical Development", "benchmark": "Pounds, pokes, squeezes, rolls clay", "standardId": "PE7a.6"}, {"ageGroup": "Bear", "category": "Physical Development", "benchmark": "Buttons, zips, buckles, laces", "standardId": "PE7a.7"}, {"ageGroup": "Bear", "category": "Physical Development", "benchmark": "Uses hand motions for <PERSON><PERSON><PERSON>", "standardId": "PE7a.8"}, {"ageGroup": "Bear", "category": "Physical Development", "benchmark": "Turns knobs to open doors", "standardId": "PE7a.9"}, {"ageGroup": "Bear", "category": "Physical Development", "benchmark": "Uses eating utensils", "standardId": "PE7a.10"}, {"ageGroup": "Bear", "category": "Physical Development", "benchmark": "Cuts along straight line", "standardId": "PE7a.11"}, {"ageGroup": "Bear", "category": "Physical Development", "benchmark": "Cuts out simple pictures and shapes, using other hand to move paper", "standardId": "PE7a.12"}, {"ageGroup": "Bear", "category": "Physical Development", "benchmark": "Uses correct scissors grip", "standardId": "PE7a.13"}, {"ageGroup": "Bear", "category": "Physical Development", "benchmark": "Attempts to tie shoe", "standardId": "PE7a.14"}, {"ageGroup": "Bear", "category": "Physical Development", "benchmark": "Pushes specific keys on a keyboard", "standardId": "PE7a.15"}, {"ageGroup": "Bear", "category": "Physical Development", "benchmark": "Arranges small pegs in pegboard", "standardId": "PE7a.16"}, {"ageGroup": "Bear", "category": "Physical Development", "benchmark": "Strings small beads", "standardId": "PE7a.17"}, {"ageGroup": "Bear", "category": "Physical Development", "benchmark": "Cuts food", "standardId": "PE7a.18"}, {"ageGroup": "Bear", "category": "Physical Development", "benchmark": "Builds a structure using small plastic building bricks", "standardId": "PE7a.19"}, {"ageGroup": "Bear", "category": "Physical Development", "benchmark": "Grips drawing and writing tools with whole hand but may use whole arm movements to make marks -4", "standardId": "PE7b.1"}, {"ageGroup": "Bear", "category": "Physical Development", "benchmark": "Holds drawing and writing tools by using a three-point finger grip but may hold the instrument too close to one end -6", "standardId": "PE7b.2"}, {"ageGroup": "Bear", "category": "Language Development", "benchmark": "Responds appropriately to specific vocabulary and simple statements, questions, and stories", "standardId": "LANG8a.1"}, {"ageGroup": "Bear", "category": "Language Development", "benchmark": "Follows simple requests not accompanied by gestures", "standardId": "LANG8b.1"}, {"ageGroup": "Bear", "category": "Language Development", "benchmark": "Follows directions of two or more steps that relate to familiar objects and experiences", "standardId": "LANG8b.2"}, {"ageGroup": "Bear", "category": "Language Development", "benchmark": "Follows detailed, instructional, multistep directions", "standardId": "LANG8b.3"}, {"ageGroup": "Bear", "category": "Language Development", "benchmark": "Asks for clarification in order to understand complex directions: carries out directions with 45052 steps", "standardId": "LANG8b.4"}, {"ageGroup": "Bear", "category": "Language Development", "benchmark": "Names familiar people, animals, and objects", "standardId": "LANG9a.1"}, {"ageGroup": "Bear", "category": "Language Development", "benchmark": "Describes and tells the use of many familiar items", "standardId": "LANG9a.2"}, {"ageGroup": "Bear", "category": "Language Development", "benchmark": "Uses some words and word-like sounds and is understood by most familiar people", "standardId": "LANG9b.1"}, {"ageGroup": "Bear", "category": "Language Development", "benchmark": "Is understood by most people; may mispronounce new, long, or unusual words", "standardId": "LANG9b.2"}, {"ageGroup": "Bear", "category": "Language Development", "benchmark": "Uses one or two word sentences or phrases", "standardId": "LANG9c.1"}, {"ageGroup": "Bear", "category": "Language Development", "benchmark": "Uses three to four word sentences; may omit some words or use some words incorrectly", "standardId": "LANG9c.2"}, {"ageGroup": "Bear", "category": "Language Development", "benchmark": "Uses complete four to six word sentences", "standardId": "LANG9c.3"}, {"ageGroup": "Bear", "category": "Language Development", "benchmark": "Makes simple statements about recent events and familiar people and objects that are not present", "standardId": "LANG9d.1"}, {"ageGroup": "Bear", "category": "Language Development", "benchmark": "Tells simple stories about objects, events, and people not present; lacks many details and a conventional beginning, middle, and end", "standardId": "LANG9d.2"}, {"ageGroup": "Bear", "category": "Language Development", "benchmark": "Tells stories about other times and places that have a logical order and that include major details", "standardId": "LANG9d.3"}, {"ageGroup": "Bear", "category": "Language Development", "benchmark": "Tells elaborate stories that refer to other times and places", "standardId": "LANG9d.4"}, {"ageGroup": "Bear", "category": "Language Development", "benchmark": "Initiates and attends to brief conversations", "standardId": "LANG10a.1"}, {"ageGroup": "Bear", "category": "Language Development", "benchmark": "Engages in conversations of at least three exchanges", "standardId": "LANG10a.2"}, {"ageGroup": "Bear", "category": "Language Development", "benchmark": "Responds to speech by looking toward the speaker; watches for signs of being understood when communicating", "standardId": "LANG10b.1"}, {"ageGroup": "Bear", "category": "Language Development", "benchmark": "Uses appropriate eye contact, pauses, and simple verbal prompts when communicating", "standardId": "LANG10b.2"}, {"ageGroup": "Bear", "category": "Language Development", "benchmark": "Uses acceptable language and basic social rules while communicating with others; may need reminders", "standardId": "LANG10b.3"}, {"ageGroup": "Bear", "category": "Cognitive Development", "benchmark": "Sustains work on age-appropriate, interesting tasks; can ignore most distractions and interruptions", "standardId": "AL11a.1"}, {"ageGroup": "Bear", "category": "Cognitive Development", "benchmark": "Sustains attention to tasks or projects over time (days to weeks); can return to activities after interruptions", "standardId": "AL11a.2"}, {"ageGroup": "Bear", "category": "Cognitive Development", "benchmark": "Selectively focuses attention based on task difficulty and shifts attention toward teacher’s goal; demonstrates concentrated effort", "standardId": "AL11a.3"}, {"ageGroup": "Bear", "category": "Cognitive Development", "benchmark": "Concentrates on tasks for extended periods but may become restless, especially during activities viewed as less interesting; repeatedly practices activities thought to be enjoyable", "standardId": "AL11a.4"}, {"ageGroup": "Bear", "category": "Cognitive Development", "benchmark": "Directs attention based on previous performance and concentrates on activities that require additional study", "standardId": "AL11a.5"}, {"ageGroup": "Bear", "category": "Cognitive Development", "benchmark": "Practices an activity many times until successful", "standardId": "AL11b.1"}, {"ageGroup": "Bear", "category": "Cognitive Development", "benchmark": "Plans and pursues a variety of appropriately challenging tasks", "standardId": "AL11b.2"}, {"ageGroup": "Bear", "category": "Cognitive Development", "benchmark": "Observes and imitates how other people solve problems; asks for a solution and uses it", "standardId": "AL11c.1"}, {"ageGroup": "Bear", "category": "Cognitive Development", "benchmark": "Solves problems without having to try every possibility", "standardId": "AL11c.2"}, {"ageGroup": "Bear", "category": "Cognitive Development", "benchmark": "Explores and investigates ways to make something happen", "standardId": "AL11d.1"}, {"ageGroup": "Bear", "category": "Cognitive Development", "benchmark": "Shows eagerness to learn about a variety of topics and ideas", "standardId": "AL11d.2"}, {"ageGroup": "Bear", "category": "Cognitive Development", "benchmark": "Imitates others in using objects in new and/or unanticipated ways", "standardId": "AL11e.1"}, {"ageGroup": "Bear", "category": "Cognitive Development", "benchmark": "Uses creativity and imagination during play and routine tasks", "standardId": "AL11e.2"}, {"ageGroup": "Bear", "category": "Cognitive Development", "benchmark": "Changes plans if a better idea is thought of or proposed", "standardId": "AL11e.3"}, {"ageGroup": "Bear", "category": "Cognitive Development", "benchmark": "Recognizes familiar people, places and objects; looks for hidden object where it was last seen", "standardId": "AL12a.1"}, {"ageGroup": "Bear", "category": "Cognitive Development", "benchmark": "Recalls familiar people, places, objects, and actions from the pat (a few months before); recalls one or two items removed from view", "standardId": "AL12a.2"}, {"ageGroup": "Bear", "category": "Cognitive Development", "benchmark": "Tells about experiences in order, provides details, and evaluates the experience; recalls three or four items removed from view", "standardId": "AL12a.3"}, {"ageGroup": "Bear", "category": "Cognitive Development", "benchmark": "Looks for a familiar persons when they are named; relates objects to events", "standardId": "AL12b.1"}, {"ageGroup": "Bear", "category": "Cognitive Development", "benchmark": "Remembers the sequence of personal routines and experiences with teacher support", "standardId": "AL12b.2"}, {"ageGroup": "Bear", "category": "Cognitive Development", "benchmark": "Draws on everyday experiences and applies this knowledge to a similar situation", "standardId": "AL12b.3"}, {"ageGroup": "Bear", "category": "Cognitive Development", "benchmark": "Matches similar objects", "standardId": "AL13a.1"}, {"ageGroup": "Bear", "category": "Cognitive Development", "benchmark": "Places objects in two or more groups based on differences in a single characteristic (color, size, shape)", "standardId": "AL13a.2"}, {"ageGroup": "Bear", "category": "Cognitive Development", "benchmark": "Groups objects by one characteristic; then regroups them using a different characteristic and indicates the reason", "standardId": "AL13a.3"}, {"ageGroup": "Bear", "category": "Cognitive Development", "benchmark": "Recognizes people, objects, and animals in pictures or photographs", "standardId": "AL14a.1"}, {"ageGroup": "Bear", "category": "Cognitive Development", "benchmark": "Draws or constructs, and then identifies what it is", "standardId": "AL14a.2"}, {"ageGroup": "Bear", "category": "Cognitive Development", "benchmark": "Plans and then uses drawings, constructions, movements, and dramatizations to represent ideas", "standardId": "AL14a.3"}, {"ageGroup": "Bear", "category": "Cognitive Development", "benchmark": "Imitates actions of others during play; uses real objects as props", "standardId": "AL14b.1"}, {"ageGroup": "Bear", "category": "Cognitive Development", "benchmark": "Acts out familiar or imaginary scenarios; may use props to stand for something else", "standardId": "AL14b.2"}, {"ageGroup": "Bear", "category": "Cognitive Development", "benchmark": "Interacts with two or more children during pretend play, assigning and/or assuming roles and discussing actions; sustains play scenario for up to 10 minutes", "standardId": "AL14a.3"}, {"ageGroup": "Bear", "category": "Literacy", "benchmark": "Joins in rhyming songs and games", "standardId": "LIT15a.1"}, {"ageGroup": "Bear", "category": "Literacy", "benchmark": "Fills in the missing rhyming word", "standardId": "LIT15a.2"}, {"ageGroup": "Bear", "category": "Literacy", "benchmark": "Generates rhyming words spontaneously", "standardId": "LIT15a.3"}, {"ageGroup": "Bear", "category": "Literacy", "benchmark": "Decides whether two words rhyme", "standardId": "LIT15a.4"}, {"ageGroup": "Bear", "category": "Literacy", "benchmark": "Sings songs and recites rhymes and refrains with repeating initial sounds", "standardId": "LIT15b.1"}, {"ageGroup": "Bear", "category": "Literacy", "benchmark": "Shows awareness that some words begin the same way", "standardId": "LIT15b.2"}, {"ageGroup": "Bear", "category": "Literacy", "benchmark": "Matches the beginning sounds of some words", "standardId": "LIT15b.3"}, {"ageGroup": "Bear", "category": "Literacy", "benchmark": "Shows awareness of separate words in sentences", "standardId": "LIT15c.1"}, {"ageGroup": "Bear", "category": "Literacy", "benchmark": "Shows awareness of separate syllables in words", "standardId": "LIT15c.2"}, {"ageGroup": "Bear", "category": "Literacy", "benchmark": "Shows understanding that a specific sequence of letters represents a spoken word", "standardId": "LIT15d.1"}, {"ageGroup": "Bear", "category": "Literacy", "benchmark": "Breaks words into syllables based on the number of vowel sounds heard", "standardId": "LIT15d.2"}, {"ageGroup": "Bear", "category": "Literacy", "benchmark": "Recognizes and names a few letters in own name", "standardId": "LIT116a.1"}, {"ageGroup": "Bear", "category": "Literacy", "benchmark": "Recognizes and names as many as 10 letters, especially those in own name", "standardId": "LIT16a.2"}, {"ageGroup": "Bear", "category": "Literacy", "benchmark": "Identifies the sounds of a few letters", "standardId": "LIT16b.2"}, {"ageGroup": "Bear", "category": "Literacy", "benchmark": "Produces the correct sounds for 45219 letters", "standardId": "LIT16b.3"}, {"ageGroup": "Bear", "category": "Literacy", "benchmark": "Shows interest in books", "standardId": "LIT17a.1"}, {"ageGroup": "Bear", "category": "Literacy", "benchmark": "Orients book correctly", "standardId": "LIT17a.2"}, {"ageGroup": "Bear", "category": "Literacy", "benchmark": "Turns pages from the front of the book t the back", "standardId": "LIT17a.3"}, {"ageGroup": "Bear", "category": "Literacy", "benchmark": "Recognizes familiar books by their covers", "standardId": "LIT17a.4"}, {"ageGroup": "Bear", "category": "Literacy", "benchmark": "Knows some features of a book (title, author, front and back covers)", "standardId": "LIT17a.5"}, {"ageGroup": "Bear", "category": "Literacy", "benchmark": "Connects specific books to authors", "standardId": "LIT17a.6"}, {"ageGroup": "Bear", "category": "Literacy", "benchmark": "Shows understanding that text is meaningful and can be read", "standardId": "LIT17b.1"}, {"ageGroup": "Bear", "category": "Literacy", "benchmark": "Indicates where to start reading and the direction to follow", "standardId": "LIT17b.2"}, {"ageGroup": "Bear", "category": "Literacy", "benchmark": "Shows awareness of various features of print; letters words, spaces, upper- and lowercase letters, some punctuation", "standardId": "LIT17b.3"}, {"ageGroup": "Bear", "category": "Literacy", "benchmark": "Contributes particular language from the book at the appropriate time", "standardId": "LIT18a.1"}, {"ageGroup": "Bear", "category": "Literacy", "benchmark": "Asks and answers questions about the text; refers to pictures", "standardId": "LIT18a.2"}, {"ageGroup": "Bear", "category": "Literacy", "benchmark": "Identifies story-related problems, events, and resolutions during conversations with an adult", "standardId": "LIT18a.3"}, {"ageGroup": "Bear", "category": "Literacy", "benchmark": "Pretends to read a familiar book, treating each page as a separate unit", "standardId": "LIT18b.1"}, {"ageGroup": "Bear", "category": "Literacy", "benchmark": "Names and describes what is on each page, using pictures as clues", "standardId": "LIT18b.2"}, {"ageGroup": "Bear", "category": "Literacy", "benchmark": "Pretends to read using some of the language from the text", "standardId": "LIT18b.3"}, {"ageGroup": "Bear", "category": "Literacy", "benchmark": "Describes the action across the pages, using pictures to order the events (may need prompts from adult)", "standardId": "LIT18b.4"}, {"ageGroup": "Bear", "category": "Literacy", "benchmark": "Pretend to read, reciting language that closely matches the text on each page and using reading-like intonation", "standardId": "LIT18b.5"}, {"ageGroup": "Bear", "category": "Literacy", "benchmark": "Tries to match oral language to words on page; points to words as he reads", "standardId": "LIT18b.6"}, {"ageGroup": "Bear", "category": "Literacy", "benchmark": "Retells some events or information from a familiar story or other text with close adult prompting", "standardId": "LIT18c.1"}, {"ageGroup": "Bear", "category": "Literacy", "benchmark": "Retells familiar stories and recounts details from a nonfiction text using pictures or props as prompts", "standardId": "LIT18c.2"}, {"ageGroup": "Bear", "category": "Literacy", "benchmark": "Makes scribbles or marks", "standardId": "LIT19a.1"}, {"ageGroup": "Bear", "category": "Literacy", "benchmark": "Makes controlled linear scribbles", "standardId": "LIT19a.2"}, {"ageGroup": "Bear", "category": "Literacy", "benchmark": "Writes mock letters or letter-like forms", "standardId": "LIT19a.3"}, {"ageGroup": "Bear", "category": "Literacy", "benchmark": "Writes letter strings (writes some letters correctly, but in an unconventional order)", "standardId": "LIT19a.4"}, {"ageGroup": "Bear", "category": "Literacy", "benchmark": "Uses drawing, dictation and controlled linear scribbles to convey a message", "standardId": "LIT19b.1"}, {"ageGroup": "Bear", "category": "Literacy", "benchmark": "Uses drawing, dictation, and mock letters or letter forms to convey a message", "standardId": "LIT19b.2"}, {"ageGroup": "Bear", "category": "Literacy", "benchmark": "Uses drawing, dictation, and letter strings to convey a message", "standardId": "LIT19b.3"}, {"ageGroup": "Bear", "category": "Literacy", "benchmark": "Uses drawing, dictation, and early invented spelling to convey a message", "standardId": "LIT19b.4"}, {"ageGroup": "Bear", "category": "Mathematics", "benchmark": "Verbally counts (not always in correct order)", "standardId": "MATH20a.1"}, {"ageGroup": "Bear", "category": "Mathematics", "benchmark": "Verbally counts to 10; counts up to five objects accurately, using one number name for each object", "standardId": "MATH20a.2"}, {"ageGroup": "Bear", "category": "Mathematics", "benchmark": "Verbally counts to 20; counts 45219 objects accurately; knows the last number states how many in all; tells what number (1-10) comes next in order by counting", "standardId": "MATH20a.3"}, {"ageGroup": "Bear", "category": "Mathematics", "benchmark": "Demonstrates understanding of the concepts of one, two, and more", "standardId": "MATH20b.1"}, {"ageGroup": "Bear", "category": "Mathematics", "benchmark": "Recognizes and names the number of items in a small set (up to 5) instantly; combines and separates up to five objects and describes the parts", "standardId": "MATH20b.2"}, {"ageGroup": "Bear", "category": "Mathematics", "benchmark": "Makes sets of 6-1- objects and then describes the parts; identifies which part has more, less, or the same (equal); counts all or counts on to find out how many", "standardId": "MATH20b.3"}, {"ageGroup": "Bear", "category": "Mathematics", "benchmark": "Recognizes and names a few numerals", "standardId": "MATH20c.1"}, {"ageGroup": "Bear", "category": "Mathematics", "benchmark": "Identifies numerals to 5 by name and connects each to counted objects", "standardId": "MATH20c.2"}, {"ageGroup": "Bear", "category": "Mathematics", "benchmark": "Identifies numerals to 10 by name and connects each to counted objects", "standardId": "MATH20c.3"}, {"ageGroup": "Bear", "category": "Mathematics", "benchmark": "Follows simple directions related to position (in, on, under, up, down)", "standardId": "MATH21a.1"}, {"ageGroup": "Bear", "category": "Mathematics", "benchmark": "Follows simple directions related to proximity (beside, between, next to)", "standardId": "MATH21a.2"}, {"ageGroup": "Bear", "category": "Mathematics", "benchmark": "Uses and responds appropriately to positional words indicating location, direction, and distance", "standardId": "MATH21a.3"}, {"ageGroup": "Bear", "category": "Mathematics", "benchmark": "Matches two identical shapes", "standardId": "MATH21b.1"}, {"ageGroup": "Bear", "category": "Mathematics", "benchmark": "Identifies a few basic shapes (circle, square, triangle)", "standardId": "MATH21b.2"}, {"ageGroup": "Bear", "category": "Mathematics", "benchmark": "Describes basic two- and three-dimensional shapes by using own words; recognizes basic shapes when they are presented in a new orientation", "standardId": "MATH21b.3"}, {"ageGroup": "Bear", "category": "Mathematics", "benchmark": "Makes simple comparisons between two objects", "standardId": "MATH22a.1"}, {"ageGroup": "Bear", "category": "Mathematics", "benchmark": "Compares and orders a small set of objects as appropriate according to size, length, weight, area, or volume", "standardId": "MATH22a.2"}, {"ageGroup": "Bear", "category": "Mathematics", "benchmark": "Uses multiples of the same unit to measure; uses numbers to compare; knows the purpose of standard measuring tools", "standardId": "MATH22a.3"}, {"ageGroup": "Bear", "category": "Mathematics", "benchmark": "Knows usual sequence of basic daily events", "standardId": "MATH22b.1"}, {"ageGroup": "Bear", "category": "Mathematics", "benchmark": "Relates time to daily routines and schedule", "standardId": "MATH22b.2"}, {"ageGroup": "Bear", "category": "Mathematics", "benchmark": "knows a few ordinal numbers", "standardId": "MATH22c.1"}, {"ageGroup": "Bear", "category": "Mathematics", "benchmark": "Creates and reads simple graphs; uses simple comparison and ordinal terms to describe findings", "standardId": "MATH22c.2"}, {"ageGroup": "Bear", "category": "Mathematics", "benchmark": "Shows interest in simple patterns in everyday life", "standardId": "MATH23a.1"}, {"ageGroup": "Bear", "category": "Mathematics", "benchmark": "Copies simple repeating patterns", "standardId": "MATH23a.2"}, {"ageGroup": "Bear", "category": "Mathematics", "benchmark": "Extends and creates simple repeating patterns", "standardId": "MATH23a.3"}, {"ageGroup": "Bear", "category": "Science and Technology", "benchmark": "Observes and explores things in the environment", "standardId": "ST24a.1"}, {"ageGroup": "Bear", "category": "Science and Technology", "benchmark": "Reacts to changes", "standardId": "ST24a.2"}, {"ageGroup": "Bear", "category": "Science and Technology", "benchmark": "Manipulates objects to understand their properties", "standardId": "ST24a.3"}, {"ageGroup": "Bear", "category": "Science and Technology", "benchmark": "Connects new observations to what she already knows", "standardId": "ST24a.4"}, {"ageGroup": "Bear", "category": "Science and Technology", "benchmark": "Identifies problems, makes predictions, thinks of ways to solve problems, and tries possible solutions", "standardId": "ST24a.5"}, {"ageGroup": "Bear", "category": "Science and Technology", "benchmark": "Organizes information", "standardId": "ST24a.6"}, {"ageGroup": "Bear", "category": "Science and Technology", "benchmark": "Makes comparisons and classifies", "standardId": "ST24a.7"}, {"ageGroup": "Bear", "category": "Science and Technology", "benchmark": "Communicates with others about discoveries", "standardId": "ST24a.8"}, {"ageGroup": "Bear", "category": "Science and Technology", "benchmark": "Represents his thinking through drawing dramatizing, graphing, or making models", "standardId": "ST24a.9"}, {"ageGroup": "Bear", "category": "Science and Technology", "benchmark": "Shows a growing ability to classify living and nonliving things", "standardId": "ST25a.1"}, {"ageGroup": "Bear", "category": "Science and Technology", "benchmark": "Communicates about the characteristics of living things", "standardId": "ST25a.2"}, {"ageGroup": "Bear", "category": "Science and Technology", "benchmark": "Demonstrates understanding that living things grow, change, and reproduce", "standardId": "ST25a.3"}, {"ageGroup": "Bear", "category": "Science and Technology", "benchmark": "Shows awareness of life in different environments or habitats", "standardId": "ST25a.4"}, {"ageGroup": "Bear", "category": "Science and Technology", "benchmark": "Groups or categorizes living things (appearance, behavior, plant, or animal)", "standardId": "ST25a.5"}, {"ageGroup": "Bear", "category": "Science and Technology", "benchmark": "Demonstrates awareness that living things go through a growth cycle", "standardId": "ST25a.6"}, {"ageGroup": "Bear", "category": "Science and Technology", "benchmark": "Examines, describes, and measures the observable features of objects", "standardId": "ST26a.1"}, {"ageGroup": "Bear", "category": "Science and Technology", "benchmark": "Demonstrates understanding that objects are made from one or more materials (metal, wood, plastic, or paper)", "standardId": "ST26a.2"}, {"ageGroup": "Bear", "category": "Science and Technology", "benchmark": "Communicates that the physical properties of objects and materials can change (when solid ice becomes liquid)", "standardId": "ST26a.3"}, {"ageGroup": "Bear", "category": "Science and Technology", "benchmark": "Displays awareness of natural forces that affect objects and materials (wind and gravity)", "standardId": "ST26a.4"}, {"ageGroup": "Bear", "category": "Science and Technology", "benchmark": "Explores and describes ways that objects can be moved in space (pushing pulling rising, sinking)", "standardId": "ST26a.5"}, {"ageGroup": "Bear", "category": "Science and Technology", "benchmark": "Explores properties of electricity and magnetism", "standardId": "ST26a.6"}, {"ageGroup": "Bear", "category": "Science and Technology", "benchmark": "Demonstrates understanding that there are different kinds of weather and that weather changes", "standardId": "ST27a.1"}, {"ageGroup": "Bear", "category": "Science and Technology", "benchmark": "Describes and measures weather", "standardId": "ST27a.2"}, {"ageGroup": "Bear", "category": "Science and Technology", "benchmark": "Communicates awareness that the environment changes (season to season, sometimes slowly and sometimes suddenly)", "standardId": "ST27a.3"}, {"ageGroup": "Bear", "category": "Science and Technology", "benchmark": "Shows awareness that different objects can be seen in the sky", "standardId": "ST27a.4"}, {"ageGroup": "Bear", "category": "Science and Technology", "benchmark": "Demonstrates understanding that people can affect the environment in positive and negative ways", "standardId": "ST27a.5"}, {"ageGroup": "Bear", "category": "Science and Technology", "benchmark": "Shows understanding that different tools and technology are used in different places for different purposes (finding information, communicating, and designing)", "standardId": "ST28a.1"}, {"ageGroup": "Bear", "category": "Science and Technology", "benchmark": "Demonstrates the appropriate use of various tools and other technology", "standardId": "ST28a.2"}, {"ageGroup": "Bear", "category": "Social Sudies", "benchmark": "Demonstrates understanding that each person has unique characteristics, ways of communicating, and ways of solving problems", "standardId": "SS29a.1"}, {"ageGroup": "Bear", "category": "Social Sudies", "benchmark": "Communicates that each person is part of a family that has unique characteristics", "standardId": "SS29a.2"}, {"ageGroup": "Bear", "category": "Social Sudies", "benchmark": "Shows awareness that each person has basic needs that must be met to stay healthy (food, shelter, clothing)", "standardId": "SS29a.3"}, {"ageGroup": "Bear", "category": "Social Sudies", "benchmark": "Shows awareness that there are similarities and differences among people and families", "standardId": "SS30a.1"}, {"ageGroup": "Bear", "category": "Social Sudies", "benchmark": "Demonstrates understanding of the various jobs of people in the community", "standardId": "SS30a.2"}, {"ageGroup": "Bear", "category": "Social Sudies", "benchmark": "Shows understanding that people buy, sell, and trade to get goods and services that they do not raise, make or find themselves", "standardId": "SS30a.3"}, {"ageGroup": "Bear", "category": "Social Sudies", "benchmark": "Communicates about the various means of transportation that people use to move goods and go from place to place", "standardId": "SS30a.4"}, {"ageGroup": "Bear", "category": "Social Sudies", "benchmark": "Communicates understanding that people have various rights and responsibilities", "standardId": "SS30a.5"}, {"ageGroup": "Bear", "category": "Social Sudies", "benchmark": "Shows increasing awareness that respect for others, cooperation, and fairness help us get along in communities", "standardId": "SS30a.6"}, {"ageGroup": "Bear", "category": "Social Sudies", "benchmark": "Demonstrates increasing understanding that there are rules in our homes, schools, and community and that each rule has a purpose", "standardId": "SS30a.7"}, {"ageGroup": "Bear", "category": "Social Sudies", "benchmark": "Demonstrates understanding that people and things change over time", "standardId": "SS31a.1"}, {"ageGroup": "Bear", "category": "Social Sudies", "benchmark": "Shows that time can be measured", "standardId": "SS31a.2"}, {"ageGroup": "Bear", "category": "Social Sudies", "benchmark": "Communicates about time (uses words such as yesterday, today, tomorrow, day, week, month, minute, hour)", "standardId": "SS31a.3"}, {"ageGroup": "Bear", "category": "Social Sudies", "benchmark": "Demonstrates understanding that we are surrounded by geographical features (mountain, hill, desert, lake, river, creek, bayou)", "standardId": "SS32a.1"}, {"ageGroup": "Bear", "category": "Social Sudies", "benchmark": "Demonstrates understanding that there is specific information that identifies a location (address)", "standardId": "SS32a.2"}, {"ageGroup": "Bear", "category": "Social Sudies", "benchmark": "Communicates that we depend on people who live far away for many necessities and information", "standardId": "SS32a.3"}, {"ageGroup": "Bear", "category": "Social Sudies", "benchmark": "Shows an increasing understanding that maps are tools with symbols that help us locate objects, find where we are and where we are going", "standardId": "SS32a.4"}, {"ageGroup": "Bear", "category": "The Arts", "benchmark": "Shows appreciation for various forms of visual art", "standardId": "ART33a.1"}, {"ageGroup": "Bear", "category": "The Arts", "benchmark": "Shows appreciation for the artwork of peers", "standardId": "ART33a.2"}, {"ageGroup": "Bear", "category": "The Arts", "benchmark": "Communicates what he sees and how it makes him feel", "standardId": "ART33a.3"}, {"ageGroup": "Bear", "category": "The Arts", "benchmark": "Uses and cares for art materials", "standardId": "ART33a.4"}, {"ageGroup": "Bear", "category": "The Arts", "benchmark": "Explores different materials, tools, and processes", "standardId": "ART33a.5"}, {"ageGroup": "Bear", "category": "The Arts", "benchmark": "Shows increasing awareness of color, line, form, texture, space, and design in her artwork or the work of others", "standardId": "ART33a.6"}, {"ageGroup": "Bear", "category": "The Arts", "benchmark": "Communicates about his artwork", "standardId": "ART33a.7"}, {"ageGroup": "Bear", "category": "The Arts", "benchmark": "Shows awareness and appreciation of different kinds of music", "standardId": "ART34a.1"}, {"ageGroup": "Bear", "category": "The Arts", "benchmark": "Expresses thoughts, feelings, and energy through music", "standardId": "ART34a.2"}, {"ageGroup": "Bear", "category": "The Arts", "benchmark": "Shows increasing awareness of various components of music: melody, pitch, rhythm, tempo, dynamics, and timbre", "standardId": "ART34a.3"}, {"ageGroup": "Bear", "category": "The Arts", "benchmark": "Communicates feelings and ideas through dance and movement", "standardId": "ART35a.1"}, {"ageGroup": "Bear", "category": "The Arts", "benchmark": "Demonstrates spatial awareness, location, directions, levels, and pathways", "standardId": "ART35a.2"}, {"ageGroup": "Bear", "category": "The Arts", "benchmark": "Demonstrates effort awareness, speed, force, and control", "standardId": "ART35a.3"}, {"ageGroup": "Bear", "category": "The Arts", "benchmark": "Demonstrates relational awareness", "standardId": "ART35a.4"}, {"ageGroup": "Bear", "category": "The Arts", "benchmark": "Shows that real-life roles can be enacted", "standardId": "ART36a.1"}, {"ageGroup": "Bear", "category": "The Arts", "benchmark": "Communicates a message or story through action and dialogue", "standardId": "ART36a.2"}, {"ageGroup": "Bear", "category": "The Arts", "benchmark": "Represents ideas through drama", "standardId": "ART36a.3"}, {"ageGroup": "Bear", "category": "The Arts", "benchmark": "Shows appreciation of the dramatizations of others", "standardId": "ART36a.4"}, {"ageGroup": "Bears 2", "category": "Social Emotional", "benchmark": "Uses adult support to calm self", "standardId": "SE1a.1"}, {"ageGroup": "Bears 2", "category": "Social Emotional", "benchmark": "Comforts self by seeking out special object/person", "standardId": "SE1a.2"}, {"ageGroup": "Bears 2", "category": "Social Emotional", "benchmark": "Able to look at situation differently delay gratification", "standardId": "SE1a.3"}, {"ageGroup": "Bears 2", "category": "Social Emotional", "benchmark": "Controls strong emotions in appropriate manner most of time", "standardId": "SE1a.4"}, {"ageGroup": "Bears 2", "category": "Social Emotional", "benchmark": "Manages strong emotions using known strategies", "standardId": "SE1a.5"}, {"ageGroup": "Bears 2", "category": "Social Emotional", "benchmark": "Demonstrates patience with personal limitations", "standardId": "SE1a.6"}, {"ageGroup": "Bears 2", "category": "Social Emotional", "benchmark": "Responds to changes in adult’s tone/expression", "standardId": "SE1b.1"}, {"ageGroup": "Bears 2", "category": "Social Emotional", "benchmark": "Accepts redirection from adults", "standardId": "SE1b.2"}, {"ageGroup": "Bears 2", "category": "Social Emotional", "benchmark": "Manages classroom rules, routines, transitions with occasional reminders", "standardId": "SE1b.3"}, {"ageGroup": "Bears 2", "category": "Social Emotional", "benchmark": "Applies basic rules in new but similar situations", "standardId": "SE1b.4"}, {"ageGroup": "Bears 2", "category": "Social Emotional", "benchmark": "Understands and explains reasons for rules", "standardId": "SE1b.5"}, {"ageGroup": "Bears 2", "category": "Social Emotional", "benchmark": "Understands “big rule” concepts", "standardId": "SE1b.6"}, {"ageGroup": "Bears 2", "category": "Social Emotional", "benchmark": "Indicates needs and wants, participates as adult attends to needs", "standardId": "SE1c.1"}, {"ageGroup": "Bears 2", "category": "Social Emotional", "benchmark": "Seeks to do things for self", "standardId": "SE1c.2"}, {"ageGroup": "Bears 2", "category": "Social Emotional", "benchmark": "Confident in meeting own needs", "standardId": "SE1c.3"}, {"ageGroup": "Bears 2", "category": "Social Emotional", "benchmark": "Takes responsibility for own well-being", "standardId": "SE1c.4"}, {"ageGroup": "Bears 2", "category": "Social Emotional", "benchmark": "Practices skills to reach the desired level of personal achievement", "standardId": "SE1c.5"}, {"ageGroup": "Bears 2", "category": "Social Emotional", "benchmark": "Accurately identifies own strengths and challenges", "standardId": "SE1c.6"}, {"ageGroup": "Bears 2", "category": "Social Emotional", "benchmark": "Demonstrates a secure attachment to one or more adults", "standardId": "SE2a.1"}, {"ageGroup": "Bears 2", "category": "Social Emotional", "benchmark": "Uses trusted adult as a secure base from which to explore the world", "standardId": "SE2a.2"}, {"ageGroup": "Bears 2", "category": "Social Emotional", "benchmark": "Manages separations without distress/engages with trusted adults", "standardId": "SE2a.3"}, {"ageGroup": "Bears 2", "category": "Social Emotional", "benchmark": "Engages with trusted adults as resources and to share mutual interests", "standardId": "SE2a.4"}, {"ageGroup": "Bears 2", "category": "Social Emotional", "benchmark": "Respectfully engages adult with different viewpoint", "standardId": "SE2a.5"}, {"ageGroup": "Bears 2", "category": "Social Emotional", "benchmark": "Reacts to others’ emotional expressions", "standardId": "SE2b.1"}, {"ageGroup": "Bears 2", "category": "Social Emotional", "benchmark": "Demonstrates concern about others feelings", "standardId": "SE2b.2"}, {"ageGroup": "Bears 2", "category": "Social Emotional", "benchmark": "Identifies basic emotional reactions of others and their causes", "standardId": "SE2b.3"}, {"ageGroup": "Bears 2", "category": "Social Emotional", "benchmark": "Recognizes that others’ feelings about a situation might be different", "standardId": "SE2b.4"}, {"ageGroup": "Bears 2", "category": "Social Emotional", "benchmark": "Recognizes that people can experience more than one emotion at the same time", "standardId": "SE2b.5"}, {"ageGroup": "Bears 2", "category": "Social Emotional", "benchmark": "Uses situational context and past experience to interpret others’ feelings", "standardId": "SE2b.6"}, {"ageGroup": "Bears 2", "category": "Social Emotional", "benchmark": "Plays near other children uses similar materials", "standardId": "SE2c.1"}, {"ageGroup": "Bears 2", "category": "Social Emotional", "benchmark": "Successful strategies for entering groups", "standardId": "SE2c.1"}, {"ageGroup": "Bears 2", "category": "Social Emotional", "benchmark": "Initiates joins in, and sustains positive interactions with small group of 44960", "standardId": "SE2c.2"}, {"ageGroup": "Bears 2", "category": "Social Emotional", "benchmark": "Interacts cooperatively in groups of 45021", "standardId": "SE2c.3"}, {"ageGroup": "Bears 2", "category": "Social Emotional", "benchmark": "Works with a group toward a shared goal", "standardId": "SE2c.4"}, {"ageGroup": "Bears 2", "category": "Social Emotional", "benchmark": "Alternates between the roles of leader and follower to sustain play -12", "standardId": "SE2c.5"}, {"ageGroup": "Bears 2", "category": "Social Emotional", "benchmark": "Seeks a preferred playmate", "standardId": "SE2d.1"}, {"ageGroup": "Bears 2", "category": "Social Emotional", "benchmark": "Plays with 44928 preferred playmates", "standardId": "SE2d.2"}, {"ageGroup": "Bears 2", "category": "Social Emotional", "benchmark": "Establishes special friendship with 1 other child (short time)", "standardId": "SE2d.3"}, {"ageGroup": "Bears 2", "category": "Social Emotional", "benchmark": "Maintains friendships for several months and around similar interests -6", "standardId": "SE2d.4"}, {"ageGroup": "Bears 2", "category": "Social Emotional", "benchmark": "Forms friendships based on personal qualities", "standardId": "SE2d.5"}, {"ageGroup": "Bears 2", "category": "Social Emotional", "benchmark": "Forms friendships based on mutual trust and understands they can still exist through disagreements", "standardId": "SE2d.6"}, {"ageGroup": "Bears 2", "category": "Social Emotional", "benchmark": "Responds appropriately to others’ expressions of wants", "standardId": "SE3a.1"}, {"ageGroup": "Bears 2", "category": "Social Emotional", "benchmark": "Takes Turns", "standardId": "SE3a.2"}, {"ageGroup": "Bears 2", "category": "Social Emotional", "benchmark": "Initiates the sharing of Materials", "standardId": "SE3a.3"}, {"ageGroup": "Bears 2", "category": "Social Emotional", "benchmark": "Cooperates and shares ideas and materials in socially acceptable way", "standardId": "SE3a.4"}, {"ageGroup": "Bears 2", "category": "Social Emotional", "benchmark": "Completes cooperative projects with other children", "standardId": "SE3a.5"}, {"ageGroup": "Bears 2", "category": "Social Emotional", "benchmark": "A<PERSON>ura<PERSON>y completes self-assessment of role in group work", "standardId": "SE3a.6"}, {"ageGroup": "Bears 2", "category": "Social Emotional", "benchmark": "Expresses feelings during conflict", "standardId": "SE3b.1"}, {"ageGroup": "Bears 2", "category": "Social Emotional", "benchmark": "Seeks adult help to resolve social problems", "standardId": "SE3b.2"}, {"ageGroup": "Bears 2", "category": "Social Emotional", "benchmark": "Suggests solutions to social problems", "standardId": "SE3b.3"}, {"ageGroup": "Bears 2", "category": "Social Emotional", "benchmark": "Resolves social problems through negotiation or compromise", "standardId": "SE3b.4"}, {"ageGroup": "Bears 2", "category": "Social Emotional", "benchmark": "Seeks conflict resolution based on interest in maintaining relationship", "standardId": "SE3b.5"}, {"ageGroup": "Bears 2", "category": "Social Emotional", "benchmark": "Considers multiple viewpoints when solving conflicts", "standardId": "SE3b.6"}, {"ageGroup": "Bears 2", "category": "Physical Development", "benchmark": "Gallops quickly in zigzag line", "standardId": "PE4a.1"}, {"ageGroup": "Bears 2", "category": "Physical Development", "benchmark": "Hops 15 feet in straight line forward and backward", "standardId": "PE4a.2"}, {"ageGroup": "Bears 2", "category": "Physical Development", "benchmark": "Skips in a curved line around obstacles", "standardId": "PE4a.3"}, {"ageGroup": "Bears 2", "category": "Physical Development", "benchmark": "Bear Crawls in all directions", "standardId": "PE4a.4"}, {"ageGroup": "Bears 2", "category": "Physical Development", "benchmark": "Runs down field with partner tossing ball back and forth", "standardId": "PE4a.5"}, {"ageGroup": "Bears 2", "category": "Physical Development", "benchmark": "Moves around stage to perform choreographed dance", "standardId": "PE4a.6"}, {"ageGroup": "Bears 2", "category": "Physical Development", "benchmark": "Runs while kicking ball forward", "standardId": "PE4a.7"}, {"ageGroup": "Bears 2", "category": "Physical Development", "benchmark": "Walks while throwing and catching ball", "standardId": "PE5a.8"}, {"ageGroup": "Bears 2", "category": "Physical Development", "benchmark": "Jogs forward while dribbling with 1 hand", "standardId": "PE6a.9"}, {"ageGroup": "Bears 2", "category": "Physical Development", "benchmark": "Holds balances very still for 44990 seconds", "standardId": "PE5a.1"}, {"ageGroup": "Bears 2", "category": "Physical Development", "benchmark": "Balances in different body shapes", "standardId": "PE5a.2"}, {"ageGroup": "Bears 2", "category": "Physical Development", "benchmark": "Walks across beam, turns, and walks in different direction", "standardId": "PE5a.3"}, {"ageGroup": "Bears 2", "category": "Physical Development", "benchmark": "Walks across log on playground, jumps, continues walking", "standardId": "PE5a.4"}, {"ageGroup": "Bears 2", "category": "Physical Development", "benchmark": "Holds balance on apparatus for 44990 seconds", "standardId": "PE5a.5"}, {"ageGroup": "Bears 2", "category": "Physical Development", "benchmark": "Moves in and out of different balances by stretching, curling, and twisting", "standardId": "PE5a.6"}, {"ageGroup": "Bears 2", "category": "Physical Development", "benchmark": "Watches ball, reaches for it, and pulls it in to complete catch", "standardId": "PE6a.1"}, {"ageGroup": "Bears 2", "category": "Physical Development", "benchmark": "Kicks for accuracy at target with varying degrees of force", "standardId": "PE6a.2"}, {"ageGroup": "Bears 2", "category": "Physical Development", "benchmark": "Throws at moving target or partner", "standardId": "PE6a.3"}, {"ageGroup": "Bears 2", "category": "Physical Development", "benchmark": "Keeps eyes on ball, watching it into the hands", "standardId": "PE6a.4"}, {"ageGroup": "Bears 2", "category": "Physical Development", "benchmark": "Kicks ball back and forth with partner while traveling", "standardId": "PE6a.5"}, {"ageGroup": "Bears 2", "category": "Physical Development", "benchmark": "Punts ball below center to send it upward and forward, watching it contact foot", "standardId": "PE6a.6"}, {"ageGroup": "Bears 2", "category": "Physical Development", "benchmark": "Dribbles the ball continuously, switching hands", "standardId": "PE6a.7"}, {"ageGroup": "Bears 2", "category": "Physical Development", "benchmark": "Outlines/traces shapes using smooth, even strokes", "standardId": "PE7a.1"}, {"ageGroup": "Bears 2", "category": "Physical Development", "benchmark": "Pours liquid during science experiment into small opening without spilling", "standardId": "PE7a.2"}, {"ageGroup": "Bears 2", "category": "Physical Development", "benchmark": "Cuts complex pictures and shapes, leaving edges smooth", "standardId": "PE7a.3"}, {"ageGroup": "Bears 2", "category": "Physical Development", "benchmark": "Folds paper to make an origami creature", "standardId": "PE7a.4"}, {"ageGroup": "Bears 2", "category": "Physical Development", "benchmark": "Uses tiny pieces to make a detailed miniature world for a social studies project", "standardId": "PE7a.5"}, {"ageGroup": "Bears 2", "category": "Physical Development", "benchmark": "Uses keyboarding skills to compose a short story on the computer", "standardId": "PE7a.6"}, {"ageGroup": "Bears 2", "category": "Physical Development", "benchmark": "Demonstrates control and appropriate pressure when using writing and drawing tools and writes legibly", "standardId": "PE7b.1"}, {"ageGroup": "Bears 2", "category": "Physical Development", "benchmark": "Moves writing or drawing utensils fluidly across the page with increasing speed and accuracy, produces letters and number symbols having accurate formation, size, proportion, slant, and spacing. May use cursive writing.", "standardId": "PE7b.2"}, {"ageGroup": "Bears 2", "category": "Language", "benchmark": "Responds appropriately to complex statements, questions, vocab, stories. Asks questions when needed. Offers opposites.", "standardId": "LANG8a.1"}, {"ageGroup": "Bears 2", "category": "Language", "benchmark": "Checks for understanding of material presented orally by asking questions and making comments. Uses context clues", "standardId": "LANG8a.2"}, {"ageGroup": "Bears 2", "category": "Language", "benchmark": "Demonstrates understanding of topic by asking clarifying questions and by recounting details from discussions or other information presented orally", "standardId": "LANG8a.3"}, {"ageGroup": "Bears 2", "category": "Language", "benchmark": "Uses knowledge to explain ideas and opinions about the topic, explains main idea when presented with information in a variety of oral formats", "standardId": "LANG8a.4"}, {"ageGroup": "Bears 2", "category": "Language", "benchmark": "Asks for clarification in order to understand complex directions: carries out directions with 45052 steps", "standardId": "LANG8b.1"}, {"ageGroup": "Bears 2", "category": "Language", "benchmark": "Incorporates new, less familiar, or technical words in everyday conversations. Correctly uses new meanings for familiar words.", "standardId": "LANG9a.1"}, {"ageGroup": "Bears 2", "category": "Language", "benchmark": "Incorporates new grade-appropriate words. Clearly describes and explains events, ideas, and feelings using relevant details", "standardId": "LANG9a.2"}, {"ageGroup": "Bears 2", "category": "Language", "benchmark": "Incorporates new grade-appropriate words. Uses several descriptive words to relay detailed and specific information", "standardId": "LANG9a.3"}, {"ageGroup": "Bears 2", "category": "Language", "benchmark": "Incorporates new grade-appropriate words. Uses conversational and academic words and phrases. Correctly uses abstract nouns", "standardId": "LANG9a.4"}, {"ageGroup": "Bears 2", "category": "Language", "benchmark": "Pronounces multisyllabic or unusual words correctly; speaks audibly", "standardId": "LANG9b.1"}, {"ageGroup": "Bears 2", "category": "Language", "benchmark": "Adjusts volume and rate of speech in order to be clearly understood when speaking to individuals and groups", "standardId": "LANG9a.2"}, {"ageGroup": "Bears 2", "category": "Language", "benchmark": "Speaks clearly when giving a lengthy description of an event or personal experience to an individual or group; adapts word choices to be appropriate to audience", "standardId": "LANG9a.3"}, {"ageGroup": "Bears 2", "category": "Language", "benchmark": "Uses appropriate expression and inflection when relaying details about a story, personal experience, or specific topic to an individual or group", "standardId": "LANG9a.4"}, {"ageGroup": "Bears 2", "category": "Language", "benchmark": "Uses long, complex, sentences and follows most grammatical rules; uses common verbs and nouns", "standardId": "LANG9c.1"}, {"ageGroup": "Bears 2", "category": "Language", "benchmark": "Generates a variety of sentence types; matches correct subject/verb agreement; uses most parts of speech correctly", "standardId": "LANG9c.2"}, {"ageGroup": "Bears 2", "category": "Language", "benchmark": "Uses multiple types of less frequently occurring nouns, including collective and irregular plural nouns; uses reflexive pronouns. Rearranges sentences to produce and expand compound sentences", "standardId": "LANG9c.3"}, {"ageGroup": "Bears 2", "category": "Language", "benchmark": "Produces simple, compound, and complex sentences for multiple purposes. Identifies functions of parts of speech", "standardId": "LANG9c.4"}, {"ageGroup": "Bears 2", "category": "Language", "benchmark": "Tells stories about other times and places that have a logical order and that include major details", "standardId": "LANG9d.1"}, {"ageGroup": "Bears 2", "category": "Language", "benchmark": "Tells elaborate stories that refer to other times and places", "standardId": "LANG9d.2"}, {"ageGroup": "Bears 2", "category": "Language", "benchmark": "Tells stories with clear sequence of events, including a climax and resolution", "standardId": "LANG9d.3"}, {"ageGroup": "Bears 2", "category": "Language", "benchmark": "Accurately and thoroughly retells previously heard stories or information", "standardId": "LANG9d.4"}, {"ageGroup": "Bears 2", "category": "Language", "benchmark": "Engages in complex, lengthy conversations of five or more exchanges", "standardId": "LANG10a.1"}, {"ageGroup": "Bears 2", "category": "Language", "benchmark": "Extends conversations by responding to comments and asking questions; asks and answers questions to clarify information", "standardId": "LANG10a.2"}, {"ageGroup": "Bears 2", "category": "Language", "benchmark": "Connects others’ ideas shared during conversations; asks questions in order to better understand grade appropriate discussions", "standardId": "LANG10a.3"}, {"ageGroup": "Bears 2", "category": "Language", "benchmark": "Contributes to a focused discussion on a specific topic, preparing in advance for planned discussions using multiple sources", "standardId": "LANG10a.4"}, {"ageGroup": "Bears 2", "category": "Language", "benchmark": "Uses acceptable language and basic social rules during communication with others", "standardId": "LANG10b.1"}, {"ageGroup": "Bears 2", "category": "Language", "benchmark": "Listens attentively while taking turns in a discussion, using nonverbal signals to show understanding and interest", "standardId": "LANG10b.1"}, {"ageGroup": "Bears 2", "category": "Language", "benchmark": "Enters discussions in respectful ways; identifies the difference between formal and informal English", "standardId": "LANG10b.2"}, {"ageGroup": "Bears 2", "category": "Language", "benchmark": "Engages politely in conversations in which both speakers present and listen to arguments respectfully", "standardId": "LANG10b.3"}, {"ageGroup": "Bears 2", "category": "Cognitive Development", "benchmark": "Sustains work on age-appropriate, interesting tasks; can ignore most distractions and interruptions", "standardId": "AL11a.1"}, {"ageGroup": "Bears 2", "category": "Cognitive Development", "benchmark": "Sustains attention to tasks or projects over time (days to weeks); can return to activities after interruptions", "standardId": "AL11a.2"}, {"ageGroup": "Bears 2", "category": "Cognitive Development", "benchmark": "Selectively focuses attention based on task difficulty and shifts attention toward teacher’s goal; demonstrates concentrated effort", "standardId": "AL11a.3"}, {"ageGroup": "Bears 2", "category": "Cognitive Development", "benchmark": "Concentrates on tasks for extended periods but may become restless, especially during activities viewed as less interesting; repeatedly practices activities thought to be enjoyable", "standardId": "AL11a.4"}, {"ageGroup": "Bears 2", "category": "Cognitive Development", "benchmark": "Directs attention based on previous performance and concentrates on activities that require additional study", "standardId": "AL11a.5"}, {"ageGroup": "Bears 2", "category": "Cognitive Development", "benchmark": "Plans and pursues a variety of appropriately challenging tasks", "standardId": "AL11b.1"}, {"ageGroup": "Bears 2", "category": "Cognitive Development", "benchmark": "Plans and pursues own goal until it is reached", "standardId": "AL11b.2"}, {"ageGroup": "Bears 2", "category": "Cognitive Development", "benchmark": "Plans and completes grade-appropriate tasks and projects with minimal adult assistance", "standardId": "AL11b.3"}, {"ageGroup": "Bears 2", "category": "Cognitive Development", "benchmark": "Finishes long assignments and projects that last for days or weeks; may briefly give up on difficult tasks but returns to complete them", "standardId": "AL11b.4"}, {"ageGroup": "Bears 2", "category": "Cognitive Development", "benchmark": "Solves problems without having to try every possibility", "standardId": "AL11c.1"}, {"ageGroup": "Bears 2", "category": "Cognitive Development", "benchmark": "Thinks problems through, considering several possibilities and analyzing results", "standardId": "AL11c.2"}, {"ageGroup": "Bears 2", "category": "Cognitive Development", "benchmark": "Solves a wide range of problems using a variety of strategies; attempts to solve problems independently before asking for assistance from adults or peers", "standardId": "AL11c.3"}, {"ageGroup": "Bears 2", "category": "Cognitive Development", "benchmark": "Plans, considers various alternatives and combines skills and strategies needed to solve problems", "standardId": "AL11c.4"}, {"ageGroup": "Bears 2", "category": "Cognitive Development", "benchmark": "Shows eagerness to learn about a variety of topics and ideas", "standardId": "AL11d.1"}, {"ageGroup": "Bears 2", "category": "Cognitive Development", "benchmark": "Uses a variety of resources to find answers to questions; participates in grade-appropriate research projects", "standardId": "AL11d.2"}, {"ageGroup": "Bears 2", "category": "Cognitive Development", "benchmark": "Shows enthusiasm for learning new things and looks for opportunities to gain new knowledge and skills; asks open-ended questions about surroundings and everyday events", "standardId": "AL11d.3"}, {"ageGroup": "Bears 2", "category": "Cognitive Development", "benchmark": "Shows interest in an increasing range of phenomena outside of direct experiences by generating questions and researching the topic", "standardId": "AL11d.4"}, {"ageGroup": "Bears 2", "category": "Cognitive Development", "benchmark": "Asks thoughtful and increasingly complex questions; builds knowledge through research projects; contributes to discussions by applying previously gathered information about a topic", "standardId": "AL11d.5"}, {"ageGroup": "Bears 2", "category": "Cognitive Development", "benchmark": "Changes plans if a better idea is thought of or proposed", "standardId": "AL11e.1"}, {"ageGroup": "Bears 2", "category": "Cognitive Development", "benchmark": "Thinks through possible long-term solutions and takes on more abstract challenges", "standardId": "AL11e.2"}, {"ageGroup": "Bears 2", "category": "Cognitive Development", "benchmark": "Exhibits creative ways to complete tasks; uses own perspective when describing directions or rules", "standardId": "AL11e.3"}, {"ageGroup": "Bears 2", "category": "Cognitive Development", "benchmark": "Accepts last minute changes and requires less detailed instructions; experiments with invention", "standardId": "AL11e.4"}, {"ageGroup": "Bears 2", "category": "Cognitive Development", "benchmark": "Reverses thoughts mentally; understands directional perspectives other than his or her own", "standardId": "AL11e.5"}, {"ageGroup": "Bears 2", "category": "Cognitive Development", "benchmark": "Tells about experiences in order, provides details, and evaluates the experience; recalls three or four items removed from view", "standardId": "AL12a.1"}, {"ageGroup": "Bears 2", "category": "Cognitive Development", "benchmark": "Uses a few deliberate strategies to remember information", "standardId": "AL12a.2"}, {"ageGroup": "Bears 2", "category": "Cognitive Development", "benchmark": "Begins to use rehearsal strategies, but may need adult prompts/cues; is able to describe details of people, places, things, and events from memory", "standardId": "AL12a.3"}, {"ageGroup": "Bears 2", "category": "Cognitive Development", "benchmark": "Uses rehearsal strategies spontaneously to remember information; uses awareness of routines to think ahead; remembers about five pieces of information at a time", "standardId": "AL12a.4"}, {"ageGroup": "Bears 2", "category": "Cognitive Development", "benchmark": "Begins to use semantic grouping strategies to help remember, but may need adult cues or instruction on how to be efficient; recognizes inconsistencies and incompleteness of information", "standardId": "AL12a.5"}, {"ageGroup": "Bears 2", "category": "Cognitive Development", "benchmark": "Draws on everyday experiences and applies this knowledge to a similar situation", "standardId": "AL12b.1"}, {"ageGroup": "Bears 2", "category": "Cognitive Development", "benchmark": "Generates a rule, strategy or idea from one learning experience and applies it in a new context", "standardId": "AL12b.2"}, {"ageGroup": "Bears 2", "category": "Cognitive Development", "benchmark": "Connects the past with the present using general time estimates between events; connects time with specific daily events and salient events with the months and seasons", "standardId": "AL12b.3"}, {"ageGroup": "Bears 2", "category": "Cognitive Development", "benchmark": "Provides general descriptions of events to occur in the future; links material learned previously and in other contexts", "standardId": "AL12b.4"}, {"ageGroup": "Bears 2", "category": "Cognitive Development", "benchmark": "Associates people and events with the past, present, and future; begins to organize and compile information from multiple sources to create a useful document connecting events", "standardId": "AL12b.5"}, {"ageGroup": "Bears 2", "category": "Cognitive Development", "benchmark": "Groups objects by one characteristic; then regroups them using a different characteristic and indicates the reason", "standardId": "AL13a.1"}, {"ageGroup": "Bears 2", "category": "Cognitive Development", "benchmark": "Groups similar objects by more than one characteristic at the same time; switches sorting rules when asked and explains the reasons", "standardId": "AL13a.2"}, {"ageGroup": "Bears 2", "category": "Cognitive Development", "benchmark": "Groups objects and words in multiple ways based on physical attributes,functions, and semantic or conceptual associations", "standardId": "AL13a.3"}, {"ageGroup": "Bears 2", "category": "Cognitive Development", "benchmark": "Forms simple hierarchical classifications", "standardId": "AL13a.4"}, {"ageGroup": "Bears 2", "category": "Cognitive Development", "benchmark": "Plans and then uses drawings, constructions, movements, and dramatizations to represent ideas", "standardId": "AL14a.1"}, {"ageGroup": "Bears 2", "category": "Cognitive Development", "benchmark": "Represents objects, places, and ideas with increasingly abstract symbols", "standardId": "AL14a.2"}, {"ageGroup": "Bears 2", "category": "Cognitive Development", "benchmark": "Shows increasing ability to interpret and record ideas and thoughts and to solve problems without concrete points of reference", "standardId": "AL14a.3"}, {"ageGroup": "Bears 2", "category": "Cognitive Development", "benchmark": "Mentally manipulates information and uses logical arguments with increasing regularity; needs concrete points of reference for complex concepts and text; reflects on her work", "standardId": "AL14a.4"}, {"ageGroup": "Bears 2", "category": "Cognitive Development", "benchmark": "Interacts with two or more children during pretend play, assigning and/or assuming roles and discussing actions; sustains play scenario for up to 10 minutes", "standardId": "AL14b.1"}, {"ageGroup": "Bears 2", "category": "Cognitive Development", "benchmark": "Plans and negotiates complex role-play; joins in detailed conversation about roles and actions; play may extend over several days", "standardId": "AL14b.2"}, {"ageGroup": "Bears 2", "category": "Cognitive Development", "benchmark": "Creates rich dialogue, props, costumes, scenery, and sound effects to support role-play", "standardId": "AL14b.3"}, {"ageGroup": "Bears 2", "category": "Cognitive Development", "benchmark": "Composes a complex play and uses body,voice, and/or technology to communicate characters’ personal thoughts, feelings, actions, and sounds; uses symbolic play themes or props to create games with rules", "standardId": "AL14b.4"}, {"ageGroup": "Bears 2", "category": "Cognitive Development", "benchmark": "Acts out real-life (including social issues) and fanciful scenarios through improvised and planned dramatic play, and performances; manages and directs play during student-created dramatizations", "standardId": "AL14b.5"}, {"ageGroup": "Bears 2", "category": "The Arts", "benchmark": "Shows appreciation for various forms of visual art", "standardId": "ART33a.1"}, {"ageGroup": "Bears 2", "category": "The Arts", "benchmark": "Shows appreciation for the artwork of peers", "standardId": "ART33a.2"}, {"ageGroup": "Bears 2", "category": "The Arts", "benchmark": "Communicates what he sees and how it makes him feel", "standardId": "ART33a.3"}, {"ageGroup": "Bears 2", "category": "The Arts", "benchmark": "Uses and cares for art materials", "standardId": "ART33a.4"}, {"ageGroup": "Bears 2", "category": "The Arts", "benchmark": "Explores different materials, tools, and processes", "standardId": "ART33a.5"}, {"ageGroup": "Bears 2", "category": "The Arts", "benchmark": "Shows increasing awareness of color, line, form, texture, space, and design in her artwork or the work of others", "standardId": "ART33a.6"}, {"ageGroup": "Bears 2", "category": "The Arts", "benchmark": "Communicates about his artwork", "standardId": "ART33a.7"}, {"ageGroup": "Bears 2", "category": "The Arts", "benchmark": "Shows awareness and appreciation of different kinds of music", "standardId": "ART34a.1"}, {"ageGroup": "Bears 2", "category": "The Arts", "benchmark": "Expresses thoughts, feelings, and energy through music", "standardId": "ART34a.2"}, {"ageGroup": "Bears 2", "category": "The Arts", "benchmark": "Shows increasing awareness of various components of music: melody, pitch, rhythm, tempo, dynamics, and timbre", "standardId": "ART34a.3"}, {"ageGroup": "Bears 2", "category": "The Arts", "benchmark": "Communicates feelings and ideas through dance and movement", "standardId": "ART35a.1"}, {"ageGroup": "Bears 2", "category": "The Arts", "benchmark": "Demonstrates spatial awareness, location, directions, levels, and pathways", "standardId": "ART35a.2"}, {"ageGroup": "Bears 2", "category": "The Arts", "benchmark": "Demonstrates effort awareness, speed, force, and control", "standardId": "ART35a.3"}, {"ageGroup": "Bears 2", "category": "The Arts", "benchmark": "Demonstrates relational awareness", "standardId": "ART35a.4"}, {"ageGroup": "Bears 2", "category": "The Arts", "benchmark": "Shows that real-life roles can be enacted", "standardId": "ART36a.1"}, {"ageGroup": "Bears 2", "category": "The Arts", "benchmark": "Communicates a message or story through action and dialogue", "standardId": "ART36a.2"}, {"ageGroup": "Bears 2", "category": "The Arts", "benchmark": "Represents ideas through drama", "standardId": "ART36a.3"}, {"ageGroup": "Bears 2", "category": "The Arts", "benchmark": "Shows appreciation of the dramatizations of others", "standardId": "ART36a.4"}]