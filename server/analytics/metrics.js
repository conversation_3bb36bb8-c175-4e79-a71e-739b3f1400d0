import moment from "moment";
import { CardProviders } from '../card_providers/cardProviders';
import { Log } from 'meteor/logging';
import { Orgs } from "../../lib/collections/orgs";
import { People } from "../../lib/collections/people";
import { Groups } from "../../lib/collections/groups";
import { Moments } from "../../lib/collections/moments";
import { Invoices } from "../../lib/collections/invoices";
import { ComputedMetrics } from "../../lib/collections/computedMetrics";
import { processPermissions } from "../../lib/permissions";
import { Engagements } from "../../lib/collections/engagements";
import { UserLogins } from "../../lib/collections/userLogins";
import _ from '../../lib/util/underscore';
import { getFteForMonth, sortGroupsByAge } from "../classList";
import { Reservations } from "../../lib/collections/reservations";
import { OrgsUtil } from '../../lib/util/orgsUtil';

// Org IDs for specific companies
const BUILDING_KIDZ_ORG_ID = 'FfkDjcS59Eg76FJqM';
const SUNSHINE_HOUSE_ORG_ID = 'zT4NtQk32ooZ6Q6kA';
const LIGHTBRIDGE_ORG_ID = 'wXZeDbmLnTNEDzL3K';
const CDS_ORG_ID = 'tJ9aLXAJBhMr8gxz9';
const ELA_ORG_ID = 'qPp44WfCMqTj6kdJF';

// List of org IDs that have their own precompute jobs
const EXCLUDED_PARENT_ORG_IDS = [
  BUILDING_KIDZ_ORG_ID,
  SUNSHINE_HOUSE_ORG_ID,
  LIGHTBRIDGE_ORG_ID,
  CDS_ORG_ID,
  ELA_ORG_ID
];

function adjustBoundaries(items, boundaries) {
	_.each(items, i=> {
		const nextBoundary = boundaries.find( b => b > i._id);
		if (nextBoundary)
			i._id = nextBoundary;
	});
	return items;
}

export class Metrics {

  /**
   * Precomputes metrics for a specific organization hierarchy
   * @param {string} rootOrgId - The ID of the root organization
   */
  static async precomputeOrgTree(rootOrgId) {
    Log.info(`Starting precompute for org tree with root ${rootOrgId}`);

    try {
      const { hierarchy } = await OrgsUtil.getOrgHierarchy(rootOrgId);
      const orgIds = hierarchy.map(org => org._id);
      Log.info(`Found ${orgIds.length} orgs in hierarchy for root ${rootOrgId}`);

      for (const orgId of orgIds) {
        try {
          await this.precomputeOrgById(orgId);
        } catch (e) {
          Log.error(`Error while precomputing data for org ${orgId} in hierarchy ${rootOrgId}:`, e);
        }
      }

      Log.info(`Completed precompute for ${orgIds.length} orgs in hierarchy with root ${rootOrgId}`);
    } catch (e) {
      Log.error(`Error getting hierarchy for org ${rootOrgId}:`, e);
    }
  }

  /**
   * Handles the precomputation of metrics for all active organizations for use in billing overview widgets.
   * Skips organizations that have their own dedicated precompute jobs.
   */
  static async precompute() {
    // Get all orgs without inactive ones
    const orgs = await Orgs.find({ inactive: { $ne: true } }, { fields: { _id: 1 } }).fetchAsync();

    // List of org IDs that have their own precompute jobs
    const EXCLUDED_PARENT_ORG_IDS = [
      'FfkDjcS59Eg76FJqM', // BUILDING_KIDZ_ORG_ID
      'zT4NtQk32ooZ6Q6kA', // SUNSHINE_HOUSE_ORG_ID
      'wXZeDbmLnTNEDzL3K', // LIGHTBRIDGE_ORG_ID
      'tJ9aLXAJBhMr8gxz9', // CDS_ORG_ID
      'qPp44WfCMqTj6kdJF'  // ELA_ORG_ID
    ];

    // Initialize a set to store all excluded org IDs (parent orgs and their children)
    let excludedOrgIds = new Set();

    // Get all child org IDs for each excluded parent org
    for (const parentOrgId of EXCLUDED_PARENT_ORG_IDS) {
      try {
        const { hierarchy } = await OrgsUtil.getOrgHierarchy(parentOrgId);
        // Add all org IDs from this hierarchy to the excluded set
        for (const org of hierarchy) {
          excludedOrgIds.add(org._id);
        }
      } catch (e) {
        Log.error(`Error getting hierarchy for org ${parentOrgId}:`, e);
      }
    }

    // Process only non-excluded orgs
    for (const org of orgs) {
      if (!excludedOrgIds.has(org._id)) {
        try {
          await this.precomputeOrgById(org._id);
        } catch(e) {
          Log.error(`Error while precomputing data for ${org._id}: `, e);
        }
      }
    }
  }

	/**
	 * Handles the precomputation of metrics for a specific organization by ID.
	 * @param {string} orgId - The ID of the organization to process.
	 */
	static async precomputeOrgById(orgId) {
		if (!orgId) {
			console.log("precomputeOrgById called without an orgId");
			return;
		}
		console.log(`Starting precompute for orgId: ${orgId}`);
		try {
			const org = await Orgs.findOneAsync(
				{ _id: orgId },
				{ fields: { 'billing': 1 } }
			);

			const billing = org?.billing || {};
			const hasAdyenInfo = billing?.enabled && billing?.adyenInfo && Object.keys(billing?.adyenInfo).length > 0;

			console.log(`Billing enabled: ${billing?.enabled} for orgId: ${orgId}`);
			console.log(`Has Adyen info: ${hasAdyenInfo} for orgId: ${orgId}`);

			// Always-included metrics
			const metricPromises = [
				this.createNewMetricIfNoneExists(orgId),
				this.computeDailySheetsSaved(orgId),
				this.computeMomentsPerTeacher(orgId),
				this.computeMonthlyActiveUsers(orgId),
				this.precomputeFteData(orgId),
			];

			console.log(`Metrics to compute (initial): createNewMetricIfNoneExists, computeDailySheetsSaved, computeMomentsPerTeacher, computeMonthlyActiveUsers, precomputeFteData for orgId: ${orgId}`);

			if (billing?.enabled) {
				console.log(`Including billing-related metrics: precomputeAmountInvoiced, precomputeOutstandingInvoices, precomputeRevenuePerformance for orgId: ${orgId}`);
				metricPromises.push(
					this.precomputeAmountInvoiced(orgId),
					this.precomputeOutstandingInvoices(orgId),
					this.precomputeRevenuePerformance(orgId)
				);

				if (hasAdyenInfo) {
					console.log(`Including Adyen payment metrics: precomputeInProgressPaymentsAndRefunds for orgId: ${orgId}`);
					metricPromises.push(
						this.precomputeInProgressPaymentsAndRefunds(orgId)
					);
				} else {
					console.log(`Skipping Adyen payment metrics: precomputeInProgressPaymentsAndRefunds for orgId: ${orgId}`);
				}
			} else {
				console.log(`Skipping billing-related metrics: precomputeAmountInvoiced, precomputeOutstandingInvoices, precomputeRevenuePerformance for orgId: ${orgId}`);
			}

			await Promise.all(metricPromises);
			try {
				await this.flagOrgAsUpdated(orgId);
			} catch (error) {
				console.log(`Error in flagOrgAsUpdated for orgId: ${orgId}`, error);
			}
			console.log(`Precompute finished for orgId: ${orgId}`);
		} catch (error) {
			console.log(`Error in precomputeOrgById for orgId: ${orgId}`, error);
		}
	}

	/**
	 * Creates a new metric record for the specified organization if none exists.
	 * @param {string} orgId - The ID of the organization to process.
	 */
	static async createNewMetricIfNoneExists(orgId) {
		if (!orgId) {
			Log.warn("Org ID is missing in createNewMetricIfNoneExists");
			return;
		}
		Log.info(`Creating new metric for orgId: ${orgId}`);
		
		try {
			const existingMetric = await ComputedMetrics.findOneAsync({ orgId });
			Log.info(`Existing metric check result: ${existingMetric ? "Found" : "Not Found"}`);
			
			if (!existingMetric) {
				await ComputedMetrics.insertAsync({ orgId });
				Log.info(`New metric inserted for orgId: ${orgId}`);
			}
		} catch (e) {
			Log.error(`Error creating new metric for org ${orgId}`, e);
		}
	}

	/**
	 * Flags an organization as updated by setting the metricsLastUpdated timestamp.
	 * @param {string} orgId - The ID of the organization to update.
	 */
	static async flagOrgAsUpdated(orgId) {
		if (!orgId) {
			return;
		}
		Log.info(`Flagging org as updated for orgId: ${orgId}`);

		try {
			await ComputedMetrics.updateAsync({ orgId }, { $set: { metricsLastUpdated: new Date() } });
		} catch (e) {
			Log.error(`Error flagging org as updated for org ${orgId}`, e);
		}
	}

	static async getAmountInvoiced(options) {
		try {
			options = options || {};
			Log.info("getAmountInvoiced called", { options });
			const orgQuery = await this.getOrgQuery(options.orgIds);
			Log.debug("Org query generated", { orgQuery });
			const metrics = await ComputedMetrics.find({ orgId: orgQuery }).fetchAsync();
			Log.debug("Fetched computed metrics", { count: metrics.length });
			const amountInvoiced = {
				"_id": "all",
				"totalAmount": 0,
				"totalCount": 0
			};
			for (const metric of metrics) {
				amountInvoiced.totalAmount += metric?.amountInvoiced?.totalAmount ?? 0;
				amountInvoiced.totalCount += metric?.amountInvoiced?.totalCount ?? 0;
			}
			Log.info("Computed amount invoiced", { amountInvoiced });
			return amountInvoiced;
		} catch (error) {
			Log.error("Error in getAmountInvoiced", { error, options });
		}
	}

	static async getRevenuePerformanceMetric(options) {
		try {
			options = options || {};
			Log.info("getRevenuePerformanceMetric called", { options });
			const orgQuery = await this.getOrgQuery(options.orgIds);
			Log.debug("Org query generated", { orgQuery });
			const metrics = await ComputedMetrics.find({ orgId: orgQuery }).fetchAsync();
			Log.debug("Fetched computed metrics", { count: metrics.length });
	
			const revenuePerformance = {
				"months": {
					"summaryOverTime": [],
					"boundaries": []
				},
				"years": {
					"summaryOverTime": [],
					"boundaries": []
				},
				"weeks": {
					"summaryOverTime": [],
					"boundaries": []
				}
			};
	
			for (const metric of metrics) {
				Log.info("Processing metric", { metricId: metric._id });
				const existingRPMonthBoundary = revenuePerformance.months.boundaries;
				const metricRPMonthBoundary = metric?.revenuePerformance?.months?.boundaries ?? [];
				revenuePerformance.months.boundaries = _.union(existingRPMonthBoundary, metricRPMonthBoundary);
	
				const existingRPYearsBoundary = revenuePerformance.years.boundaries;
				const metricRPYearsBoundary = metric?.revenuePerformance?.years?.boundaries ?? [];
				revenuePerformance.years.boundaries = _.union(existingRPYearsBoundary, metricRPYearsBoundary);
	
				const existingRPWeeksBoundary = revenuePerformance.weeks.boundaries;
				const metricRPWeeksBoundary = metric?.revenuePerformance?.weeks?.boundaries ?? [];
				revenuePerformance.weeks.boundaries = _.union(existingRPWeeksBoundary, metricRPWeeksBoundary);
	
				const existingMonthsSummary = revenuePerformance.months.summaryOverTime;
				const metricMonthsSummary = metric?.revenuePerformance?.months?.summaryOverTime ?? [];
				const newMonthsSummary = [];
				for (const monthssum of metricMonthsSummary) {
					const existingCount = _.find(existingMonthsSummary, (x) => x._id == monthssum._id);
					if (existingCount) {
						const newSum = (existingCount?.sum ?? 0) + (monthssum?.sum ?? 0);
						newMonthsSummary.push({ _id: existingCount._id, sum: newSum});
					} else {
						newMonthsSummary.push({ _id: monthssum._id, sum: monthssum?.sum ?? 0});
					}
				}
				revenuePerformance.months.summaryOverTime = newMonthsSummary;
	
				const existingYearsSummary = revenuePerformance.years.summaryOverTime;
				const metricYearsSummary = metric?.revenuePerformance?.years?.summaryOverTime ?? [];
				const newYearsSummary = []
				for (const yearssum of metricYearsSummary) {
					const existingCount = _.find(existingYearsSummary, (x) => x._id == yearssum._id);
					if (existingCount) {
						const newSum = (existingCount?.sum ?? 0) + (yearssum?.sum ?? 0);
						newYearsSummary.push({ _id: existingCount._id, sum: newSum});
					} else {
						newYearsSummary.push({ _id: yearssum._id, sum: yearssum?.sum ?? 0});
					}
				}
				revenuePerformance.years.summaryOverTime = newYearsSummary;
	
				const existingWeeksSummary = revenuePerformance.weeks.summaryOverTime;
				const metricWeeksSummary = metric?.revenuePerformance?.weeks?.summaryOverTime ?? [];
				const newWeeksSummary = []
				for (const weekssum of metricWeeksSummary) {
					const existingCount = _.find(existingWeeksSummary, (x) => x._id == weekssum._id);
					if (existingCount) {
						const newSum = (existingCount?.sum ?? 0) + (weekssum?.sum ?? 0);
						newWeeksSummary.push({ _id: existingCount._id, sum: newSum});
					} else {
						newWeeksSummary.push({ _id: weekssum._id, sum: weekssum?.sum ?? 0});
					}
				}
				revenuePerformance.weeks.summaryOverTime = newWeeksSummary;
			}
	
			Log.info("Computed revenue performance", { revenuePerformance });
			return revenuePerformance;
	
		} catch (error) {
			Log.error("Error in getRevenuePerformanceMetric", { error, options });
		}
	}
	static async getComputedMetrics(options) {
		options = options || {};
		const orgQuery = await this.getOrgQuery(options.orgIds);
		const metrics = await ComputedMetrics.find({orgId: orgQuery}).fetchAsync();
		const returnMetric = {
			"dailySheetsSaved": {
				"countOverTime": [],
				"boundaries": []
			},
			"momentsPerTeacher": {
				"teacherAndMomentCount": [],
				"countOverTime": [],
				"boundaries": []
			},
			"monthlyActiveUsers": {
				"boundaries": [],
				"activeUsersCounts": []
			},
			"amountInvoiced": {
				"_id": "all",
				"totalAmount": 0,
				"totalCount": 0
			},
			"outstandingInvoices": {
				"_id": "all",
				"totalAmount": 0,
				"totalCount": 0
			},
			"fteData": [],
			"revenuePerformance": {
				"months": {
					"summaryOverTime": [],
					"boundaries": []
				},
				"years": {
					"summaryOverTime": [],
					"boundaries": []
				},
				"weeks": {
					"summaryOverTime": [],
					"boundaries": []
				}
			},
			"inProgressPaymentsAndRefunds": {
				"refundsInProgress": 0,
				"paymentsInProgress": 0,
				"createdAt": null
			}
		};
		for (const metric of metrics ) {
			
			//Daily sheet combine
			const existingDailySheetCount = returnMetric.dailySheetsSaved.countOverTime;
			const existingDailySheetBoundaries = returnMetric.dailySheetsSaved.boundaries;
			const metricDSBoundary = metric?.dailySheetsSaved?.boundaries ?? [];
			const metricDSCount = metric?.dailySheetsSaved?.countOverTime ?? [];
			returnMetric.dailySheetsSaved.boundaries = _.union(existingDailySheetBoundaries, metricDSBoundary);
			const newDSCountArr = []
			for (const dscount of metricDSCount) {
				const existingCount = _.find(existingDailySheetCount, (x) => x._id == dscount._id);
				if (existingCount) {
					const newCount = (existingCount?.count ?? 0) + (dscount?.count ?? 0); 
					newDSCountArr.push({ _id: existingCount._id, count: newCount});
				} else {
					newDSCountArr.push({ _id: dscount._id, count: dscount?.count ?? 0});
				}
			}
			returnMetric.dailySheetsSaved.countOverTime = newDSCountArr;

			//combine moments per teacher
			const metricTeacherAndMomentCount = metric?.momentsPerTeacher?.teacherAndMomentCount ?? [];
			returnMetric.momentsPerTeacher.teacherAndMomentCount = returnMetric.momentsPerTeacher.teacherAndMomentCount.concat(metricTeacherAndMomentCount);
			
			const existingMPTCount = returnMetric.momentsPerTeacher.countOverTime;
			const metricMPTCount = metric?.momentsPerTeacher?.countOverTime ?? [];
			const newMPTCountArr = []
			for (const mptcount of metricMPTCount) {
				const existingCount = _.find(existingMPTCount, (x) => x._id == mptcount._id);
				if (existingCount) {
					const newCount = (existingCount?.count ?? 0) + (mptcount?.count ?? 0); 
					newMPTCountArr.push({ _id: existingCount._id, count: newCount});
				} else {
					newMPTCountArr.push({ _id: mptcount._id, count: mptcount?.count ?? 0});
				}
			}
			returnMetric.momentsPerTeacher.countOverTime = newMPTCountArr;

			const existingMPTBoundaries = returnMetric.momentsPerTeacher.boundaries;
			const metricMPTBoundary = metric?.momentsPerTeacher?.boundaries ?? [];
			returnMetric.momentsPerTeacher.boundaries = _.union(existingMPTBoundaries, metricMPTBoundary);

			//combine monthly active users
			const existingMAUBoundaries = returnMetric.monthlyActiveUsers.boundaries;
			const metricMAUBoundary = metric?.monthlyActiveUsers?.boundaries ?? [];
			returnMetric.monthlyActiveUsers.boundaries = _.union(existingMAUBoundaries, metricMAUBoundary);

			const existingMAUCount = returnMetric.monthlyActiveUsers.activeUsersCounts;
			const metricMAUCount = metric?.monthlyActiveUsers?.activeUsersCounts ?? [];
			const newMAUCountArr = []
			for (const maucount of metricMAUCount) {
				const existingCount = _.find(existingMAUCount, (x) => x._id == maucount._id);
				if (existingCount) {
					const newCount = (existingCount?.count ?? 0) + (maucount?.count ?? 0); 
					newMAUCountArr.push({ _id: existingCount._id, count: newCount});
				} else {
					newMAUCountArr.push({ _id: maucount._id, count: maucount?.count ?? 0});
				}
			}
			returnMetric.monthlyActiveUsers.activeUsersCounts = newMAUCountArr;

			returnMetric.amountInvoiced.totalAmount += metric?.amountInvoiced?.totalAmount ?? 0;
			returnMetric.amountInvoiced.totalCount += metric?.amountInvoiced?.totalCount ?? 0;
			returnMetric.outstandingInvoices.totalAmount += metric?.outstandingInvoices?.totalAmount ?? 0;
			returnMetric.outstandingInvoices.totalCount += metric?.outstandingInvoices?.totalCount ?? 0;

			returnMetric.fteData = returnMetric.fteData.concat(metric?.fteData ?? []);

			//revenue performance
			const existingRPMonthBoundary = returnMetric.revenuePerformance.months.boundaries;
			const metricRPMonthBoundary = metric?.revenuePerformance?.months?.boundaries ?? [];
			returnMetric.revenuePerformance.months.boundaries = _.union(existingRPMonthBoundary, metricRPMonthBoundary);
			
			const existingRPYearsBoundary = returnMetric.revenuePerformance.years.boundaries;
			const metricRPYearsBoundary = metric?.revenuePerformance?.years?.boundaries ?? [];
			returnMetric.revenuePerformance.years.boundaries = _.union(existingRPYearsBoundary, metricRPYearsBoundary);
			
			const existingRPWeeksBoundary = returnMetric.revenuePerformance.weeks.boundaries;
			const metricRPWeeksBoundary = metric?.revenuePerformance?.weeks?.boundaries ?? [];
			returnMetric.revenuePerformance.weeks.boundaries = _.union(existingRPWeeksBoundary, metricRPWeeksBoundary);

			const existingMonthsSummary = returnMetric.revenuePerformance.months.summaryOverTime;
			const metricMonthsSummary = metric?.revenuePerformance?.months?.summaryOverTime ?? [];
			const newMonthsSummary = []
			for (const monthssum of metricMonthsSummary) {
				const existingCount = _.find(existingMonthsSummary, (x) => x._id == monthssum._id);
				if (existingCount) {
					const newSum = (existingCount?.sum ?? 0) + (monthssum?.sum ?? 0); 
					newMonthsSummary.push({ _id: existingCount._id, sum: newSum});
				} else {
					newMonthsSummary.push({ _id: monthssum._id, sum: monthssum?.sum ?? 0});
				}
			}
			returnMetric.revenuePerformance.months.summaryOverTime = newMonthsSummary;

			const existingYearsSummary = returnMetric.revenuePerformance.years.summaryOverTime;
			const metricYearsSummary = metric?.revenuePerformance?.years?.summaryOverTime ?? [];
			const newYearsSummary = []
			for (const yearssum of metricYearsSummary) {
				const existingCount = _.find(existingYearsSummary, (x) => x._id == yearssum._id);
				if (existingCount) {
					const newSum = (existingCount?.sum ?? 0) + (yearssum?.sum ?? 0); 
					newYearsSummary.push({ _id: existingCount._id, sum: newSum});
				} else {
					newYearsSummary.push({ _id: yearssum._id, sum: yearssum?.sum ?? 0});
				}
			}
			returnMetric.revenuePerformance.years.summaryOverTime = newYearsSummary;
			
			const existingWeeksSummary = returnMetric.revenuePerformance.weeks.summaryOverTime;
			const metricWeeksSummary = metric?.revenuePerformance?.weeks?.summaryOverTime ?? [];
			const newWeeksSummary = []
			for (const weekssum of metricWeeksSummary) {
				const existingCount = _.find(existingWeeksSummary, (x) => x._id == weekssum._id);
				if (existingCount) {
					const newSum = (existingCount?.sum ?? 0) + (weekssum?.sum ?? 0); 
					newWeeksSummary.push({ _id: existingCount._id, sum: newSum});
				} else {
					newWeeksSummary.push({ _id: weekssum._id, sum: weekssum?.sum ?? 0});
				}
			}
			returnMetric.revenuePerformance.weeks.summaryOverTime = newWeeksSummary;

			const existingInProgressPaymentsAndRefunds = returnMetric.inProgressPaymentsAndRefunds;
			const metricInProgressPaymentsAndRefunds = metric?.inProgressPaymentsAndRefunds ?? {};
			returnMetric.inProgressPaymentsAndRefunds.refundsInProgress = existingInProgressPaymentsAndRefunds.refundsInProgress + metricInProgressPaymentsAndRefunds?.refundsInProgressTotal;
			returnMetric.inProgressPaymentsAndRefunds.paymentsInProgress = existingInProgressPaymentsAndRefunds.paymentsInProgress + metricInProgressPaymentsAndRefunds?.paymentsInProgressTotal;

		}

		return returnMetric;
	}
	static async getMetric(metric, options) {
		try {
			Log.info(`getMetric called with metric: ${metric}, options:`, options);
			var self = this;
			const result = await self[metric](options);
			Log.info(`getMetric result for metric: ${metric}`, result);
			return result;
		} catch (error) {
			Log.error(`Error in getMetric for metric: ${metric}`, error);
			return {};
		}
	}
	static async billingChargebacksReport(options) {
		try { 
			Log.info(`billingChargebacksReport called with options:`, options);
			const result = await Meteor.callAsync("billingChargebacksReport", options);
			Log.info(`billingChargebacksReport result:`, result);
			return result;
		} catch (ex) {
			Log.error(`Error in billingChargebacksReport:`, ex);
			return {};
		}
	}
	static async billingRefundsReport(options) {
		try {
			Log.info(`billingRefundsReport called with options:`, options);
			const result = await Meteor.callAsync("billingRefundsReport", options);
			Log.info(`billingRefundsReport result:`, result);
			return result;
		} catch (ex) {
			Log.error(`Error in billingRefundsReport:`, ex);
			return {};
		}
	}

	static async recentRefusals(options) {
		try { 
			Log.info(`recentRefusals called with options:`, options);
			const result = await Meteor.callAsync("billingTransactionReport", options);
			Log.info(`recentRefusals result:`, result);
			return result;
		} catch (ex) {
			Log.error(`Error in recentRefusals:`, ex);
			return {};
		}
	}
	static async reservationsForReview(options) {
		try {
			Log.info(`reservationsForReview called with options:`, options);
			const result = await Meteor.callAsync("getReservationsForBillingReview", options);
			Log.info(`reservationsForReview result:`, result);
			return result;
		} catch (ex) {
			Log.error(`Error in reservationsForReview:`, ex);
			return {};
		}
	}
	static async recentManualPayments(options) {
		try {
			Log.info(`recentManualPayments called with options:`, options);
			const result = await Meteor.callAsync("billingUndepositedPayments", options);
			Log.info(`recentManualPayments result:`, result);
			return result;
		} catch (ex) {
			Log.error(`Error in recentManualPayments:`, ex);
			return {};
		}
	}
	static async newEnrollments(options) {
		try {
			Log.info(`newEnrollments called with options:`, options);
			const result = await Meteor.callAsync("getNewEnrollments", options);
			Log.info(`newEnrollments result:`, result);
			return result;
		} catch (ex) {
			Log.error(`Error in newEnrollments:`, ex);
			return {};
		}
	}
	
	static async upcomingInvoiceEvents(options) {
		try {
			Log.info(`upcomingInvoiceEvents called with options:`, options);
			const org = await Orgs.current();
			Log.info(`Fetched organization data:`, org);
			const plans = _.filter(_.deep(org, "billing.plansAndItems"), (p) => p.type == "plan");
			Log.info(`Filtered billing plans:`, plans);
			const generateDay = _.deep(org, "billing.scheduling.generateDay");
			const generateMonthDay = _.deep(org, "billing.scheduling.generateMonthDay");
			let upcomingEvents = {};
			const rightnow = new moment();
				_.each(plans, (plan) => {
				let upcomingEvent = {};
				if (plan.frequency == 'weekly') {
				upcomingEvent.nextDate = new moment().day(generateDay).isoWeekday() > rightnow.isoWeekday() ? 
											new moment().day(generateDay) : 
											new moment().add(1, "weeks").day(generateDay);
					upcomingEvent.dateLabel = upcomingEvent.nextDate.format("MMMM D, YYYY");
					upcomingEvent.monthLabel = upcomingEvent.nextDate.format("MMM");
					upcomingEvent.dayLabel = upcomingEvent.nextDate.format("D");
					upcomingEvent.description = "Weekly invoice generation";
					upcomingEvents["weekly"] = upcomingEvent;
				}
				if (plan.frequency == 'monthly') {
				upcomingEvent.nextDate = generateMonthDay > rightnow.date() ? 
											new moment().date(generateMonthDay) : 
											new moment().add(1, "months").date(generateMonthDay);
					upcomingEvent.dateLabel = upcomingEvent.nextDate.format("MMMM D, YYYY");
					upcomingEvent.monthLabel = upcomingEvent.nextDate.format("MMM");
					upcomingEvent.dayLabel = upcomingEvent.nextDate.format("D");
					upcomingEvent.description = "Monthly invoice generation";
	                upcomingEvents["monthly"] = upcomingEvent;
				}
			});
			Log.info(`Generated upcoming invoice events:`, upcomingEvents);
			return _.values(upcomingEvents);
		} catch (ex) {
			Log.error(`Error in upcomingInvoiceEvents:`, ex);
			return [];
		}
	}

	static async amountInvoiced(options) {
		try {
			Log.info(`amountInvoiced called with options:`, options);
			await processPermissions({
				assertions: [{ context: "billing/payments", action: "read" }],
				evaluator: (person) => person.type == "admin",
				throwError: true
			});
			Log.info(`Permissions verified successfully`);
	
			const orgQuery = await this.getOrgQuery(options.orgIds);
			Log.info(`Generated org query:`, orgQuery);
			const metric = await ComputedMetrics.findOneAsync({ orgId: orgQuery });
			Log.info(`Fetched computed metric:`, metric);
	
			return metric?.amountInvoiced;
		} catch (ex) {
			Log.error(`Error in amountInvoiced:`, ex);
			return null;
		}
	}

	/**
	 * Computes the total amount invoiced for a specific organization over the past 30 days.
	 * Aggregates data from the `Invoices` collection, excluding voided invoices, to calculate
	 * the total amount invoiced and the number of unique payers.
	 *
	 * @param {string} orgId - The ID of the organization to compute the data for.
	 */
	static async precomputeAmountInvoiced(orgId) {
		if (!orgId) {
			Log.info(`Skipping precomputeAmountInvoiced: Missing orgId`);
			return;
		}
	
		Log.info(`Computing amount invoiced for orgId: ${orgId}`);
	
		try {
			const lastMonthStamp = new moment().add(-30, "days").valueOf();
			Log.info(`Last month timestamp calculated: ${lastMonthStamp}`);
			const amount = await (await Invoices.aggregate([
			{$match: {orgId: orgId, voidedAt: {"$exists": false}, createdAt: {"$gte": lastMonthStamp}}},
			{$group: {_id: "$personId", totalAmount: {$sum: "$originalAmount"}}},
			{$group: {_id: "all", totalAmount: {$sum: "$totalAmount"}, totalCount: {$sum: 1}}}
			])).toArray();
	
			Log.info(`Amount invoiced data retrieved for orgId: ${orgId}`, amount);
	        await ComputedMetrics.updateAsync({ orgId }, { $set: { amountInvoiced: amount?.[0] } });
	        Log.info(`Successfully updated amount invoiced for orgId: ${orgId}`);
		} catch (error) {
			Log.error(`Error computing amount invoiced for orgId: ${orgId}`, error);
		}
	}
	
    static async fteData(options) {
		options = options || {};
		await processPermissions({
			evaluator: (person) => person.type=="admin",
			throwError: true
		});
		const orgQuery = await this.getOrgQuery(options.orgIds);
		const metric = await ComputedMetrics.findOneAsync({orgId: orgQuery});

		return metric?.fteData;
	}

	/**
	 * Computes Full-Time Equivalent (FTE) data for a specific organization.
	 * Calculates FTE metrics for groups, including current FTE, variance, FTE for the next month,
	 * and enrollment goal percentages. Aggregates group-level data and overall totals.
	 *
	 * @param {string} orgId - The ID of the organization to compute the FTE data for.
	 */
	static async precomputeFteData(orgId) {
		if (!orgId) {
			Log.info(`Skipping precomputeFteData: Missing orgId`);
			return;
		}
		Log.info(`Computing FTE data for orgId: ${orgId}`);
		try {
			const currentOrg = await Orgs.findOneAsync({ _id: orgId });
	    if (!currentOrg) {
				Log.info(`Organization not found for orgId: ${orgId}`);
				return;
			}
	        const startMonth = new moment().startOf('week').add(1, 'week').add(1, 'days').format("MM/DD/YYYY");
			Log.info(`Start month: ${startMonth}`);
	
		let groups = await Groups.find({ orgId: currentOrg._id, includeClassList: { $ne: false }}).fetchAsync();
			groups = sortGroupsByAge(groups);
			Log.info(`Total groups fetched: ${groups.length}`);
	
			const dayMomentValue = new moment.tz(startMonth, "MM/DD/YYYY", currentOrg.getTimezone()).startOf('day').valueOf();
			const scheduledDateQueryValue = new moment(dayMomentValue).endOf('week').endOf('day').add(2, 'months').valueOf();
	        const resQuery = {
				orgId: currentOrg._id,
				scheduledDate: { "$lte": scheduledDateQueryValue },
			"$or": [ {scheduledEndDate: null}, {scheduledEndDate: {"$gt": dayMomentValue}} ],
			recurringFrequency: {"$exists" : true }
			};
	
			const allReservations = await Reservations.find(resQuery).fetchAsync();
			Log.info(`Total reservations fetched: ${allReservations.length}`);
	
			const totals = {
				name: currentOrg.name,
				currentFte: 0,
				currentVariance: 0,
				currentFtePlusOne: 0,
				preferredCapacity: 0,
			};
	
			let resultGroups = [];
			for (let x = 0; x < groups.length; x++) {
				try {
					const currentGroup = groups[x];
					totals.preferredCapacity += parseInt(currentGroup.preferredCapacity || 0);
	
					const currentFteResult = await getFteForMonth({
						startMonth,
						orgId: currentOrg._id,
						groupId: currentGroup._id,
						allReservations,
					});
	
					currentGroup.currentFte = currentFteResult.totalCount;
					totals.currentFte = (Math.round((parseFloat(currentGroup.currentFte) + parseFloat(totals.currentFte)) * 10) / 10).toFixed(1);
			currentGroup.currentVariance = (!isNaN(currentGroup.preferredCapacity)) ? (currentGroup.preferredCapacity - currentGroup.currentFte).toFixed(1) : null;
	
					const currentFtePlusOneResult = await getFteForMonth({
						orgId: currentOrg._id,
						groupId: currentGroup._id,
						includeTransition: true,
						startMonth: new moment(startMonth, "MM/DD/YYYY").add(1, 'month').startOf('month').add(5, "days").startOf('week').add(1, "days").format("MM/DD/YYYY"),
						allReservations,
					});
	
					currentGroup.currentFtePlusOne = currentFtePlusOneResult.totalCount;
					totals.currentFtePlusOne = (Math.round((parseFloat(currentGroup.currentFtePlusOne) + parseFloat(totals.currentFtePlusOne)) * 10) / 10).toFixed(1);
			currentGroup.enrollmentGoalPercentage = (currentGroup.enrollmentGoal > 0) ? ((parseFloat(currentGroup.currentFtePlusOne) / currentGroup.enrollmentGoal) *100).toFixed(2) : (0).toFixed(2);
					resultGroups.push(currentGroup);
				} catch (error) {
					Log.error(`Error processing group ${groups[x]?._id}:`, error);
				}
			}
	
		totals.currentVariance = (!isNaN(totals.preferredCapacity)) ? (totals.preferredCapacity - totals.currentFte).toFixed(1) : null;
		let results = [];
		results.push({ groups: resultGroups, totals: [totals] })
	
			await ComputedMetrics.updateAsync({ orgId }, { $set: { fteData: results || [] } });
	    Log.info(`Successfully updated FTE data for orgId: ${orgId}`);
		} catch (error) {
			Log.error(`Error computing FTE data for orgId: ${orgId}`, error);
		}
	}

	static async momentsPerTeacher(options) {
		options = options || {};
		const orgQuery = await this.getOrgQuery(options.orgIds);
		const metric = await ComputedMetrics.findOneAsync({orgId: orgQuery});

		return metric?.momentsPerTeacher;
	}

	/**
	 * Computes the number of moments per teacher for a specific organization.
	 * Aggregates data from the `Engagements` collection for the past 4 weeks,
	 * calculating moments created by each teacher and the distribution over time.
	 *
	 * @param {string} orgId - The ID of the organization to compute the data for.
	 */
	static async computeMomentsPerTeacher(orgId) {
		if (!orgId) {
			Log.info("Org ID is missing in computeMomentsPerTeacher");
			return;
		}
		Log.info(`Computing moments per teacher for orgId: ${orgId}`);
		
		try {
			const boundaries = [];
			const today = new moment().startOf('day');
			
			for (let i = 4; i >= 0; i--) {
				boundaries.push(today.clone().add(-1 * i, "weeks").valueOf());
			}
			
			const earliestDate = _.min(boundaries);
			Log.info(`Boundaries computed: ${boundaries}`);
			const totalMomentPipeline = [
                {$match: {"subType":"created_moment", "createdAt": {"$gte": earliestDate}, "orgId": orgId }},
                {$facet: {
						"teacherAndMomentCount": [
                            {"$sortByCount": "$createdBy"},
                            {"$project": { "totalStaff": {$sum:1}, "totalMoments": {"$sum":"$count"}}}
						],
						"countOverTime": [
                            {"$bucket":{
									"groupBy": "$createdAt",
									"boundaries": boundaries,
									"default": "Other",
									"output": {
                                        "count": {"$sum":1}
									}
                                }}
						]
                    }}
            ],
			totalMomentData = await (await Engagements.aggregate(totalMomentPipeline)).toArray();
			
			totalMomentData[0]["countOverTime"] = adjustBoundaries(totalMomentData[0]["countOverTime"], boundaries);
			boundaries.shift();
			totalMomentData[0]["boundaries"] = boundaries;
			
			await ComputedMetrics.updateAsync(
				{ orgId },
				{ $set: { momentsPerTeacher: totalMomentData?.[0] } }
			);
			Log.info(`Updated momentsPerTeacher metric for orgId: ${orgId}`);
		} catch (e) {
			Log.error(`Error computing moments per teacher for org ${orgId}`, e);
		}
	}

	static async monthlyActiveUsers(options) {
		options = options || {};
		const orgQuery = await this.getOrgQuery(options.orgIds);
		const metric = await ComputedMetrics.findOneAsync({orgId: orgQuery});

		return metric?.monthlyActiveUsers;
	}

	/**
	 * Computes the number of monthly active users for a specific organization.
	 * Aggregates user login data from the `UserLogins` collection over the past 4 months,
	 * grouping users by their last login dates and calculating unique active users per month.
	 *
	 * @param {string} orgId - The ID of the organization to compute the data for.
	 */
	static async computeMonthlyActiveUsers(orgId) {
		if (!orgId) {
			Log.warn("computeMonthlyActiveUsers: orgId is missing, skipping computation.");
			return;
		}
	
		Log.info(`Computing monthly active users for orgId: ${orgId}`);
	
		try {
			const boundaries = [];
			const today = new moment().startOf('day');
	
			for (let i = 4; i >= 0; i--) {
				boundaries.push(today.clone().add(-1 * i, "months").toDate());
			}
	        Log.info(`Computed boundaries for monthly active users: ${JSON.stringify(boundaries)}`);
	
			const queryDoc = [
				{
					$match: {
						"orgId": orgId,
					}
				},
				{
					"$bucket": {
						"groupBy": "$lastSeenDate",
						"boundaries": boundaries,
						"default": "Other",
						"output": {
							"allCount": {"$sum": 1},
							"visitors": {"$addToSet": {"userId": "$userId"}}
						}
					}
				},
				{"$project": {"_id": "$_id", "count": {"$size": "$visitors"}}}
			];
	
			const activeUsersData = await (await UserLogins.aggregate(queryDoc)).toArray();
			Log.info(`Fetched active users data: ${JSON.stringify(activeUsersData)}`);
			boundaries.pop();
			const returnData = {
				"boundaries": boundaries,
				"activeUsersCounts": _.reject(activeUsersData, (a) => a._id === "Other"),
			};
	
			Log.info(`Updating computed metrics for orgId: ${orgId}`);
			await ComputedMetrics.updateAsync({ orgId }, { $set: { monthlyActiveUsers: returnData } });
			Log.info(`Successfully updated monthly active users for orgId: ${orgId}`);
		} catch (error) {
			Log.error(`Error computing monthly active users for orgId: ${orgId}`, error);
		}
	}

	static async dailySheetsSaved(options) {
		options = options || {};
		const orgQuery = await this.getOrgQuery(options.orgIds);
		const metric = await ComputedMetrics.findOneAsync({orgId: orgQuery});

		return metric?.dailySheetsSaved;
	}

	/**
	 * Computes the number of daily sheets saved for a specific organization.
	 * Aggregates data from the `Moments` collection for the past 4 weeks.
	 *
	 * @param {string} orgId - The ID of the organization to compute the data for.
	 */
	static async computeDailySheetsSaved(orgId) {
		if (!orgId) {
			Log.info("Org ID is missing in computeDailySheetsSaved");
			return;
		}
		Log.info(`Computing daily sheets saved for orgId: ${orgId}`);
		
		try {
			const boundaries = [];
			const today = new moment().startOf('day');
			
			for (let i = 4; i >= 0; i--) {
				boundaries.push(today.clone().add(-1 * i, "weeks").valueOf());
			}
			
			const earliestDate = _.min(boundaries);
			Log.info(`Boundaries computed: ${boundaries}`);
			const totalMomentData = await (await Moments.aggregate([
				{ $match: { "momentType": "checkout", "createdAt": { "$gte": earliestDate }, "orgId": orgId } },
				{
					$facet: {
						"countOverTime": [
							{
								"$bucket": {
									"groupBy": "$createdAt",
									"boundaries": boundaries,
									"default": "Other",
									"output": {
										"count": { "$sum": 1 }
									}
								}
							}
						]
					}
				}
			])).toArray();
			boundaries.pop();
			totalMomentData[0]["boundaries"] = boundaries;
	 		await ComputedMetrics.updateAsync(
				{ orgId: orgId },
				{ $set: { dailySheetsSaved: totalMomentData?.[0] } }
			);
			Log.info(`Updated dailySheetsSaved metric for orgId: ${orgId}`);
		} catch (e) {
			Log.error(`Error computing daily sheets saved for org ${orgId}`, e);
		}
	}

	static async outstandingInvoices(options) {
		options = options || {};
		await processPermissions({
			assertions: [{ context: "billing/payments", action: "read" }],
			evaluator: (person) => person.type=="admin",
			throwError: true
		});
		const orgQuery = await this.getOrgQuery(options.orgIds);
		const metric = await ComputedMetrics.findOneAsync({orgId: orgQuery});

		return metric?.outstandingInvoices;
	}

	/**
	 * Computes the total outstanding invoices for a specific organization.
	 * Aggregates data from the `Invoices` collection, excluding voided invoices,
	 * to calculate the total outstanding amount and the number of open invoices.
	 *
	 * @param {string} orgId - The ID of the organization to compute the data for.
	 */
	static async precomputeOutstandingInvoices(orgId) {
		if (!orgId) {
			Log.info(`Skipping precomputeOutstandingInvoices: Missing orgId`);
			return;
		}
	
		Log.info(`Computing outstanding invoices for orgId: ${orgId}`);
	
		try {
			const amount = await (await Invoices.aggregate([
				{$match: {orgId: orgId, voidedAt: {"$exists": false}, openAmount: {"$gt": 0}}},
				{$group: {_id: "all", totalAmount: {$sum: "$openAmount"}, totalCount: {$sum: 1}}}
			])).toArray();
	
			Log.info(`Outstanding invoices data retrieved for orgId: ${orgId}`, amount);
			await ComputedMetrics.updateAsync({orgId}, {$set: {outstandingInvoices: amount?.[0]}});
			Log.info(`Successfully updated outstanding invoices for orgId: ${orgId}`);
		} catch (error) {
			Log.error(`Error computing outstanding invoices for orgId: ${orgId}`, error);
		}
	}
	
	static async pastDuePeople(options) {
		try {
			Log.info(`pastDuePeople called with options:`, options);
			options = options || {};
	
			if (options.screen === "billing") {
				await processPermissions({
					assertions: [{ context: "billing/payments", action: "read" }],
					evaluator: (person) => person.type == "admin",
					throwError: true
				});
				Log.info(`Permissions verified successfully`);
			}
	
			const currentOrg = await Orgs.current();
			Log.info(`Fetched current organization:`, currentOrg);
	
			const dueDateStamp = new moment().tz(currentOrg.getTimezone()).startOf("day").valueOf();
			Log.info(`Computed dueDateStamp:`, dueDateStamp);
	
			const allOpenInvoices = await Invoices.find({
				orgId: currentOrg._id,
				openAmount: { "$gt": 0 },
				dueDate: { "$lt": dueDateStamp }
			}).fetchAsync();
			Log.info(`Fetched open invoices count:`, allOpenInvoices.length);
	
			const groupedInvoices = _.groupBy(allOpenInvoices, (i) => i.personId);
			Log.info(`Grouped invoices by personId`);
	
			const results = [];
			for (const [personId, invoices] of Object.entries(groupedInvoices)) {
			const personDetail = await People.findOneAsync({ orgId: currentOrg._id, _id: personId }, { fields: { firstName: 1, lastName: 1, inActive: 1 } });
				const balance = _.reduce(invoices, (memo, i) => memo + i.openAmount, 0.0);
				results.push({
					personId,
					personDetail,
					balance
				});
			}
			Log.info(`Processed overdue balances for ${results.length} people`);
	
			const sortedResults = _.sortBy(results, (p) => -1 * p.balance);
			return sortedResults;
		} catch (ex) {
			Log.error(`Error in pastDuePeople:`, ex);
			return [];
		}
	}
	static async upcomingWithdrawals(options) {
		try {
			Log.info(`upcomingWithdrawals called with options:`, options);
			options = options || {};
			await processPermissions({
				assertions: [{ context: "billing/payments", action: "read" }],
			evaluator: (person) => person.type=="admin",
				throwError: true
			});
			Log.info(`Permissions verified successfully`);
	
			const currentOrg = await Orgs.current();
			Log.info(`Fetched current organization:`, currentOrg);
	
			const withdrawQuery = {
				orgId: currentOrg._id,
				type: "person",
				inActive: { "$ne": true }
			};
	
			const datePredicate = {
				"$gte": (options.startDate ? new moment(options.startDate, "MM/DD/YYYY") : new moment()).valueOf(),
				"$lt":  (options.endDate ? new moment(options.endDate, "MM/DD/YYYY") : new moment().add(30, "days")).valueOf() 
			};
			Log.info(`Computed date range:`, datePredicate);
	
			let withdrawDateField = "withdrawDate";
			if (currentOrg.profileDataPrefix())
				withdrawDateField = `${currentOrg.profileDataPrefix()}.withdrawDate`;
			if (!options.showAll)
				withdrawQuery[withdrawDateField] = datePredicate;
	
		const fieldsProjection = {firstName:1, lastName:1};
			fieldsProjection[withdrawDateField] = 1;
	
			const withdrawingPeople = await People.find(withdrawQuery, { fields: fieldsProjection }).fetchAsync();
			Log.info(`Fetched withdrawing people count:`, withdrawingPeople.length);
	
			const results = [];
	
			for (const p of withdrawingPeople) {
				try {
					const securityDeposits = await p.openSecurityDeposits({ includeResolved: options.showResolved });
					for (const sd of securityDeposits) {
				sd.collapsedWithdrawDate = currentOrg.profileDataPrefix() ? p[currentOrg.profileDataPrefix()]?.["withdrawDate"] : p["withdrawDate"];
						if (options.includeOpenInvoices) {
					sd.openInvoices = await Invoices.find({ personId: p._id, openAmount: { "$gt": 0 }, voidedAt: { "$exists": false } }, { fields: { invoiceNumber: 1, invoiceDate: 1, openAmount: 1 } }).fetchAsync();
						}
						sd.firstName = p.firstName;
						sd.lastName = p.lastName;
						sd._id = p._id;
						results.push(sd);
					}
				} catch (personEx) {
					Log.error(`Error processing person ${p._id}:`, personEx);
				}
			}
	
			const sortedResults = _.chain(results)
				.flatten()
				.sortBy(p => p.collapsedWithdrawDate)
				.value();
			Log.info(`Processed upcoming withdrawals, total records:`, sortedResults.length);
			return sortedResults;
		} catch (ex) {
			Log.error(`Error in upcomingWithdrawals:`, ex);
			return [];
		}
	}
	static async pastDueSummary(options) {
		try {
			Log.info(`pastDueSummary called with options:`, options);
			options = options || {};
			await processPermissions({
				assertions: [{ context: "billing/payments", action: "read" }],
				evaluator: (person) => person.type == "admin",
				throwError: true
			});
			Log.info(`Permissions verified successfully`);
	
			const currentOrg = await Orgs.current();
			Log.info(`Fetched current organization:`, currentOrg);
	
			let totalPastDue = 0.0;
			const buckets = {
				"< 7 days": 0.0,
				"7-14 days": 0.0,
				"14-21 days": 0.0,
				"21-28 days": 0.0,
				"Over 28": 0.0
			};
			const currentTime = new moment();
			const invoices = await Invoices.find({
				orgId: currentOrg._id,
				openAmount: { "$gt": 0 },
				dueDate: { "$lt": currentTime.valueOf() },
				voidedAt: { $exists: false }
			}).fetchAsync();
			Log.info(`Fetched invoices count:`, invoices.length);
	
			invoices.forEach(invoice => {
				try {
					const invoiceAge = currentTime.diff(new moment(invoice.dueDate), "days");
			if (invoiceAge < 7 ) buckets["< 7 days"] += invoice.openAmount;
			else if (invoiceAge < 14 ) buckets["7-14 days"] += invoice.openAmount;
			else if (invoiceAge < 21 ) buckets["14-21 days"] += invoice.openAmount;
			else if (invoiceAge < 28 ) buckets["21-28 days"] += invoice.openAmount;
					else buckets["Over 28"] += invoice.openAmount;
	
					totalPastDue += invoice.openAmount;
				} catch (invoiceEx) {
					console.error(`Error processing invoice ${invoice._id}:`, invoiceEx);
				}
			});
	
			const result = {
				buckets: _.map(buckets, (v, k) => ({ label: k, amount: v })),
				totalPastDue
			};
	
			Log.info(`Processed past due summary:`, result);
			return result;
		} catch (ex) {
			Log.error(`Error in pastDueSummary:`, ex);
		}
	}
	
	static async payersDueSummary(options) {
		try {
			Log.info(`payersDueSummary called with options:`, options);
			const currentUser =  await Meteor.userAsync();
			Log.info(`Fetched current user:`, currentUser);
			const currentOrg = await Orgs.current();
			Log.info(`Fetched current organization:`, currentOrg);
			await processPermissions({
				assertions: [{ context: "billing/payments", action: "read" }],
				evaluator: (person) => person.type == "admin",
				throwError: true
			});
			Log.info(`Permissions verified successfully`);
	
			const buckets = {};
			const invoices = await Invoices.find({
				orgId: currentOrg._id,
				"lineItems": {
					"$elemMatch": {
						"appliedDiscounts": {"$elemMatch": { "voidedAt": {$exists: false}, "type": {$in: ["reimbursable", "reimbursable-with-copay"]}}}
					}
				},
				voidedAt: {$exists: false}
			}).fetchAsync();
	
			Log.info(`Fetched invoices count:`, invoices.length);
			const currentTime = moment();
	
			invoices.forEach(invoice => {
				try {
					const invoiceAge = currentTime.diff(moment(invoice.invoiceDate, "MM/DD/YYYY"), "days");
					_.each(invoice.openPayerAmounts, (amount, payer) => {
						if (invoice.openAmountForPayer(payer) > 0) {
							if (!buckets[payer]) buckets[payer] = {};
							const rangeLabel = invoiceAge < 30 ? "under30" : "over30";
							if (!buckets[payer][rangeLabel]) buckets[payer][rangeLabel] = 0.0;
							buckets[payer][rangeLabel] += invoice.openAmountForPayer(payer);
						}
					});
				} catch (invoiceEx) {
					console.error(`Error processing invoice ${invoice._id}:`, invoiceEx);
				}
			});
	
			Log.info(`Processed payersDueSummary:`, { buckets });
			return { buckets };
		} catch (ex) {
			Log.error(`Error in payersDueSummary:`, ex);
			return { buckets: {} };
		}
	}
	
	static async inProgressPaymentsAndRefunds(options) {
		try {
			Log.info(`inProgressPaymentsAndRefunds called with options:`, options);
	
			await processPermissions({
				assertions: [{ context: "billing/payments", action: "read" }],
				evaluator: (person) => person.type=="admin",
				throwError: true
			});
			Log.info(`Permissions verified successfully`);
	
			options = options || {};
			options.orgQuery = await this.getOrgQuery(options.orgIds);
			Log.info(`Computed organization query:`, options.orgQuery);
	
			if (options.precompute) {
				Log.info(`Fetching precomputed metric for orgId:`, options.orgQuery);
				const metric = await ComputedMetrics.findOneAsync({orgId: options.orgQuery});
				Log.info(`Fetched metric:`, metric);
				return metric?.inProgressPaymentsAndRefunds;
			} else {
				Log.info(`Calculating inProgressPaymentsAndRefunds dynamically`);
				return await this.calculateInProgressPaymentsAndRefunds(options);
			}
		} catch (ex) {
			Log.error(`Error in inProgressPaymentsAndRefunds:`, ex);
		}
	}

	/**
	 * Computes the in-progress payments and refunds for a specific organization.
	 * Aggregates data to determine payments and refunds that are currently in progress.
	 *
	 * @param {string} orgId - The ID of the organization to compute the data for.
	 */
	static async precomputeInProgressPaymentsAndRefunds(orgId) {
		if (!orgId) {
			Log.info(`Skipping precomputeInProgressPaymentsAndRefunds: Missing orgId`);
			return;
		}
	
		Log.info(`Computing in-progress payments and refunds for orgId: ${orgId}`);
	
		try {
			const inProgressPaymentsAndRefunds = await this.calculateInProgressPaymentsAndRefunds({ orgQuery: orgId });
			if (!inProgressPaymentsAndRefunds) {
				Log.warn(`No data found for in-progress payments and refunds for orgId: ${orgId}`);
			}
			await ComputedMetrics.updateAsync({ orgId }, { $set: { inProgressPaymentsAndRefunds } });
	
			Log.info(`Successfully updated in-progress payments and refunds for orgId: ${orgId}`);
		} catch (error) {
			Log.error(`Error computing in-progress payments and refunds: ${error} for orgId: ${orgId}`);
		}
	}
	
	static async calculateInProgressPaymentsAndRefunds(options) {
		Log.info(`Starting calculateInProgressPaymentsAndRefunds with options: ${options}`);
	
		options = options || {};
		const dataCache = {};
	
		try {
			const startDate = (options.date ? new moment(options.date, "MM/DD/YYYY") : new moment()).endOf("day");
			Log.info(`Calculated startDate: ${startDate} for org ${options}`);
	
			const orgQuery = options.orgQuery;
			Log.info(`Org Query: ${orgQuery} for org ${options}`);
			const refundsInProgress = [];
	
			await Invoices.find({ 
				orgId: orgQuery, 
				"credits": {
					"$elemMatch": {
						"type": "refund", 
						"createdAt": { "$lt": startDate.valueOf() },
						"$or": [
							{"refundPended": true, "refundConfirmed": {"$ne": true}},
							{"refundConfirmedAt": {"$gte": startDate.valueOf()}}
						], 
						"voidedAt": {"$eq": null}
					}
				} 
			}).forEachAsync(invoice => {
				invoice.credits.filter(credit => credit.type === "refund" &&
					credit.createdAt < startDate.valueOf() &&
					((credit.refundPended && !credit.refundConfirmed) || credit.refundConfirmedAt >= startDate.valueOf()) &&
					!credit.voidedAt
				).forEach(credit => {
					Log.info(`Processing refund: ${credit} for org ${options}`);
					refundsInProgress.push({
						...credit,
						invoiceId: invoice._id,
						invoiceNumber: invoice.invoiceNumber
					});
				});
			});
			Log.info(`Total refunds in progress: ${refundsInProgress.length} for org ${options}`);
	
			const anOrg = await Orgs.findOneAsync({_id: orgQuery});
			Log.info(`Fetched organization: ${anOrg ? anOrg.name : "Not found"}`);
			const paymentProvider = await CardProviders.get(anOrg.billingCardProviderName(), orgQuery);
			Log.info(`Fetched payment provider: ${paymentProvider ? anOrg.billingCardProviderName() : "Not found"}`);
			const paymentsInProgress = [];
			const dateRange = {
				"$lt": startDate.valueOf(), 
				"$gte": startDate.clone().add(-14, "days").valueOf()
			};
			Log.info(`Date range for payments: ${dateRange} for org ${options}`);
			const invoiceQuery = {
				orgId: orgQuery,
				"credits": {
					"$elemMatch": {
						"type": "payment",
						"payment_type": {"$in": ["bank_account", "card"]},
						"createdAt": dateRange
					}
				}
			};
			Log.info(`Fetching invoices for payments... for org ${options}`);
			await Invoices.find(invoiceQuery).forEachAsync(async invoice => {
				const invoiceData = invoice.credits.filter(c => 
					c.type === "payment" &&
					["bank_account", "card"].includes(c.payment_type) &&
					c.createdAt >= dateRange["$gte"] && c.createdAt < dateRange["$lt"]
				);
	
				for (const credit of invoiceData) {
					Log.info(`Processing payment: ${credit}`);
					const payoutInfo = paymentProvider && paymentProvider.findPayoutForPayment && await paymentProvider.findPayoutForPayment({invoiceId: invoice._id, payment: credit, dataCache});
					const payoutDate = payoutInfo && new moment(payoutInfo.payoutDate).valueOf();
	
					if (!payoutInfo || (payoutDate && payoutDate > dateRange["$lt"])) {
						paymentsInProgress.push({
							...credit,
							invoiceId: invoice._id,
							invoiceNumber: invoice.invoiceNumber
						});
					}
				}
			});
	
			Log.info(`Total payments in progress: ${paymentsInProgress.length} for org ${options}`);
			const result = {
				refundsInProgress,
				refundsInProgressTotal: _.reduce(refundsInProgress, (memo, rf) => rf.amount + memo, 0),
				paymentsInProgress,
				paymentsInProgressTotal: _.reduce(paymentsInProgress, (memo, rf) => rf.amount + memo, 0),
				createdAt: new Date().valueOf()
			};
	
			Log.info(`Final calculated result: ${result} for org ${options}`);
			return result;
	
		} catch (error) {
			Log.error(`Error in calculateInProgressPaymentsAndRefunds: ${error} for org ${options}`);
		}
	}
	
	
	static async revenuePerformance(options) {
		await processPermissions({
			assertions: [{ context: "billing/payments", action: "read" }],
			evaluator: (person) => person.type=="admin",
			throwError: true
		});
		const boundaryType = _.deep(options, "timespan") || "months";
		const orgQuery = await this.getOrgQuery(options.orgIds);
		const metric = await ComputedMetrics.findOneAsync({orgId: orgQuery});

		return metric?.revenuePerformance?.[boundaryType];
	}

	/**
	 * Computes the revenue performance for a specific organization over different time intervals.
	 * Aggregates data from the `Invoices` collection, excluding voided invoices, to calculate
	 * total invoiced amounts grouped by weeks, months, and years.
	 *
	 * @param {string} orgId - The ID of the organization to compute the data for.
	 */
	static async precomputeRevenuePerformance(orgId) {
		if (!orgId) {
			Log.warn(`Skipping precomputeRevenuePerformance: Missing orgId`);
			return;
		}
	
		Log.info(`Computing revenue performance for orgId: ${orgId}`);
	
		try {
			const boundaryTypes = ["months", "years", "weeks"];
			const revenuePerformance = {};
	
			for (const type of boundaryTypes) {
				try {
					Log.info(`Processing revenue performance for boundary type: ${type}`);
					const boundaries = [];
					const today = new moment().startOf('day');
	
					for (let i = 4; i >= 0; i--) {
						boundaries.push(today.clone().add(-1 * i, type).valueOf());
					}
	
					const earliestDate = _.min(boundaries);
					Log.info(`Earliest date for ${type}: ${earliestDate}`);
					const totalInvoicedData = await (await Invoices.aggregate(
						[
						{$match: {"voidedAt": {"$exists": false}, "createdAt": {"$gte": earliestDate}, "orgId": orgId}},
						{
							$facet: {
								"summaryOverTime": [
									{
										"$bucket": {
											"groupBy": "$createdAt",
											"boundaries": boundaries,
											"default": "Other",
											"output": {
											"sum": {"$sum": "$originalAmount"}
											}
										}
									}
								]
							}
						}
					])).toArray();
					boundaries.pop();
					totalInvoicedData[0]["boundaries"] = boundaries;
					revenuePerformance[type] = totalInvoicedData?.[0];
	
					Log.info(`Revenue performance computed for ${type}`, totalInvoicedData?.[0]);
				} catch (error) {
					Log.error(`Error processing revenue performance for boundary type: ${type}`, error);
				}
			}
	
			await ComputedMetrics.updateAsync({ orgId }, { $set: { revenuePerformance } });
	
			Log.info(`Successfully updated revenue performance for orgId: ${orgId}`);
		} catch (error) {
			Log.error(`Error computing revenue performance for orgId: ${orgId}`, error);
		}
	}

	/**
	 * Generates revenue performance data based on specified options.
	 * @param {Object} options - The options for generating revenue performance.
	 * @param {string} options.timespan - The timespan for data aggregation (e.g., 'months', 'years').
	 * @param {Array<string>} options.orgIds - List of organization IDs to filter data.
	 * @param {string} options.startDate - The start date for the report.
	 * @param {string} options.endDate - The end date for the report.
	 * @param {string} options.revenueType - Revenue type (e.g., 'Family', 'Payer').
	 * @param {string} options.revenueCalculation - Type of revenue calculation ('gross' or 'net').
	 * @param {string} options.dateType - Type of date for filtering ('create' or 'periodStartDate').
	 * @param {string} options.timezone - The timezone of the logged-in org for the date boundaries.
	 * @returns {Promise<Object>} The generated revenue performance data.
	 */
	static async revenueExplorer(options) {
		const boundaryType = options.timespan || "months";
		const orgQuery = await this.getOrgQuery(options.orgIds);

		const boundaries = this.generateBoundaries(
			options.startDate,
			options.endDate,
			boundaryType,
			options.timezone
		);

		const bucketOutput = this.getBucketOutput(options);

		const invoiceQuery = this.buildInvoiceQuery(
			options,
			boundaries,
			orgQuery,
			bucketOutput,
			options.timezone
		);

		return await this.calculateRevenuePerformance(
			invoiceQuery,
			boundaries
		);
	}


	/**
	 * Generates boundaries for data aggregation based on start and end dates.
	 * @param {string} startDate - The start date for generating boundaries.
	 * @param {string} endDate - The end date for generating boundaries.
	 * @param {string} boundaryType - The type of boundary (e.g., 'months', 'years').
	 * @param {string} timezone - The timezone for the date boundaries.
	 * @returns {Array<number>} Array of timestamps representing the boundaries.
	 */
	static generateBoundaries(startDate, endDate, boundaryType, timezone = 'America/New_York') {
		const boundaries = [];
		const endDateMoment = new moment.tz(endDate, "YYYY-MM-DD", timezone).endOf("day");
		const startDateMoment = new moment.tz(startDate, "YYYY-MM-DD", timezone).startOf("day");
		const periodEndType = this.getPeriodEndType(boundaryType);

		let numBuckets = endDateMoment.diff(startDateMoment, boundaryType);

		if (boundaryType === "years" && numBuckets === 0 && startDateMoment.year() !== endDateMoment.year()) {
			numBuckets = 1;
		}

		boundaries.push(startDateMoment.valueOf());
		for (let i = 1; i <= numBuckets; i++) {
			const currentPeriod = startDateMoment.clone().add(i, boundaryType);
			boundaries.push(currentPeriod.clone().startOf(periodEndType).valueOf());
		}
		boundaries.push(endDateMoment.valueOf());

		return boundaries;
	}

	/**
	 * Creates the bucket output configuration based on revenue type and calculation.
	 * @param {Object} options - The options for generating revenue performance.
	 * @param {string} options.revenueType - Revenue type (e.g., 'Family', 'Payer').
	 * @param {string} options.revenueCalculation - Type of revenue calculation ('gross' or 'net').
	 * @returns {Object} The bucket output configuration.
	 */
	static getBucketOutput(options) {
		const bucketOutput = {};

		if (options.revenueType === "Family") {
			bucketOutput.sum = { "$sum": "$lineItemFamilyAmount" };
		} else if (options.revenueType === "Payer") {
			bucketOutput.sum = { "$sum": "$lineItemPayerAmount" };
		} else {
			bucketOutput.family = { "$sum": "$lineItemFamilyAmount" };
			bucketOutput.payer = { "$sum": "$lineItemPayerAmount" };
			bucketOutput.sum = { "$sum": { "$add": ["$lineItemFamilyAmount", "$lineItemPayerAmount"] } };
		}

		if (options.revenueCalculation === "gross") {
			bucketOutput.discount = { "$sum": "$lineItemDiscountAmount" };
			bucketOutput.net = { "$sum": { "$subtract": [{ "$add": ["$lineItemFamilyAmount", "$lineItemPayerAmount"] }, "$lineItemDiscountAmount"] } };
		}

		return bucketOutput;
	}

	/**
	 * Builds the MongoDB aggregation query based on options and boundaries.
	 * @param {Object} options - The options for generating revenue performance.
	 * @param {Array<number>} boundaries - The boundaries for the report.
	 * @param {Object} orgQuery - The organization query for filtering.
	 * @param {Object} bucketOutput - The output configuration for the aggregation.
	 * @param {string} timezone - The timezone for the date boundaries.
	 * @returns {Array<Object>} The constructed aggregation query.
	 */
	static buildInvoiceQuery(options, boundaries, orgQuery, bucketOutput, timezone) {
		const startDateMoment = new moment.tz(options.startDate, "YYYY-MM-DD", timezone).startOf("day");
		const endDateMoment = new moment.tz(options.endDate, "YYYY-MM-DD", timezone).endOf("day");

		const invoiceQuery = [
			{
				$match: {
					"voidedAt": { "$exists": false },
					"orgId": orgQuery,
					"$or": [{ "createdAt": { "$gte": startDateMoment.valueOf(), "$lt": endDateMoment.valueOf() } }]
				}
			},
			{ $unwind: "$lineItems" },
			{ $addFields: this.getAddFields(options) }
		];

		if (options.dateType !== "create") {
			invoiceQuery[0]["$match"]["$or"].push({
				"lineItems.periodStartDate": { "$gte": startDateMoment.valueOf(), "$lt": endDateMoment.valueOf() }
			});

			invoiceQuery.push({
				$match: {
					"lineItemPostingDate": {
						"$gte": startDateMoment.valueOf(),
						"$lt": endDateMoment.valueOf()
					}
				}
			});
		}

		invoiceQuery.push(
			this.getFamilyAmountAddFields(options.revenueCalculation),
			{
				$facet: {
					"summaryOverTime": [
						{
							"$bucket": {
								"groupBy": "$lineItemPostingDate",
								"boundaries": boundaries,
								"default": "Other",
								"output": bucketOutput // Use bucketOutput here
							}
						}
					]
				}
			}
		);

		return invoiceQuery;
	}

	/**
	 * Returns addFields for lineItemPayerAmount and lineItemDiscountAmount based on conditions.
	 * @param {Object} options - The options for generating revenue performance.
	 * @returns {Object} The addFields configuration for lineItemPayerAmount and lineItemDiscountAmount.
	 */
	static getAddFields(options) {
		return {
			lineItemPayerAmount: {
				"$ifNull": [
					{
						"$reduce": {
							"input": { "$filter": { "input": "$lineItems.appliedDiscounts", "cond": { "$eq": ["$$this.type", "reimbursable"] } } },
							"initialValue": 0,
							"in": { "$add": ["$$value", "$$this.amount"] }
						}
					},
					0
				]
			},
			lineItemDiscountAmount: {
				"$cond": {
					"if": { "$eq": ["$lineItems.type", "item"] },
					"then": 0,
					"else": {
						"$ifNull": [
							{
								"$reduce": {
									"input": { "$filter": { "input": "$lineItems.appliedDiscounts", "cond": { "$eq": ["$$this.type", "discount"] } } },
									"initialValue": 0,
									"in": { "$add": ["$$value", "$$this.amount"] }
								}
							},
							0
						]
					}
				}
			},
			lineItemPostingDate: options.dateType === "create" ? "$createdAt" : { "$ifNull": ["$lineItems.periodStartDate", "$createdAt"] }
		};
	}

	/**
	 * Generates additional fields for family amount calculations.
	 * @param {string} revenueCalculation - Type of revenue calculation ('gross' or 'net').
	 * @returns {Object} The addFields configuration for lineItemFamilyAmount.
	 */
	static getFamilyAmountAddFields(revenueCalculation) {
		const addFields = ["$lineItemPayerAmount"];
		if (revenueCalculation !== "gross") {
			addFields.push("$lineItemDiscountAmount");
		}

		return {
			$addFields: {
				"lineItemFamilyAmount": {
					"$subtract": [
						"$lineItems.amount",
						{ "$add": addFields }
					]
				}
			}
		};
	}

	/**
	 * Executes the aggregation query to calculate revenue performance.
	 * @param {Array<Object>} invoiceQuery - The constructed MongoDB aggregation query.
	 * @param {Array<number>} boundaries - Array of boundary timestamps.
	 * @returns {Promise<Object>} The calculated revenue performance data.
	 */
	static async calculateRevenuePerformance(invoiceQuery, boundaries) {
		const totalInvoicedData = await (await Invoices.aggregate(invoiceQuery)).toArray();
		const boundariesWithoutEndDate = boundaries.slice(0, -1);

		return {
			summaryOverTime: totalInvoicedData?.[0]?.["summaryOverTime"],
			boundaries: boundariesWithoutEndDate
		};
	}

	/**
	 * Maps a boundary type to the appropriate period end type.
	 * @param {string} boundaryType - The type of boundary (e.g., 'months', 'years').
	 * @returns {string} The corresponding period end type (e.g., 'month', 'year').
	 */
	static getPeriodEndType(boundaryType) {
		return (boundaryType === "years" && "year") ||
			(boundaryType === "months" && "month") ||
			(boundaryType === "weeks" && "week") ||
			"month";
	}

	static async mediaRequirements(options) {
		const currentUser = await Meteor.userAsync();
		const currentPerson = currentUser && await People.findOneAsync({_id: currentUser.personId});
		if (!currentPerson || currentPerson.type != "admin")
			return;

		const currentOrg = await Orgs.current(),
			allRuleGroups = await Groups.find({orgId: currentOrg._id, mediaRequirement:{"$ne":null}}).fetchAsync(),
			maxGroupMediaDays = _.max(_.map(allRuleGroups, g => g.mediaRequirement.mediaDays)),
			maxGroupAbsenceDays = _.max(_.map(allRuleGroups, g => g.mediaRequirement.mediaAbsenceDays)),
			orgMediaDays = currentOrg?.mediaRequirement?.mediaDays,
			orgAbsenceDays = currentOrg?.mediaRequirement?.mediaAbsenceDays,
			mediaDays = (orgMediaDays && orgMediaDays > maxGroupMediaDays) ? orgMediaDays : maxGroupMediaDays,
			absenceDays = (orgAbsenceDays && orgAbsenceDays > maxGroupAbsenceDays) ? orgAbsenceDays : maxGroupAbsenceDays;
		
		if (!mediaDays || !absenceDays)
			return;

		const absenceDayCheckInStamp = new moment().add( -1 * absenceDays, "days").valueOf();
		const activePeople = await People.find({orgId: currentOrg._id, type: "person", inActive:{"$ne":true}, checkedInOutTime:{"$gte": absenceDayCheckInStamp}}, {fields:{_id:1, firstName:1, lastName:1, defaultGroupId:1, checkedInOutTime: 1}}).fetchAsync();
		
		const mediaMomentSortStamp = new moment().add(-1 * mediaDays, "days").valueOf();
		const mediaMoments = await Moments.find({
			orgId: currentOrg._id, 
			sortStamp:{"$gte":mediaMomentSortStamp}, 
			mediaFiles:{"$ne":null}, 
			taggedPeople:{"$in": activePeople.map(p => p._id)},
		}, {fields: {
			taggedPeople: 1,
			mediaFiles: 1,
			sortStamp: 1
		}}).fetchAsync();
		
		let mediaPeople = [];
		_.each(activePeople, person => {
			const apGroup = person.defaultGroupId && allRuleGroups.find( g => g._id == person.defaultGroupId);
			let apMediaDays, apAbsenceDays, apMediaNumber;
			if (apGroup) {
				apMediaDays = apGroup.mediaRequirement.mediaDays;
				apAbsenceDays = apGroup.mediaRequirement.mediaAbsenceDays;
				apMediaNumber = apGroup.mediaRequirement.mediaNumber;
			} else if (currentOrg.mediaRequirement) {
				apMediaDays = currentOrg.mediaRequirement.mediaDays;
				apAbsenceDays = currentOrg.mediaRequirement.mediaAbsenceDays;
				apMediaNumber = currentOrg.mediaRequirement.mediaNumber;
			} else {
				return;
			}
			const apAbsenceCheckInStamp = new moment().add( -1 * apAbsenceDays, "days").valueOf();
			if (person.checkedInOutTime > apAbsenceCheckInStamp) {
				const mediaNumber = _.reduce( mediaMoments, (memo, mm) => {
					if (_.include(mm.taggedPeople,person._id))
						return memo + mm.mediaFiles.length;
					else
						return memo;
				}, 0);
				if (mediaNumber < apMediaNumber) {
					mediaPeople.push({
						name: person.firstName + " " + person.lastName,
						_id: person._id,
						mediaNumber,
						requiredMediaNumber: apMediaNumber,
						deficit: apMediaNumber - mediaNumber
					});
				}
			}
		});
		return _.sortBy(mediaPeople, mp => -1 * mp.deficit);
	}

	static async getOrgQuery (orgIds) {
		const currentUser = await Meteor.userAsync();
		const currentPerson = currentUser && await currentUser.fetchPerson();

		if (!currentPerson){
			return;
		}

		if (orgIds && orgIds.length > 0 && (currentPerson.masterAdmin || currentPerson.superAdmin) ) {
			const orgsScope = await currentPerson.findScopedOrgs();
			const orgsScopeList = orgsScope && _.pluck(orgsScope, "_id");
			const scopedOrgIds = _.intersection(orgsScopeList, orgIds);
			return {"$in": scopedOrgIds};
		} else {
			return currentUser.orgId;
		}
	}
}
