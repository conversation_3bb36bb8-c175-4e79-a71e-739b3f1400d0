import { Meteor } from 'meteor/meteor';
import { AvailableCustomizations } from '../../lib/customizations';
import { cloneDeep } from 'lodash';
import moment from "moment-timezone";
import { BillingInvoiceService } from '../billingInvoiceService';
import { BillingUtils } from '../../lib/util/billingUtils';
import { RegistrationUtils } from '../../lib/util/registrationUtils';
import {
    ITEM_TYPE, PLAN_TYPE, PUNCH_CARD_TYPE,
    SCALED_PLAN_FREQUENCIES
} from '../../lib/constants/billingConstants';
import { EnrollmentsService } from '../enrollments/enrollmentsService';
import { People } from '../../lib/collections/people';
import { Orgs } from '../../lib/collections/orgs';
import { OnHoldRegistrations } from '../../lib/collections/onHoldRegistrations';
import { Relationships } from '../../lib/collections/relationships';
import { Reservations } from '../../lib/collections/reservations';
import _ from '../../lib/util/underscore'

/**
 * Handles logic for the registration flow.
 */
export class RegistrationService {
    /**
     * Build the available selective week plans that a given child can register for.
     *
     * @param org
     * @param childId
     * @returns {*}
     */
    static async buildAvailableSelectiveWeekPlansForChild(org, childId) {
        const child = await People.findOneAsync({ _id: childId });
        if (!child || !org) {
            throw new Error('Invalid childId ID');
        }
        const prefix = org.profileDataPrefix();
        const plans = org.availableBillingPlans(true, false, true, true);
        const currentChildGrade = (prefix ? child[prefix]?.studentGrade : child.studentGrade) ?? null;
        const timezone = org.getTimezone();
        const currentDate = moment.tz(timezone).startOf('day').valueOf();

        const filteredPlansWithDetails = plans.filter(plan => {
            if (plan.details && plan.details.regEndDate) {
                const currentDateFormatted = moment(currentDate).startOf('day').format('YYYY-MM-DD');
                const regEndDateFormatted = moment(plan.details.regEndDate).startOf('day').format('YYYY-MM-DD');
                if (!(plan.details.regEndDate > currentDate || currentDateFormatted === regEndDateFormatted)) {
                    return false;
                }
            }
            if (plan.details && plan.details.regStartDate) {
                const currentDateFormatted = moment(currentDate).startOf('day').format('YYYY-MM-DD');
                const regStartDateFormatted = moment(plan.details.regStartDate).startOf('day').format('YYYY-MM-DD');
                return plan.details.regStartDate < currentDate || currentDateFormatted === regStartDateFormatted;
            }
            return true;
        });

        const filteredPlansWithGrades = filteredPlansWithDetails.filter(plan => {
            if (plan.details && plan.details.grades?.length && currentChildGrade) {
                return plan.details.grades.includes(currentChildGrade);
            }
            return true;
        });

        const availablePlans = await EnrollmentsService.getItemAndSelectiveWeekAvailabilities(org._id, filteredPlansWithGrades);
        const onHoldPlans = [];
        const onHoldRegistrations = await OnHoldRegistrations.find(
            {
                orgId: org._id,
                actionedDate: { $exists: false },
                isApproved: { $eq: false },
                isDenied: { $eq: false },
                "data.children": {
                    $elemMatch: { _id: childId }
                }
            },
            {
                sort: { addedStamp: 1 },
                readPreference: "secondaryPreferred"
            }
        ).fetchAsync();
        for (const registration of onHoldRegistrations) {
            const childIndex = registration.data.children.findIndex(c => c._id === childId);
            onHoldPlans.push(...registration.data.plans[childIndex]);
        }

        const todayStamp = moment().tz(timezone).startOf("day").valueOf();
        const enrolledPlans = await child.enabledBillingPlans(todayStamp, false, org) ?? [];
        const enrolledPlanIds = enrolledPlans.map(plan => plan._id) ?? [];
        const onHoldPlanIds = onHoldPlans.map(plan => plan._id) ?? [];

        const filteredAvailablePlans = availablePlans.filter(plan => {
            const isSelectiveWeekPlan = plan.details?.selectiveWeeks?.length > 0;
            let availableUnmatchedWeeks = [];

            if (isSelectiveWeekPlan) {
                const relevantPlans = onHoldPlanIds.length ? onHoldPlans : enrolledPlans;
                availableUnmatchedWeeks = RegistrationUtils.getAvailableSelectiveWeeks(plan, relevantPlans, timezone);
            }

            const isEnrolled = enrolledPlanIds.includes(plan._id);
            const isOnHold = onHoldPlanIds.includes(plan._id);
            const planIsAtCapacity = isSelectiveWeekPlan && plan.details.selectiveWeeks.every(week => week[2]);

            return isSelectiveWeekPlan && (
                (!isEnrolled && !planIsAtCapacity) ||
                availableUnmatchedWeeks.length > 0
            ) && (
                !isOnHold ||
                availableUnmatchedWeeks.length > 0
            );
        });

        return filteredAvailablePlans;
    }

    /**
     * Build the registration data object for the registration flow.
     *
     * @param orgId
     * @param personId
     * @param contactsOnly - If true, only contacts will be returned.
     * @returns {{children: *, plans: *, contacts: *}}
     */
    static async buildRegistrationDataFromFamily(orgId, personId, contactsOnly) {
        const org = await Orgs.findOneAsync({ _id: orgId });
        if (!org || !org.hasCustomization(AvailableCustomizations.REGISTRATION_FLOW)) {
            throw new Meteor.Error('403', 'You are not allowed to proceed.');
        }

        const person = await People.findOneAsync({ _id: personId, orgId: orgId });
        if (!person || person.type !== 'family') {
            throw new Meteor.Error('400', 'Invalid personId parameter.');
        }

        const childIds = [];
        const relationships = await Relationships.find({ orgId: orgId, personId: personId }).fetchAsync();
        let allFamilyIds = [];
        for (const relationship of relationships) {
            childIds.push(relationship.targetId);
            // Find all relationships to this child and add them in
            const childRelationships = await Relationships.find({ orgId: orgId, targetId: relationship.targetId }).fetchAsync();
            allFamilyIds = allFamilyIds.concat(_.pluck(childRelationships, 'personId'));
        }
        const children = await People.find({ _id: { $in: childIds }, inActive: { $ne: true } }).fetchAsync();
        const contacts = await People.find({ _id: { $in: allFamilyIds }, inActive: { $ne: true } }).fetchAsync();

        // const flattenedContacts = [].concat.apply([], _.map(contacts, contact => await RegistrationService.buildRegistrationDataForContact(contact, children, org)))
        const flattenedContacts = [];
        for (const contact of contacts) {
            const registrationData = await RegistrationService.buildRegistrationDataForContact(contact, children, org);
            flattenedContacts.push(...registrationData);
        }

        if(contactsOnly) {
            const flattenedContacts = [];
            for (const contact of contacts) {
                const registrationData = await RegistrationService.buildRegistrationDataForContact(contact, children, org);
                flattenedContacts.push(...registrationData);
            }

            return {
                contacts: flattenedContacts,           
            }
        }
        return {
            children: await Promise.all(_.map(children, async (child) => await RegistrationService.buildRegistrationDataForChild(child, org))),
            contacts: flattenedContacts,
            plans: await Promise.all(_.map(children, async (child) => await RegistrationService.buildRegistrationDataForPlans(child, org))),
        }
    }

    /**
     * Build the child's registration data.
     *
     * @param person
     * @param org
     *
     * @returns {{
     *     _id: string,
     *     firstName: string,
     *     lastName: string,
     *     birthday: "timestamp",
     *     studentGrade: string,
     *     gender: string
     * }}
     * Other profile fields can be returned as well.
     */
    static async buildRegistrationDataForChild(person, org) {
        if (person.type !== 'person') {
            throw new Meteor.Error('400', 'Invalid person parameter.');
        }
        const childData = {
            _id: person._id,
            firstName: person.firstName ?? '',
            lastName: person.lastName ?? '',
            birthday: await person.getProfileFieldValue('birthday') ?? '',
            studentGrade: await person.getProfileFieldValue('studentGrade') ?? '',
            gender: await person.getProfileFieldValue('gender') ?? ''
        };
        const questions = org.getFamilyRegistrationSettings().questions ?? [];
        for (const question of questions) {
            if ('mappedTo' in question) {
                childData[question.mappedTo] = await person.getProfileFieldValue(question.mappedTo);
            }
        }
        return childData;
    }

    /**
     * Build the contact's registration data.
     *
     * @param person
     * @param children
     * @param org
     *
     * @returns {{
     *     _id: string,
     *     firstName: string,
     *     lastName: string,
     *     relationshipDescription: string,
     *     phonePrimary: string,
     *     phoneAlt: string,
     *     profileEmailAddress: string,
     *     address: string,
     *     city: string,
     *     state: string,
     *     zip: string,
     *     primaryCaregiver: "Yes|No",
     *     authorizedPickup: "Yes|No",
     *     emergencyContact: "Yes|No",
     *     copyToChildren: "Yes|No",
     *     childIndex: number
     * }}
     */
    static async buildRegistrationDataForContact(person, children, org) {
        if (person.type !== 'family') {
            throw new Meteor.Error('400', 'Invalid person parameter.');
        }
        for (const child of children) {
            if (child.type !== 'person') {
                throw new Meteor.Error('400', 'Invalid person parameter.');
            }
        }

        const newContacts = []
        for(const [index, child] of children.entries()){
            const contactData = {
                _id: person._id,
                firstName: person.firstName,
                lastName: person.lastName,
                relationshipDescription: 'N/A',
                profileEmailAddress: person.profileEmailAddress,
                designations: person.designations || [],
                primaryCaregiver: 'No',
                authorizedPickup: 'No',
                emergencyContact: 'No',
                copyToChildren: 'No',
                childIndex: index,
            };

            const prefix = org.profileDataPrefix();
            if (prefix) {
                contactData.phonePrimary = person[prefix]?.phonePrimary;
                contactData.phoneAlt = person[prefix]?.phoneAlt;
                contactData.address = person[prefix]?.householdInformation?.parentStreetAddress;
                contactData.city = person[prefix]?.householdInformation?.parentCity;
                contactData.state = person[prefix]?.householdInformation?.parentState;
                contactData.zip = person[prefix]?.householdInformation?.parentZip;
            } else {
                contactData.phonePrimary = person.phonePrimary;
                contactData.phoneAlt = person.phoneAlt;
                contactData.address = person.householdInformation?.parentStreetAddress;
                contactData.city = person.householdInformation?.parentCity;
                contactData.state = person.householdInformation?.parentState;
                contactData.zip = person.householdInformation?.parentZip;
            }

            const relationshipsWithParent = await Relationships.find({ orgId: org._id, personId: person._id, targetId: child._id }).fetchAsync();
            const foundChildIdsSet = new Set();
            for (const relationship of relationshipsWithParent) {
                if (relationship.relationshipType === 'family') {
                    contactData.primaryCaregiver = 'Yes';
                } else if (relationship.relationshipType === 'authorizedPickup') {
                    contactData.authorizedPickup = 'Yes';
                } else if (relationship.relationshipType === 'emergencyContact') {
                    contactData.emergencyContact = 'Yes';
                }

                if (relationship.relationshipDescription && relationship.relationshipDescription !== 'N/A') {
                    contactData.relationshipDescription = relationship.relationshipDescription;
                }

                foundChildIdsSet.add(relationship.targetId);
            }

            if (foundChildIdsSet.size > 1 || children.length === 1) {
                contactData.copyToChildren = 'Yes';
            }
            newContacts.push(contactData)
        }

        return newContacts;
    }

    /**
     * Build the plans for the registration data.
     *
     * @param person The child to build out the plans data for.
     *
     * @param org
     * @returns {[{
     *     _id: string,
     *     description: string,
     *     frequency: string,
     *     program: string,
     *     scaledAmounts: [
     *         number?,
     *         number?,
     *         number?,
     *         number?,
     *         number?
     *     ],
     *     amount: number,
     *     isRequiredAdvanceNotice: true,
     *     selectedDays: [
     *         "monday"?,
     *         "tuesday"?,
     *         "wednesday"?,
     *         "thursday"?,
     *         "friday"?
     *     ],
     *     bundlePlanId?: string,
     *     startDate: string,
     *     endDate: string,
     *     planTotal: number,
     *     reservationId?: string,
     *     allocations?: Array<Record<string, any>>,
     *     regularPrice?: number,
     *     bundlePlanPrice?: number
     * }]}
     */
    static async buildRegistrationDataForPlans(person, org) {
        if (person.type !== 'person') {
            throw new Meteor.Error('400', 'Invalid person parameter.');
        }

        const todayDate = new moment().startOf('day').valueOf();
        const plans = await person.enabledBillingPlans(todayDate, false, org);
        const plansData = [];
        for (const plan of plans) {
            let reservation = null;
            if (!plan.planDetails.program) {
                continue;
            }
            if (!plan.reservationId) {
                plan.selectedDays = RegistrationUtils.setDefaultSelectedDaysByPlanConfig(plan);
            } else {
                reservation = await Reservations.findOneAsync({ _id: plan.reservationId });
                if (!reservation) {
                    plan.selectedDays = RegistrationUtils.setDefaultSelectedDaysByPlanConfig(plan);
                    delete plan.reservationId;
                } else {
                    plan.selectedDays = _.map(reservation.recurringDays, day => formatSelectedDays(day, true));
                    if (!plan.selectedDays.length) {
                        plan.selectedDays = RegistrationUtils.setDefaultSelectedDaysByPlanConfig(plan);
                    }
                }
            }
            const {calculatedAmount, planDetails, enrollmentDateFormatted, expirationDateFormatted, bundlePlanId} = plan;

            const {
                type,
                details,
                scaledAmounts,
                description,
                isRequiredAdvanceNotice,
                program,
                frequency
            } = planDetails;

            plan.type = type;
            plan.details = details;
            plan.amount = calculatedAmount;
            plan.scaledAmounts = scaledAmounts;
            plan.description = description;
            plan.isRequiredAdvanceNotice = isRequiredAdvanceNotice;
            plan.startDate = enrollmentDateFormatted;
            plan.endDate = expirationDateFormatted;
            plan.planTotal = calculatedAmount;
            plan.program = program;
            plan.frequency = frequency;

            if (bundlePlanId) {
                const bundle = RegistrationUtils.getBundle(bundlePlanId, org);
                const otherPlanInTheBundle = RegistrationUtils.getSecondPlan(person, bundle, plan);
                if (otherPlanInTheBundle) {
                    if (otherPlanInTheBundle.planDetails.scaledAmounts && otherPlanInTheBundle.planDetails.scaledAmounts.length && otherPlanInTheBundle.reservationId) {
                        const r = await Reservations.findOneAsync({ _id: otherPlanInTheBundle.reservationId, selectedPerson: person._id });
                        const bundle = _.find(org.availableBundles(true), b => b._id === bundlePlanId);
                        if (!r || !reservation || !bundle) {
                            delete plan.bundlePlanId;
                        } else {
                            const plan1 = plan._id === bundle.plans[0] ? plan : otherPlanInTheBundle;
                            const plan2 = plan._id === bundle.plans[0] ? otherPlanInTheBundle : plan;
                            const reservation1 = plan._id === plan1._id ? reservation : r;
                            const reservation2 = plan._id === plan1._id ? r : reservation;
                            const days1Count = reservation1.recurringDays?.length || 5;
                            const days2Count = reservation2.recurringDays?.length || 5;
                            plan.regularPrice = plan1.planDetails.scaledAmounts[days1Count - 1] + plan2.planDetails.scaledAmounts[days2Count - 1];
                            plan.bundlePlanPrice = bundle.scaledAmounts[days1Count - 1][days2Count - 1];
                        }
                    } else {
                        delete plan.bundlePlanId;
                    }
                } else {
                    delete plan.bundlePlanId;
                }
            }

            plansData.push(plan);
        }

        return plansData;
    }

    /**
     * Calculate the charges that are due today.
     *
     * @param registrationData
     * @param orgId
     * @returns {*|number}
     */
    static async calculateChargesDueToday(registrationData, orgId) {
        if (!registrationData || !orgId) {
            return 0;
        }

        const org = await Orgs.findOneAsync({ _id: orgId });
        if (!org) {
            return 0;
        }

        // Get the non-plan items and the registration fee
        const planItems = registrationData.plans.flat().filter(plan => plan.type === ITEM_TYPE || plan.type === PUNCH_CARD_TYPE);
        const regFee = registrationData?.registrationFee?.planTotal;

        for (let i = 0; i < registrationData.children.length; i++) {
            const plansPaymentStatus = await RegistrationService.getNewPlansInvoicingStatus(registrationData, orgId, i, true);
            if (!plansPaymentStatus.plansWithPaymentsDueToday.length) {
                continue;
            }
            const child = registrationData.children[i]._id ? await People.findOneAsync({ _id: registrationData.children[i]._id }) : null;
            for (const plan of plansPaymentStatus.plansWithPaymentsDueToday) {
                let startDate = plan.startDate;
                // Selective weeks don't get startDate on the plan object, so we have to calculate the expected start date using the earliest selected week.
                if (!startDate && plan.details?.selectiveWeeks?.length) {
                    const earliestWeek = _.min(plan.selectedWeeks);
                    if (earliestWeek !== undefined) {
                        const week = plan.details?.selectiveWeeks[earliestWeek];
                        if (week) {
                            startDate = week[0];
                        }
                    }
                }

                const period = BillingUtils.getPeriodByEffectiveDate(startDate, plan.frequency, org, child, true);
                BillingInvoiceService.proratePlanByDate(
                    plan,
                    org,
                    moment.tz(period.start, 'MM/DD/YYYY', org.getTimezone()).startOf('day').valueOf(),
                    moment.tz(period.end, 'MM/DD/YYYY', org.getTimezone()).endOf('day').valueOf(),
                    child,
                    false
                );
                RegistrationUtils.applyDiscountsToPlan(plan, org.billing?.toplinePercentDiscounts ?? false);
                planItems.push(plan);
            }
        }
        const totalRegistrationFees = await RegistrationUtils.totalRegistrationFees(registrationData, regFee, false, registrationData.contacts[0]?._id, orgId)
        return RegistrationUtils.calculateTotalAmountWithSelectiveWeek(planItems) + totalRegistrationFees.fee;
    }

    /**
     * Get the plans that need to be paid for or invoiced immediately.
     *
     * @param registrationData
     * @param orgId
     * @param childIndex
     * @param useSelectiveWeekMinWeek
     * @returns {{plansNeedingInvoices: *[], plansWithPaymentsDueToday: *[]}}
     */
    static async getNewPlansInvoicingStatus(registrationData, orgId, childIndex = null, useSelectiveWeekMinWeek = true) {
        const status = {
            plansWithPaymentsDueToday: [], // payments due today
            plansNeedingInvoices: [] // invoices needing to be created today
        };

        if (!registrationData || !orgId) {
            return status;
        }
        const org = await Orgs.findOneAsync({ _id: orgId });
        if (!org) {
            return status;
        }
        const timezone = org.getTimezone();

        let iteratorIndex = childIndex ?? 0;
        const iteratorLength = childIndex !== null ? iteratorIndex + 1 : registrationData.plans.length;

        // Get the plans that need to be invoiced immediately
        for (iteratorIndex; iteratorIndex < iteratorLength; iteratorIndex++) {
            const childId = registrationData.children[iteratorIndex]._id;
            const child = childId ? await People.findOneAsync({ _id: childId }) : null;
            for (const plan of registrationData.plans[iteratorIndex]) {
                // Only check the new plans
                if (plan.type === PLAN_TYPE && !plan.enrollmentDate) {
                    let billingCyclePosition = null;

                    if (plan.selectedWeeks?.length && useSelectiveWeekMinWeek) {
                        // If selective weeks we get the smallest week index and use that to determine the billing cycle position.
                        // This might not be the best approach if the weekly steps are manually re-organized.
                        const earliestWeek = _.min(plan.selectedWeeks);
                        if (earliestWeek !== undefined) {
                            const week = plan.details?.selectiveWeeks[earliestWeek];
                            if (week) {
                                billingCyclePosition = BillingUtils.getPositionInBillingCycle(plan, moment.tz(week[0], 'MM/DD/YYYY', timezone).valueOf(), org, child);
                            }
                        }
                    } else {
                        billingCyclePosition = BillingUtils.getPositionInBillingCycle(plan, moment.tz(plan.startDate, 'MM/DD/YYYY', timezone).valueOf(), org, child);
                    }

                    if (billingCyclePosition && !billingCyclePosition.nextBillingCycle) {
                        // Get the plan's effective date
                        const effectiveDate = moment.tz(billingCyclePosition.effectiveDate, 'MM/DD/YYYY', timezone).startOf('day').valueOf();
                        const nextInvoiceDate = moment.tz(billingCyclePosition.nextInvoiceDate, 'MM/DD/YYYY', org.getTimezone()).valueOf();
                        const generateDays = org.getGenerateDays();
                        const currentWeekInvoiceDate = moment.tz(timezone).clone().day(generateDays.week).startOf('day').valueOf();
                        const todaysDate = moment.tz(timezone).valueOf()
                        // Get the "current" period for the plan
                        const period = BillingUtils.getPeriodByEffectiveDate(moment.tz(timezone).format('MM/DD/YYYY'), plan.frequency, org, child, true);
                        const currentPeriodStartDate = period?.start ? moment.tz(period.start, 'MM/DD/YYYY', timezone).startOf('day').valueOf() : null;
                        const currentPeriodEndDate = period?.end ? moment.tz(period.end, 'MM/DD/YYYY', timezone).endOf('day').valueOf() : null;
                        
                        const isPlanEffectiveWithinCurrentPeriod = (currentPeriodStartDate <= effectiveDate && effectiveDate <= currentPeriodEndDate);
                        const isPaymentDueToday = plan.selectedWeeks?.length && useSelectiveWeekMinWeek
                            ? currentWeekInvoiceDate < todaysDate
                            : isPlanEffectiveWithinCurrentPeriod;

                        if (isPaymentDueToday) {
                            // Payment is due today since the plan's effective date is within the current ongoing period
                            status.plansWithPaymentsDueToday.push(plan);
                        } else {
                            // An invoice just needs to be created since the plan's effective date is not within the current ongoing period
                            status.plansNeedingInvoices.push(plan);
                        }
                        
                    }
                }
            }
        }

        return status;
    }

    /**
     * Updates the registration days for a parent based on the provided options and bundleOptions.
     * If the child was previously on hold, all updates to the schedule will be placed on hold pending approval.
     *
     * @param {object} options - The options object containing information for updating the registration days.
     * @param {object} [bundleOptions=null] - The bundle options object containing information for updating the bundled plan's registration days (if applicable).
     *
     * @returns {object | Promise} If the child was previously on hold, returns an object with a message indicating the changes are pending approval.
     *                            If the child was not on hold, returns an object with a message indicating the success of the update.
     *                            If needsProration is true (changes need proration), returns a Promise that resolves when proration is completed.
     * @throws {Meteor.Error} Throws an error if the 'returnChildToOnHold', 'updateOldReservation', 'addNewReservation', or 'prorateParentAdjustedPlan' methods encounter an error.
     */
    static async parentUpdateRegistrationDays(options, bundleOptions) {
        const {reservation, linkedPlan} = options;
        const personObj = await People.findOneAsync({ _id: reservation.selectedPerson });
        const org = await Orgs.findOneAsync({ _id: personObj.orgId });
        const timezone = org.getTimezone();
        const onHoldRecord = await this.childWasOnHold(personObj);
        const needsProration = this.needsProration(options, bundleOptions);
        const formattedEffectiveDate = moment.tz(options.effectiveDate, timezone).format('MM/DD/YYYY');
        const period = BillingUtils.getPeriodByEffectiveDate(formattedEffectiveDate, linkedPlan.planDetails.frequency, org, personObj, true);

        if ( onHoldRecord ) {
            try {
                await this.returnChildToOnHold(options, bundleOptions, onHoldRecord);
            } catch (e) {
                if (e.reason) {
                    throw new Meteor.Error(e.error, e.reason, e.details || '');
                } else {
                    throw new Meteor.Error('Error', e.message, '');
                }
            }
            return {message: 'Since this child was previously on hold, all updates to schedule must also be placed on hold pending approval. Your changes have been saved and will be applied once approved.'};
        }

        const updateRes = await this.updateOldReservation(options);
        const addRes = await this.addNewReservation(options);
        if (bundleOptions) {
            try {
                const bundleUpdateRes = await this.updateOldReservation(bundleOptions);
                const bundleAddRes = await this.addNewReservation(bundleOptions);
                const updatedPersonObj = await People.findOneAsync({ _id: personObj._id });
                await BillingUtils.recalculateBundleDiscount(updatedPersonObj, linkedPlan.bundlePlanId);

                if (needsProration) {
                    const prorateBundleOptions = {
                        oldReservations: [updateRes, bundleUpdateRes],
                        newReservations: [addRes, bundleAddRes],
                        personId: updatedPersonObj._id,
                        periodStart: period ? period.start : moment.tz(timezone).format('MM/DD/YYYY'),
                        periodEnd: period ? period.end : moment.tz(timezone).add(1, 'days').format('MM/DD/YYYY'),
                        org
                    }

                    await BillingInvoiceService.prorateParentAdjustedPlan(prorateBundleOptions);
                }
                return {message: 'Successfully updated bundled plan schedules.'};
            } catch (e) {
                if (e.reason) {
                    throw new Meteor.Error(e.error, e.reason, e.details || '');
                } else {
                    throw new Meteor.Error('Error', e.message, '');
                }
            }
        } else {
            try {
                if (needsProration) {
                    const prorateOptions = {
                        oldReservations: [updateRes],
                        newReservations: [addRes],
                        personId: personObj._id,
                        periodStart: period ? period.start : moment.tz(timezone).format('MM/DD/YYYY'),
                        periodEnd: period ? period.end : moment.tz(timezone).add(1, 'days').format('MM/DD/YYYY'), // I dunno is this fallback is meaningful.
                        org
                    }
                    await BillingInvoiceService.prorateParentAdjustedPlan(prorateOptions);
                }
                return {message: 'Successfully updated plan schedule.'};
            } catch (e) {
                if (e.reason) {
                    throw new Meteor.Error(e.error, e.reason, e.details || '');
                } else {
                    throw new Meteor.Error('Error', e.message, '');
                }
            }
        }
    }

    /**
     * Updates the old reservation based on the provided options.
     *
     * @param {object} options - The options object containing information for updating the old reservation.
     * @param {boolean} [buildDataOnly=false] - If true, the function returns the updated reservation data without updating it in the database.
     *
     * @returns {object | Promise} If buildDataOnly is true, returns the updated reservation data object; otherwise, returns a Promise that resolves when the reservation is updated.
     * @throws {Meteor.Error} Throws an error if the 'updateReservation' Meteor method call encounters an error.
     */
    static async updateOldReservation(options, buildDataOnly = false) {
        const {reservation, effectiveDate} = options;

        const {
            orgId,
            scheduledDate,
            selectedPerson,
            reservationType,
            scheduleType,
            groupId,
            recurringFrequency,
            recurringDays
        } = reservation;

        const org = await Orgs.findOneAsync({ _id: orgId });
        const timezone = org.getTimezone();
        const scheduledEndDate = new moment(effectiveDate).subtract(1, 'days').valueOf();
        const user = await Meteor.userAsync();
        const oldReservationUpdate = {
            scheduledDate,
            selectedPerson,
            reservationType,
            scheduleType,
            groupId,
            recurringFrequency,
            recurringDays,
            scheduledEndDate: scheduledEndDate,
            reservationId: reservation._id,
            parentAdjusted: true,
            parentAdjustedAt: moment.tz(timezone).valueOf(),
            parentAdjustedBy: await user.fetchPerson(),
        };

        if (buildDataOnly) {
            return oldReservationUpdate;
        }
        // Pass in fromRegistration = true since parents won't pass permission check
        try {
            await Meteor.callAsync('updateReservation', oldReservationUpdate, true);
            return oldReservationUpdate;
        } catch (e) {
            if (e.reason) {
                throw new Meteor.Error(e.error, e.reason, e.details || '');
            } else {
                throw new Meteor.Error('Error', e.message, '');
            }
        }
    }

    /**
     * Adds a new reservation based on the provided options.
     *
     * @param {object} options - The options object containing information for the new reservation.
     * @param {boolean} [buildDataOnly=false] - If true, the function returns the new reservation data without inserting it into the database.
     *
     * @returns {object | Promise} If buildDataOnly is true, returns the new reservation data object; otherwise, returns a Promise that resolves to the new reservation data.
     * @throws {Meteor.Error} Throws an error if any required data is missing or if the 'insertReservation' Meteor method call encounters an error.
     */
    static async addNewReservation(options, buildDataOnly = false) {
        const {reservation, effectiveDate, selectedDays, linkedPlan} = options;
        if (!reservation || !effectiveDate || !selectedDays || !linkedPlan) {
            throw new Meteor.Error( 'Missing data to add new reservation.');
        }
        const {
            orgId,
            selectedPerson,
            reservationType,
            scheduleType,
            groupId,
            recurringFrequency,
            scheduledTime,
            scheduledEndTime,
            scheduledEndDate
        } = reservation;
        const { allocations, overrideRate, bundlePlanId} = linkedPlan;


        const org = await Orgs.findOneAsync({ _id: orgId })
        const timezone = org.getTimezone();
        const newReservation = {
            scheduledDate: effectiveDate,
            selectedPerson: [selectedPerson],
            reservationType,
            scheduleType,
            recurringFrequency,
            groupId: 'default',
            overrideOverlap: true,
            recurringDays: selectedDays,
            scheduledEndDate: null,
            linkedPlan: {
                plan: linkedPlan._id,
                allocations,
                enrollment_date: new moment.tz(effectiveDate, timezone).format('MM/DD/YYYY')
            },
            closePriors: false
        }

        if(scheduledTime) {
            newReservation.scheduledTime = scheduledTime;
        }

        if(scheduledEndTime) {
            newReservation.scheduledEndTime = scheduledEndTime;
        }

        if (groupId) {
            newReservation.groupId = groupId;
        }

        if (bundlePlanId) {
            newReservation.linkedPlan.bundlePlanId = bundlePlanId;
        }

        if (overrideRate) {
            newReservation.linkedPlan.override_rate = overrideRate;
        }

        if (scheduledEndDate && scheduledEndDate > effectiveDate) {
            newReservation.scheduledEndDate = scheduledEndDate;
            newReservation.linkedPlan.expiration_date = new moment.tz(scheduledEndDate, timezone).format('MM/DD/YYYY');
        }

        if (buildDataOnly) {
            return newReservation;
        }
        // Pass in fromRegistration = true since parents won't pass permission check
        try {
            const result = await Meteor.callAsync('insertReservation', newReservation, orgId, true);
            newReservation.reservationId = result.newReservationId;
            return newReservation;
        }catch (e) {
            if (e.reason) {
                throw new Meteor.Error(e.error, e.reason, e.details || '');
            } else {
                throw new Meteor.Error('Error', e.message, '');
            }
        }
    }

    /**
     * Checks if a child's data appears in the OnHoldRegistrations collection based on the provided child's information.
     *
     * @param {object} child - The child object containing information about the child, including orgId, firstName, lastName, and birthday.
     *
     * @returns {object | null} Returns the OnHoldRegistration document if the child's data is found, otherwise returns null.
     */
    static async childWasOnHold(child) {
        const { orgId, firstName, lastName } = child;
        const org = await Orgs.findOneAsync({ _id: orgId });
        const prefix = org.profileDataPrefix();
        const birthday = prefix ? child[prefix].birthday : child.birthday;
        const timezone = org.getTimezone();
        const birthdayFormatted = moment(birthday).tz(timezone).format('MM/DD/YYYY');
        const query = {
            orgId,
            'data.children': { $elemMatch: { firstName, lastName, $or: [{birthday: birthdayFormatted},{birthday: birthday}] } }
        };
        const records = await OnHoldRegistrations.find(query).fetchAsync();

        // if multiples return the newest
        if(records.length > 1) {
            return RegistrationUtils.findNewestElement(records);
        }else{
            return records[0] || null;
        }
    }

    static async parentHasOnHoldChildren(parent) {
        const { orgId, firstName, lastName, _id } = parent;
        const query = {
            orgId,
            actionedDate: {$exists: false},
            'data.contacts': { $elemMatch: { firstName, lastName, _id } }
        };
        const records = await OnHoldRegistrations.find(query).fetchAsync();

        // if multiples return the newest
        if(records.length > 1) {
            return RegistrationUtils.findNewestElement(records);
        }else{
            return records[0] || null;
        }
    }

    /**
     * Checks whether proration is needed based on the provided options and bundleOptions.
     *
     * @param {object} options - The options object containing information about the change in number of days and billing cycle.
     * @param {object} bundleOptions - The bundle options object, if applicable.
     *
     * @returns {boolean} Returns true if proration is needed, otherwise returns false.
     */
    static needsProration(options, bundleOptions) {
        const { changeInNumberOfDays, billingCycle, linkedPlan } = options;
        const isScaledPlan = SCALED_PLAN_FREQUENCIES.includes(linkedPlan.planDetails.frequency);
        let changesToCurrentBillingCycle = billingCycle.nextBillingCycle === false;

        if (bundleOptions) {
          changesToCurrentBillingCycle = (billingCycle.nextBillingCycle === false || bundleOptions.billingCycle.nextBillingCycle === false);
        }

        return (isScaledPlan && changeInNumberOfDays > 0 && changesToCurrentBillingCycle);
    }

    /**
     * Maps updates to the registration data based on the provided old and new reservation information.
     *
     * @param {object} oldRes - The old reservation object.
     * @param {object} newRes - The new reservation object.
     * @param {object} child - The child object.
     * @param {object} registrationData - The registration data object to be updated.
     *
     * @throws {Meteor.Error} Throws an error if the child index is not found in the registration data.
     *
     * @returns {object} The updated registration data after applying the changes based on the reservation information.
     */
    static async mapUpdatesToRegistrationData(oldRes, newRes, child, registrationData) {
        const newRegistrationData = { ...registrationData };
        newRegistrationData.plans = registrationData.plans.map((planGroup) => [...planGroup]);
        const { firstName, lastName, orgId } = child;
        const org = await Orgs.findOneAsync({ _id: orgId });
        const prefix = org.profileDataPrefix();
        const birthday = prefix ? child[prefix].birthday : child.birthday;
        const timezone = org.getTimezone();
        const childIndex = newRegistrationData.children.findIndex((c) => {
            if (birthday) {
                // If birthday is defined, check all three conditions. Convert birthday to timestamp because we store registrationData birthday in 'MM/DD/YYYY' format
                const birthdayTimestamp = moment.tz(c.birthday, 'MM/DD/YYYY', timezone).valueOf();
                return c.firstName === firstName && c.lastName === lastName && birthdayTimestamp === birthday;
            } else {
                // If birthday is undefined, check only firstName and lastName
                return c.firstName === firstName && c.lastName === lastName;
            }
        });

        if (childIndex === -1) {
            throw new Meteor.Error('Invalid Child');
        }

        const oldPlan = newRegistrationData.plans[childIndex].find((plan) => {
            const idsMatch = plan._id === newRes.linkedPlan.plan;
            const noEndDate = !plan.endDate;
            const wasCreated = plan.createdAt;
            const previouslyAdjusted = plan.parentAdjusted;
            const endDateAfterNewEnrollment = moment.tz(plan.endDate, 'MM/DD/YYYY', timezone).isAfter(newRes.scheduledDate);
            return (idsMatch && wasCreated && (noEndDate || endDateAfterNewEnrollment)) || (idsMatch && wasCreated && previouslyAdjusted);
        });
        // if there are yet unapproved changes made previously we don't want to keep adding new plans to the data, find the unsaved plan and rewrite it
        const unSavedNewPlan = newRegistrationData.plans[childIndex].find(plan => plan._id === newRes.linkedPlan.plan && !plan.createdAt);

        if (oldPlan) {
            const newPlan = unSavedNewPlan ? unSavedNewPlan : { ...oldPlan };
            const formattedStartDate = moment.tz(newRes.scheduledDate, timezone).format('MM/DD/YYYY');
            const formattedEndDate = moment.tz(oldRes.scheduledEndDate, timezone).format('MM/DD/YYYY');
            newPlan.enrollmentDate = newRes.scheduledDate;
            newPlan.enrollmentForecastStartDate = newRes.scheduledDate;
            newPlan.selectedDays = newRes.recurringDays.map(day => formatSelectedDays(day, true));
            newPlan.startDate = formattedStartDate;
            newPlan.enrollmentDateFormatted = formattedStartDate;
            newPlan.enrollmentForecastStartDateFormatted = formattedStartDate;
            newPlan.endDate = newRes.scheduledEndDate ? moment.tz(newRes.scheduledEndDate, timezone).format('MM/DD/YYYY') : null;
            delete newPlan.createdAt;
            delete newPlan.reservationId;
            if (!unSavedNewPlan) newRegistrationData.plans[childIndex].push(newPlan);
            oldPlan.endDate = formattedEndDate;
            oldPlan.expirationDateFormatted = formattedEndDate;
            oldPlan.enrollmentForecastEndDateFormatted = formattedEndDate;
            // this helps with auditing and also helps target this later in the registration approval service
            oldPlan.parentAdjusted = true;
            oldPlan.parentAdjustedAt = moment.tz(timezone).valueOf();
            oldPlan.parentAdjustedEffectiveDate = newRes.scheduledDate;

            // set amount if scaled plan
            if (newPlan.scaledAmounts.length) {
                const amount = newPlan.scaledAmounts[newPlan.selectedDays.length - 1];
                newPlan.amount = amount;
                newPlan.calculatedAmount = amount;
                newPlan.planTotal = amount;
            }
        }

        if (child.agencyIdentifier) {
            newRegistrationData.children[childIndex].agencyIdentifier = child.agencyIdentifier;
        }

        if (child.subsidyCode) {
            newRegistrationData.children[childIndex].subsidyCode = child.subsidyCode;
        }

        return newRegistrationData;
    }

    /**
     * Returns a child to the on-hold status based on the provided options, bundleOptions, and on-hold record.
     *
     * @param {object} options - The options object containing reservation information.
     * @param {object} bundleOptions - The bundle options object.
     * @param {object} onHoldRecord - The on-hold record object.
     *
     * @throws {Meteor.Error} Throws an error if the current user is invalid or if any other error occurs during processing.
     */
    static async returnChildToOnHold(options, bundleOptions, onHoldRecord) {
        // this assumes the parent is the logged-in user
        const {reservation, linkedPlan} = options;
        const currentUser = await Meteor.userAsync();
        const child = await People.findOneAsync({ _id: reservation.selectedPerson });
        const org = await Orgs.findOneAsync({ _id: reservation.orgId });
        const currentPerson = await currentUser.fetchPerson();

        if (!currentPerson) {
            throw new Meteor.Error('Invalid Person');
        }

        let registrationData = null;
        const savedPlanOptions = {
            planOptions: options,
            personId: child._id,
            createdAt: moment.tz(org.getTimezone()).valueOf(),
            planIds: [linkedPlan._id],
        }

        if (!onHoldRecord.actionedDate) {
            registrationData = onHoldRecord.data;
        } else {
            try {
                registrationData = this.buildRegistrationDataFromFamily(currentPerson.orgId, currentPerson._id, false);
            } catch (error) {
                throw new Meteor.Error(error.error, error.reason);
            }
        }

        let updatedData = null;

        if (registrationData) {
            const [oldRes, newRes] = await Promise.all([
                this.updateOldReservation(options, true),
                this.addNewReservation(options, true)
            ]);
            const resolvedRegistrationData = await registrationData;
            updatedData = await this.mapUpdatesToRegistrationData(oldRes, newRes, child, resolvedRegistrationData);
            savedPlanOptions.oldReservations = [oldRes];
            savedPlanOptions.newReservations = [newRes];

            if (bundleOptions) {
                const [oldRes2, newRes2] = await Promise.all([
                    this.updateOldReservation(bundleOptions, true),
                    this.addNewReservation(bundleOptions, true)
                ]);

                updatedData = await this.mapUpdatesToRegistrationData(oldRes2, newRes2, child, resolvedRegistrationData);
                savedPlanOptions.bundlePlanOptions = bundleOptions;
                savedPlanOptions.oldReservations.push(oldRes2);
                savedPlanOptions.newReservations.push(newRes2);
                savedPlanOptions.planIds.push(bundleOptions.linkedPlan._id);
            }

            if(onHoldRecord.data.districtEmail) {
                updatedData.districtEmail = onHoldRecord.data.districtEmail;
            }

            if (updatedData.savedPlanOptions) {
                // push changes to existing savedPlanOptions if they exist so we don't pollute the database with duplicates
                for (const spo of updatedData.savedPlanOptions) {
                    // if these are unapproved changes for the same child and plan, update the planOptions instead of creating a new one.
                    if (spo.personId === child._id && spo.planIds.includes(linkedPlan._id || (bundleOptions && bundleOptions.linkedPlan._id))) {
                        spo.planOptions = savedPlanOptions.planOptions;
                        spo.planIds = savedPlanOptions.planIds;
                        spo.oldReservations = savedPlanOptions.oldReservations;
                        spo.newReservations = savedPlanOptions.newReservations;
                        spo.createdAt = savedPlanOptions.createdAt;
                    }else{
                        updatedData.savedPlanOptions.push(savedPlanOptions);
                    }
                }
            } else {
                updatedData.savedPlanOptions = [savedPlanOptions];
            }
        }

        if (!onHoldRecord.actionedDate) {
            await OnHoldRegistrations.updateAsync({ _id: onHoldRecord._id }, {
                $set: { data: updatedData, isDenied: false, isApproved: false }
            });
        } else {
            await Meteor.callAsync('placeRegistrationOnHold', onHoldRecord.orgId, updatedData);
        }
    }

    /**
     * Split out the selective week plans into individual plans for each week selected by the guardian.
     *
     * @param regData
     * @returns { regData }
     */
    static splitOutSelectWeekPlans(regData) {
        for (const child of regData.children) {
            const index = regData.children.indexOf(child);
            const splitOutPlans = [];
            const childPlans = regData.plans[index];
            for (const plan of childPlans) {
                if (!plan.createdAt && plan.type === PLAN_TYPE) {
                    if (plan.details?.selectiveWeeks?.length && plan.selectedWeeks?.length) {
                        for (const weekIndex of plan.selectedWeeks) {
                            const splitOutPlan = cloneDeep(plan);

                            // Find any single installment coups and remove them after the first week so that they don't get propagated to each week.
                            const singleUseAllocations = splitOutPlan.allocations?.filter(a => a.isSingleUse === true) || [];
                            const isFirstWeek = plan.selectedWeeks.indexOf(weekIndex) === 0;
                            if (singleUseAllocations.length && !isFirstWeek) {
                                splitOutPlan.allocations = splitOutPlan.allocations.filter(a => !a.isSingleUse);
                            }

                            splitOutPlan.startDate = plan.details.selectiveWeeks[weekIndex][0];
                            splitOutPlan.endDate = plan.details.selectiveWeeks[weekIndex][1];
                            splitOutPlans.push(splitOutPlan);
                        }
                    }
                }
            }

            // Filter out plans that have selective weeks
            const filteredPlans = childPlans.filter(plan => !plan.selectedWeeks?.length);

            // Add the split out plans to the filtered plans
            filteredPlans.push(...splitOutPlans);
            regData.plans[index] = filteredPlans;
        }

        return regData;
    }
}

/**
 * Formats a day of the week from its full name to its abbreviated form or vice versa.
 *
 * @param {string} day - The day of the week ('sunday', 'monday', etc.).
 * @param {boolean} [inverse=false] - If true, converts from abbreviated form to full name.
 * @returns {string} The formatted day of the week.
 */
export const formatSelectedDays = function (day, inverse = false) {
    if (!inverse) {
        switch (day) {
            case 'sunday':
                return 'sun';
            case 'monday':
                return 'mon';
            case 'tuesday':
                return 'tue';
            case 'wednesday':
                return 'wed';
            case 'thursday':
                return 'thu';
            case 'friday':
                return 'fri';
            case 'saturday':
                return 'sat';
        }
    } else {
        switch (day) {
            case 'sun':
                return 'sunday';
            case 'mon':
                return 'monday';
            case 'tue':
                return 'tuesday';
            case 'wed':
                return 'wednesday';
            case 'thu':
                return 'thursday';
            case 'fri':
                return 'friday';
            case 'sat':
                return 'saturday';
        }
    }
}
