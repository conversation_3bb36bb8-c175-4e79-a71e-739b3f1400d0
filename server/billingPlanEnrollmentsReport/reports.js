import { Groups } from "../../lib/collections/groups";
import { Invoices } from "../../lib/collections/invoices";
import { People } from "../../lib/collections/people";
import { Reservations } from "../../lib/collections/reservations";
const moment = require("moment-timezone");
import _ from '../../lib/util/underscore';

export class Reports {

    static async GenerateReport(currentUser, org, options) {
        const dayMap = { "mon": "M", "tue": "T", "wed": "W", "thu": "R", "fri": "F", "sat": "Sa", "sun": "Su" };
        const todayStamp = new moment.tz(org.getTimezone()).startOf("day").valueOf()
        const todayDate = new moment.tz(org.getTimezone()).startOf("day").valueOf();
        const allGroups = await Groups.find({ orgId: org._id }).fetchAsync();
        const scheduleTypes = org.getScheduleTypes();
        const groupByPersonPlans = [];
        const peopleQuery = {
            orgId: org._id,
            type: "person",
            inActive: { $ne: true },
            designations: { $nin: ["Wait List"] }
        };
        const plans = {};
        
        _.sortBy(_.deep(org, "billing.plansAndItems"), (pi) => pi.description).forEach((plan) => {
            if (plan.type == "plan" && (!options.selectedPlanId || options.selectedPlanId == "none" || plan._id == options.selectedPlanId)) {
                plan.enrolledPeoplePlans = [];
                plans[plan._id] = plan;
            }
        });
        if (!options.selectedPlanId || options.selectedPlanId == "none") {
            plans["none"] = {
                description: "Not enrolled",
                _id: "none",
                enrolledPeoplePlans: []
            };
        }
        if (options.selectedGroupId) peopleQuery.defaultGroupId = options.selectedGroupId;
        const pData = await People.find(peopleQuery, { sort: { lastName: 1, firstName: 1 } }).fetchAsync();
        for (const person of pData) {
            const personSchedules = (await Reservations.find({
                recurrenceId: { "$exists": false },
                recurringFrequency: { "$exists": true },
                "$or": [
                    { scheduledEndDate: null },
                    { scheduledEndDate: { "$gte": todayStamp } }
                ],
                selectedPerson: person._id
            }, { sort: { scheduledDate: 1 } }).fetchAsync()).map(r => {
                let targetGroup = r.groupId ? _.find(allGroups, (g) => r.groupId == g._id) : _.find(allGroups, (g) => person.defaultGroupId == g._id)
                r.targetGroupName = targetGroup && targetGroup.name;
                r.current = r.scheduledDate <= todayStamp;
                r.displayDays = _.map(r.recurringDays, (d) => { return dayMap[d] }).join(', ');
                const matchingSchedule = _.find(scheduleTypes, st => st._id == r.scheduleType);
                r.scheduleTypeName = (matchingSchedule) ? matchingSchedule.type : "No Type"
                return r;
            });
            if (person.billing) {
                person.lastInvoice = await Invoices.findOneAsync({ personId: person._id }, { sort: { createdAt: -1 } });
            }
            const group = person.defaultGroupId && _.find(allGroups, (g) => person.defaultGroupId == g._id);
            person.defaultGroupName = group && group.name;

            // New check to ensure the selectedPlanId matches billing plan
            const hasMatchingPlan = person.billing && person.billing.enrolledPlans &&
                (options.selectedPlanId === "none" || options.selectedPlanId == null || person.billing.enrolledPlans.some(p => p._id === options.selectedPlanId));

            if (options.groupByPerson) {
                const personPlans = [];
                if (person.billing && person.billing.enrolledPlans) {
                    if (person.billing.suspendUntil && person.billing.suspendUntil > new moment.tz(org.getTimezone()).startOf("day").valueOf()) {
                        person.suspendedUntil = person.billing.suspendUntil;
                    }
                    person.billing.enrolledPlans.forEach((ep) => {
                        if (plans[ep._id] && (!ep.expirationDate || ep.expirationDate > todayDate)) {
                            personPlans.push(ep);
                        }
                    });

                    if (personSchedules.length > 0 && hasMatchingPlan) {
                        const filteredPlans = options.selectedPlanId && options.selectedPlanId !== "none" 
                            ? personPlans.filter(p => p._id === options.selectedPlanId)
                            : personPlans;
                        
                        groupByPersonPlans.push({ 
                            person, 
                            age: await person.calcAge(), 
                            personPlans: filteredPlans, 
                            personSchedules 
                        });
                    }
                }
            } else {
                if ((!person.billing || _.isEmpty(person.billing.enrolledPlans)) && plans["none"]) {
                    plans["none"].enrolledPeoplePlans.push({
                        person,
                        age: await person.calcAge(),
                        currentPlan: {
                            enrollmentDate: 0
                        }
                    });
                } else if (person.billing && person.billing.enrolledPlans) {
                    if (person.billing.suspendUntil && person.billing.suspendUntil > new moment.tz(org.getTimezone()).startOf("day").valueOf()) {
                        person.suspendedUntil = person.billing.suspendUntil;
                    }
                    let enrolledCount = 0;
                    for (const ep of person.billing.enrolledPlans) {
                        const outPersonPlan = {
                            person,
                            age: await person.calcAge(),
                            currentPlan: ep
                        };
                        if (plans[ep._id] && (!ep.expirationDate || ep.expirationDate > todayDate)) {
                            plans[ep._id]["enrolledPeoplePlans"].push(outPersonPlan);
                            enrolledCount++;
                        }
                    }
                    if (enrolledCount == 0 && plans["none"]) {
                        plans["none"].enrolledPeoplePlans.push({
                            person,
                            age: await person.calcAge(),
                            currentPlan: {
                                enrollmentDate: 0
                            }
                        });
                    }
                }
            }
        }
        if (options.groupByPerson)
            return groupByPersonPlans;
        const plansList = _.values(plans);
        return _.filter(plansList, p => (!p.archived || p.enrolledPeoplePlans.length > 0) && (options.selectedPlanId != "none" || p._id == "none"));
    }
}