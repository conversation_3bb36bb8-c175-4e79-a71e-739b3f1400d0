import { AvailableCustomizations } from '../lib/customizations';
import { ReportAggregation } from './reports/reportAggregation';
import moment from 'moment-timezone';
import { BillingFrequencies, ledgerAccountTypes, lineItemTypes, PLAN_TYPE } from '../lib/constants/billingConstants';
import { CardProviders } from './card_providers/cardProviders';
import { BillingServerUtils } from './billingServerUtils';
import { LedgerDetailServiceUtils } from "../lib/util/ledgerDetailServiceUtils";
import { StringUtils } from "../lib/util/stringUtils";
import { MiscUtils } from "../lib/util/miscUtils";
import { Log } from "../lib/util/log";
import { AdyenTransactions } from '../lib/collections/adyenTransactions';
import { People } from '../lib/collections/people';
import { AllocationTypes } from '../lib/discountTypes';
import { Orgs } from '../lib/collections/orgs';
import { Invoices } from '../lib/collections/invoices';
import { Groups } from '../lib/collections/groups';
import { Deposits } from '../lib/collections/deposits';
import { CustomerChargebacksInvoices } from '../lib/collections/customerChargebacksInvoices';
import { PayerFunds } from '../lib/collections/payerFunds';
import _ from '../lib/util/underscore';
import { AdyenProvider } from './card_providers/adyenProvider';

export class LedgerDetailService {

    static async LedgerEntriesForRange(options) {
        const org = options.orgId ? await Orgs.findOneAsync(options.orgId) : await Orgs.current();
        const ledgerAccountCodes = org.getAccountLedgerCodes();
        const currentUser = options.currentUser;
        const orgIdsToNameMap = new Map();
        const hasNonFamilyFunding = org.hasCustomization(AvailableCustomizations.NON_FAMILY_FUNDS_ENABLED);
        const usePeriodDate = org.hasCustomization(AvailableCustomizations.POST_USING_PERIOD_START_DATE);
        const includeLocationUser = options.includeLocationUser;

        let { isMultiOrgs, orgsMap, orgsMeta, validOrgIds } = await this.getOrgsMapAndMeta(options, org, currentUser, orgIdsToNameMap);
        const invoiceQuery = this.generateInvoiceQuery(options, usePeriodDate, validOrgIds);

        const invoices = await Invoices.find(invoiceQuery).fetchAsync();
        const itemGroups = {
                "Plan Charges": {itemsGroup: {}, total: 0, count: 0},
                "Variable Subsidy Payer Amounts": {itemsGroup: {}, total: 0, count: 0},
                "Variable Subsidy Family Copays": {itemsGroup: {}, total: 0, count: 0},
                "Item Charges": {itemsGroup: {}, total: 0, count: 0},
                "Credits": {itemsGroup: {}, total: 0, count: 0},
                "Modified Credits": {itemsGroup: {}, total: 0, amountIncreased: 0, amountDecreased: 0, count: 0},
                "Discounts": {itemsGroup: {}, total: 0, count: 0},
                "Modified Discounts": {itemsGroup: {}, total: 0, count: 0},
                "Item Discounts": {itemsGroup: {}, total: 0, count: 0},
                "Voided Plan Charges": {itemsGroup: {}, total: 0, count: 0, reversal: true},
                "Voided Variable Subsidy Payer Amounts": {itemsGroup: {}, total: 0, count: 0, reversal: true},
                "Voided Variable Subsidy Family Copays": {itemsGroup: {}, total: 0, count: 0, reversal: true},
                "Voided Item Charges": {itemsGroup: {}, total: 0, count: 0, reversal: true},
                "Voided Credits": {itemsGroup: {}, total: 0, count: 0, reversal: true},
                "Voided Discounts": {itemsGroup: {}, total: 0, count: 0, reversal: true},
                "Voided Item Discounts": {itemsGroup: {}, total: 0, count: 0, reversal: true},
                "Payments": {itemsGroup: {}, total: 0, count: 0},
                "Adjusted Payments": {itemsGroup: {}, total: 0, count: 0},
                "Credit Memos": {itemsGroup: {}, total: 0, count: 0},
                "Refunded": {itemsGroup: {}, total: 0, count: 0},
                "Voided Payments": {itemsGroup: {}, total: 0, count: 0},
                "Settlements": {itemsGroup: {}, total: 0, count: 0},
                "Settlement Fees": {itemsGroup: {}, total: 0, count: 0},
                "Manual Deposits": {itemsGroup: {}, total: 0, count: 0},
                "Security Deposits Applied": {itemsGroup: {}, total: 0, count: 0},
                "Security Deposits Refunded": {itemsGroup: {}, total: 0, count: 0},
                "Credit Memos Refunded": {itemsGroup: {}, total: 0, count: 0},
                "Credit Memos Voided": {itemsGroup: {}, total: 0, count: 0},
                "Allocation Changes": {itemsGroup: {}, total: 0, count: 0},
                "Reversed Allocation Changes": {itemsGroup: {}, total: 0, count: 0},
                "Refunded Manual Payments": {itemsGroup: {}, total: 0, count: 0},
                "Chargeback Fees": {itemsGroup: {}, total: 0, count: 0},
                "Payer Overpayments": {itemsGroup: {}, total: 0, count: 0},
                "Unapplied Cash Applied": {itemsGroup: {}, total: 0, count: 0},
                "Security Deposit Forfeiture - Misc. Rev": {itemsGroup: {}, total:0, count: 0},
                "Security Deposit Liability/Offset": {itemsGroup: {}, total:0, count: 0},
                "Bank Withdrawal": {itemsGroup: {}, total:0, count: 0}
            };

        const pushItemGroup = function ( currentItemGroup, amount, quantity, lineItemType, lineItem, linkedDetail, orgId, modifiedDiscount, amountIncreased, amountDecreased) {
            currentItemGroup = currentItemGroup || { amount: 0, quantity: 0, transactions: [], linkedDetails: [], ledgerAccount: null, orgDetails: {} };

            // Update amount and quantity
            currentItemGroup.amount += amount;
            currentItemGroup.quantity += quantity;

            // Some groups can now have simultaneous debits and credits, so we need to track them separately (modified discounts, credits)
            if (amountIncreased) {
                if (!currentItemGroup.amountIncreased) {
                    currentItemGroup.amountIncreased = 0;
                }

                currentItemGroup.amountIncreased += amountIncreased;
            }

            if (amountDecreased) {
                if (!currentItemGroup.amountDecreased) {
                    currentItemGroup.amountDecreased = 0;
                }

                currentItemGroup.amountDecreased += amountDecreased;
            }


            if (options.includeTransactions) {
                currentItemGroup.transactions.push( lineItem );
            }

            if (options.includeLinkedDetails) {
                currentItemGroup.linkedDetails.push( linkedDetail );
            }

            if (!currentItemGroup.ledgerAccount) {
                currentItemGroup.ledgerAccount = org.getMappedLedgerAccount(lineItemType, lineItem, modifiedDiscount);
                currentItemGroup.ledgerAccountDetail = currentItemGroup.ledgerAccount && _.find(ledgerAccountCodes, lac => lac.accountNumber === currentItemGroup.ledgerAccount.accountName);
            } else if (modifiedDiscount !== undefined && currentItemGroup.ledgerAccount.type === ledgerAccountTypes.DISCOUNT_MODIFIED) {
                if (modifiedDiscount > 0) {
                    currentItemGroup.ledgerAccount.amountIncrease += Math.abs(modifiedDiscount);
                } else {
                    currentItemGroup.ledgerAccount.amountDecrease += Math.abs(modifiedDiscount);
                }
            }

            if (lineItem?._id && !currentItemGroup.accountPlanOrItemId) {
                currentItemGroup.accountPlanOrItemId = lineItem._id;
            }

            if (isMultiOrgs && orgId) {
                for (const parentOrg of orgsMap[orgId] || []) {
                    LedgerDetailServiceUtils.orgTotalInit(currentItemGroup.orgDetails, parentOrg, true);
                    LedgerDetailServiceUtils.orgTotalHelper(amount, quantity, currentItemGroup.orgDetails[parentOrg]);
                }
                const orgName = orgIdsToNameMap.get(orgId) ?? '';
                LedgerDetailServiceUtils.orgTotalInit(currentItemGroup.orgDetails, orgName);
                LedgerDetailServiceUtils.orgTotalHelper(amount, quantity, currentItemGroup.orgDetails[orgName]);
            }
            return currentItemGroup;
        }

        const updateItemGroup = function (groupSelector, description = '', amount, quantity, lineItemType, lineItem, linkedDetail, orgId, modifiedDiscount = null, amountIncreased = null, amountDecreased = null) {
            const trimmedDescription = description?.toLowerCase()?.trim();
            const itemsGroupKey = isMultiOrgs ? trimmedDescription : description;

            itemGroups[groupSelector].itemsGroup[itemsGroupKey] = pushItemGroup(
                itemGroups[groupSelector].itemsGroup[itemsGroupKey],
                amount,
                quantity,
                lineItemType,
                lineItem,
                linkedDetail,
                orgId,
                modifiedDiscount,
                amountIncreased,
                amountDecreased
            );
        }

        const applyItemToGroup = function (options) {
            const linkedDetail = {
                description: options.detailDescription,
                coversPeriodDesc: options.coversPeriodDesc,
                periodStartDate: options.periodStartDate,
                date: options.itemDate,
                targetType: options.targetType,
                target: options.target,
                amount: options.amount,
                personDefaultGroupName: options.personDefaultGroupName,
                invoiceNotes: options.invoiceNotes || '',
                ...(options.personId ? { personId: options.personId } : {})
            };

            if (options.modifiedDiscount !== undefined) {
                linkedDetail.modifiedAmount = options.modifiedDiscount;
            }

            updateItemGroup(options.groupSelector, options.description, options.amount, options.quantity, options.descriptor, options.originalItem, linkedDetail, options.orgId, options.modifiedDiscount);
            itemGroups[options.groupSelector].total += (options.amount || 0);
            itemGroups[options.groupSelector].count += options.quantity;
        }

        const orgPeople = await People.find({ orgId: invoiceQuery['orgId'], type: "person" }, {fields:{firstName:1, lastName:1, defaultGroupId:1}}).fetchAsync();
        const orgGroups = await Groups.find({ orgId: invoiceQuery['orgId'] }, {fields:{name:1}}).fetchAsync();

        const filteredInvoices = this.filterInvoicesByPeriodDate(invoices, options);
        this.mapPostingDateToInvoiceLineItems(usePeriodDate, filteredInvoices, org);
        filteredInvoices.forEach(invoice => {

                const { invoiceNameDescriptor, personDefaultGroupName, personId } = LedgerDetailServiceUtils.invoiceNameDescriptorAndPersonDefaultGroupName(invoice, orgPeople, orgGroups, includeLocationUser);
                const additionalColumnsForLinkedDetails = {
                    ...(includeLocationUser && personId ? { personId } : {})
                }
                const invoiceLineItems = invoice.lineItems || [];
                const invoiceCredits = invoice.credits || [];
                const invoiceAllocationEntries = invoice.allocationEntries || [];

                invoiceLineItems.forEach( (lineItem) => {
                    let lineItemAppliedDiscounts = lineItem.appliedDiscounts || [];
                    const processOptions = {
                        lineItem,
                        options,
                        invoice,
                        org,
                        invoiceNameDescriptor,
                        personDefaultGroupName,
                        applyItemToGroup,
                        additionalColumnsForLinkedDetails
                    }
                    this.processNonVoidedLineItem(processOptions)
                    this.processLineItemAppliedDiscounts(lineItemAppliedDiscounts, processOptions);
                });

                invoiceCredits.forEach((credit) => {
                    const creditIsARefund = credit.type === "refund";
                    const creditIsWithinReportRange = credit.createdAt >= options.startDate && credit.createdAt < options.endDate;
                    const creditHasBeenModified = credit.isModified ?? false;
                    const creditHasBeenAdjusted = credit.adjustments && credit.adjustments.length > 0;

                    if (!creditIsARefund && creditIsWithinReportRange) {
                        const item = LedgerDetailServiceUtils.creditItemGroup(credit, invoice, org, invoiceNameDescriptor, personDefaultGroupName, additionalColumnsForLinkedDetails);
                        let itemAmount = item.amount

                        if (credit.creditReason === "reallocation_to_payer" && credit.reversedAt && credit.reversedAmount) {
                            const linkedDetail = LedgerDetailServiceUtils.reversedReallocationToPayerLinkedDetail(invoiceNameDescriptor, invoice, credit, personDefaultGroupName, item.description, org.getTimezone(), additionalColumnsForLinkedDetails);
                            itemGroups["Reversed Allocation Changes"].itemsGroup[item.description] = pushItemGroup(itemGroups["Reversed Allocation Changes"].itemsGroup[item.description], credit.reversedAmount, 1, "reversedReallocationToPayer", credit, linkedDetail);
                            itemGroups["Reversed Allocation Changes"].total += credit.reversedAmount;
                            itemGroups["Reversed Allocation Changes"].count++;
                            // Have to set itemAmount because the item.amount passed from creditItemGroup is either the amount or reversed amount
                            //  This confirms the time amount is credit.amount and not credit.reversedAmount
                            itemAmount = credit.amount
                        }

                        updateItemGroup(...Object.values(item));
                        itemGroups[item.groupSelector].total += itemAmount;
                        itemGroups[item.groupSelector].count++;

                        if (credit.payerOverpaymentAmount ) {
                            const allocationSourceDefinition = _.find(org.availablePayerSources(), ps => ps.type === credit.creditPayerSource);
                            const groupDescriptor = credit.payerOverpaymentDestination + " " + (allocationSourceDefinition?.description || credit.creditPayerSource);
                            const linkedDetail = LedgerDetailServiceUtils.payerOverpaymentAmountLinkedDetail(invoiceNameDescriptor, invoice, credit, groupDescriptor, personDefaultGroupName, additionalColumnsForLinkedDetails);
                            updateItemGroup('Payer Overpayments', groupDescriptor, credit.payerOverpaymentAmount, 1, "payerOverpayment", credit, linkedDetail, invoice.orgId);
                            itemGroups["Payer Overpayments"].total += credit.payerOverpaymentAmount;
                            itemGroups["Payer Overpayments"].count++;
                        }
                    }

                    if (creditHasBeenModified) {
                        // Do all the modified credit stuff here
                        const itemGroupUpdates = this.processModifiedCredit(credit, itemGroups, invoice, invoiceNameDescriptor, personDefaultGroupName, options.startDate, options.endDate, additionalColumnsForLinkedDetails);
                        for (const update of itemGroupUpdates) {
                            updateItemGroup(...Object.values(update));
                        }
                    }

                    if (LedgerDetailServiceUtils.isVoidedManualCreditInRange(credit, options)) {
                        const linkedDetail = LedgerDetailServiceUtils.processActiveVoidedWithCreditReasonLinkedDetail(invoiceNameDescriptor, credit, invoice, personDefaultGroupName, additionalColumnsForLinkedDetails);
                        const groupDescriptor = LedgerDetailServiceUtils.getGroupDescriptor(credit);
                        updateItemGroup('Voided Payments', groupDescriptor, credit.voidedAmount, 1, lineItemTypes.PAYMENT_VOIDED, credit, linkedDetail, invoice.orgId);
                        itemGroups["Voided Payments"].total += (credit.voidedAmount || 0);
                        itemGroups["Voided Payments"].count++;
                    }

                    if (LedgerDetailServiceUtils.isVoidedOtherCreditInRange(credit, options)) {
                        const voidPayment = LedgerDetailServiceUtils.processActiveVoidedOtherReason(invoiceNameDescriptor, credit, options, invoice, personDefaultGroupName, additionalColumnsForLinkedDetails);
                        applyItemToGroup(voidPayment);
                    }

                    if (LedgerDetailServiceUtils.isRefundedManualCreditInRange(credit, options)) {
                        const linkedDetail = LedgerDetailServiceUtils.processRefundManualPaymentLinkedDetail(invoice, credit, personDefaultGroupName, invoiceNameDescriptor, additionalColumnsForLinkedDetails);
                        const groupDescriptor = LedgerDetailServiceUtils.getGroupDescriptor(credit);
                        updateItemGroup('Refunded Manual Payments', groupDescriptor, credit.refundedAmount, 1, lineItemTypes.PAYMENT_MANUAL_REFUNDED, credit, linkedDetail, invoice.orgId);
                        itemGroups["Refunded Manual Payments"].total += (credit.refundedAmount || 0);
                        itemGroups["Refunded Manual Payments"].count++;
                    }

                    if (creditHasBeenAdjusted) {
                        for (const adjustment of credit.adjustments) {
                            const adjustmentIsWithinReportRange = adjustment.adjustedAt >= options.startDate && adjustment.adjustedAt < options.endDate;
                            if (adjustmentIsWithinReportRange) {
                                const linkedDetail = LedgerDetailServiceUtils.processAdjustPaymentLinkedDetail(invoice, adjustment, invoiceNameDescriptor, personDefaultGroupName, additionalColumnsForLinkedDetails);
                                let groupDescriptor = credit.payment_type
                                    ? StringUtils.capitalizeFirstLetter(credit.payment_type.replace(/\_/g,' '))
                                    : StringUtils.capitalizeFirstLetter(credit.creditReason.replace(/\_/g,' '));
                                updateItemGroup("Adjusted Payments", groupDescriptor, adjustment.adjustedAmount, 1, lineItemTypes.PAYMENT_ADJUSTED, credit, linkedDetail, invoice.orgId);
                                itemGroups["Adjusted Payments"].total += (adjustment.adjustedAmount || 0);
                                itemGroups["Adjusted Payments"].count++;
                            }
                        }
                    }
                });

                invoiceAllocationEntries.forEach((allocationChange) => {
                    if (["bad-debt", "agency", "collections"].includes(allocationChange.destination) && allocationChange.createdAt >= options.startDate && allocationChange.createdAt < options.endDate) {
                        const { allocationSourceDescription, allocationLabel, allocationLabelReversed } = LedgerDetailServiceUtils.allocationChanges(allocationChange, org);

                        updateItemGroup("Allocation Changes", allocationSourceDescription, allocationChange.amount || allocationChange.reversedAmount, 1, allocationLabel, allocationChange, {
                            description: `Payer ${allocationChange.destination} from ${allocationSourceDescription}`,
                            date: new moment(allocationChange.createdAt).format("M/DD/YYYY"),
                            amount: allocationChange.amount || allocationChange.reversedAmount,
                            ...additionalColumnsForLinkedDetails
                        }, invoice.orgId);
                        itemGroups["Allocation Changes"].total += allocationChange.amount || allocationChange.reversedAmount;
                        itemGroups["Allocation Changes"].count++;

                        if (allocationChange.reversedAt && allocationChange.reversedAmount) {
                            updateItemGroup("Reversed Allocation Changes", allocationSourceDescription, allocationChange.reversedAmount, 1, allocationLabelReversed, allocationChange, {
                                description: `Payer ${allocationChange.destination} from ${allocationSourceDescription}`,
                                date: new moment(allocationChange.createdAt).format("M/DD/YYYY"),
                                amount: allocationChange.reversedAmount,
                                ...additionalColumnsForLinkedDetails
                            }, invoice.orgId);
                            itemGroups["Reversed Allocation Changes"].total += allocationChange.reversedAmount;
                            itemGroups["Reversed Allocation Changes"].count++;
                        }
                    }
                });

            });

        const voidedInvoiceQuery = {
            orgId: invoiceQuery['orgId'],
            voided: {$eq: true},
            $or: [{voidedAt: {"$gte": options.startDate, "$lt": options.endDate}}]
        };

        // if customization is active, find invoices that are voided and have a line item with a period start date within the report range
        if (usePeriodDate) {
            voidedInvoiceQuery["$or"].push({"lineItems.periodStartDate": {"$gte": options.startDate, "$lt": options.endDate}});
        }

        const voidedInvoices = await Invoices.find(voidedInvoiceQuery).fetchAsync();
        const filteredVoidedInvoices = this.filterInvoicesByPeriodDate(voidedInvoices, options);
        this.mapPostingDateToInvoiceLineItems(usePeriodDate, filteredVoidedInvoices, org);

        filteredVoidedInvoices.forEach( (invoice) => {

                const { invoiceNameDescriptor, personDefaultGroupName, personId } = LedgerDetailServiceUtils.invoiceNameDescriptorAndPersonDefaultGroupName(invoice, orgPeople, orgGroups, includeLocationUser);
                const additionalColumnsForLinkedDetails = {
                    ...(includeLocationUser && personId ? { personId } : {})
                }
                const invoiceVoidedInRange = invoice.voidedAt >= options.startDate && invoice.voidedAt < options.endDate;
                invoice.lineItems.filter(lineItem => invoiceVoidedInRange && invoice.voidedAt >= lineItem.postingDate).forEach( (lineItem) => {
                    if (lineItem.type === PLAN_TYPE) {
                        if (lineItem.frequency === BillingFrequencies.DAILY_CHARGED_MONTHLY) {
                            const reportStartDate = moment.tz(options.startDate, org.getTimezone());
                            const reportEndDate = moment.tz(options.endDate, org.getTimezone());
                            const {weeklyPayerAmounts, weeklyCopayAmounts, mondaysLeft} = lineItem;
                            if (LedgerDetailServiceUtils.variableLengthsMatches(weeklyCopayAmounts, mondaysLeft)) {
                                const groupSelector = "Voided Variable Subsidy Family Copays";
                                for (let i = 0; i < weeklyCopayAmounts.length; i++) {
                                    const thisMonday = moment.tz(mondaysLeft[i], 'MM/DD/YYYY', org.getTimezone());
                                    const coversPeriodDesc = `Week of ${thisMonday.format('MM/DD/YYYY')} for monthly period ${moment(lineItem.periodStartDate).format('MM/DD/YYYY')} - ${moment(invoice.invoiceDate, 'MM/DD/YYYY').endOf('month').format('MM/DD/YYYY')}`;
                                    // don't post this week if it is not in the report date range
                                    if (thisMonday.isBefore(reportStartDate) || thisMonday.isAfter(reportEndDate)) {
                                        continue;
                                    }

                                    const planCharge = LedgerDetailServiceUtils.splitMonthlyLineItemsIntoWeeklyParts(lineItem, invoice, weeklyCopayAmounts, mondaysLeft, coversPeriodDesc, groupSelector, invoiceNameDescriptor, personDefaultGroupName, i, additionalColumnsForLinkedDetails, 'Voided ');
                                    applyItemToGroup(planCharge);
                                }
                            }
                            if (mondaysLeft?.length && weeklyPayerAmounts?.length && mondaysLeft.length === weeklyPayerAmounts.length) {
                                const detailDescription = invoiceNameDescriptor + "Voided Weekly posting on plan charge for invoice " + invoice.invoiceNumber;
                                const groupSelector = "Voided Variable Subsidy Payer Amounts";
                                for (let i = 0; i < weeklyPayerAmounts.length; i++) {
                                    const thisMonday = moment.tz(mondaysLeft[i], 'MM/DD/YYYY', org.getTimezone());
                                    // don't post this week if it is not in the report date range
                                    if (thisMonday.isBefore(reportStartDate) || thisMonday.isAfter(reportEndDate)) {
                                        continue;
                                    }

                                    const planCharge = LedgerDetailServiceUtils.handleVariableSubsidyPayerAmount(lineItem, org, invoice, weeklyPayerAmounts, thisMonday, mondaysLeft, groupSelector, detailDescription, personDefaultGroupName, i, additionalColumnsForLinkedDetails);
                                    applyItemToGroup(planCharge);
                                }
                            }
                        } else {
                            const planCharge = LedgerDetailServiceUtils.voidedPlanChargeInvoiceObject(lineItem, invoice, invoiceNameDescriptor, personDefaultGroupName, additionalColumnsForLinkedDetails);
                            applyItemToGroup(planCharge);
                            _.each(lineItem.appliedDiscounts, (discount) => {
                                const isReimbursable = discount.type === "reimbursable" || discount.type === 'reimbursable-with-copay';
                                const isDiscount = discount.type === "discount";
                                const amountZero = discount.amount === 0;
                                const isVoided = discount.voidedAt;
                                if (lineItem.frequency === BillingFrequencies.DAILY_CHARGED_MONTHLY && discount.type === 'reimbursable-with-copay') {
                                    // no op -- handled above
                                } else if (isVoided && (isReimbursable || isDiscount) && amountZero && lineItem.frequency !== BillingFrequencies.DAILY_CHARGED_MONTHLY) {
                                    const voidSubsidy = LedgerDetailServiceUtils.voidSubsidyObject(lineItem, invoice, discount, org, personDefaultGroupName, invoiceNameDescriptor, isReimbursable, isDiscount, additionalColumnsForLinkedDetails);
                                    applyItemToGroup(voidSubsidy);
                                }
                            });
                        }
                    } else {
                        const detailDescription = invoiceNameDescriptor + "Voided item charge for invoice " + invoice.invoiceNumber;
                        const groupSelector = "Voided Item Charges";
                        const itemCharge = LedgerDetailServiceUtils.itemChargeObject(lineItem, invoice, personDefaultGroupName, detailDescription, groupSelector, additionalColumnsForLinkedDetails);
                        applyItemToGroup(itemCharge);

                        _.each(lineItem.appliedDiscounts, (discount) => {
                            if (lineItem.frequency === BillingFrequencies.DAILY_CHARGED_MONTHLY && discount.type === AllocationTypes.COPAY) {
                                // no op -- handled above
                            } else {
                                const detailDescription = invoiceNameDescriptor + "Voided item discount for invoice " + invoice.invoiceNumber;
                                const groupSelector = "Voided Item Discounts";
                                const itemDiscount = LedgerDetailServiceUtils.itemDiscountObject(invoice, discount, org, personDefaultGroupName, invoiceNameDescriptor, groupSelector, detailDescription, '', additionalColumnsForLinkedDetails);
                                applyItemToGroup(itemDiscount);
                            }
                        });
                    }
                });

            });

        for (const tmpOrgId of validOrgIds) {
            const tmpOrg = await Orgs.findOneAsync({_id: tmpOrgId});
            const eligibleCreditMemoTypes = tmpOrg.availableCreditMemoTypes().filter(cmt => cmt.paymentOffset && cmt.type !== "payrollDeduction" ).map(cmt => cmt.type),
                overpaymentPeople = await People.find({
                    orgId: tmpOrgId,
                    "billing.creditMemos": {
                        "$elemMatch": {
                            "createdAt": {"$gte": options.startDate, "$lt": options.endDate},
                            "type": {"$in": eligibleCreditMemoTypes},
                            "sourceQuickEntry": {"$ne": true}
                        }
                    }
                }).fetchAsync();
            overpaymentPeople.forEach(op => {
                op.billing.creditMemos.filter(opcm => _.contains(eligibleCreditMemoTypes, opcm.type) && opcm.createdAt >= options.startDate && opcm.createdAt < options.endDate && !opcm.sourceQuickEntry && !opcm.type.startsWith("prepaid_")).forEach(opcm => {
                    updateItemGroup('Credit Memos', 'Credit Memos', opcm.originalAmount, 1, lineItemTypes.CREDIT_MEMO, null, {
                        description: opcm.notes + " by " + op.firstName + " " + op.lastName,
                        date: new moment(opcm.createdAt).format("M/DD/YYYY"),
                        amount: opcm.originalAmount,
                        targetType: "person",
                        target: op._id,
                        ...(includeLocationUser ? { personId: op._id } : {})
                    }, op.orgId);
                    itemGroups["Credit Memos"].total += opcm.originalAmount;
                    itemGroups["Credit Memos"].count++;
                });
            });
            const creditMemoPayrollDeductions = tmpOrg.availableCreditMemoTypes().filter(cmt => cmt.paymentOffset && cmt.type === "payrollDeduction" ).map(cmt => cmt.type),
                overpaymentPeoplePayrollDeductions = await People.find({
                    orgId: tmpOrgId,
                    "billing.creditMemos": {
                        "$elemMatch": {
                            "createdAt": {"$gte": options.startDate, "$lt": options.endDate},
                            "type": {"$in": creditMemoPayrollDeductions},
                            "sourceQuickEntry": {"$ne": true}
                        }
                    }
                }).fetchAsync();
            overpaymentPeoplePayrollDeductions.forEach(op => {
                op.billing.creditMemos.filter(opcm => _.contains(creditMemoPayrollDeductions, opcm.type) && opcm.createdAt >= options.startDate && opcm.createdAt < options.endDate && !opcm.sourceQuickEntry && !opcm.type.startsWith("prepaid_")).forEach(opcm => {
                    updateItemGroup('Credit Memos', 'Payroll deduction', opcm.originalAmount, 1, lineItemTypes.PAYROLL_DEDUCTION, null, {
                        description: opcm.notes + " by " + op.firstName + " " + op.lastName,
                        date: new moment(opcm.createdAt).format("M/DD/YYYY"),
                        amount: opcm.originalAmount,
                        targetType: "person",
                        target: op._id,
                        ...(includeLocationUser ? { personId: op._id } : {})
                    }, op.orgId);
                    itemGroups["Credit Memos"].total += opcm.originalAmount;
                    itemGroups["Credit Memos"].count++;
                });
            });
        }

        const securityDepositSeizedPeople = await People.find({
            orgId: invoiceQuery['orgId'],
            "billing.creditMemos": {"$elemMatch":{
                    "type": "securityDepositAuto",
                    "securityActions": { "$exists": true }
                }}}).fetchAsync();
        const securityDepositItems = invoices.filter(invoice => _.some(invoice.lineItems, li => {
            return li.type === "item" && li?.originalItem?.refundableDeposit;
        }));
        securityDepositSeizedPeople.forEach(deposit => {
            let invoiceId;
            deposit.billing.creditMemos.filter(depositcm => depositcm.type === "securityDepositAuto" && depositcm.securityActions && depositcm.securityActions.length > 0)
                .forEach(depositcm => {
                    depositcm.securityActions.forEach(securityAction => {
                        securityDepositItems.forEach(securityDepositItem => {
                            if (
                                securityDepositItem &&
                                securityDepositItem.credits &&
                                securityDepositItem.credits.length > 0 &&
                                deposit._id === securityDepositItem.credits[0].paidBy
                            ) {
                                invoiceId = securityDepositItem._id;
                            }
                        });
                        if(securityAction.action === "seizedSecurityDeposit" && (securityAction.seizedAt >= options.startDate && securityAction.seizedAt < options.endDate)) {
                            updateItemGroup('Security Deposit Forfeiture - Misc. Rev', 'Security Deposit Forfeiture - Misc. Rev', securityAction.seizedAmount, 1, lineItemTypes.SECURITY_DEPOSIT_FORFEITURE,null,
                                {
                                    description: depositcm.notes + " by " + deposit.firstName + " " + deposit.lastName,
                                    date: new moment(securityAction.seizedAt).format("M/DD/YYYY"),
                                    amount: securityAction.seizedAmount,
                                    targetType: "invoice",
                                    target: invoiceId,
                                    ...(includeLocationUser ? { personId: deposit._id } : {})
                                }, deposit.orgId);
                            itemGroups['Security Deposit Forfeiture - Misc. Rev'].total += securityAction.seizedAmount;
                            itemGroups['Security Deposit Forfeiture - Misc. Rev'].count++;
                        }
                        if (securityAction.action === "reinstatedSecurityDeposit" && (securityAction.seizeReinstatedAt >= options.startDate && securityAction.seizeReinstatedAt < options.endDate)){
                            updateItemGroup('Security Deposit Liability/Offset', 'Security Deposit Liability/Offset', securityAction.reinstatedAmount, 1, lineItemTypes.SECURITY_DEPOSIT_LIABILITY,null,
                                {
                                    description: depositcm.notes + " by " + deposit.firstName + " " + deposit.lastName,
                                    date: new moment(securityAction.seizeReinstatedAt).format("M/DD/YYYY"),
                                    amount: securityAction.reinstatedAmount,
                                    targetType: "invoice",
                                    target: invoiceId,
                                    ...(includeLocationUser ? { personId: deposit._id } : {})
                                }, deposit.orgId);
                            itemGroups['Security Deposit Liability/Offset'].total += securityAction.reinstatedAmount;
                            itemGroups['Security Deposit Liability/Offset'].count++;
                        }
                    });
                });
        });

        const creditMemosRefundedPeople = await People.find({
            orgId: invoiceQuery['orgId'],
            "billing.creditMemos": {"$elemMatch":{
                    "refundedAt": {"$gte": options.startDate, "$lt": options.endDate},
                }}}).fetchAsync() || [];
        creditMemosRefundedPeople.forEach( op => {
            op.billing.creditMemos.filter( opcm => opcm.refundedAt >= options.startDate && opcm.refundedAt < options.endDate).forEach(opcm => {
                const refundGroupLabel = opcm.type === "securityDepositAuto" ? "Security Deposits Refunded" : "Credit Memos Refunded"
                const refundGroupTypeLabel = opcm.type === "securityDepositAuto" ? lineItemTypes.SECURITY_DEPOSIT_REFUND_AUTO : lineItemTypes.CREDIT_MEMO_REFUNDED;
                updateItemGroup(refundGroupLabel, refundGroupLabel, opcm.refundedAmount, 1, refundGroupTypeLabel, null, {
                    description: opcm.notes + " by " + op.firstName + " " + op.lastName,
                    date: new moment(opcm.createdAt).format("M/DD/YYYY") ,
                    amount: opcm.refundedAmount,
                    ...(includeLocationUser ? { personId: op._id } : {})
                }, op.orgId);
                itemGroups[refundGroupLabel].total += opcm.refundedAmount;
                itemGroups[refundGroupLabel].count++;
            });
        });

        const creditMemosVoidedPeople = await People.find({
            orgId: invoiceQuery['orgId'],
            "billing.creditMemos": {"$elemMatch":{
                    "voidedAt": {"$gte": options.startDate, "$lt": options.endDate},
                    "type": {"$ne": "securityDepositAuto"}
                }}}).fetchAsync() || [];
        creditMemosVoidedPeople.forEach( op => {
            op.billing.creditMemos.filter( opcm => opcm.voidedAt >= options.startDate && opcm.voidedAt < options.endDate).forEach(opcm => {
                const amount = opcm.voidedAmount || 0;
                updateItemGroup('Credit Memos Voided', 'Credit Memos Voided', opcm.voidedAmount, 1, lineItemTypes.CREDIT_MEMO_VOID, null, {
                    description: opcm.notes + " by " + op.firstName + " " + op.lastName,
                    date: new moment(opcm.createdAt).format("M/DD/YYYY") ,
                    amount,
                    ...(includeLocationUser ? { personId: op._id } : {})
                }, op.orgId);
                itemGroups["Credit Memos Voided"].total += amount;
                itemGroups["Credit Memos Voided"].count++;
            });
        });

        for (const tmpOrgId of validOrgIds) {
            const tmpOrg = await Orgs.findOneAsync({ _id: tmpOrgId });
            const orgProvider = tmpOrg.billingCardProviderName();

            if (tmpOrg?.billing?.adyenInfo?.accountCode) {
                const adyenTransactions = await AdyenTransactions.find({
                    accountCode: tmpOrg.billing.adyenInfo.accountCode,
                    datetime: {
                        $gte: new Date(options.startDate).toISOString(),
                        $lte: new Date(options.endDate).toISOString()
                    }
                }).fetchAsync();
                adyenTransactions.forEach(adyenTransaction => {
                    const amountInCents = adyenTransaction.amount; // amount is in cents
                    const amountInDollars = amountInCents / 100; // convert to dollars

                    // Convert timestamp to Date object, using datetime as fallback
                    const transactionDate = convertTimestamp(adyenTransaction.timestamp, adyenTransaction.datetime);

                    updateItemGroup('Bank Withdrawal', 'Bank Withdrawal', amountInDollars, 1, lineItemTypes.BANK_WITHDRAWAL, null, {
                        description: "Top Up/Bank Withdrawal",
                        date: formatDate(transactionDate),
                        amount: amountInDollars,
                        targetType: "topUp"
                    }, tmpOrgId);

                    itemGroups["Bank Withdrawal"].count++;
                    itemGroups["Bank Withdrawal"].total += amountInDollars;
                });
            }

            if (['adyen', 'adyen_balance'].includes(orgProvider)) {
                // AdyenProvider calls AdyenBalancePaymentProvider.billingPayoutsReport if balanceAccountId is present
                const payoutsForDates = await AdyenProvider.billingPayoutsReport({
                    orgIds: [tmpOrg._id],
                    startDate: new moment.tz(options.startDate, tmpOrg.getTimezone()).format("MM/DD/YYYY"),
                    endDate: new moment.tz(options.endDate, tmpOrg.getTimezone()).format("MM/DD/YYYY"),
                    excludeEndDate: true
                });

                const uniquePayouts = _.chain(payoutsForDates.data).map(pfd => ({
                    payoutId: pfd.id,
                    payoutReference: pfd.ref
                })).uniq(false, i => i.payoutId + "|" + i.payoutReference).value();

                for (const {payoutId, payoutReference} of uniquePayouts) {
                    // AdyenProvider calls AdyenBalancePaymentProvider.billingPayoutDetailReport if balanceAccountId is present
                    const payoutDetail = await AdyenProvider.billingPayoutDetailReport({ org: tmpOrg, payoutId, payoutReference });
                    if (payoutDetail && Object.keys(payoutDetail).length > 0) {
                        const netAmount = MiscUtils.roundToTwo(payoutDetail?.transferData?.net) || 0;
                        const feeAmount = MiscUtils.roundToTwo(payoutDetail?.transferData?.fee) || 0;
                        const settlementDate = payoutDetail?.transferData?.available_on;

                        updateItemGroup('Settlements', 'Settlements', netAmount, 1, lineItemTypes.SETTLEMENT, null, {
                            description: settlementDate,
                            date: new moment(settlementDate, "YYYY-MM-DD").format("M/DD/YYYY"),
                            amount: netAmount,
                            target: payoutId + "|" + payoutReference,
                            targetType: "payout"
                        }, tmpOrgId);
                        itemGroups["Settlements"].total += netAmount;
                        itemGroups["Settlements"].count++;

                        updateItemGroup('Settlement Fees', 'Settlement Fees', feeAmount, 1, lineItemTypes.SETTLEMENT_FEE, null, {
                            description: settlementDate,
                            date: new moment(settlementDate, "YYYY-MM-DD").format("M/DD/YYYY"),
                            amount: feeAmount,
                            target: payoutId + "|" + payoutReference,
                            targetType: "payout"
                        }, tmpOrgId);
                        itemGroups["Settlement Fees"].total += feeAmount;
                        itemGroups["Settlement Fees"].count++;

                        if (settlementDate > "2022-09-30") {
                            const chargebackFeeTotal = MiscUtils.roundToTwo(payoutDetail.transferData.chargebackFee);
                            updateItemGroup('Chargeback Fees', 'Chargeback Fees', chargebackFeeTotal, 1, lineItemTypes.CHARGEBACK_FEE, null, {
                                description: `Chargeback fees for ${settlementDate}`,
                                date: new moment(settlementDate, "YYYY-MM-DD").format("M/DD/YYYY"),
                                amount: chargebackFeeTotal,
                                target: payoutId + "|" + payoutReference,
                                targetType: "payout"
                            }, tmpOrgId);
                            itemGroups["Chargeback Fees"].total += chargebackFeeTotal;
                            itemGroups["Chargeback Fees"].count++;
                        }
                    }
                }
            }
        }

        if (options.includeBankDeposits) {
            const org = await Orgs.findOneAsync({_id: invoiceQuery['orgId']});
            const timezone = org.getTimezone();
            const depositStartDate = new moment.tz(options.startDate, timezone).format("YYYY-MM-DD");
            const depositEndDate = new moment.tz(options.endDate, timezone).format("YYYY-MM-DD");
            const depositQuery = {orgId: invoiceQuery['orgId'], depositDate:{"$gte": depositStartDate, "$lt": depositEndDate}};
            const deposits = await Deposits.find(depositQuery).fetchAsync();

            for (const deposit of deposits) {
                const cashPayerDeposit = await BillingServerUtils.getCashPayerDepositAccount(deposit);
                const description = cashPayerDeposit ? `${cashPayerDeposit.payerName} Deposits`: 'Manual Deposits';
                const lineItem = cashPayerDeposit ? {cashAccountName: cashPayerDeposit.cashAccountName || null} : null;
                updateItemGroup('Manual Deposits', description, deposit.depositTotal, 1, lineItemTypes.MANUAL_DEPOSITS, lineItem, {
                    description: (deposit.depositDate + "-" + deposit._id ) + (deposit.memo ? ` :${deposit.memo}` : ""),
                    date: new moment(deposit.depositDate, "YYYY-MM-DD").format("M/DD/YYYY") ,
                    amount: deposit.originalDepositTotal ?? deposit.depositTotal,
                    targetType: "deposit",
                    target: deposit._id
                }, deposit.orgId);

                itemGroups["Manual Deposits"].total += deposit.originalDepositTotal ?? deposit.depositTotal;
                itemGroups["Manual Deposits"].count++;

                // Loop through deposit history
                if (deposit.history?.length) {
                    const changes = BillingServerUtils.getDepositChanges(deposit);
                    for (const change of changes) {
                        updateItemGroup('Manual Deposits', description, change.depositTotalChange, 1, lineItemTypes.MANUAL_DEPOSITS, lineItem, {
                            description: (deposit.depositDate + "-" + deposit._id) + (deposit.memo ? ` :${ deposit.memo }` : ""),
                            date: new moment(change.date).format("M/DD/YYYY"),
                            amount: change.depositTotalChange,
                            targetType: "deposit",
                            target: deposit._id
                        }, deposit.orgId);
                        itemGroups["Manual Deposits"].total += change.depositTotalChange;
                        itemGroups["Manual Deposits"].count++;
                    }
                }
            }
        }

        const invoicesQuery = {orgId: invoiceQuery['orgId'], createdAt:{"$gte": options.startDate, "$lt": options.endDate}, withholdLedger: {"$ne": true}};
        const chargebackInvoices = await CustomerChargebacksInvoices.find(invoicesQuery).fetchAsync();

        _.each(chargebackInvoices, (cbi) => {
            updateItemGroup('Chargeback Fees', 'Chargeback Fees', cbi.feeTotal, 1, lineItemTypes.CHARGEBACK_FEE, null, {
                description: `Chargeback fees for ${cbi.periodString}`,
                date: new moment(cbi.createdAt).format("M/DD/YYYY") ,
                amount: cbi.feeTotal
            }, cbi.orgId);
            itemGroups["Chargeback Fees"].total += cbi.feeTotal;
            itemGroups["Chargeback Fees"].count++;
        });

        if (hasNonFamilyFunding) {

            const funds = await PayerFunds.find({
                orgId: invoiceQuery['orgId'],
                createdAt: { $gte: options.startDate, $lt: options.endDate },
            }).fetchAsync();
            for (const fund of funds) {
                const allocationSourceDefinition = _.find(org.availablePayerSources(true), ps => ps.type === fund.payerSource);
                const lineItemType = 'payerOverpayment';
                const lineItem = { payerOverpaymentDestination: fund.destination };
                const ledgerAccount = org.getMappedLedgerAccount(lineItemType, lineItem);
                const groupDescriptor = (ledgerAccount?.accountName ?? fund.destination) + ' ' + (allocationSourceDefinition?.description || fund.payerSource);
                const linkedDetail = {
                    description: 'Non-Family Funds: ' + groupDescriptor,
                    date: new moment(fund.createdAt).format('MM/DD/YYYY'),
                    amount: fund.amount,
                    overpaymentDestination: fund.destination
                };
                updateItemGroup(
                    'Payer Overpayments',
                    groupDescriptor,
                    fund.amount,
                    1,
                    'payerOverpayment',
                    lineItem,
                    linkedDetail,
                    fund.orgId
                );
                itemGroups['Payer Overpayments'].total += fund.amount;
                itemGroups['Payer Overpayments'].count++;
            }
        }

        if (isMultiOrgs) {
            // Make sure the orgs appear in the correct order for the report
            _.map(itemGroups, (obj, key) => {
                _.map(obj.itemsGroup, (group, groupKey) => {
                    const totalsArray = [];
                    for (const orgKey in group.orgDetails) {
                        group.orgDetails[orgKey].orgName = orgKey;
                        totalsArray.push(group.orgDetails[orgKey]);
                    }
                    ReportAggregation.applyMeta(totalsArray, orgsMeta, 'orgName');
                    const rekeyedTotals = {};
                    for (const tmp of totalsArray) {
                        rekeyedTotals[tmp.orgName] = tmp;
                    }
                    group.orgDetails = rekeyedTotals;
                    return group;
                });
                return obj;
            });
        }

        return itemGroups;
    }

    /**
     * Retrieves organizational hierarchy map, metadata, and valid organization IDs based on the given options and user context.
     *
     * @param {Object} options - The options for filtering organizations.
     * @param {Array<string>} options.orgIds - List of organization IDs to filter.
     * @param {Object} org - The current organization object.
     * @param {string} org._id - The ID of the current organization.
     * @param {Object} currentUser - The current user context.
     * @param {Function} currentUser.fetchPerson - A function that returns the person data for the current user.
     * @param {Map} orgIdsToNameMap - A map to populate with valid organization IDs and their names.
     *
     * @returns {Object} An object containing the following:
     */
    static async getOrgsMapAndMeta(options, org, currentUser, orgIdsToNameMap) {
        let isMultiOrgs = false;
        let orgsMap = [];
        let orgsMeta = [];
        let validOrgIds = [org._id];

        if (options.orgIds && options.orgIds.length > 0 && currentUser) {
            const cuser = await currentUser.fetchPerson();
            const orgsScope = await cuser?.findScopedOrgs();           
            const orgsScopeList = orgsScope && _.pluck(orgsScope, '_id');
            validOrgIds = _.intersection(orgsScopeList, options.orgIds);

            isMultiOrgs = true;
            const fullOrgs = await Orgs.find().fetchAsync();
            const validOrgs = await Orgs.find({ _id: { $in: validOrgIds } }, { fields: { _id: 1, parentOrgId: 1, name: 1 } }).fetchAsync();
            orgsMap = ReportAggregation.orgHierarchyMap(fullOrgs, validOrgs);
            orgsMeta = ReportAggregation.orgsMeta(fullOrgs, validOrgs);

            for (const validOrg of validOrgs) {
                orgIdsToNameMap.set(validOrg._id, validOrg.name);
            }
        }

        return { isMultiOrgs, orgsMap, orgsMeta, validOrgIds };
    }

    /**
     * Generates a MongoDB query to fetch invoices based on various conditions.
     *
     * @param {Object} options - Options for the query.
     * @param {number} options.startDate - The start date (timestamp) for filtering records.
     * @param {number} options.endDate - The end date (timestamp) for filtering records.
     * @param {boolean} usePeriodDate - Whether to include filtering by `lineItems.periodStartDate`.
     * @param {Array<string>} validOrgIds - List of valid organization IDs to include in the query.
     *
     * @returns {Object} The generated MongoDB query object.
     */
    static generateInvoiceQuery(options, usePeriodDate, validOrgIds) {
        const query = {
            orgId: { "$in": validOrgIds },
            $or: [
                { createdAt: { "$gte": options.startDate, "$lt": options.endDate } },
                { "credits.createdAt": { "$gte": options.startDate, "$lt": options.endDate } },
                { "credits.voidedAt": { "$gte": options.startDate, "$lt": options.endDate } },
                { "credits.refundedAt": { "$gte": options.startDate, "$lt": options.endDate } },
                {
                    "allocationEntries": {
                        "$elemMatch": {
                            "createdAt": { "$gte": options.startDate, "$lt": options.endDate },
                            "destination": { "$in": ["bad-debt", "agency", "collections"] }
                        }
                    }
                },
                {
                    "lineItems.appliedDiscounts.createdAt": {
                        "$gte": options.startDate,
                        "$lt": options.endDate
                    }
                },
                {
                    "lineItems.appliedDiscounts.updatedAt": {
                        "$gte": options.startDate,
                        "$lt": options.endDate
                    }
                },
                {
                    "lineItems.appliedDiscounts.modificationHistory": {
                        "$elemMatch": {
                            "modifiedAt": { "$gte": options.startDate, "$lt": options.endDate }
                        }
                    }
                },
                {
                    "credits.modificationHistory": {
                        "$elemMatch": {
                            "modifiedAt": { "$gte": options.startDate, "$lt": options.endDate }
                        }
                    }
                },
                {
                    "credits.adjustments": {
                        "$elemMatch": {
                            "adjustedAt": { "$gte": options.startDate, "$lt": options.endDate }
                        }
                    }
                }
            ]
        };

        if (usePeriodDate) {
            query["$or"].push(
                {"lineItems.periodStartDate": {"$gte": options.startDate, "$lt": options.endDate}}
            );
        }

        return query;
    }

    /**
     * Maps the posting date to each invoice's line items and their applied discounts.
     *
     * @param {boolean} usePeriodDate - Whether to use the period start date for posting dates.
     * @param {Array<Object>} invoices - The list of invoices to process.
     * @param {Object} org - The organization object providing timezone information.
     * @returns {Array<Object>} The updated list of invoices with posting dates applied.
     */
    static mapPostingDateToInvoiceLineItems(usePeriodDate, invoices, org) {
        return invoices.map(invoice => {
            invoice.lineItems.forEach(lineItem => {
                lineItem.postingDate = (usePeriodDate && lineItem.periodStartDate) || invoice.createdAt;
                this.mapPostingDateToLineItemDiscounts(usePeriodDate, lineItem, org);
            });
            return invoice;
        });
    }


    /**
     * Maps the posting date to applied discounts within a line item.
     *
     * @param {boolean} usePeriodDate - Whether to use the period start date for posting dates.
     * @param {Object} lineItem - The line item containing applied discounts.
     * @param {Object} org - The organization object providing timezone information.
     * @returns {Array<Object>} The updated list of applied discounts.
     */
    static mapPostingDateToLineItemDiscounts(usePeriodDate, lineItem, org) {
        if (!org || !lineItem.appliedDiscounts) {
            return;
        }

        // Ensure appliedDiscounts is an array
        if (!Array.isArray(lineItem.appliedDiscounts)) {
            lineItem.appliedDiscounts = Object.values(lineItem.appliedDiscounts);
        }

        const timezone = org.getTimezone();

        return lineItem.appliedDiscounts.map(discount => {
            discount.postingDate = this.getDiscountPostingDate(discount, usePeriodDate, lineItem, timezone);

            if (discount.modificationHistory?.length) {
                discount.modificationHistory = discount.modificationHistory.map(history => {
                    history.postingDate = this.getDiscountModificationPostingDate(history, usePeriodDate, lineItem, timezone);
                    return history;
                });
            }

            return discount;
        });
    }

    /**
     * Calculates the posting date for a discount based on its creation or voided date.
     *
     * @param {Object} discount - The discount object.
     * @param {boolean} usePeriodDate - Whether to use the period start date for posting dates.
     * @param {Object} lineItem - The line item associated with the discount.
     * @param {string} timezone - The organization's timezone.
     * @returns {number} The calculated posting date as a timestamp.
     */
    static getDiscountPostingDate(discount, usePeriodDate, lineItem, timezone) {
        if (!discount.createdAt) {
            return lineItem.postingDate
        }

        const today = moment.tz(timezone).startOf('day');
        const periodStartDate = moment.tz(lineItem.periodStartDate, timezone);
        const actionDate = moment.tz(discount.voidedAt || discount.createdAt, timezone);

        return this.returnPeriodStartOrActionDateBasedOnToday(usePeriodDate, actionDate, periodStartDate, today).startOf('day').valueOf();
    }

    /**
     * Calculates the posting date for a discount's modification history.
     *
     * @param {Object} history - A modification history entry for a discount.
     * @param {boolean} usePeriodDate - Whether to use the period start date for posting dates.
     * @param {Object} lineItem - The line item associated with the discount.
     * @param {string} timezone - The organization's timezone.
     * @returns {number} The calculated posting date as a timestamp.
     */
    static getDiscountModificationPostingDate(history, usePeriodDate, lineItem, timezone) {
        if (!history.modifiedAt) {
            return lineItem.postingDate
        }

        const today = moment.tz(timezone).startOf('day');
        const periodStartDate = moment.tz(lineItem.periodStartDate, timezone);
        const actionDate = moment.tz(history.modifiedAt, timezone);

        return this.returnPeriodStartOrActionDateBasedOnToday(usePeriodDate, actionDate, periodStartDate, today).startOf('day').valueOf();
    }

    /**
     * Determines the appropriate posting date based on the current date, period start date, and action date.
     *
     * @param {boolean} usePeriodDate - Whether to use the period start date for posting dates.
     * @param {Object} actionDate - The action date as a moment object.
     * @param {Object} periodStartDate - The period start date as a moment object.
     * @param {Object} today - The current date as a moment object.
     * @returns {Object} The appropriate posting date as a moment object.
     */
    static returnPeriodStartOrActionDateBasedOnToday(usePeriodDate, actionDate, periodStartDate, today) {
        if (!usePeriodDate) {
            return actionDate;
        }

        if (today.isBefore(periodStartDate)) {
            return periodStartDate;
        }

        return actionDate;
    }

    /**
     * Filters invoices by their period start date.
     *
     * @param {Array<Object>} invoices - The list of invoices to filter.
     * @param {Object} options - Filtering options.
     * @param {number} options.periodStartDate - The start of the period for filtering.
     * @param {number} options.periodStartDateEnd - The end of the period for filtering.
     * @returns {Array<Object>} The filtered list of invoices.
     */
    static filterInvoicesByPeriodDate(invoices, options) {
        if (!invoices || !Array.isArray(invoices)) {
            return [];
        }

        const { periodStartDate, periodStartDateEnd } = options;
        return invoices.filter(invoice => !periodStartDate || invoice.lineItems.find(lineItem => lineItem.periodStartDate >= periodStartDate && lineItem.periodStartDate <= periodStartDateEnd ));
    }

    /**
     * Processes applied discounts for a line item and applies them to the appropriate group.
     *
     * @param {Array<Object>} lineItemAppliedDiscounts - The applied discounts for the line item.
     * @param {Object} processOptions - Options for processing the discounts.
     * @param {Object} processOptions.lineItem - The line item being processed.
     * @param {Object} processOptions.options - Additional options for processing discounts.
     * @param {Object} processOptions.invoice - The invoice associated with the line item.
     * @param {Object} processOptions.org - The organization object.
     * @param {string} processOptions.invoiceNameDescriptor - A descriptor for the invoice name.
     * @param {string} processOptions.personDefaultGroupName - The default group name for the person.
     * @param {Function} processOptions.applyItemToGroup - A function to apply the processed item to the appropriate group.
     */
    static processLineItemAppliedDiscounts(lineItemAppliedDiscounts, processOptions) {
        const { lineItem, options, invoice, org, invoiceNameDescriptor, personDefaultGroupName, applyItemToGroup, additionalColumnsForLinkedDetails={} } = processOptions;
        if (lineItem.type === PLAN_TYPE) {
            lineItemAppliedDiscounts = MiscUtils.convertNumericKeyedObjectToArray(lineItemAppliedDiscounts);
            lineItemAppliedDiscounts.forEach((discount) => {
                const planCharges = LedgerDetailServiceUtils.planChargeFromAppliedDiscounts(lineItem, discount, org, invoice, invoiceNameDescriptor, personDefaultGroupName, options, additionalColumnsForLinkedDetails);
                planCharges.forEach((planCharge) => {
                    if (planCharge) {
                        applyItemToGroup(planCharge);
                    }
                });
            });
        } else {
            lineItemAppliedDiscounts.forEach((discount) => {
                const groupSelector = "Item Discounts";
                const detailDescription = invoiceNameDescriptor + "Item discount for invoice " + invoice.invoiceNumber;
                const appendDescription = "Item discount - ";
                const itemDiscount = LedgerDetailServiceUtils.itemDiscountObject(invoice, discount, org, personDefaultGroupName, invoiceNameDescriptor, groupSelector, detailDescription, appendDescription, additionalColumnsForLinkedDetails);
                applyItemToGroup(itemDiscount);
            });
        }
    }

    /**
     * Processes non-voided line items by generating charges and applying them to the appropriate group.
     *
     * @param {Object} processOptions - Options for processing the line item.
     * @param {Object} processOptions.lineItem - The line item being processed.
     * @param {Object} processOptions.options - Additional options for processing.
     * @param {Object} processOptions.invoice - The invoice associated with the line item.
     * @param {Object} processOptions.org - The organization object providing additional context.
     * @param {string} processOptions.invoiceNameDescriptor - Descriptor for the invoice name.
     * @param {string} processOptions.personDefaultGroupName - The default group name for the person.
     * @param {Function} processOptions.applyItemToGroup - A function to apply the processed item to the appropriate group.
     */
    static processNonVoidedLineItem(processOptions) {
        const { lineItem, options, invoice, org, invoiceNameDescriptor, personDefaultGroupName, applyItemToGroup, additionalColumnsForLinkedDetails={} } = processOptions
        if (LedgerDetailServiceUtils.activePostingDateNotVoided(lineItem, options, invoice)) {
            if (lineItem.type === PLAN_TYPE) {
                const planCharges = LedgerDetailServiceUtils.planChargeFromDailyChargedMonthly(lineItem, org, invoiceNameDescriptor, invoice, personDefaultGroupName, options, additionalColumnsForLinkedDetails);
                planCharges.forEach((planCharge) => {
                    if (planCharge) {
                        applyItemToGroup(planCharge);
                    }
                })
            } else {
                const detailDescription = invoiceNameDescriptor + "Item charge for invoice " + invoice.invoiceNumber;
                const groupSelector = "Item Charges";
                const itemCharge = LedgerDetailServiceUtils.itemChargeObject(lineItem, invoice, personDefaultGroupName, detailDescription, groupSelector, additionalColumnsForLinkedDetails);
                applyItemToGroup(itemCharge);
            }
        }
    }

    /**
     * Processes modified credits and creates the corresponding item group updates.
     *
     * @param {Object} credit - The credit with modification history.
     * @param {Object} itemGroups - The collection of item groups to be updated.
     * @param {Object} invoice - The invoice associated with the credit.
     * @param {string} invoiceNameDescriptor - Descriptor for the invoice name.
     * @param {string} personDefaultGroupName - Default group name for the person.
     * @param {Date|string} startDate - The start date of the report range.
     * @param {Date|string} endDate - The end date of the report range.
     * @param {Object} additionalColumnsForLinkedDetails - Additional properties to include in linked detail objects.
     * @returns {Array<Object>} An array of item group updates for the modified credits.
     */
    static processModifiedCredit(credit, itemGroups, invoice, invoiceNameDescriptor, personDefaultGroupName, startDate, endDate, additionalColumnsForLinkedDetails={}) {
        const itemGroupUpdates = [];
        for (const history of credit.modificationHistory) {
            const modificationHappenedWithinReportRange = LedgerDetailServiceUtils.modifiedCreditIsWithinReportRange(history.modifiedAt, startDate, endDate);

            if (!modificationHappenedWithinReportRange) {
                continue;
            }

            const linkedDetail = LedgerDetailServiceUtils.processModifiedCreditLinkedDetail(invoice, history, invoiceNameDescriptor, personDefaultGroupName, additionalColumnsForLinkedDetails);
            const groupDescriptor = StringUtils.capitalizeFirstLetter(credit.creditReason.replace(/\_/g,' '));
            const amountDidIncrease = history.amountModified > 0;
            const amountModified = Math.abs(history.amountModified) || 0;
            const update = {
                group: "Modified Credits",
                descriptor: groupDescriptor,
                amount: history.amountModified,
                count: 1,
                type: lineItemTypes.CREDIT_MODIFIED,
                credit,
                linkedDetail,
                orgId: invoice.orgId,
                modifiedDiscount: null,
                amountIncreased: 0,
                amountDecreased: 0
            }

            if (amountDidIncrease) {
                update.amountIncreased = amountModified;
                itemGroups["Modified Credits"].amountIncreased += amountModified;
            } else {
                update.amountDecreased = amountModified;
                itemGroups["Modified Credits"].amountDecreased += amountModified;
            }

            itemGroupUpdates.push(update)
            itemGroups["Modified Credits"].total += history.amountModified;
            itemGroups["Modified Credits"].count++;
        }
        return itemGroupUpdates;
    }
}

const convertTimestamp = (timestamp, fallbackDatetime) => {
    try {
        // Convert to string and remove any decimal points
        const timestampStr = timestamp.toString().split('.')[0];

        // Check if the timestamp is in microseconds (16 digits) or seconds (10 digits)
        let date;
        if (timestampStr.length > 13) {
            // Convert microseconds to milliseconds
            date = new Date(parseInt(timestampStr) / 1000);
        } else {
            // Assume it's in seconds, convert to milliseconds
            date = new Date(parseInt(timestampStr) * 1000);
        }

        // Check if the date is valid
        if (isNaN(date.getTime())) {
            throw new Error('Invalid date');
        }

        return date;
    } catch (error) {
        Log.error(`Error converting timestamp: ${error.message}. Using fallback datetime.`);
        return new Date(fallbackDatetime);
    }
};

const formatDate = (date) => {
    return date.toLocaleDateString("en-US", {month: "numeric", day: "2-digit", year: "numeric"});
};
