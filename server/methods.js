import Stripe from 'stripe';
const AWS = require("aws-sdk");
import { Meteor } from 'meteor/meteor';
const stripe = new Stripe(Meteor.settings.stripe.secretKey, { apiVersion: "2019-08-14" });
import AES from 'crypto-js/aes';
import Utf8 from 'crypto-js/enc-utf8';
import CryptoJS from 'crypto-js';
const moment = require('moment-timezone');
import currency from 'currency.js';
const dot = require('dot-object');
import { AdyenProvider } from './card_providers/adyenProvider';
import { ChildcareCRM } from './childcareCrmService';
import { KinderConnect } from './kinderConnectService';
import { checkWithoutPin } from './reports/checkWithoutPin';
import { Roster } from './californiaReports/roster';
import { staffTimeReportAggregates } from './reports/staffTimeReportAggregates';
import { chargesAttendance } from './reports/chargesAttendance';
import { Reports } from './billingPlanEnrollmentsReport/reports';
import fs from "fs";
import { ReportAggregation } from "./reports/reportAggregation";
import { DreamboxService } from "./dreamboxService";
import { WaitListReport } from './reports/wait_list_report';
import { PeopleUtils } from './people/peopleUtils';
import { BillingUtils } from '../lib/util/billingUtils';
import { ChildcareCrmUtil } from "./childcareCrmUtil";
import { StaffApiService } from "./api/staffApiService";
import { BillingInvoiceService } from './billingInvoiceService';
import { CurriculumUtils } from '../lib/activities/curriculumUtils';
import { OrgsUtil } from "../lib/util/orgsUtil";
import { AdpService } from "./adpService";
import { DraftUtils } from "../lib/util/draftUtils";
import { PayerUtils } from "../lib/util/payerUtils";
import { CrmFixService } from "./crmFixService";
import { AvailableCustomizations } from "../lib/customizations";
import { PinService } from "./pinService";
import { ProfileReportService } from "./profileReportService";
import { EmailServerUtils } from './emailServerUtils';
import { ReportGeneralAttendanceService } from './reports/reportGeneralAttendanceService';
import { BillingReportAgingService } from './reports/billingReportAgingService';
import { LedgerDetailService } from "./ledgerDetailService";
import { CustomizableOrgProperties } from '../lib/constants/orgConstants';
import { Log } from '../lib/util/log';
import { BenchmarkService } from "./analytics/benchmarkService";
import { GenerateManualInvoiceService } from '../lib/generateManualInvoiceService';
import { PinCodeCheckinService } from './pinCodeCheckinService';
import { Cache } from '../lib/util/cacheUtils';
import { CACHE_KEYS } from '../lib/constants/cacheConstants';
import { MessagesUtils } from '../lib/util/messagesUtils';
import { ReportsUtil } from "./reportsUtil";
import { OrgCreationService } from './orgs/orgCreationService';
import { AbleToWorkAtService } from "../lib/ableToWorkAtService";
import { AwsBillingService } from "./awsBillingService";
import { AWSCognitoService } from '../api/v2/services/cognitoService';
const cognitoService = new AWSCognitoService();
import { ReportAdpService } from "./reports/reportAdpService";
import { HistoryAuditService } from './historyAuditService';
import { HistoryAuditChangeTypes, HistoryAuditPeoplePerformedByNames, HistoryAuditRecordTypes } from '../lib/constants/historyAuditConstants';
import { UnknownProblem } from '../api/v2/exceptions/unknownProblem';
import { UsersRepository } from "../api/v2/repositories/usersRepository";
import { SSR } from '../lib/util/ssrUtils';
import { Blaze } from "meteor/blaze";
import { AVAILABLE_WEEKDAYS } from '../lib/constants/enrollmentConstants';
import { USER_TYPES } from '../lib/constants/profileConstants';
import { EnrollmentsService } from './enrollments/enrollmentsService';
import { RelUtils } from "../lib/util/relUtils";
import { People } from '../lib/collections/people';
import { ScheduleUtils } from '../lib/util/scheduleUtils';
import { AvailableActionTypes, AvailablePermissions } from '../lib/constants/permissionsConstants';
import { InvoiceModificationService } from './invoices/invoiceModificationService';
import { InvoiceUpdateService } from '../lib/invoiceUpdateService';
import { MembershipService } from "./people/membershipService";
import { ErrorCodes } from '../lib/constants/errorConstants';
import { Relationships } from '../lib/collections/relationships';
import { processPermissions } from '../lib/permissions';
import { NewMessages } from '../lib/collections/newMessages';
import { SharedConfigs } from './collections/sharedConfigs';
import { ChildcareCrmAccounts } from '../lib/collections/childcareCrmAccounts';
import { Orgs } from '../lib/collections/orgs';
import _ from '../lib/util/underscore';
import {
	getFteForMonth,
	getGroupMemorizedRateAndDiscount,
	getRoomEnrollmentsData,
	personPayCalculation,
	sortGroupsByAge,
	timeCardPersonPayCalculation
} from './classList';
import { UserInvitations } from '../lib/collections/userInvitations';
import { Reservations } from '../lib/collections/reservations';
import { Groups } from '../lib/collections/groups';
import { Curriculums } from '../lib/collections/curriculum';
import { CurriculumBanks } from '../lib/collections/curriculumBank';
import { CurriculumThemeBanks } from '../lib/collections/curriculumThemeBank';
import { CurriculumThemes } from '../lib/collections/curriculumThemes';
import { OnHoldRegistrations } from '../lib/collections/onHoldRegistrations';
import { creditFamilyPersonInvoices, generateEmailWhiteLabelData, invoicePerson } from './util';
import { addMailchimpUser, processAnalytics } from './analytics';
import { Invoices } from '../lib/collections/invoices';
import { processBillingEmail } from './processBillingEmail';
import { SavedDrafts } from '../lib/collections/savedDrafts';
import { MappedLedgerEntriesForRange } from './methodsBilling';
import { AuditLedgerBatches } from './collections/auditLedgerBatches';
import { Messages } from '../lib/collections/messages';
import { scheduleGroupDashboardRecalculation } from './agenda/agendaScheduler';
import { createInvitationCreateUser, deactivatePersonUpdateUser, registerUserUpdateUser, userHasMembership } from './usersAndPeople';
import { Announcements } from '../lib/collections/announcements';
import { processSummaryMail2021 } from './emails/v2021/processSummaryMail2021';
import { afterInsertMomentTimeCard, afterUpdateMomentTimeCard, afterDeleteMomentTimeCard } from './timeCardMomentCallbacks';
import { DateTimeUtils } from '../lib/util/dateTimeUtils';
import { Moments } from '../lib/collections/moments';
import { createWmgEngineApi, sendWmgStateResetMessage, signWmgConfigToken, signWmgEngineToken, signWmgPlayerToken } from './wmgHelpers';
import { processRealtime } from './processRealtime';
import { Foods } from '../lib/collections/food';
import { MPSurveysEMA } from '../lib/collections/mpSurveyEMA';
import { TimeCards } from '../lib/collections/timeCards';
import { AuditCarePlans } from './collections/auditCareplans';
import { AuditLedgerBatchesHistorical } from './collections/auditLedgerBatchesHistorical';
import { DebugLogs } from './collections/debugLogs';
import { OrgAdditionalDatas } from './collections/orgAdditionalDatas';
import { processInvitation } from './processInvitation';
import { sendSMSMessage } from './snsHelper';
import { OrgServerUtils } from './orgs/orgServerUtils';
import { Metrics } from './analytics/metrics';
import { processOnboardingVerificationCodeEmail } from './processOnboardingEmails';
import { processTwoFactorEmail } from './processTwoFactorEmail';
import { Engagements } from '../lib/collections/engagements';
import { QrCodes } from './collections/qrCodes';
import { searchPeople } from './search';
import { generatePeekHtmlPopOut } from './generatePeekHtmlPopOut';
import logger from "../imports/winston";
import { AttendanceGridByDateService } from './reports/attendanceGridByDateService';
import { InvoicesService } from './invoices/invoicesService';
import { scheduleBookingUtils } from '../lib/util/scheduleBookingUtils';
import { ReservationService } from './services/reservationService';

const expressDriveUpArrivalTime = {
	"~10 minutes away": 10,
	"~20 minutes away": 20,
	"~30 minutes away": 30,
	"30+ minutes away": 45,
	"Remove arrival time": null,
};

const usersRepository = new UsersRepository();

Meteor.methods({
	checkWithoutPin,
	chargesAttendance,
	async fetchInvoices(options) {
		check(options, Match.Maybe(Object));

		if (!this.userId) {
		throw new Meteor.Error("not-authorized");
		}

		const user = await Meteor.users.findOneAsync(this.userId);
		if (!user) throw new Meteor.Error("user-not-found");

		const userPerson = await user.fetchPerson();
		let query = { orgId: user.orgId };

		if (options?.status === "open") {
		query["openAmount"] = { "$gt": 0 };
		}
		if (options?.status === "pastdue") {
		query["openAmount"] = { "$gt": 0 };
		query["dueDate"] = { "$lte": new moment().valueOf() };
		}
		if (options?.dateSince) {
		const dateMap = {
			last14: 14,
			last30: 30,
			last90: 90
		};
		if (dateMap[options.dateSince]) {
			query["createdAt"] = {
			"$gt": new moment().subtract(dateMap[options.dateSince], "days").valueOf()
			};
		}
		}

		if (options?.startDate || options?.endDate) query["createdAt"] = {};
		if (options?.startDate) query["createdAt"]["$gte"] = options.startDate;
		if (options?.endDate) query["createdAt"]["$lt"] = options.endDate;
		if (options?.invoiceId) query["_id"] = options.invoiceId;

		const targetPerson = options?.targetPersonId && await People.findOneAsync({
		orgId: user.orgId,
		_id: options.targetPersonId
		});

		const adminAccess = await processPermissions({
		assertions: [{ context: "billing/invoices", action: "read" }],
		evaluator: (person) => person.type === "admin"
		});

		if (userPerson.type === "family") {
		const connectedPeople = _.map(
			await Relationships.find({ orgId: user.orgId, personId: user.personId, relationshipType: "family" }).fetchAsync(),
			(r) => r.targetId
		);
		if (options?.targetPersonId && _.contains(connectedPeople, options.targetPersonId)) {
			query["personId"] = options.targetPersonId;
		} else {
			query["personId"] = { $in: connectedPeople };
		}
		} else if (options?.targetPersonId && adminAccess) {
		if (targetPerson?.type === "family") {
			const connectedPeople = _.map(
			await Relationships.find({ orgId: user.orgId, personId: targetPerson._id, relationshipType: "family" }).fetchAsync(),
			(r) => r.targetId
			);
			query["personId"] = { $in: connectedPeople };
		} else {
			query["personId"] = options.targetPersonId;
		}
		}

		if ((adminAccess && options) || userPerson.type === "family") {
			const invoices = await Invoices.find(query, { sort: { createdAt: -1 } }).fetchAsync();
			return invoices;
		} else {
			return [];
		}
	},
	async getInvoices(query) {
		return await InvoicesService.getInvoice(query);
	},
	async getInvoiceById(query) {
		return await InvoicesService.getInvoiceById(query);
	},
	async updateCreditCardInfo(options) {
		this.unblock();
		var currentUser = await Meteor.userAsync();
		const cuser = await currentUser.fetchPerson();
		if (!currentUser ||
			cuser?.type != "admin")
			throw new Meteor.Error(403, "Access denied");

		var selectedPlan = options.planType ? options.planType : "standard";

		const org = await Orgs.findOneAsync({ _id: currentUser.orgId });

		let stripeCustomerId = org.stripeCustomerId;
		if (!stripeCustomerId) {

			const customer = await stripe.customers.create({
				email: currentUser.fetchPrimaryEmail(),
				source: options.token.id,
			});

			stripeCustomerId = customer.id;

			const subscriptionMap = await haredConfigs.findOneAsync({ "key": "subscriptionMap" });

			const selectedPlanDetail = subscriptionMap.data[selectedPlan];

			const subscription = await stripe.subscriptions.create({
				customer: stripeCustomerId,
				items: [{ plan: selectedPlanDetail.stripeId }],
			});

			let planDetails = {
				plan: selectedPlan,
				planCode: selectedPlanDetail.stripeId,
				description: selectedPlanDetail.description,
				subscriptionId: subscription.id,
				createdAt: new Date().valueOf(),
				cretedBy: "IN-APP REGISTRATION"
			};

			await Orgs.updateAsync({ _id: org._id }, {
				$set: {
					stripeCustomerId: stripeCustomerId,
					planDetails: planDetails
				}
			});
		} else {
			const customer = await stripe.customers.update(
				stripeCustomerId, {
				source: options.token.id
			});
		}

		return;
	},
	async activateSubscription(options) {
		this.unblock();
		var currentUser = await Meteor.userAsync();
		const cuser = await currentUser.fetchPerson();
		if (!currentUser ||
			cuser?.type != "admin")
			throw new Meteor.Error(403, "Access denied");

		const seatCount = parseInt(options.seatCount || 0),
			selectedPlan = options.selectedPlan,
			org = await Orgs.current();
		let term = options.term;
		console.log(seatCount, selectedPlan, term);

		if ((selectedPlan != "recovery-suite" && seatCount <= 0) || !_.contains(["recovery-suite", "core", "advanced"], selectedPlan) || !_.contains(["annual", "monthly"], term))
			throw new Meteor.Error(500, "It looks like something is wrong with the plan you've selected.  Please check your details and try again.");
		if (!org.planPaymentSource)
			throw new Meteor.Error(500, "Please make sure to setup a payment type before activating your account.");

		let price;
		if (selectedPlan == "recovery-suite") {
			term = "annual";
			price = 249.0;
		} else {
			if (selectedPlan == "core") price = 2.0 * seatCount;
			if (selectedPlan == "advanced") price = 3.0 * seatCount;
			if (term == "annual" && selectedPlan != "recovery-suite") price = price * 12 * 0.9;
		}

		const planDescription = selectedPlan + " " + numeral(price).format("$0.00") + " / " + term;

		const paymentLine = await AdyenProvider.payOrgInvoice({
			orgId: org._id,
			amount: price,
			description: "MomentPath subscription initial fee " + planDescription,
			createdForPersonId: currentUser.personId
		});

		console.log("paymentLine", paymentLine);

		if (paymentLine) {
			await Orgs.updateAsync({ _id: org._id }, {
				"$set": {
					"planDetails.plan": selectedPlan,
					"planDetails.term": term,
					"planDetails.seatCount": seatCount,
					"planDetails.activated": true,
					"planDetails.activationDate": new moment().format("YYYY-MM-DD")
				}
			});
		}
	},
	async 'getOrgInvoices'(options) {
		this.unblock();
		var currentUser = await Meteor.userAsync();
		const cuser = await currentUser.fetchPerson();
		if (!currentUser ||
			cuser?.type != "admin")
			throw new Meteor.Error(403, "Access denied");

		const startDate = new moment(options.startDate, "MM/DD/YYYY").valueOf(),
			endDate = new moment(options.endDate, "MM/DD/YYYY").add(1, "day").valueOf(),
			currentOrg = await Orgs.current();
		console.log("orgId", currentOrg._id, startDate, endDate);
		return await OrgInvoices.find({ orgId: currentOrg._id, createdAt: { "$gte": startDate, "$lt": endDate } }).fetchAsync();
	},
	async 'getStripePlanBillingInfo'() {
		var currentUser = await Meteor.userAsync();
		const cuser = await currentUser.fetchPerson();
		if (!currentUser ||
			cuser?.type != "admin")
			throw new Meteor.Error(403, "Access denied");

		const org = await Orgs.findOneAsync({ _id: currentUser.orgId });

		let stripeCustomerId = org.stripeCustomerId;
		if (stripeCustomerId) {
			const customer = await stripe.customers.retrieve(stripeCustomerId);
			if (customer) {
				return {
					validChargeAccount: true,
					subscriptions: customer.subscriptions && customer.subscriptions.data && customer.subscriptions.data[0],
					source: customer.sources && customer.sources.data && _.chain(customer.sources.data)
						.filter((s) => { return s.id == customer.default_source; })
						.map((s) => { return s.object == 'card' ? { label: s.brand + " ends in " + s.last4 } : "other" })
						.first()
						.value()
				}
			}
		}

		return {
			validChargeAccount: false
		}
	},
	'stripeCreateCustomer': async function (token, planCode, planQuantity) {
		this.unblock();
		var currentUser = await Meteor.userAsync();
		if (!currentUser ||
			cuser?.type != "admin")
			throw new Meteor.Error(403, "Access denied");
		var org = await Orgs.current();

		var stripeCustomerData = {
			source: token,
			email: currentUser.fetchPrimaryEmail(),
			plan: planCode
		};
		if (planQuantity && planQuantity === parseInt(planQuantity, 10))
			stripeCustomerData.quantity = planQuantity;
		try {
			const customer = await stripe.customers.create(stripeCustomerData);
			return customer;
		} catch (e) {
			return e;
		}
	},
	'hasChildcareCrm': async function (orgId) {
		this.unblock();
		return Boolean(await ChildcareCrmUtil.getChildcareCrmAccount(orgId));
	},
	'resendInvitation': async function (invitationId) {
		this.unblock();
		var currentUser = await Meteor.userAsync();
		const cuser = await currentUser.fetchPerson();
		if (!currentUser ||
			cuser?.type != "admin")
			throw new Meteor.Error(403, "Access denied");
		var org = await Orgs.current();
		var invite = await UserInvitations.findOneAsync({ _id: invitationId, orgId: org._id });
		if (invite) {
			await processInvitation(invitationId);
			return { "processed": true };
		} else {
			throw new Meteor.Error(500, "couldn't find that invitation");
		}
	},
	'resendInvitationByPersonId': async function (personId) {
		this.unblock();
		var currentUser = await Meteor.userAsync();
		const cuser = await currentUser.fetchPerson();
		if (!currentUser ||
			cuser?.type != "admin")
			throw new Meteor.Error(403, "Access denied");

		var targetPerson = await People.findOneAsync({ _id: personId });
		var inviteQuery = { personId: personId, orgId: currentUser.orgId, used: { $ne: true } };
		var user = await targetPerson.findAssociatedUser();
		var invite = await UserInvitations.findOneAsync(inviteQuery);
		if (invite) {
			if (user && user.emails && user.emails.length > 0 && invite.email != user.emails[0].address)
				await UserInvitations.updateAsync({ _id: invite._id }, { $set: { email: user.emails[0].address } });
			await processInvitation(invite._id);
			return { "processed": true };
		}
		else {
			throw new Meteor.Error(500, "couldn't find an invitation for user");
		}
	},
	'resendAllInvitations': async function () {
		this.unblock();
		var currentUser = await Meteor.userAsync();
		const cuser = await currentUser.fetchPerson();
		if (!currentUser || cuser?.type != "admin")
			throw new Meteor.Error(403, "Access denied");

		const familyIdsData = await People.find({ orgId: currentUser.orgId, type: "family", inActive: { "$ne": true } }, { fields: { _id: 1 } }).fetchAsync();
		const familyIds = familyIdsData.map(p => p._id);
		const removeFamilyIds = (await Meteor.users.find({ orgId: currentUser.orgId, personId: { "$in": familyIds }, pending: { $ne: true } }, { fields: { personId: 1 } }).fetchAsync()).map(p => p.personId);
		const filteredFamilyIds = familyIds.filter(fid => !removeFamilyIds.includes(fid));

		const familiesToSendInvites = await People.find({ _id: { $in: filteredFamilyIds } }).fetchAsync();
		const personIds = familiesToSendInvites.map(p => p._id);
		const openInvites = await UserInvitations.find({ orgId: currentUser.orgId, personId: { $in: personIds }, used: false }).fetchAsync();
		const openInviteMap = openInvites.reduce((map, invite) => {
			map[invite.personId] = invite;
			return map;
		}, {});

		for (const p of familiesToSendInvites) {
			const openInvite = openInviteMap[p._id];
			if (openInvite) {
				await processInvitation(openInvite._id);
			} else {
				const emailAdd = await p.getEmailAddress();
				if (emailAdd) {
					var invitationId = await UserInvitations.insertAsync({
						token: tokenString(),
						email: emailAdd,
						orgId: p.orgId,
						personId: p._id,
						used: false
					});
					await processInvitation(invitationId);
				}
			}
		}
	},
	'checkEmailExistsForOrg': async function (emailAddress, orgIdFromRegistration = null, regId = null, noCognito = false) {
		this.unblock();
		const user = await Meteor.userAsync();
		const orgId = orgIdFromRegistration || user['orgId'];
		if (!orgId) {
			throw new Meteor.Error('404', 'No org found for user');
		}

		const emailExistsMessage = await EmailServerUtils.emailExists(emailAddress, orgId, regId, noCognito);
		return emailExistsMessage;
	},

	'checkIfUserHasEnroll': async function (user, orgId) {
		this.unblock();
		if (!user || !orgId) {
			return null;
		}

		const crmAccount = await ChildcareCrmAccounts.findOneAsync({ 'centers': { $elemMatch: { 'orgId': orgId } } });
		if (!crmAccount) {
			return null;
		}

		const staffApiService = new StaffApiService();
		const token = await ChildcareCRM.login({ username: crmAccount.username, password: crmAccount.password });
		if (!token) {
			return null;
		}
		const headers = { 'Content-Type': 'application/json', 'Authorization': `Bearer ${token}`, 'X-UI-Request': true };
		try {
			return await staffApiService.findMatchingEnrollStaff(headers, user._id);
		} catch (e) {
			console.log('Error fetching enroll user: ', e);
		}
	},

	'updateEnrollRecoveryEmail': async function (enrollUser, newEmail, orgId) {
		this.unblock();
		if (!enrollUser || !newEmail || !orgId) {
			return null;
		}

		enrollUser.recovery_email = newEmail;
		const staffApiService = new StaffApiService();
		try {
			return await staffApiService.updateEnrollStaffRecoveryEmail(enrollUser, orgId);
		} catch (e) {
			console.log('Error updating enroll recovery email: ', e);
		}
	},

	'removeUserFromPool': async function (emailAddress, orgId, personId) {
		this.unblock();
		const user = await cognitoService.searchUsers('username', emailAddress);
		if (user.length) {
			await cognitoService.deleteUser(user[0].Username);
		}
		const person = await People.findOneAsync({ _id: personId });
		const meteorUser = await person?.findAssociatedUser();
		if (meteorUser) {
			// make sure the user cannot log in
			const crypto = require('crypto');
			await Accounts.setPasswordAsync(meteorUser._id, crypto.randomBytes(32).toString('hex'));
		}
		// clean out invitations for the person
		await UserInvitations.removeAsync({ orgId, personId });
	},
	async dashboardAggregates() {
		this.unblock();
		var currentUser = await Meteor.userAsync();
		const cuser = await currentUser.fetchPerson();
		if (!currentUser ||
			cuser?.type != "admin")
			throw new Meteor.Error(403, "Access denied");

		var moments = await Moments.aggregate(
			[
				{
					$match: {
						orgId: currentUser.orgId,
						momentType: "food",
						sortStamp: { $gte: new moment().startOf("month").valueOf() }
					}
				},
				{
					$group: {
						_id: "$foodType",
						total: {
							$sum: {
								$size: "$taggedPeople"
							}
						}
					}
				}
			]
		);
		return await moments.toArray();
	},
	async engagementAggregates() {
		this.unblock();
		var currentUser = await Meteor.userAsync();
		const cuser = await currentUser.fetchPerson();
		if (!currentUser ||
			cuser?.type != "admin")
			throw new Meteor.Error(403, "Access denied");

		var startDateNum = new moment().subtract(14, 'd').startOf('day').valueOf();

		var engagementTypeDataCursor = await Engagements.aggregate(
			[
				{
					$match: {
						orgId: currentUser.orgId,
						createdAt: { $gte: startDateNum }
					}
				},
				{
					$group: {
						_id: { $concat: ["$type", "_", "$subType"] },
						num: { $sum: 1 }
					}
				},
				{
					$sort: { num: -1 }
				}
			]
		);
		var engagementTypeData = await engagementTypeDataCursor.toArray();

		var engagementPersonRawDataCursor = await Engagements.aggregate(
			[
				{
					$match: {
						orgId: currentUser.orgId,
						createdAt: { $gte: startDateNum }
					}
				},
				{
					$group: {
						_id: "$targetPersonId",
						num: { $sum: 1 }
					}
				},
				{
					$sort: {
						num: -1
					}
				}
			]
		);
		var engagementPersonRawData = await engagementPersonRawDataCursor.toArray();

		var engagementPersonData =
			_.chain(await People.find({ orgId: currentUser.orgId, type: "person", inActive: { $ne: true } }).fetchAsync())
				.map(function (p) {
					var rec = _.find(engagementPersonRawData, function (x) { return x._id == p._id; })
					return { _id: p._id, label: p.lastName + ', ' + p.firstName, groupId: p.defaultGroupId, num: rec ? rec.num : 0 };
				})
				.sortBy(function (p) { return -1 * p.num; })
				.first(10)
				.value();

		var engagementGroupData =
			_.chain(await Groups.find({ orgId: currentUser.orgId }).fetchAsync())
				.map(function (g) {
					var groupCount = 0;
					_.each(
						_.filter(engagementPersonData, function (p) { return p.groupId == g._id; }),
						function (p) { groupCount += p.num }
					);
					return { label: g.name, num: groupCount };
				})
				.value();

		var engagementTrendRawDataCursor = await Engagements.aggregate(
			[
				{
					$match: {
						orgId: currentUser.orgId,
						createdAt: { $gte: startDateNum }
					}
				},
				{
					$group: {
						_id: {
							year: { $year: { $add: [new Date(0), "$createdAt"] } },
							month: { $month: { $add: [new Date(0), "$createdAt"] } },
							day: { $dayOfMonth: { $add: [new Date(0), "$createdAt"] } }
						},
						num: { $sum: 1 }
					}
				},
				{
					$sort: {
						_id: 1
					}
				}
			]
		);
		var engagementTrendRawData = await engagementTrendRawDataCursor.toArray();

		var engagementTrendData = [];
		_.each(_.range(14), function (d) {
			var curDay = new moment().subtract(13 - d, 'days');
			var dayData = _.find(engagementTrendRawData, function (t) {
				return t._id.year == curDay.year() && t._id.month == curDay.month() + 1 && t._id.day == curDay.date();
			});
			engagementTrendData.push({
				label: curDay.format("M/DD"),
				num: dayData ? dayData.num : 0
			});
		});

		return {
			typeData: engagementTypeData,
			personData: engagementPersonData,
			groupData: engagementGroupData,
			trendData: engagementTrendData
		};
	},
	async mealsReportAggregates(options) {
		this.unblock();
		var currentUser = await Meteor.userAsync();
		const cuser = await currentUser.fetchPerson();
		if (!currentUser ||
			cuser?.type != "admin")
			throw new Meteor.Error(403, "Access denied");

		var startDateNum = DateTimeUtils.getDatestampInTimezone(options.startDate, (await Orgs.current()).getTimezone());
		var endDateNum = DateTimeUtils.getDatestampInTimezone(options.endDate, (await Orgs.current()).getTimezone(), "endOf");

		var moments = await Moments.aggregate(
			[
				{
					$match: {
						orgId: currentUser.orgId,
						momentType: "food",
						sortStamp: { $gte: startDateNum, $lte: endDateNum }
					}
				},
				{
					$group: {
						_id: {
							year: { $year: { $add: [new Date(0), "$createdAt"] } },
							month: { $month: { $add: [new Date(0), "$createdAt"] } },
							day: { $dayOfMonth: { $add: [new Date(0), "$createdAt"] } },
							foodType: "$foodType"
						},
						total: {
							$sum: {
								$size: "$taggedPeople"
							}
						}
					}
				}
			]
		);
		return await moments.toArray();
	},
	async mealsReportDetailAggregates(options) {
		this.unblock();
		var currentUser = await Meteor.userAsync();
		const cuser = await currentUser.fetchPerson();
		const currentOrg = await Orgs.current();
		if (!currentUser ||
			cuser?.type != "admin")
			throw new Meteor.Error(403, "Access denied");

		var startDateNum = DateTimeUtils.getDatestampInTimezone(options.startDate, currentOrg.getTimezone());
		var endDateNum = DateTimeUtils.getDatestampInTimezone(options.endDate, currentOrg.getTimezone(), "endOf");

		let query = {
			orgId: currentUser.orgId,
			momentType: "food",
			sortStamp: { $gte: startDateNum, $lte: endDateNum }
		};


		if (options.groupId) {
			let peopleIds = [];
			const people = await People.find({ orgId: currentUser.orgId, defaultGroupId: options.groupId, inActive: { $ne: true } }, { fields: { _id: 1 } }).fetchAsync();

			for (const p of people) {
				peopleIds.push(p._id);
			}

			query["taggedPeople"] = { "$in": peopleIds };
		}
		if (options.personId) query["taggedPeople"] = options.personId;

		var moments = await Moments.find(query).fetchAsync();

		let mealMap = {};
		_.each(moments, (m) => {
			_.each(m.taggedPeople, (p) => {
				const dayKey = new moment(m.sortStamp).format("MM/DD/YYYY");
				const mapKey = p + "|" + dayKey;
				if (!mealMap[mapKey]) mealMap[mapKey] = {};
				const mealKey = (m.foodType || "unspecified").toLowerCase().replace(" ", "");
				mealMap[mapKey][mealKey] = true;
			});
		});

		let output = _.map(mealMap, (val, key) => { return { personId: key.split("|")[0], date: key.split("|")[1], meals: val } });
		return output;
	},
	'classListGroupDays': async function (options) {
		this.unblock();
		let currentUser = await Meteor.userAsync();
		const userPerson = await currentUser?.fetchPerson()
		if (!currentUser || userPerson.type !== "admin") {
			throw new Meteor.Error(403, "Access denied");
		}

		var org = await Orgs.current();
		var results = [];

		const allTotals = await getRoomEnrollmentsData({ orgId: org._id, page: options.page, pageSize: options.pageSize });
		_.each(allTotals.groupList, (g) => {
			// const totals = getTotalClassListDefaultDays({ orgId: org._id, groupId: g._id});
			const totals = allTotals.groupData[g._id];
			const ratio = parseInt(g.ratio);

			const staff = {
				M: Math.ceil(totals.totals.M / ratio),
				T: Math.ceil(totals.totals.T / ratio),
				W: Math.ceil(totals.totals.W / ratio),
				R: Math.ceil(totals.totals.R / ratio),
				F: Math.ceil(totals.totals.F / ratio),
			};

			results.push({ totals, staff, ratio: g.ratio, name: g.name, capacity: g.preferredCapacity });
		})
		return { groupData: results, pagination: allTotals.pagination };
	},
	'waitListReport': async function () {
		this.unblock();
		var currentUser = await Meteor.userAsync();
		const cuser = await currentUser?.fetchPerson?.();
		if (!currentUser || cuser?.type != "admin")
			throw new Meteor.Error(403, "Access denied");
		return await WaitListReport.GenerateReport()
	},
	'updateHubspotId': async function (option) {
		this.unblock();
		var currentUser = await Meteor.userAsync();
		const cuser = await currentUser?.fetchPerson?.();
		if (!currentUser || cuser?.type !== 'admin') {
			throw new Meteor.Error('403', 'Access denied');
		}
		const person = await People.findOneAsync({ _id: option.personId }, { readPreference: 'secondaryPreferred' });
		const importSlateDocumentId = person?.type === 'person';
		const importIndex = (!option.importIndex && parseInt(option.importIndex) !== 0) ? undefined : parseInt(option.importIndex);
		await People.updateAsync({ _id: option.personId }, { $set: { hubspotId: option.hubspotId, importIndex: importIndex, importSlateDocumentId: importSlateDocumentId } });
	},
	'classListScheduleBased': async function (options) {
		this.unblock();
		const currentUser = await Meteor.userAsync();
		const cuser = await currentUser?.fetchPerson?.();
		if (!currentUser || cuser?.type != "admin")
			throw new Meteor.Error(403, "Access denied");

		const org = await Orgs.current(),
			timezone = org.getTimezone(),
			holidays = org.getHolidays(),
			output = {},
			daySlots = ["mon", "tue", "wed", "thu", "fri"],
			scheduleTypes = _.filter(org.getScheduleTypes(), st => !st.hideInForecasting),
			prefix = org.profileDataPrefix(),
			startDate = new moment.tz(options.startDate, "MM/DD/YYYY", timezone),
			endDate = startDate.clone().add(6, "days"),
			startDateNum = startDate.startOf('day').valueOf(),
			endDateNum = endDate.endOf('day').valueOf();
		//gather schedule slots
		output.groups = [];
		output.programs = {
			"infants": {
				daySlots: {}
			},
			"preschool": {
				daySlots: {}
			}
		};
		output.summaries = {
			totalDiscounts: 0,
			totalCurrentRevenue: 0,
			totalFullTimeRevenue: 0,
			totalVariance: 0,
			varianceToIdealRevenue: 0,
			totalPayroll: 0
		};
		output.groupTypes = { "Other": { daySlots: {} } };
		_.map(org.getGroupTypes(), groupType => {
			output.groupTypes[groupType] = { daySlots: {} };
		});
		const groupsQuery = { orgId: org._id, includeClassList: { $ne: false } };
		const availableGroups = await Groups.find(groupsQuery).fetchAsync();

		let sortingOptions = {}
		let groupsTotalCount = 0
		if (options.paginationRequestObj !== null) {
			let groupsLimit = 1
			let skip = 0
			if (options.paginationRequestObj.currentPage !== 1) {
				skip = options.paginationRequestObj.currentPage
			}
			if (options.paginationRequestObj.isPreviousPage && options.paginationRequestObj.currentPage > 1) {
				skip = options.paginationRequestObj.currentPage - 2
			}
			if (options.paginationRequestObj.currentPage === 1 && !options.paginationRequestObj.isPreviousPage) {
				groupsLimit = 2
			}
			sortingOptions = {
				sort: { name: 1 },
				skip: skip,
				limit: groupsLimit
			}
		}

		if (!options.groupId) {
			groupsTotalCount = await Groups.find(groupsQuery).countAsync()
		}

		if (options.groupId)
			groupsQuery._id = options.groupId;

		const groups = await Groups.find(groupsQuery, sortingOptions).fetchAsync(),
			resQuery = {
				orgId: org._id,
				$or: [
					{
						$and: [
							{ recurringFrequency: { $exists: true } },
							{
								$or: [
									{ scheduledEndDate: null },
									{ scheduledEndDate: { $gte: startDateNum } }
								]
							}
						]
					},
					{
						$and: [
							{ recurringFrequency: { $exists: false } },
							{
								scheduledDate: {
									$gte: startDateNum,
									$lt: endDateNum
								}
							}
						]
					}
				]
			};

		const allReservations = await Reservations.find(resQuery).fetchAsync();
		for (const group of groups) {
			const programLabel = group.typeInfant ? "infants" : "preschool";
			const groupTypeLabel = group.getGroupType() || "Other";
			group.groupPeople = [];
			group.dayTotals = {};
			group.dayTotalsList = {};
			group.totalTuition = 0;
			group.totalDiscount = 0;
			group.totalCurrentRevenue = 0;
			group.totalVariance = 0;
			group.fullTuition = 0;
			group.defaultTuition = 0;
			group.ageRangeLabel = group.getAgeRangeLabel();
			group.slotCounts = 0;
			group.slotTotals = 0;
			group.fteTotals = 0;
			group.totalEnrolled = 0;
			group.totalCapacity = 0;
			group.dailyCapacities = {};


			if (group.defaultBillingPlanId) {
				group.defaultBillingPlan = _.find(_.deep(org, "billing.plansAndItems"), p => p._id == group.defaultBillingPlanId);
				if (group.defaultBillingPlan) { // We should check if the default billing plan still exists in the org
					group.defaultTuition = group.defaultBillingPlan.amount;
				}
			}
			const activeGroupReservations = allReservations.filter(r => r.groupId == group._id),
				groupPeopleIds = activeGroupReservations.map(r => r.selectedPerson),
				defaultReservationsOnly = allReservations.filter(r => !r.groupId),
				defaultReservationsOnlyPeopleIds = defaultReservationsOnly.map(r => r.selectedPerson),
				groupPeopleQuery = {
					orgId: org._id,
					$or: [
						{ _id: { "$in": groupPeopleIds } }
					],
					type: "person"
				};
			if (!options.onlyReservationPeople) {
				groupPeopleQuery["$or"].push({ defaultGroupId: group._id });
			} else {
				groupPeopleQuery["$or"].push({ defaultGroupId: group._id, _id: { "$in": defaultReservationsOnlyPeopleIds } });
			}

			const groupPeople = await People.find(groupPeopleQuery, { sort: { lastName: 1, firstName: 1 } }).fetchAsync();

			// BEGIN PEOPLE LOOP
			for (const person of groupPeople) {
				const outputPerson = {
					_id: person._id,
					name: person.lastName + ", " + person.firstName,
					birthday: prefix ? (person[prefix] && person[prefix].birthday) : person.birthday,
					age: person.calcAgeRawFromDate(prefix, "years", startDateNum),
					withdrawDate: person.getWithdrawDate({currentOrg: org}),
					destination: "",
					daySlots: {},
					nextTransitionSlots: {},
					tuition: 0,
					discount: 0,
					currentRevenue: 0,
					variance: 0,
					inActive: person.inActive
				}, personScheduledGroupReservations = activeGroupReservations.filter(r => r.selectedPerson == person._id),
					extraReservations = allReservations.filter(r => group._id == person.defaultGroupId && r.selectedPerson == person._id && (!r.groupId || r.groupId == group._id) && !_.contains(personScheduledGroupReservations.map(psgr => psgr._id), r._id)),
					personReservations = personScheduledGroupReservations
						.concat(extraReservations);
				let nextTransition = _.chain(allReservations)
					.filter(r => r.selectedPerson == person._id && r.scheduledDate > startDateNum)
					.sortBy(r => r.scheduledDate)
					.first()
					.value();

				if (!nextTransition && new moment.tz(person.getEnrollmentDate({currentOrg:org}), "MM/DD/YYYY", timezone).isAfter(startDate)) {
					nextTransition = _.chain(allReservations)
						.filter(r => r.selectedPerson == person._id && r.scheduledDate > startDateNum)
						.sortBy(r => r.scheduledDate)
						.first()
						.value();
				}

				// Only calculate this person for the group if a reservation exists for THIS group or its a prospect
				const hasGroupReservation = personReservations?.length > 0 || nextTransition?.length > 0;
				if (!hasGroupReservation) {
					continue;
				}

				if (outputPerson.withdrawDate) {
					const withdrawDateValue = new moment.tz(outputPerson.withdrawDate, "MM/DD/YYYY", timezone).valueOf();
					if (withdrawDateValue >= startDateNum &&
						(!nextTransition
							// && (personReservations.length == 0 || personReservations[0].scheduledDate > withdrawDateValue))
							||
							withdrawDateValue < nextTransition.scheduledDate
						)) { //|| (!nextTransition ) (withdrawDateValue < nextTransition.scheduledDate ) &&
						nextTransition = { scheduledDate: withdrawDateValue };
						outputPerson.destination = "Withdraw";
					}
				}

				if (nextTransition) {
					outputPerson.transition = nextTransition.scheduledDate;
					outputPerson.nextTransition = nextTransition;
					const gId = nextTransition?.groupId ?? person.defaultGroupId;
					const transitionGroup = _.find(availableGroups, (g) => g._id == gId);
					if (transitionGroup && outputPerson?.destination != "Withdraw") {
						outputPerson.destination = transitionGroup.name;
						outputPerson.destinationGroupId = transitionGroup._id;
					}
				}

				let personGroupStartDate = startDate.clone(), personGroupEndDate = endDate.clone();
				const activePersonReservations = personReservations.filter(r => r.scheduledDate < endDateNum);
				const activePersonNonRecurringReservations = personReservations.filter(r => !r.recurringFrequency && !r.recurrenceId && !r.cancellationReason);
				let personGroupSlotsCount = 0, personGroupTransitionsCount = 0;

				_.each(daySlots, (daySlot, daySlotIndex) => {
					const dateOfDay = startDate.clone().add(daySlotIndex, "days").format("YYYY-MM-DD");
					if (_.contains(_.map(holidays, h => h.date), dateOfDay)) return;
					if (!group.dayTotals[daySlot]) { group.dayTotals[daySlot] = {}; group.dayTotalsList[daySlot] = {}; }

					outputPerson.daySlots[daySlot] = {};
					outputPerson.nextTransitionSlots[daySlot] = {};

					_.each(personReservations.filter(r => _.contains(r.recurringDays, daySlot)), matchedDayReservation => {
						const matchedScheduleType = matchedDayReservation && scheduleTypes.find(st => st._id == matchedDayReservation.scheduleType);

						let startDateSlot = 0, exitDateSlot = 7;
						if (matchedDayReservation && matchedDayReservation.scheduledDate > startDateNum) {
							startDateSlot = new moment(matchedDayReservation.scheduledDate).diff(startDate, "days");
							personGroupStartDate = startDate.clone().add(startDateSlot, "days");
						}
						let exitDateSource;
						if (nextTransition &&
							outputPerson.destinationGroupId != group._id &&
							nextTransition.scheduledDate < endDateNum &&
							(!matchedDayReservation.scheduledEndDate || matchedDayReservation.scheduledEndDate > nextTransition.scheduledDate)) {
							exitDateSlot = new moment(nextTransition.scheduledDate).diff(startDate, "days");
							personGroupEndDate = startDate.clone().add(exitDateSlot, "days");
							exitDateSource = "nextTransition";
						} else if (matchedDayReservation?.scheduledEndDate && matchedDayReservation.scheduledEndDate < endDateNum) {
							exitDateSlot = new moment(matchedDayReservation.scheduledEndDate).add(1, "days").diff(startDate, "days");
							personGroupEndDate = startDate.clone().add(exitDateSlot, "days");
							exitDateSource = "schedule";
						}

						const isNextTransition = nextTransition
							&& nextTransition._id == matchedDayReservation._id
							&& (nextTransition.groupId == group._id || (!nextTransition.groupId && person.defaultGroupId == group._id))
							&& nextTransition.scheduledDate > endDateNum;
						if (isNextTransition || (matchedDayReservation.scheduledDate < endDateNum && daySlotIndex >= startDateSlot && (daySlotIndex < exitDateSlot || (outputPerson?.destination == "Withdraw" && exitDateSource == "nextTransition" && daySlotIndex == exitDateSlot)))) {

							_.each(scheduleTypes.filter(st => st.startTime), st => {
								const stLabel = st.startTime + " - " + st.endTime,
									presentInSlot = (matchedScheduleType && (matchedScheduleType._id == st._id || !matchedScheduleType.startTime)) ? true : false;

								if (!isNextTransition) {
									outputPerson.daySlots[daySlot][stLabel] = outputPerson.daySlots[daySlot][stLabel] || presentInSlot;
									if (!group.dayTotals[daySlot][stLabel]) {
										group.dayTotals[daySlot][stLabel] = 0;
										group.dayTotalsList[daySlot][stLabel] = [];
									}

									if (presentInSlot) {
										group.dayTotals[daySlot][stLabel] += 1;
										group.dayTotalsList[daySlot][stLabel].push(outputPerson.name);
										personGroupSlotsCount++;
									}
								} else if (presentInSlot) {
									outputPerson.nextTransitionSlots[daySlot][stLabel] = true;
									personGroupTransitionsCount++;
								}
							});

						}

					});

					// Add one-off schedules that exist on this day
					_.each(activePersonNonRecurringReservations, reservation => {
						if ((new moment.tz(reservation.scheduledDate, timezone).day() - 1) !== daySlotIndex) {
							return;
						}

						const matchedScheduleType = scheduleTypes.find(st => st._id == reservation.scheduleType);
						_.each(scheduleTypes.filter(st => st.startTime), st => {
							const stLabel = st.startTime + " - " + st.endTime;
							const presentInSlot = (matchedScheduleType && (matchedScheduleType._id === st._id || !matchedScheduleType.startTime)) ? true : false;

							outputPerson.daySlots[daySlot][stLabel] = outputPerson.daySlots[daySlot][stLabel] || presentInSlot;
							if (!group.dayTotals[daySlot][stLabel]) {
								group.dayTotals[daySlot][stLabel] = 0;
								group.dayTotalsList[daySlot][stLabel] = [];
							}

							if (presentInSlot) {
								group.dayTotals[daySlot][stLabel] += 1;
								group.dayTotalsList[daySlot][stLabel].push(outputPerson.name);
								personGroupSlotsCount++;
							}
						});
					});
				});

				outputPerson.hasSameGroupTransition = personGroupSlotsCount > 0 && personGroupTransitionsCount > 0;

				//exclude future transitions other than the next one
				if (!_.isEmpty(outputPerson.daySlots) || nextTransition?.groupId == group._id || outputPerson.groupId == group._id) {

					//find billing plan
					if (activePersonReservations.length || activePersonNonRecurringReservations.length) {
						const matchedPlans = [];
						if (activePersonReservations.length) {
							matchedPlans.push(..._.filter(
								_.deep(person, "billing.enrolledPlans"),
								ep => ep.planDetails &&
									ep.planDetails.type == "plan" &&
									ep.enrollmentDate < personGroupEndDate &&
									(!ep.expirationDate || ep.expirationDate >= personGroupStartDate)
							));
						}

						outputPerson.fullTuition = group.defaultTuition;

						_.each(matchedPlans, matchedPlan => {
							let discountAmount = 0;
							const curPlan = _.find(org.billing.plansAndItems, (p) => { return p._id == matchedPlan._id; });
							const fullRevenueAmount = matchedPlan?.overrideRate || curPlan?.amount,
								availableAllocations = _.filter(matchedPlan.allocations, (a) => { return a.allocationType != "discount" || !a.discountExpires || startDateNum < a.discountExpires }),
								sumOfDollarDiscounts = _.chain(availableAllocations)
									.filter((a) => { return a.allocationType != "family" && a.amountType == "dollars"; })
									.reduce((memo, a) => { return memo + a.amount; }, 0.0)
									.value();
							_.each(availableAllocations.filter(a => a.allocationType == "discount"), a => {
								if (a.amountType == "dollars")
									discountAmount += a.amount;
								else
									discountAmount += BillingUtils.roundToTwo((fullRevenueAmount - (org.billing.toplinePercentDiscounts ? 0 : sumOfDollarDiscounts)) * a.amount / 100.0);

								if (discountAmount < 0) discountAmount = 0;

							});

							const currentRevenueAmount = fullRevenueAmount - discountAmount;
							outputPerson.currentRevenue += currentRevenueAmount;
							outputPerson.tuition += fullRevenueAmount;
							outputPerson.discount += discountAmount;

							group.totalTuition += fullRevenueAmount;
							group.totalDiscount += discountAmount;
							group.totalCurrentRevenue += currentRevenueAmount;

							output.summaries.totalDiscounts += discountAmount;
							output.summaries.totalCurrentRevenue += fullRevenueAmount - discountAmount;
							output.summaries.totalFullTimeRevenue += fullRevenueAmount

							outputPerson.currentRevenueAmount = (outputPerson.currentRevenueAmount || 0) + currentRevenueAmount;
						});

						const personVariance = (outputPerson.currentRevenueAmount || 0) - (outputPerson.fullTuition || 0);
						outputPerson.variance = personVariance;
						group.totalVariance += (personVariance || 0);
						output.summaries.totalVariance += (personVariance || 0);

						group.fullTuition += outputPerson.fullTuition;
						output.summaries.totalFullTuition += outputPerson.fullTuition;
					}
					if (personGroupSlotsCount > 0 || (!options.onlyCurrentSchedules && (personGroupTransitionsCount > 0 || person.defaultGroupId == group._id))) {
						group.groupPeople.push(outputPerson);
					}

				}
			}
			//END PEOPLE LOOP

			_.each(daySlots, (daySlot, daySlotIndex) => {
				const dateOfDay = startDate.clone().add(daySlotIndex, "days").format("YYYY-MM-DD");
				if (!output.programs[programLabel].daySlots[daySlot]) {
					output.programs[programLabel].daySlots[daySlot] = {};
				}
				if (!output.groupTypes[groupTypeLabel].daySlots[daySlot]) {
					output.groupTypes[groupTypeLabel].daySlots[daySlot] = {};
				}
				if (_.contains(_.map(holidays, h => h.date), dateOfDay)) {
					return;
				}
				const dailyCapacity = parseInt(group.capacity) || 0;
				group.dailyCapacities[daySlot] = dailyCapacity;
				_.each(scheduleTypes.filter(st => st.startTime), st => {
					const stLabel = st.startTime + " - " + st.endTime;
					if (!output.programs[programLabel].daySlots[daySlot][stLabel]) output.programs[programLabel].daySlots[daySlot][stLabel] = {
						enrolled: 0,
						capacity: 0,
						staff: 0
					};
					if (!output.groupTypes[groupTypeLabel].daySlots[daySlot][stLabel]) output.groupTypes[groupTypeLabel].daySlots[daySlot][stLabel] = {
						enrolled: 0,
						capacity: 0,
						staff: 0
					};

					if (group.dayTotals[daySlot] && group.dayTotals[daySlot][stLabel]) {
						output.groupTypes[groupTypeLabel].daySlots[daySlot][stLabel].enrolled += group.dayTotals[daySlot][stLabel];
						output.programs[programLabel].daySlots[daySlot][stLabel].enrolled += group.dayTotals[daySlot][stLabel];
						group.slotTotals += group.dayTotals[daySlot][stLabel];
						group.fteTotals += group.dayTotals[daySlot][stLabel] * (st.fteCount || 1.0);
						if ((group.ratio || 0) > 0) {
							output.groupTypes[groupTypeLabel].daySlots[daySlot][stLabel].staff += Math.ceil(group.dayTotals[daySlot][stLabel] / group.ratio);
							output.programs[programLabel].daySlots[daySlot][stLabel].staff += Math.ceil(group.dayTotals[daySlot][stLabel] / group.ratio);
						}
					}
					group.slotCounts += 1;
					group.totalCapacity += dailyCapacity;
					output.groupTypes[groupTypeLabel].daySlots[daySlot][stLabel].capacity += dailyCapacity;
					output.programs[programLabel].daySlots[daySlot][stLabel].capacity += dailyCapacity;
				});
			});

			const averageFTE = (group.fteTotals / 5);
			group.weeklyFTEPercent = (group.capacity > 0) ? averageFTE / (group.preferredCapacity || group.capacity) * 100 : 0;
			group.totalEnrolled = group.slotTotals;
			group.staffNeeded = group.ratio > 0 ? Math.ceil(averageFTE / group.ratio) : 0;
			group.weeklyEnrollmentPercent = (group.totalCapacity > 0) && (group.totalEnrolled / group.totalCapacity * 100);
			group.averageFTE = averageFTE;

			const groupPayroll = await group.getGroupPayroll();
			output.summaries.totalPayroll += groupPayroll.weeklyPayroll;
			output.groups.push(group);
		}

		output.summaries.idealRevenuePercent = org?.forecasting?.idealRevenue || 100;
		output.summaries.idealRevenue = output.summaries.totalFullTimeRevenue * (output.summaries.idealRevenuePercent / 100);
		output.summaries.varianceToIdealRevenue = output.summaries.totalCurrentRevenue - output.summaries.idealRevenue;
		output.summaries.totalPayrollPercentage = output.summaries.totalCurrentRevenue && (output.summaries.totalPayroll / output.summaries.totalCurrentRevenue * 100);
		output.summaries.targetPayrollPercentage = org?.forecasting?.targetPayroll || 0;
		output.summaries.targetPayrollVariance = output.summaries.targetPayrollPercentage - output.summaries.totalPayrollPercentage;
		output.groupsTotalCount = groupsTotalCount
		if (options.fetchSummaryFlag) {
			output.fetchSummaryFlag = options.fetchSummaryFlag
		}
		return output;
	},
	'reportPayrollRatio': async function (options) {
		this.unblock();
		var currentUser = await Meteor.userAsync();
		const cuser = await currentUser.fetchPerson();
		if (!currentUser || cuser?.type != "admin")
			throw new Meteor.Error(403, "Access denied");

		var org = await Orgs.current();
		//start date sent as moment().toDate().valueOf(); AND is the monday of the week;
		var settings = org.customStaffPaySettings;
		var overtimeThreshold = (settings && settings.overtimeThreshold && settings.overtimeActive) ? parseInt(settings.overtimeThreshold) : 40;
		var customPayTypes = (settings && settings.types) ? settings.types : [];
		var startDateNum = DateTimeUtils.getDatestampInTimezone(options.startDate, org.getTimezone());
		var startWeek = new moment.tz(startDateNum, org.getTimezone()).startOf('day'); //start date sent as moment().toDate().valueOf();
		var endWeek = new moment.tz(startDateNum, org.getTimezone()).add(6, 'days').endOf('day');
		var currentMomentDay = new moment();
		var currentDayValue = new moment().day();

		var groups = await Groups.find({ orgId: org._id, includeClassList: { $ne: false } }, { sort: { name: 1 } }).fetchAsync();
		var staff = await People.find({ orgId: org._id, inActive: { $ne: true }, type: { $in: ["admin", "staff"] } }, { sort: { lastName: 1, firstName: 1 } }).fetchAsync()


		var centerTotal = {
			payrollRatio: 0,
			weeklyPayroll: 0,
			weeklyLoadedPayroll: 0,
			memorizedRate: 0,
			discounts: 0,
		};

		var calculatedStaffPayroll = [];
		for (const p of staff) {
			var payRate = p["payRate"];
			var exemptStatus = p["employeeClassification"];
			if (org.profileDataPrefix()) {
				payRate = 0;
				var profileDataObj = p[org.profileDataPrefix()];
				if (profileDataObj) {
					payRate = profileDataObj["payRate"] || 0;
					exemptStatus = profileDataObj["employeeClassification"] || "non-exempt";
				}
			}
			var staffForecast = p && p.classList && p.classList.staffForecast;
			if (!payRate || !staffForecast) continue;

			var results = null;
			if (startWeek > currentMomentDay) {
				//projected results
				results = personPayCalculation({ payRate, staffForecast, overtimeThreshold, exemptStatus });
			} else if (currentMomentDay.isBetween(startWeek, endWeek)) {
				//current week results
				results = personPayCalculation({ payRate, staffForecast, overtimeThreshold, exemptStatus });
			} else {
				//historical results
				results = await timeCardPersonPayCalculation({ overtimeThreshold, customPayTypes, payRate, exemptStatus, staffForecast, orgId: org._id, personId: p._id, startDate: startWeek.format("MM/DD/YYYY"), endDate: endWeek.format("MM/DD/YYYY") })
			}

			calculatedStaffPayroll.push({
				firstName: p.firstName,
				lastName: p.lastName,
				payRate,
				isCenterGroup: (!p.defaultGroupId) ? true : false,
				defaultGroupId: p.defaultGroupId,
				weeklyPayroll: results.weeklyPayroll,
				weeklyLoadedPayroll: results.weeklyLoadedPayroll
			})
		};

		for (const g of groups) {
			// Memorized group rates...not accounting for frequency of rate (assumes monthly)
			var groupResult = await getGroupMemorizedRateAndDiscount({
				org,
				group: g,
				startDay: startWeek.format("MM/DD/YYYY"),
			});

			g.weeklyPayroll = 0;
			g.weeklyLoadedPayroll = 0;
			var groupStaff = _.filter(calculatedStaffPayroll, (s) => s.defaultGroupId == g._id);
			for (const p of groupStaff) {
				g.weeklyPayroll += parseFloat(p.weeklyPayroll);
				g.weeklyLoadedPayroll += parseFloat(p.weeklyLoadedPayroll);
			}

			g.memorizedRate = groupResult.memorizedRate;
			g.discounts = groupResult.discounts;
			centerTotal.memorizedRate += groupResult.memorizedRate;
			centerTotal.discounts += groupResult.discounts;
			centerTotal.weeklyLoadedPayroll += g.weeklyLoadedPayroll;
			centerTotal.weeklyPayroll += g.weeklyPayroll;
			g.payrollRatio = (g.memorizedRate > 0) ? ((g.weeklyLoadedPayroll / g.memorizedRate * 4.33) * 100).toFixed(2) : 0;
		}

		var centerGroup = {
			_id: "center",
			name: "Additional Staff",
			weeklyPayroll: 0.0,
			weeklyLoadedPayroll: 0.0,
			payrollRatio: 0,
		};
		var groupStaff = _.filter(calculatedStaffPayroll, (s) => s.isCenterGroup == true);
		_.each(groupStaff, (p) => {
			centerGroup.weeklyPayroll = currency(centerGroup.weeklyPayroll).add(p.weeklyPayroll).value;
			centerGroup.weeklyLoadedPayroll = currency(centerGroup.weeklyLoadedPayroll).add(p.weeklyLoadedPayroll).value;
		})
		centerTotal.weeklyLoadedPayroll += centerGroup.weeklyLoadedPayroll;
		centerTotal.weeklyPayroll += centerGroup.weeklyPayroll;
		centerTotal.payrollRatio = (centerTotal.memorizedRate > 0) ? ((centerTotal.weeklyLoadedPayroll / centerTotal.memorizedRate * 4.33) * 100).toFixed(2) : 0;

		groups.push(centerGroup);

		return {
			centerTotal,
			groups,
			centerGroup,
			calculatedStaffPayroll,
		};

	},
	'adminDashboardClassListWidget': async function (options) {
		this.unblock();
		const user = await Meteor.userAsync();
		const currentPerson = await user?.fetchPerson?.();
		if (currentPerson?.type != "admin") throw new Meteor.Error(403, "Access denied");


		const currentOrg = await Orgs.current();
		const orgIds = options.orgIds || [currentOrg._id];
		const orgs = await Orgs.find({ _id: { $in: orgIds } }).fetchAsync();
		const startMonth = new moment().startOf('week').add(1, 'week').add(1, 'days').format("MM/DD/YYYY");

		const results = [];
		for (const o of orgs) {
			let groups = await Groups.find({ orgId: o._id, includeClassList: { $ne: false } }).fetchAsync();
			groups = sortGroupsByAge(groups);

			const dayMomentValue = new moment.tz(startMonth, "MM/DD/YYYY", o.getTimezone()).startOf('day').valueOf(),
				scheduledDateQueryValue = new moment(dayMomentValue).endOf('week').endOf('day').add(2, 'months').valueOf(),
				resQuery = {
					orgId: o._id,
					scheduledDate: { "$lte": scheduledDateQueryValue },
					"$or": [{ scheduledEndDate: null }, { scheduledEndDate: { "$gt": dayMomentValue } }],
					recurringFrequency: { "$exists": true }
				},
				allReservations = await Reservations.find(resQuery).fetchAsync();

			const totals = {
				name: o.name,
				currentFte: 0,
				currentVariance: 0,
				currentFtePlusOne: 0,
				preferredCapacity: 0,
			};

			let resultGroups = [];
			for (let x = 0; x < groups.length; x++) {
				const g = groups[x];
				totals.preferredCapacity += parseInt(g.preferredCapacity || 0);
				const currentFteResult = await getFteForMonth({
					startMonth,
					orgId: o._id,
					groupId: g._id,
					allReservations,
				});
				g.currentFte = currentFteResult.totalCount;
				totals.currentFte = (Math.round((parseFloat(g.currentFte) + parseFloat(totals.currentFte)) * 10) / 10).toFixed(1);
				g.currentVariance = (!isNaN(g.preferredCapacity)) ? (g.preferredCapacity - g.currentFte).toFixed(1) : null;

				const currentFtePlusOneResult = await getFteForMonth({
					orgId: o._id,
					groupId: g._id,
					includeTransition: true,
					startMonth: new moment(startMonth, "MM/DD/YYYY").add(1, 'month').startOf('month').add(5, "days").startOf('week').add(1, "days").format("MM/DD/YYYY"),
					allReservations,
				});
				g.currentFtePlusOne = currentFtePlusOneResult.totalCount;
				totals.currentFtePlusOne = (Math.round((parseFloat(g.currentFtePlusOne) + parseFloat(totals.currentFtePlusOne)) * 10) / 10).toFixed(1);
				g.enrollmentGoalPercentage = (g.enrollmentGoal > 0) ? ((parseFloat(g.currentFtePlusOne) / g.enrollmentGoal) * 100).toFixed(2) : (0).toFixed(2);
				resultGroups.push(g);
			};

			totals.currentVariance = (!isNaN(totals.preferredCapacity)) ? (totals.preferredCapacity - totals.currentFte).toFixed(1) : null;

			results.push({ groups: resultGroups, totals: [totals] })
		}

		return results;

	},
	'classListReportAggregates': async function (options) {
		this.unblock();
		var currentUser = await Meteor.userAsync();
		const cuser = await currentUser.fetchPerson();
		if (!currentUser || cuser?.type != "admin")
			throw new Meteor.Error(403, "Access denied");

		const org = await Orgs.current();
		const orgArray = (options.orgs && options.orgs.length > 0) ? options.orgs : [org._id];
		const startMonth = moment(options.startMonth, "MM/DD/YYYY").startOf('week').add(1, 'days').format("MM/DD/YYYY");
		const endMonth = moment(options.startMonth, "MM/DD/YYYY").endOf('week').format("MM/DD/YYYY");
		const result = [];
		for (const orgId of orgArray) {
			const currentOrg = await Orgs.findOneAsync({ _id: orgId });
			if (!currentOrg) {
				continue;
			}
			const timezone = currentOrg.getTimezone();
			const overtimeThreshold = currentOrg.customStaffPaySettings?.overtimeThreshold ?? 40;

			const ageRangeStart = !isNaN(options.ageRangeStart) && parseFloat(options.ageRangeStart),
				ageRangeEnd = !isNaN(options.ageRangeEnd) && parseFloat(options.ageRangeEnd);

			var groupsQuery = { orgId: currentOrg._id, includeClassList: { $ne: false } };
			if (options.selectedGroupId) groupsQuery["_id"] = options.selectedGroupId;
			var groupsRaw = await Groups.find(groupsQuery).fetchAsync();

			var groups = _.filter(groupsRaw, g => {
				let groupAgeStart = g?.ageGroup?.begin || 0;
				let groupAgeEnd = g?.ageGroup?.end || 0;
				if (g?.ageGroup?.type == "years") {
					groupAgeStart = groupAgeStart * 12;
					groupAgeEnd = groupAgeEnd * 12;
				}
				return (!ageRangeStart || groupAgeStart >= ageRangeStart) && (!ageRangeEnd || groupAgeEnd <= ageRangeEnd);
			});
			/*
				ORDER GROUPS BY AGE GROUP
				we store the begin/end as a string and mongoDB does not interpret it well
				doing best to sort by combined age group
			*/
			groups = sortGroupsByAge(groups);

			var totals = {
				maxCapacity: 0,
				preferredCapacity: 0,
				currentFte: 0,
				currentVariance: 0,
				occupancyPercentage: 0,
				currentFtePlusOne: 0,
				currentFtePlusTwo: 0,
				currentFtePlusThree: 0,
				currentFtePlusFour: 0,
				memorizedRate: 0,
				discounts: 0,
				weeklyPayroll: 0,
				weeklyLoadedPayroll: 0,
				monthlyLoadedPayroll: 0,
				payrollRatio: 0,
				childDetails: [],
			};

			const dayMomentValue = new moment.tz(startMonth, "MM/DD/YYYY", timezone).startOf('day').valueOf(),
				scheduledDateQueryValue = new moment(dayMomentValue).endOf('week').endOf('day').add(4, 'months').valueOf(),
				resQuery = {
					orgId: currentOrg._id,
					scheduledDate: { "$lte": scheduledDateQueryValue },
					"$or": [{ scheduledEndDate: null }, { scheduledEndDate: { "$gte": dayMomentValue } }],
					recurringFrequency: { "$exists": true }
				},
				allReservations = await Reservations.find(resQuery).fetchAsync();

			for (const g of groups) {
				if (!options.laborUtilizationOnly) {
					totals.maxCapacity += parseInt(g.capacity || 0);
					totals.preferredCapacity += parseInt(g.preferredCapacity || 0);

					const currentFteResult = await getFteForMonth({
						startMonth,
						orgId: currentOrg._id,
						groupId: g._id,
						allReservations,
						includeInactive: true
					});
					g.currentFte = currentFteResult.totalCount;
					totals.currentFte = (Math.round((parseFloat(g.currentFte) + parseFloat(totals.currentFte)) * 10) / 10).toFixed(1);

					g.currentVariance = (!isNaN(g.preferredCapacity)) ? (g.preferredCapacity - g.currentFte).toFixed(1) : null;
					g.occupancyPercentage = (!isNaN(g.preferredCapacity)) ? parseInt((g.currentFte / g.preferredCapacity) * 100) : null;

					if (options.includeChildDetails)
						g.childDetails = currentFteResult.groupChildren

					const currentFtePlusOneResult = await getFteForMonth({
						orgId: currentOrg._id,
						groupId: g._id,
						includeTransition: true,
						startMonth: moment(options.startMonth, "MM/DD/YYYY").add(1, 'month').startOf('month').add(5, "days").startOf('week').add(1, "days").format("MM/DD/YYYY"),
						allReservations,
						includeInactive: true
					});
					g.currentFtePlusOne = currentFtePlusOneResult.totalCount;
					totals.currentFtePlusOne = (Math.round((parseFloat(g.currentFtePlusOne) + parseFloat(totals.currentFtePlusOne)) * 10) / 10).toFixed(1);

					g.enrollmentGoalPercentage = (g.enrollmentGoal > 0) ? ((parseFloat(g.currentFtePlusOne) / g.enrollmentGoal) * 100).toFixed(2) : (0).toFixed(2);

					const currentFtePlusTwoResult = await getFteForMonth({
						orgId: currentOrg._id,
						groupId: g._id,
						includeTransition: true,
						startMonth: moment(options.startMonth, "MM/DD/YYYY").add(2, 'month').startOf('month').add(5, "days").startOf('week').add(1, "days").format("MM/DD/YYYY"),
						allReservations,
						includeInactive: true
					});
					g.currentFtePlusTwo = currentFtePlusTwoResult.totalCount;
					totals.currentFtePlusTwo = (Math.round((parseFloat(g.currentFtePlusTwo) + parseFloat(totals.currentFtePlusTwo)) * 10) / 10).toFixed(1);

					const currentFtePlusThreeResult = await getFteForMonth({
						orgId: currentOrg._id,
						groupId: g._id,
						includeTransition: true,
						startMonth: moment(options.startMonth, "MM/DD/YYYY").add(3, 'month').startOf('month').add(5, "days").startOf('week').add(1, "days").format("MM/DD/YYYY"),
						allReservations,
						includeInactive: true
					});
					g.currentFtePlusThree = currentFtePlusThreeResult.totalCount;
					totals.currentFtePlusThree = (Math.round((parseFloat(g.currentFtePlusThree) + parseFloat(totals.currentFtePlusThree)) * 10) / 10).toFixed(1);

					const currentFtePlusFourResult = await getFteForMonth({
						orgId: currentOrg._id,
						groupId: g._id,
						includeTransition: true,
						startMonth: moment(options.startMonth, "MM/DD/YYYY").add(4, 'month').startOf('month').add(5, "days").startOf('week').add(1, "days").format("MM/DD/YYYY"),
						allReservations,
						includeInactive: true
					});
					g.currentFtePlusFour = currentFtePlusFourResult.totalCount;
					totals.currentFtePlusFour = (Math.round((parseFloat(g.currentFtePlusFour) + parseFloat(totals.currentFtePlusFour)) * 10) / 10).toFixed(1);
				}

				//memorized group rates...not accounting for frequency of rate ( assumes monthly )
				var groupResult = await getGroupMemorizedRateAndDiscount({
					org: currentOrg,
					group: g,
					startDay: startMonth,
				});

				g.memorizedRate = groupResult.memorizedRate;
				g.discounts = groupResult.discounts;
				totals.memorizedRate += groupResult.memorizedRate;
				totals.discounts += groupResult.discounts;

				//Group Payroll ratio calculation
				const groupPayroll = await g.getGroupPayroll({
					useAttendance: !!options.laborUtilizationOnly,
					startDate: startMonth,
					endDate: endMonth,
					overtimeThreshold: overtimeThreshold
				})
				Object.assign(g, groupPayroll);
				g.payrollRatio = (g.memorizedRate > 0) ? ((g.weeklyLoadedPayroll / g.memorizedRate * 4.33) * 100).toFixed(2) : 0;
				totals.weeklyPayroll += g.weeklyPayroll;
				totals.weeklyLoadedPayroll += g.weeklyLoadedPayroll;
				totals.monthlyLoadedPayroll += g.monthlyLoadedPayroll;
			}

			const centerGroup = await org.getCenterPayroll({
				useAttendance: !!options.laborUtilizationOnly,
				startDate: startMonth,
				endDate: endMonth,
				overtimeThreshold: overtimeThreshold
			});
			totals.weeklyPayroll += centerGroup.weeklyPayroll;
			totals.weeklyLoadedPayroll += centerGroup.weeklyLoadedPayroll;
			totals.monthlyLoadedPayroll += centerGroup.monthlyLoadedPayroll;
			groups.push(centerGroup);

			totals.currentVariance = (!isNaN(totals.preferredCapacity)) ? (totals.preferredCapacity - totals.currentFte).toFixed(1) : null;
			totals.occupancyPercentage = (!isNaN(totals.preferredCapacity)) ? parseInt((totals.currentFte / totals.preferredCapacity) * 100) : null;

			totals.payrollRatio = (totals.memorizedRate > 0) ? ((totals.weeklyLoadedPayroll / totals.memorizedRate * 4.33) * 100).toFixed(2) : 0;
			result.push({
				name: currentOrg.name,
				groups,
				totals
			});
		}
		return result;
	},
	'momentsReportAggregates': async function (options) {
		var currentUser = await Meteor.userAsync();
		const cuser = await currentUser.fetchPerson();
		const currentOrg = await Orgs.current();
		if (!currentUser ||
			cuser?.type != "admin")
			throw new Meteor.Error(403, "Access denied");
		if (options.momentType == "")
			throw new Meteor.Error(500, "A moment type must be selected");

		const query = await ReportsUtil.createMomentsReportFilter(options, currentUser.orgId, currentOrg.getTimezone());
		var	output = await Moments.find(query, { sort: { sortStamp: 1 } }).fetchAsync();
		for (const d of output) {
			d.peopleListString = await d.peopleList(true);
			d.formattedDescriptionString = await d.formattedDescription();
			d.momentTypePrettyTranslation = await d.momentTypePrettyTranslation();
		}
		return output;
	},
	'getWmgToken': async function () {
		var currentUser = await Meteor.userAsync();
		var currentPerson = currentUser ? await currentUser.fetchPerson() : null;
		var org = await Orgs.current();

		if (!currentUser || !currentPerson || !org.hasCustomization("wmg/enabled"))
			throw new Meteor.Error(403, "Access denied");

		//TODO: REMOVE THIS NEXT LINE WHEN READY
		// return signWmgPlayerToken(org.wmgCenterId, currentPerson._id);
		//TODO: REMOVE ABOVE LINE WHEN READY

		//find available parent view child
		var parentView = null;
		const allRelationships = await Relationships.find({ personId: currentPerson._id }).fetchAsync();
		for (const r of allRelationships) {
			const relationshipPerson = await People.findOneAsync({ _id: r.targetId });
			if (relationshipPerson && relationshipPerson.billing && parentView == null) {
				const plans = relationshipPerson.billing.enrolledPlans || [];
				parentView = _.find(plans, function (p) {
					if (p && p.planDetails && p.planDetails.description) {
						const parentView = p.planDetails.description.replace(/ /g, '').trim().toLowerCase();
						if (parentView == "parentview") return true;
					}
					return false;
				});
			}
		}

		if (parentView) {
			return signWmgPlayerToken(org.wmgCenterId, currentPerson._id);
		} else {
			throw new Meteor.Error(403, "Parent View Plan Required");
		}
	},
	'curriculumReportAggregates': async function (options) {
		this.unblock();
		var currentUser = await Meteor.userAsync();
		const cuser = await currentUser.fetchPerson();
		if (!currentUser ||
			cuser?.type != "admin")
			throw new Meteor.Error(403, "Access denied");

		var currentOrg = await Orgs.findOneAsync({ _id: currentUser.orgId });
		moment.tz.setDefault(currentOrg.getTimezone());

		var startDateNum = options.scheduledStartDate;
		var endDateNum = options.scheduledEndDate;
		var query = { orgId: currentUser.orgId, scheduledDate: { $gte: startDateNum, $lte: endDateNum } };
		if (options.selectedGroup && options.selectedGroup != "") query["selectedGroups"] = options.selectedGroup;
		if (options.selectedStandards && options.selectedStandards.length > 0) query["selectedStandards"] = { "$in": options.selectedStandards };
		if (options.selectedTypes && options.selectedTypes.length > 0) query["selectedTypes"] = { "$in": options.selectedTypes };
		if (options.searchText && options.searchText != "") query["$text"] = { "$search": options.searchText };
		console.log("queryfor curric", query);
		var allCurriculums = await Curriculums.find(query).fetchAsync();
		for (const curriculum of allCurriculums) {
			curriculum.formattedScheduledDate = new moment(curriculum.scheduledDate).format("MM/DD/YYYY");
			var groupList = "";
			for (const groupId of curriculum.selectedGroups) {
				const group = await Groups.findOneAsync({ orgId: currentUser.orgId, _id: groupId });
				if (group) {
					if (groupList != "") groupList = groupList + ", ";
					groupList = groupList + group.name
				}
			};
			if (groupList == "") groupList = "All";
			curriculum.groupsFormatted = groupList;
		};
		return allCurriculums;
	},
	'learningReportData': async function (options) {
		this.unblock();
		var currentUser = await Meteor.userAsync();
		const cuser = await currentUser.fetchPerson();
		if (!currentUser ||
			cuser?.type != "admin")
			throw new Meteor.Error(403, "Access denied");

		var currentOrg = await Orgs.findOneAsync({ _id: currentUser.orgId });
		moment.tz.setDefault(currentOrg.getTimezone());
		const dateRange = { $gte: options.startDateNum, $lte: options.endDateNum };

		var query = { orgId: currentUser.orgId, sortStamp: dateRange };
		if (options.selectedGroup && options.selectedGroup != "") query["selectedGroups"] = options.selectedGroup;
		if (options.selectedStandards && options.selectedStandards.length > 0) query["selectedStandards"] = { "$in": options.selectedStandards };
		if (options.selectedTypes && options.selectedTypes.length > 0) query["selectedTypes"] = { "$in": options.selectedTypes };
		if (options.searchText && options.searchText != "") query["$text"] = { "$search": options.searchText };
		console.log("queryfor curric", query);
		var allCurriculums = await Moments.find(query).fetchAsync();
	},
	'schedulingReportAggregates': async function (options) {
		this.unblock();
		var currentUser = await Meteor.userAsync();
		const cuser = await currentUser.fetchPerson();
		if (!currentUser ||
			cuser?.type != "admin")
			throw new Meteor.Error(403, "Access denied");

		var currentOrg = await Orgs.findOneAsync({ _id: currentUser.orgId });
		moment.tz.setDefault(currentOrg.getTimezone());

		var startDateNum = options.scheduledDate;
		console.log("startDateNum", startDateNum);

		var allReservations = await Reservations.find({ orgId: currentUser.orgId, scheduledDate: startDateNum }).fetchAsync();

		for (const res of allReservations) {
			const p = await People.findOneAsync({ orgId: currentUser.orgId, _id: res.selectedPerson });
			if (p) {
				res.person = p;
			}
		}

		// put mapping of type of care provider to person here
		var staffReservations = _.filter(allReservations, function (r) {
			return r.person && (r.person.type == "admin" ||
				(r.person.type == "staff" && r.person.staffType && r.person.staffType != "support")
			);
		});
		var personReservations = _.filter(allReservations, function (r) { return r.person && r.person.type == "person" });

		const hourCount = 12, hourSlots = 4, startHour = 7;
		var timeslots = _.map(_.range(0, hourCount * hourSlots, 1), function (hourNum) {
			const rawHour = Math.floor(hourNum / hourSlots) + startHour;
			const formattedHour = rawHour >= 12 ? rawHour - 12 : rawHour;
			const hourAmPm = rawHour >= 12 ? "pm" : "am";
			const minuteNum = (hourNum % hourSlots) * 15;
			const slotStart = new moment(formattedHour + ":" + minuteNum + hourAmPm, "h:mm a");
			return {
				hourNum: formattedHour,
				minuteNum: minuteNum,
				hourAmPm: hourAmPm,
				momentSlotStart: slotStart,
				momentSlotEnd: slotStart.clone().add(15, "m")
			};
		});

		_.each(timeslots, function (timeslot) {
			const filterTimeslot = function (reservation) {
				const startTime = new moment(reservation.scheduledTime, "h:mm a"),
					endTime = new moment(reservation.scheduledEndTime, "h:mm a");
				return startTime <= timeslot.momentSlotStart && endTime >= timeslot.momentSlotEnd;
			};
			const staffInSlot = _.filter(staffReservations, filterTimeslot);
			const peopleInSlot = _.filter(personReservations, filterTimeslot);

			timeslot.peopleCount = {};
			timeslot.peopleCount.level1 = _.size(_.filter(peopleInSlot, function (p) { return p.person.carePlan && p.person.carePlan.loc == "1"; }));
			timeslot.peopleCount.level2 = _.size(_.filter(peopleInSlot, function (p) { return p.person.carePlan && p.person.carePlan.loc == "2"; }));
			timeslot.peopleCount.level3 = _.size(_.filter(peopleInSlot, function (p) { return p.person.carePlan && p.person.carePlan.loc == "3"; }));
			timeslot.staffCount = _.size(staffInSlot);
			timeslot.requiredEmployees = Math.ceil(timeslot.peopleCount.level1 / 8) +
				Math.ceil(timeslot.peopleCount.level2 / 6) +
				Math.ceil(timeslot.peopleCount.level3 / 4);

			timeslot.surplusDeficit = timeslot.staffCount - timeslot.requiredEmployees;
		});
		_.each(timeslots, function (timeslot) {
			timeslot.momentSlotStart = timeslot.momentSlotStart.valueOf();
			timeslot.momentSlotEnd = timeslot.momentSlotEnd.valueOf();
		});

		return timeslots;
	},
	async billingExpandedReportAggregates(options) {
		this.unblock();
		var currentUser = await Meteor.userAsync();
		const cuser = await currentUser.fetchPerson();
		if (!currentUser ||
			cuser?.type != "admin")
			throw new Meteor.Error(403, "Access denied");

		var currentOrg = await Orgs.findOneAsync({ _id: currentUser.orgId });
		moment.tz.setDefault(currentOrg.getTimezone());

		var startDateNum = moment(options.startDate, "MM/DD/YYYY").startOf('day').valueOf();
		var endDateNum = moment(options.endDate, "MM/DD/YYYY").endOf('day').valueOf();

		var query = {
			$and: [
				{ sortStamp: { $gte: startDateNum, $lte: endDateNum } },
				{ momentType: { $in: ["checkin", "checkout"] } },
				{ orgId: currentUser.orgId }
			]
		};

		const people = await People.find({ orgId: currentUser.orgId, type: "person" }).fetchAsync();
		const peopleIds = _.map(people, (p) => { return p._id; });
		query["$and"].push({ owner: { $in: peopleIds } });

		const checkinMoments = await Moments.find(query).fetchAsync();
		_.each(checkinMoments, (m) => { if (!m.date) m.date = new moment(m.sortStamp).format("MM/DD/YYYY"); });
		_.each(people, (person) => {
			let hoursTotal = 0.0, lateMinutesTotal = 0.0, calculatedHoursTotal = 0.0, unitsTotal = 0;
			const personCheckins = _.chain(checkinMoments).where({ owner: person._id, momentType: "checkin" }).sortBy("sortStamp").groupBy("date").value();

			_.each(personCheckins, (cis, date) => {
				const checkout = _.chain(checkinMoments).where({ owner: person._id, date: date, momentType: "checkout" }).sortBy("sortStamp").last().value();
				if (checkout) {
					const checkin = _.first(cis);
					//const hours = Math.ceil(moment(checkout.sortStamp).diff(moment(checkin.sortStamp), 'minutes')/60);

					var overrideElapsedTime;
					if (currentOrg.hasCustomization("report/attendance/useQuarterIntervals")) {
						const checkInTimeMoment = new moment(checkin.sortStamp);
						const roundedCheckInTime = checkInTimeMoment.minute(Math.round(checkInTimeMoment.minute() / 15) * 15).second(0).valueOf();
						const checkOutTimeMoment = new moment(checkout.sortStamp);
						const roundedCheckOutTime = checkOutTimeMoment.minute(Math.round(checkOutTimeMoment.minute() / 15) * 15).second(0).valueOf();
						overrideElapsedTime = ((roundedCheckOutTime - roundedCheckInTime) / 1000 / 60 / 60);
					}
					const calculatedHours = moment(checkout.sortStamp).diff(moment(checkin.sortStamp), 'minutes') / 60;
					const hours = overrideElapsedTime || calculatedHours;
					const units = calculatedHours * 4;
					const roundedUnits = (units % 1) > (8.0 / 15.0) ? Math.ceil(units) : Math.floor(units);
					hoursTotal = hoursTotal + hours;
					calculatedHoursTotal = calculatedHoursTotal + calculatedHours;
					unitsTotal = unitsTotal + roundedUnits;

					const currentLateMinutes = moment(checkout.sortStamp).diff(new moment(checkout.date + " 6:00 pm", "MM/DD/YYYY h:mm a"), "minutes");
					if (currentLateMinutes > 0) lateMinutesTotal += currentLateMinutes;
				}
			});
			person.hours = hoursTotal;
			person.calculatedHours = calculatedHoursTotal;
			person.unitsTotal = unitsTotal;

			person.lateMinutes = lateMinutesTotal;
		});

		const reservations = await Reservations.findWithRecurrence({ startDateValue: startDateNum, endDateValue: endDateNum, query: { orgId: currentUser.orgId } });
		_.each(reservations, (r) => {
			r.reservationDate = new moment(r.scheduledDate).format("MM/DD/YYYY");
			const reservationCheckins = _.where(checkinMoments, { momentType: "checkin", owner: r.selectedPerson, date: r.reservationDate });
			const checkinCount = reservationCheckins.length;
			if (checkinCount > 0) {
				r.checkedIn = true;
				r.firstCheckin = _.first(reservationCheckins);
			}
		});

		return _.chain(people)
			.map((p) => {
				return {
					name: p.firstName + " " + p.lastName,
					paymentClassification: (p.payer || "").capitalizeFirstLetter(),
					numFullDays: _.chain(checkinMoments).where({ owner: p._id, momentType: "checkin" }).pluck("date").uniq().size().value(),
					detailedDates: _.chain(checkinMoments)
						.where({ owner: p._id, momentType: "checkin" })
						.sortBy("date")
						.pluck("date")
						.uniq()
						.map((d) => { return moment(d, "MM/DD/YYYY").format("MMMM-D"); })
						.reduce((memo, d) => { const dp = d.split("-"); return memo + (memo.indexOf(dp[0]) < 0 ? (memo.length > 0 ? "<br/>" : "") + dp[0] + " " + dp[1] : "," + dp[1]); }, "")
						.value(),
					scholarshipAllowance: p.scholarshipAllowance,
					scholarshipAmount: p.scholarshipAmount,
					hours: p.hours,
					units: p.unitsTotal, //p.hours ? (p.hours * 60 / 15).toFixed(2) : "", //p.hours * 4.0,
					noShows: _.chain(reservations).filter((r) => { return r.selectedPerson == p._id && r.cancellationReason == "No Call"; }).size().value(),
					billableNoShows: _.chain(reservations).filter((r) => { return r.selectedPerson == p._id && !_.contains(["Medicaid Waiver", "Choice", "Title III"], p.payer) && r.cancellationReason == "No Call"; }).size().value(),
					noShowDates: _.chain(reservations)
						.filter((r) => { return r.selectedPerson == p._id && !_.contains(["Medicaid Waiver", "Choice", "Title III"], p.payer) && r.cancellationReason == "No Call"; })
						.sortBy("reservationDate")
						.pluck("reservationDate")
						.uniq()
						.map((d) => { return moment(d, "MM/DD/YYYY").format("MMMM-D"); })
						.reduce((memo, d) => { const dp = d.split("-"); return memo + (memo.indexOf(dp[0]) < 0 ? (memo.length > 0 ? "<br/>" : "") + dp[0] + " " + dp[1] : "," + dp[1]); }, "")
						.value(),
					cancellations: _.chain(reservations).filter((r) => { return r.selectedPerson == p._id && r.cancellationReason; }).size().value(),
					billableCancellations: _.chain(reservations).filter((r) => { return r.selectedPerson == p._id && r.cancellationReason && r.cancellationReason.indexOf("Approved") == -1; }).size().value(),
					billableCancellationDays: _.chain(reservations)
						.filter((r) => { return r.selectedPerson == p._id && r.cancellationReason && r.cancellationReason.indexOf("Approved") == -1; })
						.sortBy("reservationDate")
						.pluck("reservationDate")
						.uniq()
						.map((d) => { return moment(d, "MM/DD/YYYY").format("MMMM-D"); })
						.reduce((memo, d) => { const dp = d.split("-"); return memo + (memo.indexOf(dp[0]) < 0 ? (memo.length > 0 ? "<br/>" : "") + dp[0] + " " + dp[1] : "," + dp[1]); }, "")
						.value(),
					lateMinutes: p.lateMinutes

				};
			})
			.filter((p) => { return p.numFullDays > 0 || p.cancellations > 0 || p.noShows > 0; })
			.sortBy("name")
			.value();
	},
	async staffPayData(options) {
		this.unblock();
		const currentUser = await Meteor.userAsync();
		const cuser = await currentUser.fetchPerson();
		if (!currentUser || cuser?.type !== "admin") {
			throw new Meteor.Error(403, "Access denied");
		}
		const currentOrg = await Orgs.findOneAsync({ _id: currentUser.orgId });
		const orgArray = (options.orgs && options.orgs.length > 0) ? options.orgs : [currentOrg._id];
		const typesArray = options.includeAdmins ? ['staff', 'admin'] : ['staff'];
		const data = [];
		const staff = await People.find({ orgId: { $in: orgArray }, type: { $in: typesArray } }).fetchAsync();
		const orgNamesMap = {};
		for (const member of staff) {
			if (member.inActive) {
				continue;
			}
			if (!orgNamesMap[member.orgId]) {
				const org = await Orgs.findOneAsync({ _id: member.orgId });
				orgNamesMap[member.orgId] = org.name ?? '';
			}
			const payRate = member.profileData?.payRate ?? member.payRate;
			const payRateFormatted = payRate ? numeral(payRate).format('$0.00') : '';
			data.push({
				site: orgNamesMap[member.orgId],
				name: member.firstName + ' ' + member.lastName,
				payRate: payRateFormatted,
				status: member.profileData?.employeeClassification ?? member.employeeClassification
			});
		}
		data.sort((a, b) => {
			if (a.site === b.site) {
				return a.name.localeCompare(b.name);
			}
			return a.site.localeCompare(b.site);
		});
		return data;
	},
	async enrollmentStatusV2(options) {
		this.unblock();
		var currentUser = await Meteor.userAsync();
		const cuser = await currentUser.fetchPerson();
		console.log("options", options);
		if (!currentUser ||
			cuser?.type != "admin")
			throw new Meteor.Error(403, "Access denied");

		var currentOrg = await Orgs.findOneAsync({ _id: currentUser.orgId });
		moment.tz.setDefault(currentOrg.getTimezone());
		var onlyIncludeWithdrawnChildren = options.includeOnly == "withdrawn",
			onlyIncludeNewEnrollees = options.includeOnly == "enrollees",
			onlyIncludeWaitlistees = options.includeOnly == "waitlistees";

		var orgArray = (options.orgs && options.orgs.length > 0) ? options.orgs : [currentOrg._id];
		var personTypeArray = (options.personTypes && options.personTypes.length > 0) ? options.personTypes : ["person"];

		var startDateNum = moment(options.startDate).startOf('day').valueOf();
		var endDateNum = moment(options.endDate).endOf('day').valueOf();

		var query = {
			orgId: { $in: orgArray },
			type: { $in: personTypeArray },
			designations: { $nin: ["Wait List"] },
		};

		var enrollmentExists = {};
		var enrollmentDateVal = {};
		var withdrawExists = {};
		var withdrawDateVal = {};

		const waitlistAddedQuery = { waitlistAddedDate: { $gte: startDateNum, $lte: endDateNum } };
		if (onlyIncludeWaitlistees) {
			delete query.designations;
			query["waitlistAddedDate"] = waitlistAddedQuery["waitlistAddedDate"];
		}
		else if (currentOrg.profileDataPrefix()) {
			enrollmentExists[`${currentOrg.profileDataPrefix()}.enrollmentDate`] = { $exists: false };
			enrollmentDateVal[`${currentOrg.profileDataPrefix()}.enrollmentDate`] = { $lte: endDateNum };
			// query[`${currentOrg.profileDataPrefix()}.enrollmentDate`] = { $lte: endDateNum };
			withdrawExists[`${currentOrg.profileDataPrefix()}.withdrawDate`] = { $exists: false };
			withdrawDateVal[`${currentOrg.profileDataPrefix()}.withdrawDate`] = { $gte: startDateNum };
			if (onlyIncludeWithdrawnChildren) {
				query[`${currentOrg.profileDataPrefix()}.withdrawDate`] = { $gte: startDateNum, $lte: endDateNum }
			} else if (onlyIncludeNewEnrollees) {
				query[`${currentOrg.profileDataPrefix()}.enrollmentDate`] = { $gte: startDateNum, $lte: endDateNum };
			} else {
				delete query.designations;
				query["$or"] = [waitlistAddedQuery, { "$and": [{ $or: [withdrawExists, withdrawDateVal] }, { $or: [enrollmentExists, enrollmentDateVal] }] }];
			}
		} else {
			// query.enrollmentDate = { $lte: endDateNum };
			enrollmentExists["enrollmentDate"] = { $exists: false };
			enrollmentDateVal["enrollmentDate"] = { $lte: endDateNum };
			withdrawExists["withdrawDate"] = { $exists: false }
			withdrawDateVal["withdrawDate"] = { $gte: startDateNum };
			if (onlyIncludeWithdrawnChildren) {
				query["withdrawDate"] = { $gte: startDateNum, $lte: endDateNum };
			} else if (onlyIncludeNewEnrollees) {
				query["enrollmentDate"] = { $gte: startDateNum, $lte: endDateNum };
			} else {
				delete query.designations;
				query["$or"] = [waitlistAddedQuery, { "$and": [{ $or: [withdrawExists, withdrawDateVal] }, { $or: [enrollmentExists, enrollmentDateVal] }] }];
			}
		}

		const allGroups = await Groups.find({ orgId: { $in: orgArray } }, { fields: { name: 1 } }).fetchAsync();
		const allOrgs = await Orgs.find({ _id: { $in: orgArray } }, { fields: { _id: 1, parentOrgId: 1, name: 1, billing: 1, valueOverrides: 1, customizations: 1 } }).fetchAsync();
		const fullOrgs = await Orgs.find().fetchAsync();
		const orgsMap = ReportAggregation.orgHierarchyMap(fullOrgs, allOrgs);
		const orgsMeta = ReportAggregation.orgsMeta(fullOrgs, allOrgs);

		let scheduleTypes = [];
		let allTuitionItems = []
		_.each(allOrgs, function (o) {
			if (o.billing && o.billing.plansAndItems) {
				var tuitionItems = _.filter(o.billing.plansAndItems, (pi) => pi.category == 'tuition');
				allTuitionItems = allTuitionItems.concat(tuitionItems);
				scheduleTypes = scheduleTypes.concat(_.filter(o.getScheduleTypes(), st => !st.hideInForecasting));
			}
		});

		const resQuery = {
			orgId: { $in: orgArray },
			$or: [{ scheduledEndDate: null }, { scheduledEndDate: { "$gte": startDateNum } }],
			recurringFrequency: { "$exists": true }
		};
		const allReservations = await Reservations.find(resQuery).fetchAsync();

		const rowData = [];
		let total = 0
		const withdrawnFteSummary = {
			tuition: 0.0,
			fteTotal: 0.0,
		};

		const peopleList = await People.find(query, { sort: { lastName: 1, firstName: 1 }, fields: { engagements: 0, lastMomentByType: 0, lastMoment: 0, lastInformedArrival: 0 } }).fetchAsync()
		peopleList.forEach((p) => {

			++total;

			let orgName = "n/a";
			const personOrg = _.find(allOrgs, function (o) {
				return o._id == p.orgId
			});
			if (personOrg) orgName = personOrg.name;

			let groupName = "";
			const personGroup = _.find(allGroups, function (g) {
				return g._id == p.defaultGroupId;
			})
			if (personGroup) groupName = personGroup.name;

			let recurTuition = "", potentialLostTuition = 0.0;
			let fteTotal = 0.0;

			const personEnrollmentDate = p.getEnrollmentDate({ currentOrg }),
				enrollmentDateNum = personEnrollmentDate && new moment(personEnrollmentDate, "MM/DD/YYYY").valueOf(),
				personWithdrawDate = p.getWithdrawDate({ currentOrg }),
				withdrawDateNum = personWithdrawDate && new moment(personWithdrawDate, "MM/DD/YYYY").valueOf(),
				personWaitListDate = p.waitlistAddedDate && new moment(p.waitlistAddedDate).format("MM/DD/YYYY"),
				waitlistDateNum = personWaitListDate && p.waitlistAddedDate; //_.contains(p.designations, "Wait List") &&

			if (p.type == "person") {
				recurTuition = 0.0;
				var allEnrolledPlans = (p?.billing?.enrolledPlans ?? []).filter(ep => ep.enrollmentDate <= endDateNum); //( ep => !ep.expirationDate || ep.expirationDate >= startDateNum);
				allEnrolledPlans.sort((a, b) => b.enrollmentDate - a.enrollmentDate);
				//get total tuitiion from enrolled plans and find overlapping
				let mostRecentEnrolledTuitionPlan = null;
				_.each(allEnrolledPlans.filter(ep => !ep.expirationDate || ep.expirationDate >= startDateNum), (plan) => {
					var matchedTuitionPlan = _.find(allTuitionItems, (ti) => ti._id == plan._id);
					if (mostRecentEnrolledTuitionPlan == null && matchedTuitionPlan) {
						mostRecentEnrolledTuitionPlan = matchedTuitionPlan;
						if (!mostRecentEnrolledTuitionPlan.expirationDate) mostRecentEnrolledTuitionPlan.expirationDate = new moment().add(1, 'year').valueOf();
						recurTuition += parseFloat(matchedTuitionPlan.amount);
					} else if (matchedTuitionPlan) {
						if (!matchedTuitionPlan.expirationDate || matchedTuitionPlan.expirationDate >= mostRecentEnrolledTuitionPlan.expirationDate) {
							recurTuition += parseFloat(matchedTuitionPlan.amount);
						}
					}

				});

				const mostRecentExpiredTuitionPlan = _.chain(allEnrolledPlans)
					.filter(ep => (!ep.expirationDate || ep.expirationDate <= withdrawDateNum) && _.contains(_.pluck(allTuitionItems, "_id"), ep._id))
					.sortBy(ep => { -1 * (!ep.expirationDate ? Number.MAX_VALUE : ep.expirationDate); })
					.first()
					.value();

				if (mostRecentExpiredTuitionPlan) {
					const matchedTuitionPlan = _.find(allTuitionItems, (ti) => ti._id == mostRecentExpiredTuitionPlan._id);
					if (matchedTuitionPlan) {
						const planAmount = mostRecentExpiredTuitionPlan.overrideRate || matchedTuitionPlan.amount,
							availableAllocations = _.filter(mostRecentExpiredTuitionPlan.allocations,
								(a) => a.allocationType == "discount" && (!a.discountExpires || a.discountExpires >= withdrawDateNum)),
							sumOfDollarDiscounts = _.chain(availableAllocations)
								.filter((a) => { return a.allocationType != "family" && a.amountType == "dollars"; })
								.reduce((memo, a) => { return memo + a.amount; }, 0.0)
								.value();

						let totalDiscounts = 0;
						_.each(availableAllocations, a => {
							let discountAmount = 0;
							if (a.amountType == "dollars")
								discountAmount = a.amount;
							else
								discountAmount = BillingUtils.roundToTwo((planAmount - (personOrg.billing.toplinePercentDiscounts ? 0 : sumOfDollarDiscounts)) * a.amount / 100.0);

							if (discountAmount < 0) discountAmount = 0;
							totalDiscounts += discountAmount;
						});

						potentialLostTuition += parseFloat(planAmount) - totalDiscounts;
					}
				}
				recurTuition = recurTuition.toFixed(2);
				if (parseInt(recurTuition) == 0) recurTuition = "";

				let personReservations = allReservations.filter(r => r.selectedPerson == p._id && r.scheduledDate < endDateNum);
				personReservations.sort((a, b) => b.scheduledDate - a.scheduledDate);

				const firstReservation = personReservations.shift();
				let mostRecentReservations = firstReservation ? [firstReservation] : [];
				_.each(personReservations, (r) => {
					if (!r.scheduledEndDate || new moment(r.scheduledEndDate).isSameOrAfter(new moment(mostRecentReservations?.[0]?.scheduledEndDate), 'day')) {
						mostRecentReservations.push(r);
					}
				});

				_.each(mostRecentReservations, (r) => {
					const st = scheduleTypes.find(st => st._id == r?.scheduleType);
					if (st) {
						const fteCount = st.fteCount?.length > 0 ? parseFloat(st.fteCount) : 1.0;
						fteTotal += (fteCount * r?.recurringDays?.length ?? 0);
					}
				})

				if (parseFloat(fteTotal) > 0) {
					fteTotal = fteTotal / 5;
					fteTotal = (Math.round(fteTotal * 10) / 10).toFixed(1);
				}
			}

			const newRow = {
				firstName: p.firstName,
				lastName: p.lastName,
				type: (p.type == "person") ? "child" : p.type,
				orgName,
				orgId: p.orgId,
				groupName,
				enrollmentDate: personEnrollmentDate,
				withdrawDate: personWithdrawDate,
				waitlistDate: personWaitListDate,
				enrolledInPeriod: enrollmentDateNum && enrollmentDateNum >= startDateNum && enrollmentDateNum <= endDateNum,
				withdrewInPeriod: withdrawDateNum && withdrawDateNum >= startDateNum && withdrawDateNum <= endDateNum,
				waitlistInPeriod: waitlistDateNum && waitlistDateNum >= startDateNum && waitlistDateNum <= endDateNum,
				deactivated: !p.isActive(),
				deactivationReason: p.deactivationReason,
				tuition: recurTuition,
				fteTotal,
			};

			rowData.push(newRow);

			if (newRow.withdrewInPeriod) {
				newRow.lostTuition = potentialLostTuition.toFixed(2);
				let newTuition = parseFloat(withdrawnFteSummary.tuition);
				if (newRow.lostTuition > 0) newTuition += parseFloat(newRow.lostTuition); //parseFloat(newRow.tuition);
				withdrawnFteSummary.tuition = newTuition.toFixed(2);

				if (newRow.fteTotal != "") withdrawnFteSummary.fteTotal = (parseFloat(withdrawnFteSummary.fteTotal) + parseFloat(newRow.fteTotal)).toFixed(1);
			}

		});

		let summaryCounter = 0;
		const summaryInit = (summaries, orgName, isRollup) => {
			if (!summaries[orgName]) {
				summaries[orgName] = {
					total: 0,
					enrolled: 0,
					withdrawn: 0,
					waitlistees: 0,
					withdrawalReasons: {},
					isRollup
				}
			}
		}
		const summaryHelper = (row, summary) => {
			summary.total++;
			if (row.enrolledInPeriod) {
				summary.enrolled++;
			}
			if (row.withdrewInPeriod) {
				summary.withdrawn++;
			}
			if (row.waitlistInPeriod) {
				summary.waitlistees++;
			}
			if (row.deactivationReason) {
				summary["withdrawalReasons"][row.deactivationReason] = (summary["withdrawalReasons"][row.deactivationReason] || 0) + 1;
			}
		};
		const summaries = {};
		summaryInit(summaries, 'All', true);
		_.each(rowData, (row) => {
			for (const parentOrg of orgsMap[row.orgId] || []) {
				summaryInit(summaries, parentOrg, true);
				summaryHelper(row, summaries[parentOrg]);
			}
			const orgName = row.orgName;
			summaryInit(summaries, orgName);
			summaryHelper(row, summaries[orgName]);
			summaryHelper(row, summaries['All']);
		});
		console.log('sum', summaries);

		const centerTotalsArray = [];
		for (const key in summaries) {
			if (key === 'All') {
				continue;
			}
			summaries[key].orgName = key;
			centerTotalsArray.push(summaries[key]);
		}
		ReportAggregation.applyMeta(centerTotalsArray, orgsMeta, 'orgName');
		const rekeyedCenterTotals = { 'All': summaries['All'] };
		for (const tmp of centerTotalsArray) {
			rekeyedCenterTotals[tmp.orgName] = tmp;
		}

		return {
			rows: rowData,
			total,
			withdrawnFteSummary,
			breakdowns: rekeyedCenterTotals
		};

	},
	async subsidyReport(options) {
		this.unblock();
		var currentUser = await Meteor.userAsync();
		const cuser = await currentUser.fetchPerson();
		if (!currentUser ||
			cuser?.type != "admin")
			throw new Meteor.Error(403, "Access denied");

		var currentOrg = await Orgs.findOneAsync({ _id: currentUser.orgId });
		if (!currentOrg || !currentOrg.hasCustomization("report/subsidy/enabled")) {
			throw new Meteor.Error(500, "No integration enabled for Subsidy Report");
		}
		moment.tz.setDefault(currentOrg.getTimezone());

		var orgArray = (options.orgs && options.orgs.length > 0) ? options.orgs : [currentOrg._id];

		var startDateNum = moment(options.startDate).startOf('day').valueOf();
		var endDateNum = moment(options.endDate).endOf('day').valueOf();

		var query = {
			orgId: { $in: orgArray },
			type: "person",
		};

		// NOTE We Assume the prefix since its a customer specific Report;
		var enrollmentExists = { "profileData.enrollmentDate": { $exists: false } };
		var enrollmentDateVal = { "profileData.enrollmentDate": { $lte: endDateNum } };
		var withdrawExists = { "profileData.withdrawDate": { $exists: false } };
		var withdrawDateVal = { "profileData.withdrawDate": { $gte: startDateNum } };
		query["$and"] = [{ $or: [withdrawExists, withdrawDateVal] }, { $or: [enrollmentExists, enrollmentDateVal] }];

		const allOrgs = await Orgs.find({ _id: { $in: orgArray } }, { fields: { name: 1 } }).fetchAsync();
		const rowData = [];
		let totalChildren = 0
		var familyIncludeList = [];
		const allPeoples = await People.find(query, { sort: { lastName: 1, firstName: 1 } }).fetchAsync();
		for (const p of allPeoples) {
			var parents = "";
			const allRelationships = await Relationships.find({ $or: [{ personId: p._id }, { targetId: p._id }], relationshipType: "family", primaryCaregiver: true, orgId: p.orgId }).fetchAsync();
			for (const r of allRelationships) {
				familyIncludeList.push(r.personId);
				const familyPerson = await People.findOneAsync(r.personId);
				if (familyPerson) {
					parents += `| ${familyPerson.firstName} ${familyPerson.lastName} |`;
				}
			}

			++totalChildren;

			let orgName = "n/a";
			const personOrg = _.find(allOrgs, function (o) {
				return o._id == p.orgId
			});
			if (personOrg) orgName = personOrg.name;

			var profileData = (p.profileData) ? dot.dot(p.profileData) : {};

			rowData.push({
				firstName: p.firstName,
				lastName: p.lastName,
				orgName,
				type: "Child",
				parents,
				enrollmentDate: p.getEnrollmentDate({ currentOrg }),
				withdrawDate: p.getWithdrawDate({ currentOrg }),
				deactivated: !p.isActive(),
				deactivationReason: p.deactivationReason,
				profileData,
			});
		};

		let totalSingleParents = 0;
		let totalParents = 0;
		await People.find({ _id: { $in: familyIncludeList }, type: "family" }).forEachAsync((p) => {
			++totalParents;

			let orgName = "n/a";
			const personOrg = _.find(allOrgs, function (o) {
				return o._id == p.orgId
			});
			if (personOrg) orgName = personOrg.name;

			if (p.profileData && p.profileData.singleParent && p.profileData.singleParent.toLowerCase() == "yes") {
				++totalSingleParents;
			}
			var profileData = (p.profileData) ? dot.dot(p.profileData) : {};

			rowData.push({
				firstName: p.firstName,
				lastName: p.lastName,
				orgName,
				type: "Family",
				profileData,
			});
		});

		return {
			rows: rowData,
			totals: {
				totalChildren,
				totalParents,
				totalSingleParents,
				totalFamilies: ((totalParents - totalSingleParents) / 2 + totalSingleParents)
			},
		};

	},
	staffTimeReportAggregates: function(...args) {
		this.unblock();
		return staffTimeReportAggregates(...args);
	},
	'trackEngagement': async function (engagementData) {
		this.unblock();
		check(engagementData.type, String);
		if (engagementData.subType) check(engagementData.subType, String);
		if (engagementData.detail) check(engagementData.detail, String);
		const user = await Meteor.userAsync();
		if (!user) return;

		processAnalytics({ engagementData });
	},
	'trackClientActivity': async function (activityData) {
		this.unblock();
		var currentUser = await Meteor.userAsync();
		const currentOrg = await Orgs.current();
		var currentPerson = currentUser && await currentUser.fetchPerson();
		if (!currentUser || !_.include(["media-share", "report-created", "report-exported", "moment-reaction", 'report-queued'], activityData.label))
			throw new Meteor.Error(403, "Access denied");
		activityData = activityData || {};

		activityData.orgId = currentUser.orgId;
		activityData.orgName = currentOrg.name;
		activityData.personId = currentPerson._id;
		activityData.type = currentPerson.type;

		processAnalytics({ metaCxData: { type: activityData.label, data: activityData } });
	},
	'webHelpBadge': async function () {
		this.unblock();
		var currentUser = await Meteor.userAsync();
		if (!currentUser) {
			throw new Meteor.Error(403, "Access denied");
		}

		return await SharedConfigs.findOneAsync({ key: "appHelpInfo" });
	},
	'runInvoicesNow': async function () {
		this.unblock();
		var currentUser = await Meteor.userAsync();
		const cuser = await currentUser.fetchPerson();
		if (!currentUser ||
			!(cuser.superAdmin))
			throw new Meteor.Error(403, "Access denied");

		console.log("queueing runinvoices");
		await AwsBillingService.runInvoicesForOrg(currentUser.orgId);

	},
	'runAutoPaymentsNow': async function (options) {
		this.unblock();
		var currentUser = await Meteor.userAsync();
		const currentOrg = await Orgs.current();
		const cuser = await currentUser.fetchPerson();
		if (!currentUser || !(cuser.superAdmin)) {
			throw new Meteor.Error(403, "Access denied");
		}
		const dueDateStamp = new moment.tz(options.runDate, 'MM/DD/YYYY', currentOrg.getTimezone()).startOf('day').valueOf();
		console.log("dueDateStamp", dueDateStamp);
		const batchStamp = Date.now();
		console.log("queueing runAutoPayments");
		await AwsBillingService.runAutopays(currentUser.orgId, dueDateStamp, batchStamp);

	},

	// Delete after BUGS-2915 passes QA
	'runPrecomputeNow': async function (orgId) {
		this.unblock();
		await Metrics.precomputeOrgById(orgId);
	},

	'generateItemSchedulesForChild': async function (pendingItemCharges, personId) {
		this.unblock();
		await processPermissions({
			assertions: [{ context: "reservations", action: "edit" }],
			evaluator: (person) => person.type === USER_TYPES.ADMIN || person.type === USER_TYPES.STAFF,
			throwError: true
		});

		try {
			const currentOrg = await Orgs.current();
			const timezone = currentOrg.getTimezone();
			const items = pendingItemCharges.map((item) => item.originalItem);
			const childEnrolledItemIds = pendingItemCharges.map((item) => item._id);
			return await EnrollmentsService.generateItemSchedulesForChild(personId, items, timezone, childEnrolledItemIds);
		} catch (error) {
			Log.error("Error in 'generateItemSchedulesForChild':" + error.reason || error.message);
			throw new Meteor.Error(error.error || 'Error', error.reason || error.message);
		}
	},

	'generateManualInvoice': async function (options, orgId = null, fromRegistration = false, allReservationIds) {
		this.unblock();
		if (!fromRegistration) {
			await processPermissions({
				assertions: [{ context: "billing/invoices", action: "edit" }, {
					context: "billing/invoices/itemCharges",
					action: "edit"
				}],
				evaluator: (person) => person.type == "admin",
				throwError: true
			});
		}
		const user = await Meteor.userAsync();
		const currentUser = orgId ? null : user;
		const invoiceOptions = GenerateManualInvoiceService.generateInvoiceOptions(options, currentUser, orgId)
		if (allReservationIds) {
			invoiceOptions["allReservationIds"] = allReservationIds
		}
		try {
			await AwsBillingService.createSingleInvoice(invoiceOptions);
			
			const personId = options.personId;
			if (personId) {
				Meteor.setTimeout(async () => {
					try {
						await People.updateAsync(
							{ _id: personId },
							{ $set: { "billing.pendingCharges": [] } }
						);
					} catch (err) {
						console.error("Error clearing pending charges:", err);
					}
				}, 1000);
			}
		} catch (e) {
			throw new Meteor.Error(500, "There was an issue with generating the invoice: " + e.message);
		}
	},
	'resendInvoice': async function (options) {
		this.unblock();
		var currentUser = await Meteor.userAsync();
		await processPermissions({
			assertions: [{ context: "billing/invoices", action: "edit" }, { context: "billing/invoices/resend", action: "edit" }],
			evaluator: (person) => person.type == "admin",
			throwError: true
		});

		const invoice = await Invoices.findOneAsync({ orgId: currentUser.orgId, _id: options.invoiceId });
		if (invoice)
			await processBillingEmail({ emailType: "invoice", personId: invoice.personId, invoiceId: options.invoiceId });
	},
	async insertBillingCreditMemo(options) {
		this.unblock();
		await processPermissions({
			assertions: [{ context: "billing/payments", action: "edit" }, { context: "billing/creditMemos/create", action: "edit" }],
			evaluator: (person) => person.type == "admin",
			throwError: true
		});

		const currentUser = await Meteor.userAsync();
		const person = await People.findOneAsync({ orgId: currentUser.orgId, _id: options.personId });
		if (!person || person.type != "family") throw new Meteor.Error(500, "Cannot create credit for this type of person");

		await People.updateAsync({ _id: person._id }, {
			$push: {
				"billing.creditMemos": {
					_id: Random.id(),
					type: options.type,
					createdAt: new moment().valueOf(),
					createdBy: currentUser._id,
					notes: options.notes,
					openAmount: parseFloat(options.amount),
					originalAmount: parseFloat(options.amount)
				}
			}
		});

		const invoicesCredited = await creditFamilyPersonInvoices({ familyPersonId: person._id });

		return invoicesCredited;
	},
	async voidCreditLine(options) {
		this.unblock();
		let currentUser = await Meteor.userAsync();
		await processPermissions({
			assertions: [{ context: "billing/payments", action: "edit" }, { context: "billing/payments/void", action: "edit" }],
			evaluator: (person) => person.type === "admin",
			throwError: true
		});

		const invoice = await Invoices.findOneAsync({ orgId: currentUser.orgId, _id: options.invoiceId });

		if (!invoice) {
			throw new Meteor.Error(500, "Invoice not found");
		}

		let creditLine = invoice.credits[options.existingCreditIndex];

		if (!creditLine) {
			throw new Meteor.Error(500, "Credit / payment not found.");
		}

		let amountAdjustment = parseFloat(creditLine.amount);
		creditLine.amount = 0;
		creditLine.voidedBy = currentUser.personId;
		creditLine.voidedAt = new Date().valueOf();
		creditLine.voidedReason = options.void_reason;
		creditLine.voidedNote = options.void_note;
		creditLine.voidedAmount = amountAdjustment;

		const creditKey = "credits." + options.existingCreditIndex;
		const query = { "$set": {} };
		query["$set"][creditKey] = creditLine;

		if (creditLine.creditReason !== 'reimbursable') {
			query["$inc"] = { openAmount: amountAdjustment };
		}

		await Invoices.updateByIdWithJournalEntry(options.invoiceId, query, {
			userId: currentUser._id,
			personId: currentUser.personId,
			orgId: currentUser.orgId,
			reason: `Voided credit line with reason ${options.void_reason}${options.void_note ? ` and note ${options.void_note}` : ''}`,
			reasonLocation: `server/methods.js:voidCreditLine`
		});

		if (creditLine.payment_type === "credit_memo") {
			await People.updateAsync({
				orgId: currentUser.orgId,
				"billing.creditMemos._id": creditLine.creditMemoId,
			},
				{ "$inc": { "billing.creditMemos.$.openAmount": amountAdjustment } });
		}
	},
	async manualRefundCreditLine(options) {
		this.unblock();
		let currentUser = await Meteor.userAsync();
		await processPermissions({
			assertions: [{ context: "billing/payments", action: "edit" }, { context: "billing/payments/refund", action: "edit" }],
			evaluator: (person) => person.type == "admin",
			throwError: true
		});

		const invoice = await Invoices.findOneAsync({ orgId: currentUser.orgId, _id: options.invoiceId });
		if (!invoice) throw new Meteor.Error(500, "Invoice not found");

		let creditLine = invoice.credits[options.existingCreditIndex];
		if (!creditLine) throw new Meteor.Error(500, "Credit / payment not found.");

		let amountAdjustment = parseFloat(creditLine.amount);
		creditLine.amount = 0;
		creditLine.refundedBy = currentUser.personId;
		creditLine.refundedAt = new Date().valueOf();
		creditLine.refundedNote = options.notes;
		creditLine.refundedAmount = amountAdjustment;

		const creditKey = "credits." + options.existingCreditIndex;
		let query = { "$set": {} };
		query["$set"][creditKey] = creditLine;
		query["$inc"] = { openAmount: amountAdjustment };

		await Invoices.updateByIdWithJournalEntry(options.invoiceId, query, {
			userId: currentUser._id,
			personId: currentUser.personId,
			orgId: currentUser.orgId,
			reason: `Manual refund of credit line${options.notes ? ` with note ${options.notes}` : ''}`,
			reasonLocation: `server/methods.js:manualRefundCreditLine`
		});

		await HistoryAuditService.logRefund({
			amount: amountAdjustment,
			invoiceNumber: invoice.invoiceNumber,
			invoiceId: invoice._id,
			parentId: creditLine.paidBy,
			childId: invoice.personId,
			performedByUser: currentUser,
		});
	},
	async manualReverseReallocation(options) {
		this.unblock();
		const currentOrg = await Orgs.current();
		options.currentUser = await Meteor.userAsync();
		options.timezone = currentOrg.getTimezone() || 'America/New_York';

		await processPermissions({
			assertions: [{ context: "billing/invoices", action: "edit" }],
			evaluator: (person) => person.type === "admin",
			throwError: true
		});

		try {
			await BillingInvoiceService.reverseReallocation(options)
		} catch (e) {
			throw new Meteor.Error(e.error, e.reason);
		}
	},
	async allOpenInvoicesForParent(personId) {
		this.unblock();
		const person = await People.findOneAsync(personId)

		// Use the openInvoices method to fetch invoices
		const openInvoices = await person.openInvoices();

		// Fetch related people data in a single query
		const children = await People.find({
			_id: { $in: openInvoices.map(invoice => invoice.personId) }
		}, {
			fields: { firstName: 1, lastName: 1 }
		}).fetchAsync();

		// Create a lookup map for faster access to people's names
		const personMap = {};
		children.forEach(child => {
			personMap[child._id] = `${child.firstName} ${child.lastName}`;
		});

		// Map invoices to include personName
		return openInvoices.map(invoice => ({
			...invoice,
			personName: personMap[invoice.personId] || 'Unknown'
		}));
	},
	async adjustCreditLine(options) {
		this.unblock();
		const currentOrg = await Orgs.current();
		options.currentUser = await Meteor.userAsync();
		options.timezone = currentOrg.getTimezone() || 'America/New_York';

		await processPermissions({
			assertions: [
				{ context: "billing/payments/void", action: "edit" },
				{ context: "billing/payments/refund", action: "edit" }
			],
			evaluator: (person) => person.type === "admin",
			throwError: true
		});

		try {
			await BillingInvoiceService.adjustPayment(options)
		} catch (e) {
			throw new Meteor.Error(e.error, e.reason, e.details);
		}
	},
	async superAdminList(options) {
		this.unblock();
		const showAll = options.showAll || false;
		const includeParentOrg = options.includeParentOrg || false;
		const includeChildrenCount = options.includeChildrenCount || false;

		var currentUser = await Meteor.userAsync();
		const cuser = await currentUser.fetchPerson();
		if (!currentUser ||
			!(cuser.superAdmin))
			throw new Meteor.Error(403, "Access denied");


		var rawUsers = Meteor.users.rawCollection();

		var usercountscursor = rawUsers.aggregate([
			{ $group: { _id: "$orgId", count: { $sum: 1 } } }
		]);
		var usercounts = await usercountscursor.toArray();

		const orgs = await Orgs.find({ "name": { "$ne": "My Care Organization" } }, { fields: { name: 1, parentOrgId: 1 } }).fetchAsync();
		let mappedOrgs = await Promise.all(orgs.map(async (o) => {
			var orgCount = _.find(usercounts, function (uc) { return uc._id == o._id; });
			if (orgCount) {
				o.userCount = orgCount.count;
			}
			if (includeParentOrg) {
				if (o.parentOrgId) {
					var parentOrg = await Orgs.findOneAsync({ _id: o.parentOrgId });
					o.parentOrg = parentOrg;
				} else {
					o.parentOrg = null;
				}
			}
			if (includeChildrenCount) {
				o.childrenCount = await Orgs.find({ parentOrgId: o._id }).countAsync();
			}
			return o;
		}));
		if (!showAll) {
			mappedOrgs = _.filter(mappedOrgs, function (o) { return !o.inactive && o.userCount && o.userCount > 0; });
		}
		return _.sortBy(mappedOrgs, 'name');
	},
	async 'superadminGetCustomers'(options) {
		this.unblock();
		var currentUser = await Meteor.userAsync();
		const cuser = await currentUser.fetchPerson();
		if (!currentUser ||
			!(cuser.superAdmin))
			throw new Meteor.Error(403, "Access denied");

		if (options.orgId)
			return await Orgs.findOneAsync({ _id: options.orgId });
		else
			return await Orgs.find({}, { fields: { "name": 1, "inactive": 1, "engagementCounts": 1, "enableSwitchOrg": 1, "parentOrgId": 1 }, sort: { name: 1 } }).fetchAsync();
	},
	'superadminGetLineItemsForVoidedInvoices': async function (options) {
		this.unblock();
		try {
			const result = await Invoices.getLineItemsWithoutVoidsForVoidedInvoices(options);
			return result;
		} catch (e) {
			console.error(e);
			throw new Meteor.Error(e.error, e.reason);
		}
	},
	'superAdminFixLineItemsForVoidedInvoices': async function (options) {
		this.unblock();
		const invoiceIds = options.invoiceIds;

		const currentUser = await Meteor.userAsync();
		const currentUserId = currentUser && currentUser._id;

		if (!invoiceIds || !Array.isArray(invoiceIds) || invoiceIds.length === 0) {
			throw new Meteor.Error(400, "No invoiceIds provided");
		}

		if (!currentUserId) {
			throw new Meteor.Error(400, "No currentUserId provided");
		}

		await Promise.all(invoiceIds.map(async (invoiceId) => {
			const retrievedInvoice = await Invoices.findOneAsync({ _id: invoiceId });
			if (!retrievedInvoice) {
				throw new Meteor.Error(400, `Invoice not found for id: ${invoiceId}`);
			}
			const voidedAtDatetime = retrievedInvoice.voidedAt;
			if (!voidedAtDatetime) {
				throw new Meteor.Error(400, `Invoice has not been voided for id: ${invoiceId}`);
			}
			if (retrievedInvoice.lineItems.length === 0) {
				throw new Meteor.Error(400, `Invoice has no lineItems for id: ${invoiceId}`);
			}

			const lineItemsWithDiscountsToFix = retrievedInvoice.lineItems.map((lineItem) => {
				const needToApplyUpdate = _.some(lineItem.appliedDiscounts, (appliedDiscount) => {
					return appliedDiscount.amount > 0 || !appliedDiscount.voidedAt;
				});
				if (!needToApplyUpdate) {
					return lineItem
				}
				return {
					...lineItem,
					appliedDiscounts: lineItem.appliedDiscounts.map((appliedDiscount) => {
						return {
							...appliedDiscount,
							amount: 0,
							voidedAt: voidedAtDatetime,
						};
					}),
				};
			});

			if (lineItemsWithDiscountsToFix.length === 0) {
				throw new Meteor.Error(400, `No lineItems with discounts to fix for id: ${invoiceId}`);
			}

			const result = await Invoices.updateByIdWithLog(invoiceId, {
				$set: {
					lineItems: lineItemsWithDiscountsToFix,
				},
			}, {
				userId: currentUserId,
				orgId: retrievedInvoice.orgId, // Is this a fair assumption? This is a superadmin route, so technically there's no orgId, but we want orgId to be set for logging purposes
				reason: `Super admin migration to fix line items`,
				reasonLocation: 'server/methods.js:superAdminFixLineItemsForVoidedInvoices',
			});

			return result;
		}));
	},
	'trackMediaLike': async function (likeData) {
		this.unblock();
		var moment = await Moments.findOneAsync({ _id: likeData.momentId });
		if (moment) {
			var setObject = {}, setValue = {};
			setValue.type = "like";
			setValue.createdAt = new Date().valueOf();
			setValue.personId = likeData.personId;
			if (likeData.recipientId) {
				var person = await People.findOneAsync({ _id: likeData.recipientId });
				if (person) setValue.sender = person.firstName + " " + person.lastName;
			}
			setObject["reactions." + likeData.recipientId] = setValue;
			await Moments.updateAsync({ _id: likeData.momentId }, { "$set": setObject });
			processAnalytics({
				engagementData: {
					orgId: moment.orgId,
					type: "email",
					subType: "summary_like",
					detail: moment.momentType,
					momentId: moment._id,
					createdBy: "SYSTEM",
					targetPersonId: likeData.personId,
					sourcePersonId: likeData.recipientId
				}
			});
		}
		return;
	},
	'getAllOrgMembershipsForUser': async function (options) {
		this.unblock();
		const currentUser = await Meteor.userAsync();

		if (!currentUser) {
			throw new Meteor.Error(403, "Access denied");
		}

		const memberships = currentUser?.membership || [];
		const currentOrg = await Orgs.current();
		const mappings = [{ orgId: currentOrg._id, orgName: currentOrg.name, personId: currentUser.personId }];
		for (const membership of memberships) {
			const org = await Orgs.findOneAsync({ _id: membership.orgId });
			mappings.push({ orgId: org._id, orgName: org.name, personId: membership.personId })
		}

		// console.log("all mappings", mappings);
		const returnMappings = _.uniq(mappings, false, (i) => { return i.orgId });
		// console.log("uniq mappings", returnMappings);

		return returnMappings;
	},
	'getOrgAdditionalData': async function (options) {
		this.unblock();
		var currentUser = await Meteor.userAsync();
		const cuser = await currentUser.fetchPerson();
		if (!currentUser ||
			!(cuser?.type == "admin"))
			throw new Meteor.Error(403, "Access denied");

		const orgAddlConfig = await OrgAdditionalDatas.findForOrg(currentUser.orgId);
		if (!orgAddlConfig?.data)
			throw new Meteor.Error(500, "No additional data configured.");

		if (options.path == "achGeneration") {
			await processPermissions({
				assertions: [{ context: "billing/payments/achGeneration", action: "edit" }],
				evaluator: (person) => person.type == "admin",
				throwError: true
			});

			return orgAddlConfig.data.achGeneration;
		}
	},
	'insertAuditCarePlan': async function (passedAuditData) {
		this.unblock();
		var currentUser = await Meteor.userAsync();
		const cuser = await currentUser.fetchPerson();
		if (!currentUser ||
			!(cuser?.type == "admin" || cuser?.type == "staff"))
			throw new Meteor.Error(403, "Access denied");
		var currentOrg = await Orgs.findOneAsync({ _id: currentUser.orgId });

		var auditData = {
			modifiedByUserId: currentUser._id,
			modifiedByPersonId: currentUser.personId,
			targetPersonId: passedAuditData.targetPersonId,
			action: passedAuditData.action,
			fieldId: passedAuditData.fieldId,
			value: passedAuditData.value,
			createdAt: new Date().valueOf(),
			orgId: currentUser.orgId
		};

		await AuditCarePlans.insertAsync(auditData);

	},
	"getAuditCarePlans": async function (personId, fieldId) {
		this.unblock();
		var currentUser = await Meteor.userAsync();
		const cuser = await currentUser.fetchPerson();
		if (!currentUser ||
			!(cuser?.type == "admin" || cuser?.type == "staff"))
			throw new Meteor.Error(403, "Access denied");

		const carePlans = await AuditCarePlans.find({ orgId: currentUser.orgId, fieldId: fieldId, targetPersonId: personId }).fetchAsync();
		const updatedCarePlans = [];

		for (const cp of carePlans) {
			const updatedByPerson = await People.findOneAsync({ orgId: currentUser.orgId, _id: cp.modifiedByPersonId });
			cp.updatedByPerson = updatedByPerson;
			updatedCarePlans.push(cp);
		}

		return updatedCarePlans;
	},
	"getCheckedInStaff": async function () {
		this.unblock();
		var currentUser = await Meteor.userAsync();
		var currentPerson = (currentUser) ? await currentUser.fetchPerson() : null;
		if (!currentUser || !currentPerson) {
			throw new Meteor.Error(404, "Requesting User/Person Not Found");
		}

		if (!_.contains(["staff", "admin"], currentPerson.type)) {
			throw new Meteor.Error(403, "Access denied");
		}

		//uses existing index on People Collection orgId_1_type_1 and executes a filter on _id
		var checkedInStaff = await People.find({ _id: { $nin: [currentPerson._id] }, type: { $in: ["staff", "admin"] }, checkedIn: true, checkInGroupId: currentPerson.checkInGroupId, orgId: currentPerson.orgId }, { fields: { firstName: 1, lastName: 1 } }).fetchAsync();
		return checkedInStaff || [];
	},
	"switchUserMembership": async function (requestedOrgId) {
		this.unblock();
		const currentUser = await Meteor.userAsync();
		let memberships = currentUser.membership || [];

		// Find all users with the same email (case insensitive) and get the memberships
		if (currentUser && _.isArray(currentUser.emails)) {
			for (const email of currentUser.emails) {
				const users = await usersRepository.findUser(email.address, currentUser._id);
				for (const u of users) {
					if (_.isArray(u.membership)) {
						memberships = memberships.concat(u.membership);
					}
				}
			}
		}
		const unique = [];
		const distinctMemberships = [];
		// Get the distinct membership org ids; make sure to use the first membership that matches
		for (let i = 0; i < memberships.length; i++) {
			if (!unique[memberships[i].orgId]) {
				distinctMemberships.push(memberships[i]);
				unique[memberships[i].orgId] = 1;
			}
		}

		const existingMembership = _.findWhere(distinctMemberships, { orgId: requestedOrgId });
		if (existingMembership) {
			// check the requested membership person
			var userUpdate = { $set: { personId: existingMembership.personId, orgId: existingMembership.orgId }, $addToSet: { membership: { orgId: currentUser.orgId, personId: currentUser.personId } } };
			await Meteor.users.updateAsync(currentUser._id, userUpdate);
			return true;
		}
		return false;
	},
	"getFamilyPeople": async function (personId) {
		this.unblock();
		var currentUser = await Meteor.userAsync();
		var userPerson = currentUser && await currentUser.fetchPerson();
		if (!currentUser || !userPerson) {
			throw new Meteor.Error(403, "Access denied");
		}
		const currentOrg = await Orgs.current();
		const thisPerson = await People.findOneAsync(personId);
		const tRel = await Relationships.find({ targetId: thisPerson._id }).fetchAsync();
		const pRel = await Relationships.find({ personId: thisPerson._id }).fetchAsync();

		let relationships = thisPerson && ((thisPerson.type == "person" && tRel) ||
			(thisPerson.type == "family" && thisPerson._id == userPerson._id && pRel)),
			familyPeople = relationships && await Promise.all(relationships.map(async (r) => {
				const fpUser = await Meteor.users.findOneAsync({ personId: r.personId }),
					rPersonId = thisPerson.type == "person" ? r.personId : r.targetId;

				const personDoc = await People.findOneAsync({ _id: rPersonId });
				const availableContactMethods = personDoc ? personDoc.availableContactMethods({ currentOrg }) : [];
				return {
					relationshipType: r.relationshipType,
					relationshipDescription: r.relationshipDescription,
					person: personDoc,
					availableContactMethods,
					email: fpUser && fpUser.emails && fpUser.emails.length > 0 && fpUser.emails[0].address,
					relationship: r
				};
			}));
		return { familyPeople };
	},
	"checkLoginInfo": function (email) {
		return {
			samlEnabled: false
		};
	},

	async onboardingSendUpdate(data) {
		var currentUser = await Meteor.userAsync();
		const cuser = await currentUser.fetchPerson();
		const currentOrg = await Orgs.current();
		if (!currentUser ||
			!(cuser?.type == "admin" || cuser?.type == "staff"))
			throw new Meteor.Error(403, "Access denied");

		let personUpdate = {};
		if (data.phone) personUpdate["phonePrimary"] = data.phone;
		if (data.email) personUpdate["emailPrimary"] = data.email;
		console.log("data to update", data);
		await People.updateAsync({ _id: cuser._id }, { $set: personUpdate });

		if (data.orgname) {
			await Orgs.updateAsync({ _id: currentOrg._id }, { $set: { "name": data.orgname } });
		}

		if (data.email && IsValidEmail(data.email)) {
			if (currentUser.emails && currentUser.emails.length > 0) Accounts.removeEmail(currentUser._id, currentUser.emails[0].address);
			Accounts.addEmail(currentUser._id, data.email);
		}

		const moment = await Moments.findOneAsync({ orgId: currentOrg._id }, { sort: { DateTime: -1, limit: 1 } });
		if (moment) {
			console.log("moment to send", moment);
			await processRealtime(moment._id);
			const signupData = "Orgname: " + data.orgname + "<br/>Phone: " + data.phone + "<br/>Email: " + data.email + "<br/>UID: " + currentUser._id + "<br/>OID: " + currentUser.orgId + "<br/>";
		}
	},
	async updateCrmStatuses(orgId) {
		this.unblock();
		await ChildcareCRM.updateStatusInfo(orgId);
	},
	async fixAllWithdrawn() {
		this.unblock();
		const user = await Meteor.userAsync();
		const currentPerson = await user?.fetchPerson?.();
		if (!currentPerson ||
			currentPerson.type != "admin" ||
			!currentPerson.superAdmin)
			throw new Meteor.Error(403, "Access denied");

		console.log('Fix All Withdrawn started');
		const crmOrgIds = await ChildcareCrmUtil.getAllChildcareCrmOrgs();
		for (const orgId of crmOrgIds) {
			const childList = await CrmFixService.listProblemChildren(orgId);
			for (const child of childList) {
				const loopChild = await People.findOneAsync(child._id);
				// just a little safety here
				if (loopChild) {
					await CrmFixService.undoWithdrawn(loopChild);
				}
			}
		}
		console.log('Fix All Withdrawn Complete');
	},
	async getCrmOrgs() {
		this.unblock();
		const user = await Meteor.userAsync();
		const currentPerson = await user?.fetchPerson?.();
		if (!currentPerson ||
			currentPerson.type != "admin" ||
			!currentPerson.superAdmin)
			throw new Meteor.Error(403, "Access denied");
		const crmOrgIds = await ChildcareCrmUtil.getAllChildcareCrmOrgs();
		return await Orgs.find({ _id: { $in: crmOrgIds } }).fetchAsync();
	},
	async getProblemChildren(orgId) {
		this.unblock();
		const user = await Meteor.userAsync();
		const currentPerson = await user?.fetchPerson?.();
		if (!currentPerson ||
			currentPerson.type != "admin" ||
			!currentPerson.superAdmin)
			throw new Meteor.Error(403, "Access denied");
		const list = await CrmFixService.listProblemChildren(orgId);
		await CrmFixService.attachLastStatusInfo(list, orgId);
		return list;
	},
	async getPreviouslyFixedChildren(orgId) {
		this.unblock();
		const user = await Meteor.userAsync();
		const currentPerson = await user?.fetchPerson?.();
		if (!currentPerson ||
			currentPerson.type != "admin" ||
			!currentPerson.superAdmin)
			throw new Meteor.Error(403, "Access denied");
		return await CrmFixService.listPreviouslyFixed(orgId);
	},
	async getChildInfo(childId) {
		this.unblock();
		const user = await Meteor.userAsync();
		const currentPerson = await user?.fetchPerson?.();
		if (!currentPerson ||
			currentPerson.type != "admin" ||
			!currentPerson.superAdmin)
			throw new Meteor.Error(403, "Access denied");
		return await People.findOneAsync(childId);
	},
	getCrmUiBaseUrl() {
		return Meteor.settings.childcareCrmUIUrl;
	},
	async undoCrmWithdrawn(childId) {
		this.unblock();
		const user = await Meteor.userAsync();
		const currentPerson = await user?.fetchPerson?.();
		if (!currentPerson ||
			currentPerson.type != "admin" ||
			!currentPerson.superAdmin)
			throw new Meteor.Error(403, "Access denied");
		const child = await People.findOneAsync(childId);
		if (child) {
			return await CrmFixService.undoWithdrawn(child);
		}
		return false;
	},
	async syncChildcareCrmAccount(curId) {
		this.unblock();
		const user = await Meteor.userAsync();
		const person = user && await user.fetchPerson();
		if (!person || !person.superAdmin) {
			throw new Meteor.Error(403, "Access Denied");
		}

		const crmAccount = await ChildcareCrmAccounts.findOneAsync(curId);
		if (crmAccount) {
			Meteor.defer(async function () {
				try {
					await ChildcareCRM.syncAll(crmAccount._id);
					let totalCenters = (crmAccount.centers && crmAccount.centers.length) ? crmAccount.centers.length : 0;
					for (let x = 0; x < totalCenters; x++) {
						const centerOrgId = crmAccount.centers[x].orgId;
						if (centerOrgId) await ChildcareCRM.findAndUpdateNewEnrollments(centerOrgId);
					}
				} catch (e) {
					console.log(e);
					await ChildcareCrmAccounts.updateAsync({ _id: crmAccount._id }, { $set: { syncStatus: "error", syncError: e } });
				}
			})
		} else {
			throw new Meteor.Error(404, "Account Not Found");
		}

		return true;
	},
	async createCrmAccount(data) {
		const user = await Meteor.userAsync();
		const person = user && await user.fetchPerson();
		if (!person || !person.superAdmin) {
			throw new Meteor.Error(403, "Access Denied");
		}

		const accountId = await ChildcareCrmAccounts.insertAsync(data);
		return accountId;
	},
	async topHasDreambox(orgId) {
		this.unblock();
		const org = await Orgs.findOneAsync({ _id: orgId });
		const topOrg = await org.findTopOrg();
		return topOrg.hasCustomization('integrations/dreambox/enabled');
	},
	async updateDreamboxIntegration(data) {
		this.unblock();
		const user = await Meteor.userAsync();
		const person = user && await user.fetchPerson();
		if (!person || !person.superAdmin) {
			throw new Meteor.Error(403, "Access Denied");
		}
		const org = await Orgs.findOneAsync({ _id: data.orgId });
		if (!org) {
			return;
		}
		delete data.orgId;
		DreamboxService.getDefaultFields(data);
		await Orgs.updateAsync({ _id: org._id }, { $set: { dreambox: data } });
	},
	async deleteDreambox(orgId) {
		this.unblock();
		const user = await Meteor.userAsync();
		const person = user && await user.fetchPerson();
		if (!person || !person.superAdmin) {
			throw new Meteor.Error(403, "Access Denied");
		}
		const org = await Orgs.findOneAsync({ _id: orgId });
		if (!org) {
			return;
		}
		await Orgs.updateAsync({ _id: org._id }, { $unset: { 'dreambox': 1 } });
	},
	async toggleDreambox(orgId) {
		this.unblock();
		const user = await Meteor.userAsync();
		const person = user && await user.fetchPerson();
		if (!person || !person.superAdmin) {
			throw new Meteor.Error(403, "Access Denied");
		}
		const org = await Orgs.findOneAsync({ _id: orgId });
		if (!org) {
			return;
		}
		const newValue = org.hasCustomization(DreamboxService.customizationName) ? 'false' : 'true';
		await Meteor.callAsync("changeCustomerProperty", orgId, { area: 'customizations', key: DreamboxService.customizationName, value: newValue });
	},
	async getDreamboxFile(orgId) {
		this.unblock();
		return DreamboxService.processOrg(orgId);
	},
	async manualUploadDreambox(orgId) {
		this.unblock();
		Meteor.defer(async () => {
			const records = await DreamboxService.processOrg(orgId);
			const org = await Orgs.findOneAsync({ _id: orgId });
			DreamboxService.upload(org, records);
		});
	},
	async createAppAccount(data) {
		if (!data.temporary && !data.internalProvision && (await Meteor.users.find({ "emails.address": data.email }).countAsync()) > 0) {
			throw new Meteor.Error(500, "That email address appears to already be signed up for MomentPath. Try our <a href='/forgotpassword'>page</a>");
		} else if (!data.temporary && !data.internalProvision && await UserInvitations.findOneAsync({ "email": data.email })) {
			const ui = await UserInvitations.findOneAsync({ "email": data.email });
			await processInvitation(ui._id);
			throw new Meteor.Error(500, "It looks like that email address has already been invited to MomentPath. Your invite will be re-sent.");
		} else {
			let orgData = {
				createdAt: new Date().valueOf(),
				registrationSource: 'app',
				registrationNeeds: data.needs,
				registrationIndustry: data.industry,
				registrationChildcareType: data.childcareType,
				registrationRole: data.role,
				customizations: {},
				enabledMomentTypes: []
			};
			if (data.timezone) {
				orgData.timezone = data.timezone;
			}
			if (data.guided) orgData.registrationGuided = true;
			if (data.temporary) orgData.temporary = true;
			if (data.industry == "childcare" || data.industry == "schools") {
				if (!data.orgname) orgData.name = "Learn and Grow Childcare";
				orgData.language = "translationsEnChildCare";
				orgData.customizations = Orgs.defaultOrg.customizations;
				orgData.valueOverrides = Orgs.defaultOrg.valueOverrides;
				orgData.enabledMomentTypes.push("behaviorChild");
				orgData.enabledMomentTypes.push("covidHealth");
			}
			if (data.industry == "schools") {
				orgData.customizations["moments/sleep/enabled"] = false;
				orgData.customizations["moments/potty/enabled"] = false;
			}
			if (data.industry == "adult" || data.industry == "senior") {
				orgData.language = "translationsEnAdultCare";
				orgData.customizations["moments/sleep/enabled"] = false;
				orgData.customizations["moments/learning/enabled"] = false;
				orgData.customizations["moments/mood/enabled"] = true;
				orgData.enabledMomentTypes.push("medicationRoutine");
			}

			if (data.internalProvision && data.disableTrial)
				orgData.planDetails = {};

			if (data.orgname) orgData.name = data.orgname;

			if (!orgData.name)
				orgData.name = "My Care Organization";

			const oId = await Orgs.insertAsync(orgData);

			if (!data.onboardingv2 && (!data.internalProvision || data.provisionMetacx)) {
				processAnalytics({
					metaCxData: {
						type: 'new-org', data: {
							orgId: oId,
							orgName: orgData.name
						}
					}
				});
				console.log("new org tracked to metacx");
			}

			if (oId && !data.internalProvision) {

				const newPerson = {
					type: "admin",
					orgId: oId,
					firstName: "Admin",
					lastName: "User",
					initialOrgUser: true,
					inActive: false,
				};
				if (data.onboardingv2) newPerson["onboardingData"] = { v2Started: true };
				const pId = await People.insertAsync(newPerson);
				const performedBy = await Meteor.userAsync();
				await HistoryAuditService.logPersonChange({
					changeType: HistoryAuditChangeTypes.ADD,
					performedByUser: performedBy,
					previousState: null,
					currentState: { ...newPerson, _id: pId },
				});

				if (pId && data.email && !data.guided) {
					var invitationId = await UserInvitations.insertAsync({
						token: tokenString(),
						email: data.email,
						orgId: oId,
						personId: pId,
						used: false,
						createdAt: new Date().valueOf(),
						createdBy: "PQL"
					});

					if (invitationId) {
						this.unblock();
						await processInvitation(invitationId);
					}
				} else if (pId) {
					const randomEmail = data.email || ("demouser-" + Random.id() + "@momentpath.com");
					const randomPassword = data.password || Random.id();
					const activationCode = data.onboardingv2 && Math.random().toString().substring(2, 8);

					let userId = await Accounts.createUserAsync({
						email: randomEmail,
						password: randomPassword
					});
					if (activationCode) addMailchimpUser(data.email);

					if (userId) {
						await Meteor.users.updateAsync(userId, { $set: { orgId: oId, personId: pId, activationCode } });
						if (data.email && !data.onboardingv2) Accounts.sendVerificationEmail(userId);
						if (data.onboardingv2) await processOnboardingVerificationCodeEmail(userId);

						//insert sample data
						const groupMap = {};
						for (const g of Orgs.defaultOrg.groups) {
							g.orgId = oId;
							g.sampleData = true;
							const groupId = await Groups.insertAsync(g);
							groupMap[g.origId] = groupId;
						};
						const peopleMap = {};
						for (const p of Orgs.defaultOrg.people) {
							p.orgId = oId;
							p.defaultGroupId = groupMap[p.defaultGroupId];
							if (p.checkInGroupOrigId) {
								p.checkinGroupId = groupMap[p.checkInGroupOrigId];
								p.checkedIn = true;
								p.checkedInById = pId;
								p.checkedInOutTime = new Date().valueOf();
							}
							p.sampleData = true;
							p.inActive = false;
							const personId = await People.insertAsync(p);
							peopleMap[p.origId] = personId;
							const performedBy = await Meteor.userAsync();
							await HistoryAuditService.logPersonChange({
								changeType: HistoryAuditChangeTypes.ADD,
								performedByUser: performedBy,
								previousState: null,
								currentState: { ...p, _id: personId },
							});
						}
						for (r of Orgs.defaultOrg.relationships) {
							r.orgId = oId;
							r.targetId = peopleMap[r.targetId];
							r.personId = peopleMap[r.personId];
							r.sampleData = true;
							await Relationships.insertAsync(r);
						};

						return { email: randomEmail, token: randomPassword };
					}

				}
			} else if (data.internalProvision) {
				return oId;
			}
		}
	},
	'registerUser': async function (invitationToken, password) {
		check(password, String);
		if (password && password.trim().length == 0) throw new Meteor.Error(500, "Password cannot be blank");

		var invitation = await UserInvitations.findOneAsync({ token: invitationToken, used: false });

		if (!invitation)
			throw new Meteor.Error(500, "Incorrect invitation token or token already used. You may want to try the forgot password link.");

		const person = await People.findOneAsync({ _id: invitation.personId });
		let user = await person.findAssociatedUser();
		const org = await Orgs.findOneAsync({ _id: invitation.orgId });

		const staffApiService = new StaffApiService();
		if (!user) {
			user = await usersRepository.findUserByEmail(invitation.email);
		}

		if (invitation && !user) {
			var id = await Accounts.createUserAsync({
				email: invitation.email.trim(),
				password: password
			});
			if (id) {
				await Meteor.users.updateAsync(id, {
					$set: {
						orgId: invitation.orgId,
						personId: invitation.personId,
						pending: false
					}
				});
				await UserInvitations.updateAsync(invitation._id, { $set: { used: true } });
				await staffApiService.createEnrollStaff(id, password);
			}

		} else if (invitation && user) {
			await Accounts.setPasswordAsync(user._id, password);
			await registerUserUpdateUser({ person, user, invitation });
			await UserInvitations.updateAsync(invitation._id, { $set: { used: true } });
			await staffApiService.createEnrollStaff(user._id, password);
			// AWS code to add user to cognitopool
			const userPayload = {
				'email': invitation.email, 
				'password': password,
				'userId': user._id,
				'orgId': invitation.orgId
			}
			await cognitoService.createCognitoUser(userPayload).then(data => {
				console.log('User created and password set successfully into cognito pool:', data);
			})
				.catch(err => {
					console.error('Error creating user or setting password in cognito:', err);
				});
		} else
			throw new Meteor.Error(500, "There was an issue updating your account. You may want to try the forgot password link.");

		if (person.needsPinEmail) {
			await PinService.sendPinEmail(person._id);
		}
		if (person.needsPinText) {
			await PinService.sendPinText(person._id);
		}
		await AbleToWorkAtService.setPrimarySite(person._id);

		processAnalytics({
			metaCxData: {
				type: 'activate-family', data: {
					orgId: invitation.orgId,
					orgName: org.name
				}
			}
		});
	},
	'createInvitation': async function (personId, email) {
		var currentUser = await Meteor.userAsync();
		const cuser = await currentUser.fetchPerson();
		var p = await People.findOneAsync(personId);
		if (!currentUser ||
			!(cuser?.type == "admin") ||
			!(p["orgId"] == currentUser["orgId"]))
			throw new Meteor.Error(403, "Access denied");

		// we're trying to create an invitation for a person that has no saved email address on their profile
		// we should validate that the email provided does not currently exist in the org
		if (p && email && ((!p.email && !p.profileEmailAddress) || (p.email && p.email != email) || (p.profileEmailAddress && p.profileEmailAddress != email))) {
			const emailExists = await Meteor.callAsync('checkEmailExistsForOrg', email);
			if (emailExists) {
				throw new Meteor.Error(400, "Email already exists in this organization");
			}
		}

		if (!email) throw new Meteor.Error(400, "Email required for invitation");
		var existingUser = await p.findAssociatedUser();

		if (!existingUser || (existingUser && existingUser.pending)) {
			var invitationId = await UserInvitations.insertAsync({
				token: tokenString(),
				email: email.trim(),
				orgId: currentUser["orgId"],
				personId: personId,
				used: false
			});

			//obj is { person, email }
			await createInvitationCreateUser({ person: p, email: email.trim() })

			await processInvitation(invitationId);
		}
	},
	async changeAndVerifyEmail(email) {
		var currentUser = await Meteor.userAsync();
		if (!currentUser)
			throw new Meteor.Error(403, "Access denied");

		await Meteor.users.updateAsync(Meteor.userId(), { $set: { 'emails.0.address': email.trim(), 'verified': false } });

		Accounts.sendVerificationEmail(Meteor.userId());

		const signupData = "Email: " + email + "<br/>UID: " + currentUser._id + "<br/>OID:" + currentUser.orgId + "<br/>";
		await Email.sendAsync({
			from: "LineLeader support <<EMAIL>>",
			to: '<EMAIL>',
			subject: "A product qualified lead - verify email step",
			html: signupData
		});
	},
	async toggleImmunizationReminder() {
		const user = await Meteor.userAsync();
		const person = await user?.fetchPerson?.();
		if (person?.type != "admin")
			throw new Meteor.Error(403, "Access denied");

		const org = await Orgs.current();
		const val = org.hasCustomization("mobile/immunizationAlert/disabled");
		await Orgs.updateAsync({ _id: org._id }, { $set: { "customizations.mobile/immunizationAlert/disabled": !val } });
	},
	async changeOrgName(orgName) {
		this.unblock();
		var currentUser = await Meteor.userAsync();
		const cuser = await currentUser.fetchPerson();
		if (!currentUser || cuser?.type != "admin")
			throw new Meteor.Error(403, "Access denied");

		await Orgs.updateAsync({ _id: currentUser.orgId }, { $set: { name: orgName } });
	},
	async changeLegalFacilityName(legalFacilityName) {
		this.unblock();
		var currentUser = await Meteor.userAsync();
		const cuser = await currentUser.fetchPerson();
		if (!currentUser || cuser?.type != "admin")
			throw new Meteor.Error(403, "Access denied");

		await Orgs.updateAsync({ _id: currentUser.orgId }, { $set: { legalFacilityName: legalFacilityName } });
	},
	async changeFacilityLicenseNumber(facilityLicenseNumber) {
		this.unblock();
		var currentUser = await Meteor.userAsync();
		const cuser = await currentUser.fetchPerson();
		if (!currentUser || cuser?.type != "admin")
			throw new Meteor.Error(403, "Access denied");

		await Orgs.updateAsync({ _id: currentUser.orgId }, { $set: { facilityLicenseNumber: facilityLicenseNumber } });
	},
	async changeOrgShortCode(shortCode) {
		var currentUser = await Meteor.userAsync();
		const cuser = await currentUser.fetchPerson();
		if (!currentUser || cuser?.type != "admin")
			throw new Meteor.Error(403, "Access denied");
		if (shortCode.length > 30) {
			throw new Meteor.Error(403, "Short Code must be under 31 characters")
		}

		await Orgs.updateAsync({ _id: currentUser.orgId }, { $set: { shortCode: shortCode } });
	},
	async setOnboardingValue(data) {
		var currentUser = await Meteor.userAsync();
		const cuser = await currentUser.fetchPerson();
		if (!currentUser || cuser?.type != "admin")
			throw new Meteor.Error(403, "Access denied");
		let updateData = {};
		if (data.visitedGroups) updateData["onboardingData.visitedGroups"] = true;
		if (data.visitedTendlyU) updateData["onboardingData.visitedTendlyU"] = true;
		if (data.visitedEffortlessEntry) updateData["onboardingData.visitedEffortlessEntry"] = true;
		if (data.visitedMeals) updateData["onboardingData.visitedMeals"] = true;
		if (data.visitedCalendar) updateData["onboardingData.visitedCalendar"] = true;
		if (data.visitedBilling) updateData["onboardingData.visitedBilling"] = true;
		if (data.visitedReporting) updateData["onboardingData.visitedReporting"] = true;
		if (data.visitedInquiries) updateData["onboardingData.visitedInquiries"] = true;
		if (data.visitedMobileApps) updateData["onboardingData.visitedMobileApps"] = true;
		if (data.visitedStaff) updateData["onboardingData.visitedStaff"] = true;
		if (data.currentTrack) updateData["onboardingData.currentTrack"] = data.currentTrack;
		if (data.createMessage) updateData["onboardingData.createMessage"] = true;
		if (data.trialInfo) {
			updateData["onboardingData.trialInfo"] = {
				multipleLocations: data.trialInfo.multipleLocations,
				peopleCount: data.trialInfo.peopleCount,
				competitorUsed: data.trialInfo.competitorUsed,
				timeframe: data.trialInfo.timeframe
			};
			await Orgs.updateAsync({ _id: currentUser.orgId }, { $inc: { trialLength: 7 } });
		}
		if (data.requestDemo) {
			updateData["onboardingData.demoRequested"] = true;
			const signupData = "Email: " + data.requestDemo.email + "<br/>Phone: " + data.requestDemo.phone + "<br/>Prefers: " + data.requestDemo.preferredMethod
				+ "<br/>UID: " + currentUser._id + "<br/>OID:" + currentUser.orgId + "<br/>";
			await Email.sendAsync({
				from: "LineLeader support <<EMAIL>>",
				to: '<EMAIL>',
				subject: "A product qualified lead - request demo",
				html: signupData
			});
		}
		if (data.billingLearnMore) {
			const signupData = "Email: " + data.billingLearnMore + "<br/>UID: " + currentUser._id + "<br/>OID:" + currentUser.orgId + "<br/>";
			await Email.sendAsync({
				from: "LineLeader support <<EMAIL>>",
				to: '<EMAIL>',
				subject: "A product qualified lead - request billing info",
				html: signupData
			});
			updateData["onboardingData.billingLearnMoreAddress"] = data.billingLearnMore;
		}
		if (data.enableInquiries) {
			await Orgs.updateAsync({ _id: currentUser.orgId }, { "$set": { "customizations.inquiries/enabled": true } });
			updateData["onboardingData.enabledInquiries"] = true;
		}
		if (data.sendMobileApps) {
			updateData["onboardingData.visitedMobileApps"] = true;
			let messageBody = "Try MomentPath's mobile app now by visiting ";
			switch (data.sendMobileApps.deviceType) {
				case "apple":
					messageBody += "https://itunes.apple.com/us/app/tend-ly/id1086197417";
					break;
				case "android":
					messageBody += "https://play.google.com/store/apps/details?id=ly.tend.tendly.tendlyapp";
					break;
				case "amazon":
					messageBody += "https://www.amazon.com/Tendly-LLC-Tend-ly/dp/B01N26NF2D";
					break;
			}
			try {
				var client = new Twilio({
					from: Meteor.settings.TWILIO_NUMBER,
					sid: Meteor.settings.TWILIO_ACCOUNT_SID,
					token: Meteor.settings.TWILIO_AUTH_TOKEN
				});
				var result = client.sendSMS({
					to: data.sendMobileApps.phone,
					body: messageBody
				});
			} catch (err) {
				console.log('SMS Failure: 500 to ' + subscriber.phonePrimary, err.message);
			}
		}
		if (updateData != {}) await Orgs.updateAsync({ _id: currentUser.orgId }, { $set: updateData });
	},
	async changeAutoCheckoutTime(val) {
		var currentUser = await Meteor.userAsync();
		var currentPerson = currentUser && await currentUser.fetchPerson();
		if (!currentPerson || currentPerson.type != "admin") {
			throw new Meteor.Error(403, "Access denied");
		}

		const timeVal = new moment(val, "HH:mm").set('minute', 0).format("HH:mm");
		await Orgs.updateAsync({ _id: currentUser.orgId }, { "$set": { autoCheckoutTime: timeVal } });
	},
	async changeCustomizationValue(flag, value) {
		console.log(flag, value);
		var currentUser = await Meteor.userAsync();
		const cuser = await currentUser.fetchPerson();
		if (!currentUser ||
			cuser?.type != "admin" ||
			!(_.find(await Orgs.customizableMomentTypes(), (mt) => { return (mt.flag == flag) || (mt.adminOnly == flag) }) ||
				_.contains([
					"people/pinCodeCheckin/enabled",
					AvailableCustomizations.AUTO_PIN,
					"people/staffRequiredPinCodeCheckin/enabled",
					"people/phonePinCheckin/enabled",
					"people/familyCheckin/enabled",
					"people/multipleCheckin/enabled",
					"people/checkInCheckOutQrCodesExpire/enabled",
					"moments/checkin/autocheckout",
					"messages/administrativeVisibility/enabled",
					"messages/suppressStaffMessageCenterNotifications/enabled",
					"people/qrCodeCheckin/enabled",
					"messages/disableStaffMessages/enabled",
					"moments/staffRequiredCheckin/enabled",
					"moments/staffTimeConfirmation/enabled",
					"moments/incompleteFTF/enabled",
					"people/kioskMasterPinAdminOnly/enabled",
					"people/chatSupport/enabled",
					"people/checkInCheckOutPickDropReason/enabled",
					AvailableCustomizations.SCHEDULE_TYPES_FORCED_LINK_TO_PLANS
				], flag)))
			throw new Meteor.Error(403, "Access denied");
		await Orgs.changeCustomizationValue(currentUser.orgId, flag, value);
	},
	async updateOrgInfo(label, value) {
		this.unblock();
		var currentUser = await Meteor.userAsync();
		const cuser = await currentUser.fetchPerson();
		if (!currentUser ||
			cuser?.type != "admin")
			throw new Meteor.Error(403, "Access denied");
		let updateData = {};
		if (label == "kioskPinCode") {
			updateData["kioskPinCode"] = value;
		}
		if (label == "replyToAddress") {
			if (value != "" && !IsValidEmail(value)) throw new Meteor.Error(500, "Invalid email address specified");
			updateData["replyToAddress"] = value;
		}
		if (label === "registrationEmails") {
			const emailList = value.split(',')
			if (!value) {
				throw new Meteor.Error(400, "Please enter the email address");
			}
			emailList.forEach(item => {
				if (!IsValidEmail(item)) {
					throw new Meteor.Error(400, "Invalid email address specified");
				}
			});
			updateData["registrationSettings.registrationEmails"] = value;
		}
		if (label == "timezone") {
			updateData["timezone"] = value;
		}
		if (label == "forecastingDefaults") {
			updateData["forecasting"] = {
				idealRevenue: parseFloat(value.idealRevenue),
				targetPayroll: parseFloat(value.targetPayroll)
			};
		}
		if (label == "mediaRequirement") {
			const mediaNumber = parseInt(value.mediaNumber) ? parseInt(value.mediaNumber) : null,
				mediaDays = parseInt(value.mediaDays) ? parseInt(value.mediaDays) : null,
				mediaAbsenceDays = parseInt(value.mediaAbsenceDays) ? parseInt(value.mediaAbsenceDays) : null;
			const mediaReviewRequired = value.mediaReviewRequired;

			if (mediaNumber && mediaDays && mediaAbsenceDays) {
				updateData["mediaRequirement"] = {
					mediaNumber,
					mediaDays,
					mediaAbsenceDays,
					mediaReviewRequired
				}
			} else if (mediaReviewRequired) {
				updateData["mediaRequirement"] = {
					mediaReviewRequired,
				}
			} else {
				await Orgs.updateAsync({ _id: currentUser.orgId }, { "$unset": { mediaRequirement: 1 } });
				return;
			}

		}
		if (updateData != {})
			await Orgs.updateAsync({ _id: currentUser.orgId }, { "$set": updateData });
	},
	async removeSampleData() {
		this.unblock();
		var currentUser = await Meteor.userAsync();

		await People.updateAsync({ orgId: currentUser.orgId, sampleData: true }, { $set: { sampleData: false } });
		await removeAsync({ orgId: currentUser.orgId, isDemo: true });
		await Orgs.updateAsync({ _id: currentUser.orgId }, { $unset: { temporary: 1 } });
	},
	evaporateSigner(options) {
		if (!this.userId) {
			throw new Meteor.Error('not-authorised');
		}
		let signingKey;

		function hmac(k, v) {
			return AWS.util.crypto.hmac(k, v, 'buffer');
		}
		try {
			const date = hmac(['AWS4', Meteor.settings.AWSSecretAccessKey].join(''), options.dateString.substr(0, 8));
			const region = hmac(date, 'us-east-1');
			const service = hmac(region, 's3');
			const signing = hmac(service, 'aws4_request');
			signingKey = AWS.util.crypto.hmac(signing, decodeURIComponent(options.stringToSign), 'hex');
		} catch (err) {
			console.log("error on file signing", err);
			throw new Meteor.Error('Error creating v4 sig: ' + err);
		}

		return signingKey;
	},
	'rnSignUpload': function (options) {
		if (!this.userId)
			throw new Meteor.Error('not-authorised');

		console.log("signing:", options);
		const s3 = createNewS3Client();
		const url = s3.getSignedUrl('putObject', {
			Bucket: "tendlymruploads",
			Key: options.key,
			ContentType: options.contentType
		});
		return { url };
	},
	'uppySigner': function (stringToSign) {
		var crypto = require('crypto');
		var authSecret = Meteor.settings.transloaditKey;
		var signature = crypto
			.createHmac('sha1', authSecret)
			.update(Buffer.from(JSON.stringify(stringToSign), 'utf-8'))
			.digest('hex');
		console.log("singing result", stringToSign, signature);
		return signature;
	},
	'uppyCreateMultipartUpload': function (options) {
		if (!this.userId)
			throw new Meteor.Error('not-authorised');

		const s3 = createNewS3Client();
		const data = s3.createMultipartUpload({
			Bucket: 'tendlymruploads',
			Key: options.key,
			ContentType: options.type,
			Expires: 300
		});
		console.log("got upload started", data);
		return {
			key: data.Key,
			uploadId: data.UploadId
		};
	},
	'uppyGetUploadedParts': function (options) {
		if (!this.userId)
			throw new Meteor.Error('not-authorised');
		console.log('getting parts');

		const s3 = createNewS3Client();
		let parts = [];

		listPartsPage(0);

		function listPartsPage(startAt) {
			const data = s3.listParts({
				Bucket: 'tendlymruploads',
				Key: options.key,
				UploadId: options.uploadId,
				PartNumberMarker: startAt
			});

			parts = parts.concat(data.Parts)

			if (data.IsTruncated) {
				// Get the next page.
				listPartsPage(data.NextPartNumberMarker)
			} else {
				done();
			}
		}

		function done() {

			return parts;
		}
	},
	'uppySignPartUpload': function (options) {
		if (!this.userId)
			throw new Meteor.Error('not-authorised');

		const s3 = createNewS3Client();
		const url = s3.getSignedUrl('uploadPart', {
			Bucket: "tendlymruploads",
			Key: options.key,
			UploadId: options.uploadId,
			PartNumber: options.partNumber,
			Body: '',
			Expires: 300
		});
		return { url };
	},
	'uppyAbortMultipartUpload': function (options) {
		if (!this.userId)
			throw new Meteor.Error('not-authorised');

		const s3 = createNewS3Client();
		s3.abortMultipartUpload({
			Bucket: "tendlymruploads",
			Key: options.key,
			UploadId: options.uploadId
		});
		return {};
	},
	'uppyCompleteMultipartUpload': function (options) {
		if (!this.userId)
			throw new Meteor.Error('not-authorised');

		const s3 = createNewS3Client();
		const data = s3.completeMultipartUpload({
			Bucket: "tendlymruploads",
			Key: options.key,
			UploadId: options.uploadId,
			MultipartUpload: {
				Parts: options.parts
			}
		});
		return {
			location: data.location
		};
	},
	'updateMediaUploadStatus': async function (options) {
		var currentUser = await Meteor.userAsync();
		const cuser = await currentUser.fetchPerson();
		if (!currentUser || !(cuser?.type == "admin" || cuser?.type == "staff"))
			throw new Meteor.Error(403, "Access denied");

		const query = { orgId: currentUser.orgId, "mediaFiles.mediaToken": options.tokenId };
		const moment = await Moments.findOneAsync(query);

		if (moment) {
			let fileIndex = -1, pendingCount = 0;
			_.each(moment.mediaFiles, (mf, idx) => {
				if (mf.mediaToken == options.tokenId) {
					fileIndex = idx;
				} else if (mf.pending) {
					pendingCount++;
				}
			});
			if (fileIndex >= 0) {
				const updateKey = "mediaFiles." + fileIndex + ".pending";
				let query = { "$set": {} };
				query["$set"][updateKey] = false;
				await Moments.updateAsync({ _id: moment._id }, query);
				if (pendingCount == 0) {
					console.log("sending real-time update for queued moment:" + moment._id);
					Meteor.defer(async function () {
						await processRealtime(moment._id);
					});
				}
			}
		}
	},
	'captureDebugLog': async function (logData) {
		var currentUser = await Meteor.userAsync();
		const cuser = await currentUser.fetchPerson();
		if (!currentUser || !(cuser?.type == "admin" || cuser?.type == "staff"))
			throw new Meteor.Error(403, "Access denied");

		await DebugLogs.insertAsync({
			userId: currentUser._id,
			createdAt: new Date().valueOf(),
			logData: logData
		});
	},
	'processAllAdp': async function () {
		const jobOrgs = await Orgs.find({ "ftpCredentials.adp.url": { $nin: [null, ''] } }).fetchAsync();
		for (const jobOrg of jobOrgs) {
			try {
				await Meteor.callAsync("processAdpCsv", jobOrg);
			} catch (ex) {
				console.error("Error processing ADP CSV for organization", jobOrg._id, ex);
			}
		}
	},
	'getAdpPayrollReport': async function (options) {
		async function getAdpPayrollForOrg(orgId) {
			const results = await staffTimeReportAggregates({
				startDate: startDate.valueOf(),
				endDate: endDate.valueOf(),
				orgId: orgId,
				frequency: 'weekly'
			});
			const orgs = [];
			const orgTypes = [];
			const payrollData = [];
			for (const i in results.rows) {
				for (const result of results.rows[i]) {
					if (!orgs[result.orgId]) {
						const org = await Orgs.findOneAsync({ _id: result.orgId });
						orgs[result.orgId] = org.getShortCode();
						orgTypes[result.orgId] = org.customStaffPaySettings?.types || [];
						orgTypes[result.orgId].push({ type: 'Standard', adpCode: org.customStaffPaySettings?.standardPayAdpCode || '' });
					}

					if (!result.employeeId) {
						continue;
					}
					const person = await People.findOneAsync({ _id: result.personId });
					if (person.profileData?.employeeClassification === 'Exempt') {
						continue;
					}
					const employeeInfo = result.employeeId.split('-');
					const coCode = employeeInfo[0] ?? null;
					const fileNum = employeeInfo[1] ?? null;
					const orgCode = (orgs[result.orgId] ?? '') + '000';
					const types = orgTypes[result.orgId] || [];
					if (!result.classificationDetails) {
						continue;
					}
					for (const detail of result.classificationDetails) {
						if (detail.type === 'Standard') {
							if (detail.totalHours) {
								payrollData.push([
									coCode,
									batchId,
									fileNum,
									Math.round(detail.totalHours * 100) / 100,
									null,
									AdpService.getAdpPayCodeFromType(types, 'Standard'),
									null,
									orgCode,
									null,
									parseInt(i) + 1
								]);
							}
							if (detail.totalOvertimeHours) {
								payrollData.push([
									coCode,
									batchId,
									fileNum,
									null,
									Math.round(detail.totalOvertimeHours * 100) / 100,
									AdpService.getAdpPayCodeFromType(types, 'Standard'),
									null,
									orgCode,
									null,
									parseInt(i) + 1
								]);
							}
						} else {
							payrollData.push([
								coCode,
								batchId,
								fileNum,
								null,
								null,
								AdpService.getAdpPayCodeFromType(types, detail.type),
								Math.round((detail.totalHours) * 100) / 100,
								orgCode,
								null,
								parseInt(i) + 1
							]);
							if (detail.totalOvertimeHours) {
								payrollData.push([
									coCode,
									batchId,
									fileNum,
									null,
									Math.round(detail.totalOvertimeHours * 100) / 100,
									AdpService.getAdpPayCodeFromType(types, detail.type),
									null,
									orgCode,
									null,
									parseInt(i) + 1
								]);
							}
						}
					}
				}
			}
			return payrollData;
		}

		const now = new Date();
		const startDate = options.startDate;
		const endDate = options.endDate;
		// Set the headers
		const headers = [
			'Co Code',			// company code; K78 = corporate, K77 = center
			'Batch ID',			// generated during export – typically the date the file is pulled
			'File #',			// employee id
			'Reg Hours',		// regular hours worked
			'O/T Hours',		// overtime hours worked
			'Hours 4 Code',		// Non-regular hours, like PTO, Jury Duty -- not captured ATM
			'Hours 4 Amount',	// number of non-regular hours
			'Temp Dept',		// School short code with 3 zeroes at the end
			'Temp Rate',		// Blank column
			'Workweek'			// Report is 2 weeks; which week is this? 1 or 2?
		];
		const currentOrg = await Orgs.current();
		const topOrg = await currentOrg.findTopOrg();
		const topOrgSettings = topOrg?.customStaffPaySettings || {};
		const batchId = `${now.getDay()}${now.getMonth()}${now.getFullYear()}`;
		const user = await Meteor.userAsync();
		const subOrgs = await currentOrg.findScopedOrgs(user.orgId);
		const payrollData = [];
		for (const row of await getAdpPayrollForOrg(user.orgId)) {
			payrollData.push(row);
		}
		for (const subOrg of subOrgs) {
			if (subOrg._id === user.orgId) continue;
			for (const row of await getAdpPayrollForOrg(subOrg._id)) {
				payrollData.push(row);
			}
		}

		const formattedPayrollData = ReportAdpService.sortReportADP(payrollData, parseInt(topOrgSettings.overtimeThreshold), topOrgSettings.frequency)

		formattedPayrollData.unshift(headers);
		return formattedPayrollData;
	},
	'processAdpCsv': async function (org) {
		this.unblock();
		await AdpService.processAdp(org);
	},
	'submitRegistration': async function (options) {
		this.unblock();
		const orgId = options.orgId;
		const org = await Orgs.findOneAsync({ _id: orgId });

		if (!org || !org.hasCustomization("inquiries/registration/enabled"))
			throw new Meteor.Error(500, "Invalid organization for registration");

		const formFields = _.object(_.map(options.formFields, _.values));

		//validate common data
		let validationErrors = [];

		var commonData = {
			centerPreference: formFields["center_preference"],
			referralSource: formFields["referral_source"],
			referralSourceOther: formFields["other_referral_source"],
			preferredContactMethod: formFields["contact_method"],
			hasSibling: formFields["sibling"],
			employee: formFields["employee"],
			schedule: formFields["schedule"],
			tourRequest: formFields["tour_request"],
			householdIncome: formFields["household_income"],
			householdSize: formFields["household_size"],
			onmywayprekEligible: formFields["onmywayprek_eligible"],
			additionalInformation: formFields["additional_information"],
			hours: {},
			familyMembers: []
		};

		if (!commonData.householdIncome || commonData.householdIncome == "")
			validationErrors.push("You must supply household income.");
		if (!commonData.householdSize || commonData.householdSize == "")
			validationErrors.push("You must supply household size.");

		["mon", "tue", "wed", "thu", "fri"].forEach((day) => {
			if (formFields["day_" + day + "_start_hour"])
				commonData.hours[day] = {
					start: formFields["day_" + day + "_start_hour"],
					end: formFields["day_" + day + "_end_hour"] || ""
				};
		});
		let attemptedParents = 0;
		for (var i = 1; i <= 2; i++) {
			if (formFields["parent_" + i + "_first_name"]) {
				const newFamily = {
					firstName: formFields["parent_" + i + "_first_name"],
					lastName: formFields["parent_" + i + "_last_name"],
					birthday: formFields["parent_" + i + "_dob"],
					ssn: formFields["parent_" + i + "_ssn"],
					address: formFields["parent_" + i + "_address"],
					city: formFields["parent_" + i + "_city"],
					state: formFields["parent_" + i + "_state"],
					zipcode: formFields["parent_" + i + "_zipcode"],
					phone: formFields["parent_" + i + "_phone"],
					email: formFields["parent_" + i + "_email"],
				}
				attemptedParents++;
				if (!newFamily.firstName || !newFamily.lastName || !newFamily.phone)
					validationErrors.push("Parent/guardian " + i + ": You must supply at least first name, last name, and phone number.");
				else
					commonData.familyMembers.push(newFamily);
			}
		}
		if (attemptedParents == 0)
			validationErrors.push("You must supply at least one parent's information.");

		//validate people on board
		const registrationFamilyId = Random.id();
		let validRegistrations = [], attemptedChildren = 0;
		for (var i = 1; i <= 4; i++) {
			if (formFields["child_" + i + "_first_name"] && formFields["child_" + i + "_first_name"]) {
				var insertPersonData = {};
				insertPersonData.orgId = orgId;
				insertPersonData.type = "prospect";
				insertPersonData.inActive = false;
				insertPersonData.createdAt = new Date().valueOf();
				insertPersonData.createdBy = "ONLINE_REGISTRATION";
				insertPersonData.registrationSubmittedAt = new Date().valueOf();
				insertPersonData.registrationFamilyId = registrationFamilyId;
				insertPersonData.firstName = formFields["child_" + i + "_first_name"];
				insertPersonData.lastName = formFields["child_" + i + "_last_name"];
				insertPersonData.profileData = _.extend(JSON.parse(JSON.stringify(commonData)), {
					desiredStartDate: formFields["child_" + i + "_start_date"]
				});
				insertPersonData.profileData.birthday = formFields["child_" + i + "_dob"];
				insertPersonData.profileData.registrationSubmissionDate = new moment.tz(org.getTimezone()).startOf("day").valueOf();
				attemptedChildren++;
				if (!insertPersonData.firstName || !insertPersonData.lastName || !insertPersonData.profileData.birthday)
					validationErrors.push("For child " + i + " you must supply at least first name, last name, and birthday");
				else
					validRegistrations.push(insertPersonData);
			}
		}
		if (attemptedChildren == 0)
			validationErrors.push("You must submit at least one child's information to complete this registration.")

		if (validationErrors.length > 0)
			throw new Meteor.Error(500, "There were errors with your submission: \n" + validationErrors.join("\n"));

		let completedRegistrations = [];
		let personId = null;
		for (const regData of validRegistrations) {
			const personId = await People.insertAsync(regData);
			completedRegistrations.push(personId);
			await HistoryAuditService.logPersonChange({
				changeType: HistoryAuditChangeTypes.ADD,
				performedByName: HistoryAuditPeoplePerformedByNames.CUSTOM_REGISTRATION,
				previousState: null,
				currentState: { ...regData, _id: personId },
			});
		}

		return completedRegistrations;

	},
	async manageSuspendInvoicing(options) {
		this.unblock();
		const currentOrg = await Orgs.current();
		await processPermissions({
			assertions: [{ context: "billing/configuration/system", action: "edit" }],
			evaluator: (person) => person.type == "admin",
			throwError: true
		});
		if (options.suspendDateStamp)
			await Orgs.updateAsync({ _id: currentOrg._id }, { $set: { "billing.suspendInvoicingUntilDate": options.suspendDateStamp } });
		else if (options.suspendInvoicing)
			await Orgs.updateAsync({ _id: currentOrg._id }, { $set: { "billing.suspendInvoicingIndefinitely": true } });
		else
			await Orgs.updateAsync({ _id: currentOrg._id }, { $unset: { "billing.suspendInvoicingIndefinitely": 1, "billing.suspendInvoicingUntilDate": 1 } });

		return true;
	},
	async sendBalanceReminder() {
		this.unblock();
		const allOrgs = await Orgs.find({ "billing.dunningComs.enabled": true }).fetchAsync();

		for (const org of allOrgs) {
			console.log(`Starting Dunning Comms for Org ${org.name}`);

			const dueDateStamp = moment().tz(org.getTimezone()).startOf("day").valueOf();
			const allOpenInvoices = await Invoices.find({
				orgId: org._id,
				openAmount: { $gt: 1 },
				dueDate: { $lt: dueDateStamp }
			}).fetchAsync();
			for (const invoice of allOpenInvoices) {
				const dueDate = invoice.dueDate;
				const personId = invoice.personId;
				const todaysDate = new Date().valueOf();
				const currentPerson = await People.findOneAsync({ _id: personId });
				const primaryCaregiver = await Relationships.findOneAsync({
					targetId: personId,
					primaryCaregiver: true
				});

				if (!primaryCaregiver || !currentPerson) {
					continue;
				}

				if ((todaysDate - dueDate) / (86400 * 1000) < org.billing.dunningComs.day) {
					continue;
				}

				if (dueDate < org.billing.dunningComs.epochDate) {
					continue;
				}

				if (
					currentPerson.invoiceReminders?.emailsSent >=
					org.billing.dunningComs.total
				) {
					continue;
				}

				if (
					currentPerson.invoiceReminders?.lastSentTime &&
					(todaysDate - currentPerson.invoiceReminders?.lastSentTime) /
					(86400 * 1000) < org.billing.dunningComs.followUp
				) {
					continue;
				}

				await People.updateAsync(
					{ _id: personId },
					{
						$set: {
							invoiceReminders: {
								emailsSent: (currentPerson.invoiceReminders?.emailsSent || 0) + 1,
								lastSentTime: todaysDate
							}
						}
					}
				);
				await processBillingEmail({ emailType: "balanceNotice", personId: primaryCaregiver.personId });
			};
			if (allOpenInvoices && allOpenInvoices.length) {
				const invoicePeople = allOpenInvoices.map((invoice) => invoice.personId);
				await People.updateAsync(
					{ orgId: org._id, _id: { $nin: invoicePeople } },
					{ $unset: { invoiceReminders: "" } },
					{ multi: true }
				);
			}
		};

		console.log('Dunning Comms Job Complete');
	},
	async sendBalanceNotice(options) {
		this.unblock();
		await processPermissions({
			assertions: [{ context: "billing/invoices", action: "edit" }],
			evaluator: (person) => person.type == "admin",
			throwError: true
		});

		const currentUser = await Meteor.userAsync();
		await People.updateAsync({ orgId: currentUser.orgId, _id: options.personId }, { "$set": { "billing.lastBalanceNoticeSentAt": new Date().valueOf() } });

		await processBillingEmail({ emailType: "balanceNotice", personId: options.personId, message: options.noticeMessage, sendCopy: options.sendCopy });
	},
	async sendManualPlanInvoice(options) {
		const { parentAdjusted } = options;
		if (!parentAdjusted) {
			await processPermissions({
				assertions: [{ context: "billing/invoices", action: "edit" }],
				evaluator: (person) => person.type == "admin",
				throwError: true
			});
		}
		const user = await Meteor.userAsync();
		const orgId = options.orgId ?? user.orgId;
		const currentUser = parentAdjusted ? null : user;
		const invoiceOptions = GenerateManualInvoiceService.generateInvoiceOptionsForSendManualPlanAction(options, currentUser, orgId);
		if (options.reservationIds) {
			invoiceOptions["allReservationIds"] = options.reservationIds
		}
		try {
			await AwsBillingService.createSingleInvoice(invoiceOptions);
			
			const personId = options.personId;
			if (personId) {
				Meteor.setTimeout(async () => {
					try {
						await People.updateAsync(
							{ _id: personId },
							{ $set: { "billing.pendingCharges": [] } }
						);
					} catch (err) {
						console.error("Error clearing pending charges:", err);
					}
				}, 1000);
			}
		} catch (e) {
			throw new Meteor.Error(500, "There was an issue with generating the invoice: " + e.message);
		}
	},
	async markChargebackResolved(options) {
		await processPermissions({
			assertions: [{ context: "billing/invoices", action: "edit" }],
			evaluator: (person) => person.type == "admin",
			throwError: true
		});
		const currentUser = await Meteor.userAsync();
		Invoices.updateByComplexQueryWithJournalEntry({
			orgId: currentUser.orgId,
			_id: options.invoiceId,
			"credits": {
				"$elemMatch": {
					"type": "chargeback",
					"adyenInfo.pspReference": options.chargebackPspId
				}
			}
		}, {
			"$set": {
				"credits.$.chargebackResolvedAt": new moment().valueOf(),
				"credits.$.chargebackResolvedBy": currentUser.personId,
			}
		}, {
			userId: currentUser._id,
			personId: currentUser.personId,
			orgId: currentUser.orgId,
			reason: `Chargeback resolved`,
			reasonLocation: `server/methods.js:markChargebackResolved`
		});
	},
	async voidInvoice(options) {
		this.unblock();
		const currentUser = await Meteor.userAsync();

		try {
			await processPermissions({
				assertions: [{ context: "billing/invoices", action: "edit" }, { context: "billing/invoices/void", action: "edit" }],
				evaluator: (person) => person.type === "admin",
				throwError: true
			});
		} catch (error) {
			throw new Meteor.Error(ErrorCodes.INSUFFICIENT_PRIVILEGES, 'You do not have the necessary permissions to void this invoice.');
		}

		const invoice = await Invoices.findOneAsync({ orgId: currentUser.orgId, _id: options.invoiceId });

		if (!invoice) {
			throw new Meteor.Error('Not found', "This invoice is not found.");
		}

		if (!invoice.isVoidable()) {
			throw new Meteor.Error("Invoice not voidable", "This invoice is not voidable. Invoices that are already voided or have credits applied cannot be voided.");
		}

		const originalAmountWithPayers = invoice.originalAmountWithPayers();
		try {
			await BillingInvoiceService.voidDiscountsInInvoice(invoice);
		} catch (e) {
			if (e.error === ErrorCodes.INSUFFICIENT_PRIVILEGES) {
				throw new Meteor.Error(ErrorCodes.INSUFFICIENT_PRIVILEGES, 'You do not have the necessary permissions to void this invoice.');
			}
			throw new Meteor.Error(
				e.error || ErrorCodes.UNKNOWN,
				e.reason || e.message || 'You could not void this invoice at this time. Please try again later.'
			);
		}

		await Invoices.updateByIdWithJournalEntry(options.invoiceId, {
			"$set": {
				"openAmount": 0.00,
				"discountAmount": 0.00,
				"originalAmount": originalAmountWithPayers,
				"openPayerAmounts": {},
				"voided": true,
				"voidedBy": currentUser.personId,
				"voidedAt": new Date().valueOf()
			}
		}, {
			userId: currentUser._id,
			personId: currentUser.personId,
			orgId: currentUser.orgId,
			reason: `Voided invoice`,
			reasonLocation: `server/methods.js:voidInvoice`
		});
	},
	async modifyDiscount(options) {
		this.unblock();
		const currentUser = await Meteor.userAsync();
		const org = await Orgs.current();

		if (!options.skipCustomizationCheck && org.hasCustomization(AvailableCustomizations.DISABLE_INVOICE_MODIFICATION)) {
			throw new Meteor.Error('Invalid operation', "Invoices cannot be modified after they are issued.");
		}

		try {
			await processPermissions({
				assertions: [{ context: "billing/invoices/modify", action: "edit" }],
				evaluator: (person) => person.type === "admin",
				throwError: true
			});
		} catch (e) {
			throw new Meteor.Error(ErrorCodes.INSUFFICIENT_PRIVILEGES, 'You do not have permission to modify this discount.');
		}

		try {
			await InvoiceUpdateService.modifyInvoiceDiscount(options, currentUser, org);
		} catch (e) {
			throw new Meteor.Error('Error modifying discount', 'Error modifying discount: ' + e.error);
		}
	},
	async addDiscount(options) {
		this.unblock();
		const currentUser = await Meteor.userAsync();
		const org = await Orgs.current();

		if (!currentUser) {
			throw new Meteor.Error(500, "Logged in user is invalid");
		}

		if (!org) {
			throw new Meteor.Error(500, "Current Org is invalid");
		}

		if (org.hasCustomization("billing/disableInvoiceModification")) {
			throw new Meteor.Error(500, "Invoices cannot be modified after they are issued.");
		}

		await processPermissions({
			assertions: [{ context: "billing/invoices/modify", action: "edit" }],
			evaluator: (person) => person.type === "admin",
			throwError: true
		});

		options.currentUser = currentUser;
		options.org = org;
		try {
			await BillingInvoiceService.addDiscount(options);
		} catch (e) {
			throw new Meteor.Error(e.error, e.reason);
		}
	},
	async removeReimbursableDay(options) {
		this.unblock();
		var currentUser = await Meteor.userAsync();

			await processPermissions({
				assertions: [{ context: "billing/invoices", action: "edit" }],
				evaluator: (person) => person.type == "admin",
				throwError: true
			});

		const invoice = await Invoices.findOneAsync({ orgId: currentUser.orgId, _id: options.invoiceId });
		if (!invoice) throw new Meteor.Error(500, "No proper invoice selected");

		const updateQuery = {
			"$pull": {}
		}
		const pullKey = "lineItems.$.appliedDiscounts." + options.allocationIndex + ".coveredDays";

		updateQuery["$pull"][pullKey] = options.coveredDay;

		const findQuery = { _id: invoice._id, "lineItems._id": options.lineItemId };

		Invoices.updateByComplexQueryWithJournalEntry(findQuery, updateQuery, {
			userId: currentUser._id,
			personId: currentUser.personId,
			orgId: currentUser.orgId,
			reason: `Removed reimbursable day`,
			reasonLocation: `server/methods.js:removeReimbursableDay`
		});
	},

	async reallocatePayer(options) {
		this.unblock();
		const currentUser = await Meteor.userAsync();
		const org = await Orgs.current();

		processPermissions({
			assertions: [{ context: AvailablePermissions.BILLING_INVOICES_MODIFY, action: AvailableActionTypes.EDIT }],
			evaluator: (person) => person.type === USER_TYPES.ADMIN,
			throwError: true
		});

		try {
			return await InvoiceModificationService.reallocatePayerBalance(options, currentUser, org);
		} catch (e) {
			throw new Meteor.Error(`An error occurred while reallocating the payer: ${e.error}`);
		}
	},

	async addReimbursableDay(options) {
		this.unblock();
		var currentUser = await Meteor.userAsync();

		await processPermissions({
			assertions: [{ context: "billing/invoices", action: "edit" }],
			evaluator: (person) => person.type == "admin",
			throwError: true
		});

		const invoice = await Invoices.findOneAsync({ orgId: currentUser.orgId, _id: options.invoiceId });
		if (!invoice) throw new Meteor.Error(500, "No proper invoice selected");

		const
			updateQuery = {
				"$push": {}
			},
			pushKey = "lineItems.$.appliedDiscounts." + options.allocationIndex + ".coveredDays";

		updateQuery["$push"][pushKey] = options.coveredDay;

		const findQuery = { _id: invoice._id, "lineItems._id": options.lineItemId };

		Invoices.updateByComplexQueryWithJournalEntry(findQuery, updateQuery, {
			userId: currentUser._id,
			personId: currentUser.personId,
			orgId: currentUser.orgId, // TODO: Confirm
			reason: `Added reimbursable day`,
			reasonLocation: `server/methods.js:addReimbursableDay`
		});
	},
	async billingFamilyBalanceReport(options) {
		this.unblock();
		const currentUser = await Meteor.userAsync();
		const currentOrg = await Orgs.current();

		console.log("current org ==", currentOrg._id);
		console.log("current user org id  ==", currentUser.orgId);
		console.log("in billingFamilyBalanceReport 1==");

		await processPermissions({
			assertions: [{ context: "billing/payments", action: "read" }],
			evaluator: (person) => person.type == "admin",
			throwError: true
		});

		moment.tz.setDefault(currentOrg.getTimezone());
		const startDate = new moment(options.startDate, "MM/DD/YYYY").valueOf();
		const endDate = new moment(options.endDate, "MM/DD/YYYY").add(1, "days").valueOf();
		const passedFamilyId = options.familyId && options.familyId != "" ? [options.familyId] : null;

		//get the families
		const familyPeopleIds = passedFamilyId || _.pluck(await Relationships.find({ orgId: currentUser.orgId, relationshipType: "family" }).fetchAsync(), "personId");
		const familyPeople = await People.find({ "type": "family", _id: { "$in": familyPeopleIds } }).fetchAsync();
		const familyBalances = [];

		for (const fp of familyPeople) {
			// get all the invoices
			const familyMembers = _.pluck(await Relationships.find({ orgId: currentUser.orgId, personId: fp._id, relationshipType: "family" }).fetchAsync(), "targetId");

			const allInvoices = await Invoices.find({
				orgId: currentUser.orgId,
				personId: { "$in": familyMembers }
			}).fetchAsync();

			const allInvoicePeople = await People.find({ orgId: currentUser.orgId, _id: { "$in": allInvoices.map(i => i.personId) } }).fetchAsync();

			// assign family splits
			//allInvoices.forEach(i => {
			//	if (!i.familySplits) {

			//		const ip = allInvoicePeople.find(aip => aip._id == i.personId);
			//		i.familySplits = ip.billingFamilySplits() || {};
			//	}
			//});

			//starting balance first
			let startingBalance = 0.0, runningTotal = 0.0;
			_.each(
				_.filter(allInvoices, (i) => {
					i.invoiceDateStamp = new moment(i.invoiceDate, "M/DD/YYYY").valueOf();
					return i.invoiceDateStamp < startDate;
				}),
				(i) => {
					const familyMemberAmountDue = i.voided ? i.openAmount : i.amountDueForFamilyMember(fp._id) || 0;
					runningTotal += familyMemberAmountDue >= 0 ? familyMemberAmountDue : 0;
				}
			);

			startingBalance = runningTotal;
			let entries = _.filter(allInvoices, (i) => { return i.invoiceDateStamp >= startDate && i.invoiceDateStamp < endDate; }),
				sortedEntries = _.sortBy(entries, "invoiceDateStamp");
			_.each(sortedEntries, (i) => {
				const person = allInvoicePeople.find(aip => aip._id == i.personId);
				i.personName = person && (person.lastName + ', ' + person.firstName);
				i.personId = person && person._id;
				i.personInActive = person && person.inActive;
				i.familyMemberOpenAmount = i.voided ? i.openAmount : i.amountDueForFamilyMember(fp._id, endDate);
				i.familyMemberPaidAmount = i.amountPaidByFamilyMember(fp._id, endDate);
				i.otherCreditsAmount = _.reduce(i.credits, (mem, c) => { return (c.type == "credit" && !(c.paidBy || c.creditReason == "manual_payment") && c.createdAt <= endDate) ? mem + c.amount : mem; }, 0.0);
				runningTotal = runningTotal + i.familyMemberOpenAmount;
				i.runningTotal = runningTotal;
			});

			const openCreditMemos = _.filter(fp.availableCreditMemos(), cm => cm.createdAt <= endDate);
			let creditMemoBalance = 0.0, cmRunningTotal = runningTotal;
			_.each(openCreditMemos,
				(cm) => {
					creditMemoBalance += cm.openAmount;
					cm.usedAmount = cm.originalAmount - cm.openAmount;
					cmRunningTotal -= cm.openAmount;
					cm.runningTotal = cmRunningTotal;
				}
			);

			familyBalances.push({
				familyPersonId: fp._id,
				familyPersonName: fp.lastName + ", " + fp.firstName,
				familyPersonInActive: fp.inActive,
				startingBalance,
				endingBalance: runningTotal - creditMemoBalance,
				entries: sortedEntries,
				openCreditMemos
			});
		}
		const sortByField = options.sortByField || "familyPersonName";
		let output = _.sortBy(_.filter(familyBalances, (fb) => { return fb.entries.length > 0 || fb.openCreditMemos.length > 0; }), fb => { return sortByField == 'endingBalance' ? fb.endingBalance * -1 : fb[sortByField] });

		const familyBalanceType = options.familyBalanceType || "";
		if (familyBalanceType === "pastDue") {
			output = _.filter(output, fb => { return fb.endingBalance > 0; });
		} else if (familyBalanceType === "prepaid") {
			output = _.filter(output, fb => { return fb.endingBalance < 0; });
		}

		return output;
	},
	async billingPastDueSummary(options) {
		this.unblock();
		const currentUser = await Meteor.userAsync();
		const currentOrg = await Orgs.current();

		await processPermissions({
			assertions: [{ context: "billing/payments", action: "read" }],
			evaluator: (person) => person.type == "admin",
			throwError: true
		});

		const dueDateStamp = new moment().tz(currentOrg.getTimezone()).startOf("day").valueOf(),
			allOpenInvoices = await Invoices.find({ orgId: currentUser.orgId, "openAmount": { "$gt": 0 }, dueDate: { "$lt": dueDateStamp } }).fetchAsync();

		const groupedInvoices = _.groupBy(allOpenInvoices, (i) => i.personId);
		const results = [];

		for (const [personId, invoices] of Object.entries(groupedInvoices)) {
			const personDetail = await People.findOneAsync({ orgId: currentUser.orgId, _id: personId }, { fields: { firstName: 1, lastName: 1, inActive: 1 } });
			const balance = _.reduce(invoices, (memo, i) => memo + i.openAmount, 0.0);
			results.push({
				personId,
				personDetail,
				balance
			});
		}

		const sortedResults = _.sortBy(results, (p) => -1 * p.balance);
		return sortedResults;
	},

	async billingReceiptsReport(options) {
		this.unblock();
		const currentUser = await Meteor.userAsync();
		const currentOrg = await Orgs.current();

		await processPermissions({
			assertions: [{ context: "billing/payments", action: "read" }],
			evaluator: (person) => person.type == "admin",
			throwError: true
		});

		moment.tz.setDefault(currentOrg.getTimezone());
		const startDate = new moment(options.startDate, "MM/DD/YYYY").valueOf();
		const endDate = new moment(options.endDate, "MM/DD/YYYY").add(1, "days").valueOf();

		let runningTotal = 0.0, payments = [];
		const allInvoices = await Invoices.find({ orgId: currentUser.orgId, "credits.createdAt": { "$gte": startDate, "$lt": endDate } }).fetchAsync();
		_.each(allInvoices, (i) => {

			const filteredCredits = _.filter(i.credits, (c) => {
				const x =
					c.createdAt >= startDate && c.createdAt < endDate &&
					((_.isEmpty(options.paymentType) && c.creditReason == "manual_payment") ||
						(_.contains(options.paymentType, "manual_cash") && (
							(c.creditReason == "manual_payment" && c.creditManualPaymentMethod == "cash"))) ||
						(_.contains(options.paymentType, "manual_check") && (
							(c.creditReason == "manual_payment" && c.creditManualPaymentMethod == "check"))) ||
						(_.contains(options.paymentType, "manual_card") && (
							(c.creditReason == "manual_payment" && c.creditManualPaymentMethod == "card"))) ||
						(_.contains(options.paymentType, "manual_ach") && (
							(c.creditReason == "manual_payment" && c.creditManualPaymentMethod == "ach")))
					);

				return x;
			});

			_.each(filteredCredits, (c) => {
				runningTotal = runningTotal + c.amount;
				let typeDescription = "", description = "", payer = "";
				switch (c.creditReason || c.type) {
					case "manual_payment":
						typeDescription = "Manual payment"
							+ (c.creditManualPaymentMethod ? " - " + c.creditManualPaymentMethod : "");
						description = (c.creditNote ? " - " + c.creditNote : "");
						break;
					case "security_deposit_refund":
						typeDescription = "Security deposit refund";
						description = (c.creditNote ? " - " + c.creditNote : "");
						break;
					case "bad_debt":
						typeDescription = "Bad debt";
						description = (c.creditNote ? " - " + c.creditNote : "");
						break;
					case "payroll_deduction":
						typeDescription = "Payroll deduction";
						description = (c.creditNote ? " - " + c.creditNote : "");
						break;
					default:
						typeDescription = "Other";
						description = c.creditNote;
						break;
				}
				payer = c.paidByDesc ? c.paidByDesc : "";
				payments.push({
					createdAt: c.createdAt,
					type: typeDescription,
					payer,
					payerId: c.paidBy,
					description,
					invoiceNumber: i.invoiceNumber,
					invoiceId: i._id,
					amount: c.amount
				});
			});
		});
		const allCreditMemoPeople = await People.find({
			orgId: currentUser.orgId,
			"billing.creditMemos.createdAt": { "$gte": startDate, "$lt": endDate }
		}).fetchAsync(),
			selectedCMTypes = _.chain(options.paymentType).filter((pt) => pt.startsWith("credit_memo_")).map((pt) => pt.replace("credit_memo_", "")).value();

		// Adding for loop here
		for (let p of allCreditMemoPeople) {
			const filteredCreditMemos = _.filter(p.billing.creditMemos, (cm) => {
				return (_.isEmpty(options.paymentType) ||
					_.contains(selectedCMTypes, cm.type)) &&
					cm.createdAt >= startDate && cm.createdAt < endDate && !cm.voidedBy && cm.type !== 'systemOverpayment';
			});
			for (let cm of filteredCreditMemos) {
				runningTotal = runningTotal + cm.originalAmount;
				const cmType = _.find(currentOrg.availableCreditMemoTypes(), (cmt) => { return cmt.type == cm.type });
				payments.push({
					createdAt: cm.createdAt,
					type: "Credit Memo" + (cmType ? " - " + cmType.description : ""),
					description: (cm.notes || ""),
					payer: p.firstName + " " + p.lastName,
					payerId: p._id,
					amount: cm.originalAmount,
					invoiceNumber: ""
				});
			}
		}
		return {
			entries: _.sortBy(payments, "createdAt"),
			totalAmount: runningTotal
		};
	},
	async backfillCrmStatus(personId) {
		await ChildcareCRM.backfillPersonStatus(personId);
	},
	async billingAdminSummaryReport(options) {
		const currentUser = await Meteor.userAsync();
		const currentOrg = await Orgs.current();
		await processPermissions({
			assertions: [{ context: "billing/payments", action: "read" }],
			evaluator: (person) => person.type == "admin",
			throwError: true
		});

		moment.tz.setDefault(currentOrg.getTimezone());
		const startDate = new moment(options.startDate, "MM/DD/YYYY").valueOf();
		const endDate = new moment(options.endDate, "MM/DD/YYYY").add(1, "days").valueOf();
		const periodStartDate = options.periodStartDate && new moment(options.periodStartDate, "MM/DD/YYYY").valueOf(),
			periodStartDateEnd = periodStartDate && options.periodStartDateEnd && new moment(options.periodStartDateEnd, "MM/DD/YYYY").valueOf();

		const itemGroups = await LedgerDetailService.LedgerEntriesForRange({
			startDate,
			endDate,
			includeLinkedDetails: options.includeLinkedDetails,
			periodStartDate,
			periodStartDateEnd,
			orgIds: options.orgIds,
			currentUser
		});
		return itemGroups;
	},
	async getSwitchableMemberships() {
		this.unblock();
		const user = await Meteor.userAsync();
		return await MembershipService.getMemberships(usersRepository, user);
	},
	async getSuperadminAvailableOrgs(orgId) {
		this.unblock();
		const currentUser = await Meteor.userAsync();
		const person = await currentUser.fetchPerson();

		if (!person?.superAdmin) {
			throw new Meteor.Error(403, "Access denied");
		}
		const o = await Orgs.findOneAsync(orgId);
		const orgs = await o.findScopedOrgs(o._id);
		orgs.sort((a, b) => a.name.localeCompare(b.name));
		return orgs;
	},
	async getSwitchableOrgs(options) {
		const currentUser = await Meteor.userAsync();
		const person = await currentUser.fetchPerson(),
			org = await Orgs.current();

		if (!currentUser ||
			(person.type != "admin") ||
			!(person.masterAdmin || person.superAdmin) ||
			!(org.parentOrgId || org.enableSwitchOrg)
		) {
			if (!options.failSilently)
				throw new Meteor.Error(403, "Access denied - not in a switchable org");
			else
				return;
		}
		return _.sortBy(await person.findScopedOrgs(options), "name");
	},
	async getOrgsInUsersOrg() {
		this.unblock();
		const user = await Meteor.userAsync();
		const currentPerson = await user?.fetchPerson();
		const currentOrg = await Orgs.current();
		if (!currentPerson) {
			throw new Meteor.Error("Invalid Person", "Cannot get the orgs for this person");
		}
		if (!currentOrg) {
			throw new Meteor.Error("Invalid Org", "Cannot get the current org");
		}
		return OrgServerUtils.getOrgsFromPersonOrg(currentOrg, currentPerson);
	},
	async getSwitchableSites() {
		this.unblock();
		const user = await Meteor.userAsync();
		const person = await user?.fetchPerson();
		const org = await Orgs.current();

		if (!person ||
			(person.type !== "admin") ||
			!(person.masterAdmin || person.superAdmin) ||
			!(org.parentOrgId || org.enableSwitchOrg)
		) {
			throw new Meteor.Error(403, "Access denied - not in a switchable org");
		}
		return await OrgsUtil.getSwitchableSites(org, person);
	},
	async getChildOrgs() {
		this.unblock();
		const currentUser = await Meteor.userAsync();
		const person = await currentUser.fetchPerson();
		const org = await Orgs.current();

		if (!org || !currentUser || (person.type !== "admin") || !(person.masterAdmin || person.superAdmin)) {
			return;
		}
		return _.sortBy(await org.findScopedOrgs(org._id), "name");
	},
	async enableMasterAdmin(options) {
		this.unblock();
		const currentUser = await Meteor.userAsync();
		const person = await currentUser.fetchPerson(),
			org = await Orgs.current();

		if (!person.superAdmin)
			await processPermissions({
				assertions: [{ context: "people/roleAssignments", action: "edit" }],
				evaluator: (testPerson) => testPerson.type == "admin" && (testPerson.masterAdmin || testPerson.superAdmin),
				throwError: true
			});

		if (!org.parentOrgId && !org.enableSwitchOrg)
			throw new Meteor.Error(403, "Access denied - not a valid switchable org");

		const setQuery = {};

		if (options.scope == "global" && (person.masterAdminGlobal || person.superAdmin)) {
			setQuery["masterAdminGlobal"] = options.enabled == 'yes' ? true : null;
			setQuery["masterAdmin"] = true;
		} else
			setQuery["masterAdmin"] = options.enabled == 'yes' ? true : null;

		if (!_.isEmpty(setQuery))
			await People.updateAsync({ _id: options.personId, orgId: org._id, type: "admin" }, { $set: setQuery });
		if (options.enabled === 'yes' && options.scope !== 'global') {
			const adminPerson = await People.findOneAsync({ _id: options.personId, orgId: org._id, type: "admin" });
			if (!adminPerson.topmostOrgId) {
				const topOrg = await adminPerson.topmostOrg();
				await People.updateAsync({ _id: options.personId }, { $set: { topmostOrgId: topOrg?._id } });
			}
		}
	},
	async setMasterAdminTopmostOrgId(options) {
		this.unblock();
		const currentUser = await Meteor.userAsync();
		const person = await currentUser.fetchPerson(),
			org = await Orgs.current();

		if (!person.superAdmin)
			await processPermissions({
				assertions: [{ context: "people/roleAssignments", action: "edit" }],
				evaluator: (testPerson) => testPerson.type == "admin" && (testPerson.masterAdmin || testPerson.superAdmin),
				throwError: true
			});

		if (!org.parentOrgId && !org.enableSwitchOrg)
			throw new Meteor.Error(403, "Access denied - not a valid switchable org");

		const userScopedOrgs = await person.findScopedOrgs(),
			matchedScopedOrg = userScopedOrgs.find(o => o._id == options.topmostOrgId),
			targetPerson = await People.findOneAsync({ _id: options.personId, type: "admin", orgId: org._id, masterAdmin: true });

		if (!matchedScopedOrg || !targetPerson)
			throw new Meteor.Error(403, "Access denied - you do not have permission to set this particular topmost org");

		await People.updateAsync({ _id: targetPerson._id, orgId: org._id }, { $set: { topmostOrgId: matchedScopedOrg._id } });
	},
	async updatePersonRoles(options) {
		this.unblock();
		const currentUser = await Meteor.userAsync();
		const currentOrg = await Orgs.current();
		const currentPerson = currentUser && await currentUser.fetchPerson();
		if (!currentPerson.superAdmin)
			await processPermissions({
				assertions: [{ context: "people/roleAssignments", action: "edit" }],
				evaluator: (person) => person.type == "admin",
				throwError: true
			});
		const targetPerson = await People.findOneAsync({ orgId: currentPerson.orgId, _id: options.personId });

		if (!targetPerson)
			throw new Meteor.Error(403, "Access denied");

		if (!(currentPerson.masterAdmin || currentPerson.superAdmin)) {
			if (targetPerson.masterAdmin || targetPerson.superAdmin)
				throw new Meteor.Error(403, "You cannot modify the roles of a master admin.");

			let enabledRolesDetails = await currentOrg.enabledRolesDetails();
			if (_.contains(options.selectedRoles, _.filter(enabledRolesDetails, rd => !rd.localAdminCanAssign)))
				throw new Meteor.Error(403, "You cannot assign or modify the role of a user that has a non-local role assigned.");
		}

		await People.updateAsync({ _id: options.personId }, { "$set": { roles: options.selectedRoles } });
	},
	async transferInquiry(options) {
		this.unblock();
		const currentUser = await Meteor.userAsync();
		const person = await currentUser.fetchPerson(),
			org = await Orgs.current();

		if (!currentUser ||
			(person.type != "admin") ||
			(!person.masterAdmin && !person.superAdmin) ||
			(!org.enableSwitchOrg))
			throw new Meteor.Error(403, "Access denied");

		const destOrg = await Orgs.findOneAsync({ _id: options.destOrgId });
		if (!destOrg || destOrg.parentOrgId != org._id)
			throw new Meteor.Error(500, "Invalid transfer destination");

		const transferPerson = await People.findOneAsync({ orgId: org._id, _id: options.personId, type: "prospect" });
		if (!transferPerson)
			throw new Meteor.Error(500, "No such person found to transfer");

		await People.updateAsync({ _id: options.personId }, { "$set": { "orgId": options.destOrgId, "type": "person" } });

	},
	async getBusinessTaxId() {
		this.unblock();
		const currentUser = await Meteor.userAsync();
		const person = await currentUser.fetchPerson(),
			org = await Orgs.current();;

		if (person.type != "admin") throw new Meteor.Error(403, "Access denied");

		const encTaxId = _.deep(org, "billing.legalEntity.business_tax_id");
		if (encTaxId) {
			const bytes = AES.decrypt(encTaxId, Meteor.settings.mpEntityKey);
			return bytes.toString(Utf8);
		}
	},
	async getYearEndStatementData(options) {
		this.unblock();
		const currentUser = await Meteor.userAsync();
		let person = await currentUser.fetchPerson();
		let org = await Orgs.current();
		let queryOrgId = currentUser.orgId;

		moment.tz.setDefault(org.getTimezone());

		// A deactivated account can login and request statement data
		// we ensure the orgId and personId sent from client are associated with their user record
		if (options.deactivatedUser) {
			if (!userHasMembership({ user: currentUser, orgId: options.orgId, personId: options.personId })) {
				throw new Meteor.Error(403, "Access denied: membership not valid")
			}

			person = await People.findOneAsync({ _id: options.personId })
			org = await Orgs.findOneAsync({ _id: options.orgId });

			if (!person || !org) {
				throw new Meteor.Error(404, "Error generating payment statement: Associations not found")
			}

			queryOrgId = org._id;
		}

		if (!currentUser ||
			(person.type != "family" && person.type != "admin") ||
			(person.type == "family" && options.personId != person._id) ||
			!_.deep(org, "billing.enabled")
		)
			throw new Meteor.Error(403, "Access denied");

		const selectedPerson = await People.findOneAsync({ _id: options.personId });
		if (!selectedPerson)
			throw new Meteor.Error(500, "Person not found.");

		if (selectedPerson) {
			let returnData = {
				generated: true,
				reportType: options.reportType,
				dateInfo: {
					dateType: options.dateType
				}
			};

			let allLegalEntities = [];
			allLegalEntities = allLegalEntities.concat(_.deep(org, "billing.legalEntityHistory") || []);

			const maxEndDate = allLegalEntities.length > 0 && _.chain(allLegalEntities)
				.pluck("endDate")
				.sort()
				.reverse()
				.first(1)
				.value();

			const currentLegalEntity = {
				business_name: _.deep(org, "billing.legalEntity.business_name"),
				address: _.deep(org, "billing.legalEntity.address"),
				city: _.deep(org, "billing.legalEntity.city"),
				state: _.deep(org, "billing.legalEntity.state"),
				zipcode: _.deep(org, "billing.legalEntity.zipcode"),
				startDate: maxEndDate && new moment(maxEndDate, "YYYY-MM-DD").add(1, "day").format("YYYY-MM-DD"),
				endDate: new moment().add(1, "day").format("YYYY-MM-DD"),
				"business_tax_id": _.deep(org, "billing.legalEntity.business_tax_id")
			};
			allLegalEntities.push(currentLegalEntity);
			for (const le of allLegalEntities) {
				const encTaxId = le["business_tax_id"];
				if (encTaxId) {
					const bytes = AES.decrypt(encTaxId, Meteor.settings.mpEntityKey);
					le.taxId = bytes.toString(Utf8);
				}

				//get all the invoices
				let startDate, endDate;
				if (options.dateType == "custom") {
					startDate = new moment(options.startDate, "MM/DD/YYYY").startOf("day").valueOf();
					endDate = new moment(options.endDate, "MM/DD/YYYY").endOf("day").valueOf();
					returnData.dateInfo.startDate = options.startDate;
					returnData.dateInfo.endDate = options.endDate;
				}
				else {
					const taxYear = options.taxYear;
					startDate = new moment("1/1/" + taxYear, "MM/DD/YYYY").valueOf();
					endDate = new moment("12/31/" + taxYear, "MM/DD/YYYY").endOf('day').valueOf();
					returnData.dateInfo.taxYear = taxYear;
				}

				const leStartDate = le.startDate && new moment(le.startDate, "YYYY-MM-DD").valueOf(),
					leEndDate = le.endDate && new moment(le.endDate, "YYYY-MM-DD").valueOf();

				if (leStartDate && leStartDate > startDate) startDate = leStartDate;
				if (leEndDate && leEndDate < endDate) endDate = leEndDate;

				le.formattedStartDate = new moment(startDate).format("MM/DD/YYYY");
				le.formattedEndDate = new moment(endDate).format("MM/DD/YYYY");

				const familyMembers = _.pluck(await Relationships.find({ orgId: queryOrgId, personId: options.personId, relationshipType: "family" }).fetchAsync(), "targetId");
				const allInvoices = await Invoices.find({ orgId: queryOrgId, personId: { "$in": familyMembers }, "credits.createdAt": { "$gte": startDate, "$lt": endDate } }).fetchAsync();

				//starting balance first
				let runningTotal = 0.0, paymentSummaryMap = {}, paymentDetailMap = {};

				_.each(allInvoices, (i) => {

					i.invoiceDateStamp = new moment(i.invoiceDate, "M/DD/YYYY").valueOf();
					//console.log("credits", i.creditsForFamilyMember(options.personId))
					const allCredits = i.creditsForFamilyMember(options.personId, true),
						chargedBackPaymentIds = _.chain(i.credits || []).filter(c => c.type == "chargeback" && c.adyenInfo).map(c => c.adyenInfo.originalReference).value(),
						filteredCredits = _.filter(allCredits, (c) => {
							return (c.createdAt >= startDate && c.createdAt < endDate) &&
								!(c.adyenInfo && _.contains(chargedBackPaymentIds, c.adyenInfo.pspReference))
								&& (c.creditMemoType !== 'securityDepositAuto')
						});
					_.each(filteredCredits, (c) => {
						c.invoiceNumber = i.invoiceNumber;
						c.invoiceLineItems = i.lineItems;
						if (c.type == "refund") c.amount = c.amount * -1;
					});

					const familyMemberPaidAmount = _.reduce(filteredCredits, (memo, c) => { return memo + c.amount; }, 0.00) || 0.00;

					if (!paymentSummaryMap[i.personId]) { paymentSummaryMap[i.personId] = 0.0; paymentDetailMap[i.personId] = []; }
					paymentSummaryMap[i.personId] = paymentSummaryMap[i.personId] + familyMemberPaidAmount;
					paymentDetailMap[i.personId] = paymentDetailMap[i.personId].concat(filteredCredits);

					runningTotal = runningTotal + familyMemberPaidAmount;

				});
				le.totalPaid = runningTotal;

				le.summaries = [];

				let overallTotal = 0.00;
				for (const [k, v] of Object.entries(paymentSummaryMap)) {
					let personData = {};
					const targetPerson = await People.findOneAsync({ _id: k });
					personData.details = targetPerson;
					personData.totalAmount = v;
					personData.transactions = _.sortBy(paymentDetailMap[k], t => t.createdAt);
					le.summaries.push(personData);
					overallTotal = overallTotal + v;
				}
				le.totalPaid = overallTotal;
			};

			returnData.legalEntities = _.filter(allLegalEntities, le => le.totalPaid > 0);

			return returnData;
		}
	},
	async getCurriculumTypes() {
		this.unblock();
		const currentUser = await Meteor.userAsync();
		const person = await currentUser.fetchPerson(),
			org = await Orgs.current();

		if (!currentUser)
			throw new Meteor.Error(403, "Access denied");

		const types = org.availableCurriculumTypes();
		return types;
	},
	async updateCurriculumTypes(curriculumTypes) {
		const currentUser = await Meteor.userAsync();
		const person = await currentUser.fetchPerson(),
			org = await Orgs.current();

		if (!currentUser || person.type != "admin")
			throw new Meteor.Error(403, "Access denied");

		await Orgs.updateAsync({ _id: org._id }, { "$set": { "valueOverrides.curriculumTypes": curriculumTypes } });
	},
	async searchCurriculumItems(options) {
		this.unblock();
		if (options.searchText || options.searchTags) {
			const org = await Orgs.current();
			const query = {
				orgId: org._id,
				archived: { "$ne": true },

			};
			query["$text"] = { $search: options.searchText };
			if (options.searchTags && options.searchTags.length > 0)
				query.selectedTypes = { "$in": options.searchTags };

			const matches = Curriculums.find(query, {

				fields: {
					score: { $meta: "textScore" }
				},

				sort: {
					score: { $meta: "textScore" }
				}
			}),
				allItems = await matches.mapAsync((m) => ({ id: m._id, text: m.headline, description: m.message, item: m }));

			return _.first(allItems, 50);
		} else return [];
	},
	async searchCampaignRecipients(options) {
		this.unblock();
		if (options.searchText) {
			const org = await Orgs.current();
			if (!org) return;
			const query = {
				orgId: org._id,
				type: "prospect",
				inActive: { $ne: true }
			};
			query["$text"] = { $search: options.searchText };
			const matches = await People.find(query, {
				fields: {
					score: { $meta: "textScore" }
				},
				sort: {
					score: { $meta: "textScore" }
				}
			}).fetchAsync();
			let output = [];

			matches.forEach((m) => {
				const personName = m.firstName + ' ' + m.lastName;
				let familyMembers = [], familyNames = [];
				if (m.profileData && m.profileData.familyMembers) {
					m.profileData.familyMembers.forEach((fm, pos) => {
						if (fm.phone || fm.email) {
							const fmName = fm.firstName + ' ' + fm.lastName;
							familyMembers.push({ id: m._id, pos, text: fmName, description: 'Family (' + personName + ')', type: "individual" });
							familyNames.push(fmName);
						}
					});
				}
				output.push({ id: m._id, text: personName, description: 'Entire Family (' + familyNames.join(', ') + ')', type: 'family' });
				//if (familyMembers.length > 0) output = output.concat(familyMembers);
			});

			return output;
		}
		else return [];
	},
	async sendCampaignMessage(options) {
		this.unblock();
		const currentUser = await Meteor.userAsync();
		const person = await currentUser.fetchPerson(),
			org = await Orgs.current();

		if (!currentUser || person.type != "admin")
			throw new Meteor.Error(403, "Access denied");

		const recipients = [];
		if (options.recipients && options.recipients.length > 0) {
			for (const recipient of options.recipients) {
				const inquiryPerson = await People.findOneAsync({ orgId: org._id, _id: recipient.id, type: "prospect" });
				if (recipient.type == "family") {
					for (const fm of inquiryPerson.profileData.familyMembers) {
						recipients.push({
							email: fm.email,
							phone: fm.phone,
							inquiryId: inquiryPerson._id,
							name: fm.firstName + ' ' + fm.lastName
						});
					}
				}
			}
		}

		const filteredRecipients = _.filter(recipients, (recipient) => (options.selectedMethod == "sms" && recipient.phone) || recipient.email);
		if (filteredRecipients.length == 0) throw new Meteor.Error(500, "One or more valid recipients must be specified.");

		if (options.selectedMethod == "sms") {
			filteredRecipients.forEach(recipient => {
				sendSMSMessage(recipient.phone, options.smsMessage, org.originationNumber);
			});
		} else {
			SSR.compileTemplate('campaignMessage', await Assets.getTextAsync('email_templates/v2021/campaign_message.html'));
			const whiteLabel = generateEmailWhiteLabelData(currentOrg);
			for (const recipient of filteredRecipients) {
				const inquiryPerson = await People.findOneAsync(recipient.inquiryId);
				const inquiryName = inquiryPerson.firstName + " " + inquiryPerson.lastName;
				var emailData = {
					message: options.htmlMessage,
					subject: "Re: " + inquiryName + " - " + options.subject,
					org: org
				};
				emailData.whiteLabel = whiteLabel;
				emailData.backgroundColor = `${whiteLabel.primaryColor}1A`;
				emailData.headerOrgNameColor = "#8E8E93";
				emailData.headerBgColor = whiteLabel.primaryColor;
				emailData.secondaryColor = whiteLabel.secondaryColor;
				emailData.assetPrefix = `emailAssets/${whiteLabel.emailAssetPrefix}`;
				emailData.currentYear = new moment().format("YYYY");
				const emailOptions = {
					from: "LineLeader support <<EMAIL>>",
					to: recipient.email,
					subject: options.subject,
					html: SSR.render('campaignMessage', emailData)
				};
				if (org.replyToAddress) emailOptions.replyTo = org.replyToAddress;
				if (org.fromAddress) emailOptions.from = org.fromAddress;
				try {
					await Email.sendAsync(emailOptions);
				} catch (e) {
					console.log(e);
				}
			}
		}
		const timezone = org.getTimezone(), dateMoment = new moment().tz(timezone);
		_.chain(filteredRecipients)
			.groupBy(fr => fr.inquiryId)
			.each(async (messageRecipients, inquiryId) => {
				await Moments.insertAsync({
					orgId: org._id,
					date: dateMoment.format("MM/DD/YYYY"),
					time: dateMoment.format("h:mm a"),
					momentType: "prospect",
					momentTypePretty: "Prospect",
					prospectMomentType: "campaignMessage",
					prospectMomentMessageType: options.selectedMethod,
					prospectMomentMessageRecipients: messageRecipients,
					prospectMomentMessageBody: options.htmlMessage || options.smsMessage,
					prospectMomentMessageSubject: options.subject,
					taggedPeople: [inquiryId],
					createdAt: new Date().valueOf(),
					createdBy: currentUser._id,
					createdByPersonId: person._id,
					comment: options.selectedMethod == "sms" ? options.smsMessage : null,
					prospectType: options.selectedMethod == "sms" ? "Text" : "Email",
					sortStamp: new Date().valueOf()
				});
			});
	},
	async markMessageRead(options) {
		this.unblock();
		let currentUser = await Meteor.userAsync();
		let currentPerson = currentUser && await currentUser.fetchPerson(), org = await Orgs.current();
		if (!currentUser || !currentPerson || !_.contains(["admin", "staff", "family"], currentPerson.type))
			throw new Meteor.Error(403, "Access denied");
		const message = await Messages.findOneAsync({ orgId: currentUser.orgId, _id: options.messageId });
		if ((message.personId == currentUser.personId ||
			_.contains(message.currentRecipients, currentUser.personId) ||
			(org.hasCustomization("messages/administrativeVisibility/enabled") && currentPerson.type == "admin")
		) &&
			!_.contains(message.markedAsRead, currentUser.personId)) {
			await Messages.updateAsync({ _id: options.messageId }, { "$addToSet": { markedAsRead: currentUser.personId } });
			await NewMessages.upsertAsync({ personId: currentUser.personId }, { $set: { hasNotification: false } });
		}
	},
	async personHasNewMessage() {
		this.unblock();
		let currentUser = await Meteor.userAsync();
		let currentPerson = currentUser && await currentUser.fetchPerson();
		if (!currentUser || !currentPerson) {
			throw new Meteor.Error(403, "Access denied");
		}
		// Find documents containing user as a current recipient and not been marked as read by user
		const newMessageDoc = await NewMessages.findOneAsync({ personId: currentPerson._id }, { readPreference: "secondaryPreferred" });
		return (newMessageDoc) ? newMessageDoc : { _id: currentUser._id, hasNotification: false };
	},
	async getPersonMessages(personId) {
		this.unblock();
		return await NewMessages.findOneAsync({ personId: personId });
	},
	async getDocumentRepositoryLink(options) {
		this.unblock();
		const currentUser = await Meteor.userAsync();
		const person = await currentUser.fetchPerson(),
			org = await Orgs.current();

		if (!currentUser || !person || !(_.contains(["admin", "staff", "family"], person.type)))
			throw new Meteor.Error(403, "Access denied");

		let targetKey, targetBucket = "momentpathdocumentrepository";

		if (options.type == "template") {
			const documentDefinition = _.find(org.documentDefinitions, (dd) => { return dd._id == options.templateId; });
			if (documentDefinition) targetKey = documentDefinition.repositoryKey;
		} else if (options.type == "document") {
			if (person.type == "family" && !await Relationships.findOneAsync({ personId: person._id, targetId: options.personId, orgId: org._id, relationshipType: "family" }))
				throw new Meteor.Error(403, "Access denied");
			const targetPerson = await People.findOneAsync({ orgId: org._id, _id: options.personId }),
				targetDocument = targetPerson && targetPerson.documentItems && targetPerson.documentItems[options.documentId];
			console.log("targetPerson", targetPerson, "targetDocument", targetDocument);
			if (targetDocument) {
				targetKey = targetDocument.repositoryKey;
				if (targetDocument.repositorySource && _.contains(["mpairslate", "momentpathdocumentrepository"], targetDocument.repositorySource))
					targetBucket = targetDocument.repositorySource;
			}
		}
		if (targetKey) {
			AWS.config.region = 'us-east-1';
			console.log("document", targetBucket, targetKey);
			let localAccessKey, localSecretKey;
			if (targetBucket == "mpairslate") {
				console.log("using mpairslate creds", Meteor.settings.mpAWSaccessKey);
				localAccessKey = Meteor.settings.mpAWSaccessKey;
				localSecretKey = Meteor.settings.mpAWSsecretKey;
			} else {
				console.log("using traditional creds", Meteor.settings.AWSAccessKeyId);
				localAccessKey = Meteor.settings.AWSAccessKeyId;
				localSecretKey = Meteor.settings.AWSSecretAccessKey;
			}
			console.log("using access key", localAccessKey);
			AWS.config.region = 'us-east-1';
			const localCredentials = new AWS.Credentials(localAccessKey, localSecretKey);
			AWS.config.credentials = localCredentials;

			const s3 = new AWS.S3({
				apiVersion: '2006-03-01',
				credentials: localCredentials
			});

			const getPublicUrl = function (obj) {
				return new Promise((resolve, reject) => {
					s3.getSignedUrl('getObject', obj, (error, url) => {
						if (error) {
							reject(error);
						} else {
							resolve(url);
						}
					});
				});
			}
			const url = await getPublicUrl({
				Bucket: targetBucket,
				Key: targetKey,
				Expires: 90 // seconds
			});

			return { url };
		}
	},
	async getMessageCenterCount(options) {
		this.unblock();
		//MAPS TO PUBLICATION QUERIES;
		const currentUser = await Meteor.userAsync();
		const person = currentUser && await currentUser.fetchPerson();
		if (!currentUser || !person) {
			throw new Meteor.Error(403, "Access denied");
		}

		const searchTerm = options.searchTerm;
		if (options && options.admin) {
			const query = { orgId: person.orgId };
			const adminCount = searchTerm.length > 0 ?
				(await (await Messages.aggregate(MessagesUtils.getMessageQueryWithSearchTerm(query, searchTerm, person._id))).toArray()).length :
				await Messages.find(query).countAsync();
			return { total: adminCount }
		}

		let qualifiedRecipients = [currentUser.personId];
		qualifiedRecipients = qualifiedRecipients.concat(
			_.map(
				await Relationships.find({ orgId: currentUser.orgId, personId: currentUser.personId, relationshipType: "family" }).fetchAsync(),
				(r) => { return r.targetId; }
			)
		);
		let qualifiedQuery = {
			orgId: currentUser.orgId,
			currentRecipients: { "$in": qualifiedRecipients },
		};
		if (options?.excludeArchived) {
			qualifiedQuery.markedArchived = { $ne: person._id };
		}

		const qualifiedRecipientMessagesCount = searchTerm.length > 0 ?
			(await (await Messages.aggregate(MessagesUtils.getMessageQueryWithSearchTerm(qualifiedQuery, searchTerm, person._id))).toArray()).length :
			await Messages.find(qualifiedQuery).countAsync();

		let personQuery = {
			orgId: currentUser.orgId,
			personId: currentUser.personId,
		};
		if (options?.excludeArchived) {
			personQuery.markedArchived = { $ne: person._id };
		}

		const personMessagesCount = searchTerm.length > 0 ?
			(await (await Messages.aggregate(MessagesUtils.getMessageQueryWithSearchTerm(personQuery, searchTerm, person._id))).toArray()).length :
			await Messages.find(personQuery).countAsync();

		return {
			total: (qualifiedRecipientMessagesCount + personMessagesCount),
			personMessages: personMessagesCount,
			recipientMessages: qualifiedRecipientMessagesCount
		};


	},
	async getActivationsData(options) {
		this.unblock();
		const currentUser = await Meteor.userAsync();
		const person = currentUser && await currentUser.fetchPerson(),
			org = await Orgs.current();

		if (!currentUser || !person || !(_.contains(["admin", "staff"], person.type)))
			throw new Meteor.Error(403, "Access denied");

		const activePeople = await People.find({ orgId: org._id, inActive: { $ne: true }, type: "person" }, { fields: { firstName: 1, lastName: 1 } }).fetchAsync(),
			targetIds = activePeople.map((p) => p._id),
			activeRelationships = await Relationships.find({ orgId: org._id, relationshipType: "family", targetId: { $in: targetIds } }, { fields: { personId: 1, targetId: 1 } }).fetchAsync(),
			familyIds = activeRelationships.map((r) => r.personId),
			associatedFamilies = await People.find({ orgId: org._id, inActive: { $ne: true }, type: { "$in": ["admin", "staff", "family"] }, _id: { $in: familyIds } }).fetchAsync(),
			associatedFamilyIds = associatedFamilies.map((f) => f._id),
			activatedFamilyUsers = await Meteor.users.find({ $or: [{ orgId: org._id, personId: { $in: associatedFamilyIds } }, { "membership.orgId": org._id, "membership.personId": { $in: associatedFamilyIds } }] }, { fields: { personId: 1, pending: 1, membership: 1 } }).fetchAsync(),
			outstandingInvitations = await UserInvitations.find({ orgId: org._id, used: false }).fetchAsync(),
			stats = {};

		associatedFamilies.forEach((familyPerson) => {
			familyPerson.activeUserAccount = _.find(activatedFamilyUsers, (fu) => {
				var topLevelFind = (fu.personId == familyPerson._id && !fu.pending);
				var nestedFind = _.find(fu.membership || [], (m) => m.personId == familyPerson._id && !fu.pending);
				return topLevelFind || nestedFind;
			});

			familyPerson.pendingUserAccount = _.find(activatedFamilyUsers, (fu) => {
				var topLevelFind = (fu.personId == familyPerson._id && fu.pending);
				var nestedFind = _.find(fu.membership || [], (m) => m.personId == familyPerson._id && fu.pending)
				return topLevelFind || nestedFind;
			});

			familyPerson.outstandingInvitations = _.filter(outstandingInvitations, (oi) => oi.personId == familyPerson._id);
			if (!familyPerson.activeUserAccount && (familyPerson.pendingUserAccount || familyPerson.outstandingInvitations.length > 0)) {
				familyPerson.isPending = true;
			} else if (!familyPerson.activeUserAccount && familyPerson.outstandingInvitations.length == 0) {
				familyPerson.noInvite = true;
			}
		});

		activePeople.forEach((person) => {
			person.associatedFamilies = [];
			activeRelationships.filter((r) => r.targetId == person._id).forEach((r) => {
				const f = associatedFamilies.find((af) => af._id == r.personId);
				if (f) person.associatedFamilies.push(f);
			});
		});

		stats["totalPeople"] = activePeople.length;
		stats["totalFamilies"] = associatedFamilies.length;
		stats["totalUninvitedFamilyMembers"] = associatedFamilies.filter((af) => { return af.noInvite == true }).length;
		stats["totalUnactivatedFamilyMembers"] = associatedFamilies.filter((af) => { return af.isPending == true }).length;
		stats["totalActivatedFamilyMembers"] = associatedFamilies.filter((af) => { return (af.activeUserAccount) ? true : false }).length;

		if (options && options.includeDetail) {
			stats["detailData"] = activePeople;
		}
		return stats;
	},
	async enrollmentStatusReport(options) {
		this.unblock();
		const currentUser = await Meteor.userAsync();
		const person = await currentUser.fetchPerson(),
			org = await Orgs.current();

		if (!currentUser ||
			(person.type != "admin"))
			throw new Meteor.Error(403, "Access denied");

		const enrollments = await People.find({
			orgId: org._id,
			type: { "$in": ["person", "prospect"] },
			"$or": [
				{ "createdAt": { "$gte": options.startDate, "$lt": options.endDate } },
				{ "deactivatedAt": { "$gte": options.startDate, "$lt": options.endDate } }
			]
		}).fetchAsync(), outputs = {};
		outputs["Enrollments"] = {
			"people": _.filter(enrollments, (p) => p.createdAt >= options.startDate && p.createdAt <= options.endDate)
		};
		outputs["Enrollments"]["counts"] = outputs["Enrollments"]["people"].length;
		_.each(
			_.filter(enrollments, (p) => p.deactivatedAt >= options.startDate && p.deactivatedAt < options.endDate),
			(deactivatedPerson) => {
				if (outputs[deactivatedPerson.deactivationReason]) {
					outputs[deactivatedPerson.deactivationReason]["people"].push(deactivatedPerson);
					outputs[deactivatedPerson.deactivationReason]["counts"]++;
				} else {
					outputs[deactivatedPerson.deactivationReason] = {
						"people": [deactivatedPerson],
						"counts": 1
					};
				}
			}
		);
		return _.map(outputs, (val, key) => ({ label: key, count: val.counts, people: _.sortBy(val.people, (p) => p.lastName + " " + p.firstName) }));
	},
	async groupEnrollmentsReport(options) {
		this.unblock();
		const currentUser = await Meteor.userAsync();
		const person = await currentUser.fetchPerson(),
			org = await Orgs.current();

		if (!currentUser ||
			(person.type != "admin"))
			throw new Meteor.Error(403, "Access denied");

		const groupedEnrollments = {};

		if (options.selectionMethod == "historical-schedule") {
			const historicalDatestamp = new moment.tz(options.historicalDate, "YYYY-MM-DD", org.getTimezone()).valueOf(),
				historicalEnrollments = await Reservations.find({
					orgId: org._id,
					recurringFrequency: { "$ne": null },
					//groupId: {"$ne": null},
					"$and": [
						{
							"$or": [
								{ "scheduledDate": null },
								{ "scheduledDate": { "$lte": historicalDatestamp } }
							]
						},
						{
							"$or": [
								{ "scheduledEndDate": null },
								{ "scheduledEndDate": { "$gte": historicalDatestamp } }
							]
						}
					]
				}).fetchAsync(),
				enrollmentPeople = await People.find({
					orgId: org._id,
					_id: { "$in": historicalEnrollments.map(he => he.selectedPerson) },
					type: "person"
				}).fetchAsync();

			for (const e of enrollmentPeople) {
				const matchedEnrollment = historicalEnrollments.find(he => he.selectedPerson == e._id);
				const group = matchedEnrollment && await Groups.findOneAsync({ _id: matchedEnrollment.groupId || e.defaultGroupId });
				const groupName = (group && group.name) || "Unspecified";

				if (!groupedEnrollments[groupName]) {
					groupedEnrollments[groupName] = [];
				}
				groupedEnrollments[groupName].push(e);
			}
		} else {
			const enrollments = await People.find({
				orgId: org._id,
				type: "person",
				inActive: { "$ne": true }
			}).fetchAsync();

			for (const e of enrollments) {
				const group = await Groups.findOneAsync({ _id: e.defaultGroupId });
				const groupName = (group && group.name) || "Unspecified";

				if (!groupedEnrollments[groupName]) {
					groupedEnrollments[groupName] = [];
				}
				groupedEnrollments[groupName].push(e);
			}
		}
		return _.sortBy(_.map(groupedEnrollments, (val, key) => ({ label: key, count: val.length, people: _.sortBy(val, p => `${p.lastName}|${p.firstName}`) })), (i) => i.label);
	},
	async documentsDueSummary(options) {
		this.unblock();
		options = options || {};
		const currentUser = await Meteor.userAsync();
		const person = currentUser && await currentUser.fetchPerson(),
			org = await Orgs.current();

		if (!person ||
			(person.type != "admin"))
			throw new Meteor.Error(403, "Access denied");

		const documentQuery = [], masterDocumentIds = [], masterDocuments = org.activeDocumentDefinitions();
		_.each(masterDocuments, (dd) => {
			const documentKey = "documentItems." + dd._id,
				unapprovedDocumentKey = "documentItems." + dd._id + ".approvedAt",
				currentQuery = {}, unapprovedQuery = {};
			currentQuery[documentKey] = { "$exists": false };
			documentQuery.push(currentQuery);
			unapprovedQuery[unapprovedDocumentKey] = { "$exists": false };
			documentQuery.push(unapprovedQuery);
			masterDocumentIds.push(dd._id);
		});


		if (_.isEmpty(documentQuery)) return;
		const query = {
			orgId: org._id,
			type: "person"
		};
		if (!options.includeInactive) query["inActive"] = { "$ne": true };
		if (!options.includeWaitList) query["designations"] = { "$nin": ["Wait List"] };
		if (!options.showAll) query["$or"] = documentQuery;
		if (options.selectedGroupId) query["defaultGroupId"] = options.selectedGroupId;


		const people = await People.find(query, { fields: { firstName: 1, lastName: 1, documentItems: 1, designations: 1 }, sort: { lastName: 1, firstName: 1 } }).fetchAsync();
		return _.chain(people)
			.map((p) => {
				let documentsDetail = [];

				masterDocuments.forEach((md) => {
					const matchedItem = (p.documentItems && p.documentItems[md._id]) || {};
					if (!p.documentItems || !Object.keys(p.documentItems).includes(md._id)) {
						return
					}
					let addToSet = true;
					const detailItem = {
						definition: md,
						matchedItem
					};
					if (matchedItem && matchedItem.templateOptionResult) {
						if (matchedItem.templateOptionResult.action == "ack") {
							detailItem.status = "Acknowledged " + new moment(matchedItem.templateOptionResult.date).format("M/D/YYYY");
							if (!options.showAll) addToSet = false;
						}
					} else if (matchedItem && matchedItem.approvedAt) {
						detailItem.status = "Approved " + new moment(matchedItem.approvedAt).format("M/D/YYYY");
						if (!options.showAll) addToSet = false;
					} else if (matchedItem && matchedItem.rejectedAt) {
						detailItem.status = "Rejected " + new moment(matchedItem.rejectedAt).format("M/D/YYYY");
					} else if (matchedItem && matchedItem.createdAt) {
						detailItem.status = "Uploaded " + new moment(matchedItem.createdAt).format("M/D/YYYY");
					} else {
						detailItem.status = "Not uploaded";
					}

					if (addToSet) documentsDetail.push(detailItem);
				});

				return {
					personDetail: p,
					personId: p._id,
					missingDocumentsCount: _.filter(documentsDetail, (dd) => !dd.status.startsWith("Approved ")).length,
					documentsDetail: options.includeDetail ? documentsDetail : null
				};
			})
			.filter((p) => options.showAll || p.missingDocumentsCount > 0)
			.sortBy((p) => p.missingDocumentsCount * -1)
			.value();
	},
	async staffImpactSummary() {
		this.unblock();
		const currentUser = await Meteor.userAsync();
		const person = currentUser && await currentUser.fetchPerson(),
			org = await Orgs.current();

		if (!currentUser ||
			!(person.type == "admin" || person.type == "staff"))
			throw new Meteor.Error(403, "Access denied");

		const details = [],
			todayMoments = Moments.find({ orgId: org._id, createdByPersonId: person._id, createdAt: { "$gte": new moment().startOf("day").valueOf() } }, { fields: { reactions: 1 } }),
			momentCount = await todayMoments.countAsync(),
			reactionsCount = _.reduce(await todayMoments.fetchAsync(), (memo, moment) => memo + (moment.reactions ? Object.keys(moment.reactions).length : 0), 0),
			momentMapData = await todayMoments.fetchAsync();
		const momentMap = momentMapData.map((m) => m._id),
			engagements = await Engagements.find({ orgId: org._id, momentId: { $in: momentMap }, subType: { $ne: "created_moment" } }).countAsync();
		const viewsCount = momentMap && engagements;

		if (momentCount > 0) details.push({ label: "Moments", count: momentCount });
		if (reactionsCount > 0) details.push({ label: "Reactions", count: reactionsCount });
		if (viewsCount > 0) details.push({ label: "Views", count: viewsCount });

		let message = "Be great today! Check here for an update on your progress.";
		const totalCount = momentCount + reactionsCount + viewsCount;
		if (totalCount > 5) message = "Keep going! You're on your way to making a great impact today.";
		if (totalCount > 15) message = "Families are loving all the feedback. Keep it up!";

		return {
			details,
			message
		};
	},
	async billingUpcomingBillingsReport(options) {
		const currentUser = await Meteor.userAsync(),
			org = await Orgs.current();

			await processPermissions({
			assertions: [{ context: "billing/invoices", action: "read" }],
			evaluator: (person) => person.type == "admin",
			throwError: true
		});

		moment.tz.setDefault(org.getTimezone());

		const rangeStart = new moment(options.startDate, "MM/DD/YYYY"),
			rangeEnd = new moment(options.endDate, "MM/DD/YYYY"),
			numDays = rangeEnd.diff(rangeStart, "days") + 1;

		let invoices = [];
		const batchStamp = Date.now(),
			simulateIncludePendingItemsRegister = options.includePendingItems ? {} : null;

		let numDaysArr = Array.from({ length: numDays }, (_, index) => index);

		for (const dayAdd of numDaysArr) {
			const todayStartStamp = new moment(rangeStart).add(dayAdd, 'days').tz(org.getTimezone()).startOf("day").valueOf();
			console.log("simulating invoices for : ", todayStartStamp);

			const people = await People.find({
				orgId: org._id,
				inActive: { $ne: true },
				type: "person",
				$and: [
					{ $or: [{ "billing.enrolledPlans": { $gt: [] } }, { "billing.pendingCharges": { $gt: [] } }] },
					{ $or: [{ "billing.suspendUntil": { "$exists": false } }, { "billing.suspendUntil": { $lt: todayStartStamp } }] }
				]
			}).fetchAsync();

			for (const person of people) {
				const newInvoice = await invoicePerson({
					person: person,
					org: org,
					batchStamp: batchStamp,
					simulateOnly: true,
					overrideTodayStamp: todayStartStamp,
					simulateIncludePendingItemsRegister
				});

				if (newInvoice) {
					newInvoice.personDetails = {
						firstName: person.firstName,
						lastName: person.lastName,
						_id: person._id
					};
					invoices.push(newInvoice);
				}
			}

		}


		return invoices;
	},
	async billingPlanEnrollmentsReport(options) {
		this.unblock();
		//the code here to return all the children not enrolled by site
		await processPermissions({
			assertions: [{ context: "billing/invoices", action: "read" }],
			evaluator: (person) => person.type == "admin",
			throwError: true
		});
		const currentUser = await Meteor.userAsync();
		let org = await Orgs.current();
		if (typeof options.orgIds !== 'undefined')
			if (options.orgIds.length > 0)
				org = options.orgIds.length == 1 ? await Orgs.findOneAsync(options.orgIds[0]) : null;
		let arrReports = []
		if (org == null) {
			const currentOrgs = await Orgs.find({ _id: { $in: options.orgIds } }).fetchAsync();
			const allOrgs = await Orgs.find().fetchAsync();
			const orgsMap = ReportAggregation.orgHierarchyMap(allOrgs, currentOrgs);
			const aggs = [];
			const orgsMeta = ReportAggregation.orgsMeta(allOrgs, currentOrgs);
			for (const orgId of options.orgIds) {
				let tmpOrg = await Orgs.findOneAsync(orgId)
				const getReports = await Reports.GenerateReport(currentUser, tmpOrg, options)[0];
				const tmpResults = { 'Name': tmpOrg.name, 'enrollments': getReports };
				arrReports.push(tmpResults);
				if (!options.groupByPerson) {
					for (const pOrg of orgsMap[tmpOrg._id] || []) {
						aggs[pOrg] = (aggs[pOrg] || 0) + tmpResults.enrollments.enrolledPeoplePlans.length;
					}
				}
			};
			for (const arrReport of arrReports) {
				if (aggs[arrReport.Name]) {
					arrReport.aggTotal = aggs[arrReport.Name];
					arrReport.isRollup = true;
				}
			}
			ReportAggregation.applyMeta(arrReports, orgsMeta, 'Name');
			return arrReports
		}
		else
			return await Reports.GenerateReport(currentUser, org, options);

	},
	'billingAutopayEnrollmentReport': async function (options) {
		this.unblock();
		const currentUser = await Meteor.userAsync();
		const currentPerson = currentUser && await currentUser.fetchPerson(),
			currentOrg = await Orgs.current();

		if (!currentUser || currentPerson.type != "admin")
			throw new Meteor.Error(403, "Access denied");

		let orgIds = [currentOrg._id];
		if (options.orgIds && options.orgIds.length > 0) {
			const orgsScope = await currentPerson.findScopedOrgs(),
				orgsScopeList = orgsScope && _.pluck(orgsScope, "_id");
			orgIds = _.intersection(orgsScopeList, options.orgIds);
		}

		const orgs = await Orgs.find({ _id: { "$in": orgIds } }).fetchAsync();

		const query = { orgId: { "$in": orgIds }, type: "person", inActive: { "$ne": true }, "billing.enrolledPlans": { $gt: [] } };
		if (!options.includeWaitList) query["designations"] = { "$nin": ["Wait List"] };

		const enrolledInPlans = await People.find(query).fetchAsync(),
			relationshipsToEnrollees = await Relationships.find({ targetId: { "$in": enrolledInPlans.map(p => p._id) }, relationshipType: "family" }).fetchAsync(),
			familyPeople = await People.find({ orgId: { "$in": orgIds }, type: "family", inActive: { $ne: true }, _id: { "$in": relationshipsToEnrollees.map(r => r.personId) } }).fetchAsync();

		const results = await Promise.all(enrolledInPlans.map(async (person) => {
			const personRelationshipPeopleIds = relationshipsToEnrollees.filter(r => r.targetId == person._id).map(r => r.personId),
				relatedPeople = familyPeople.filter(p => _.contains(personRelationshipPeopleIds, p._id)),
				relatedAutopayPeopleResults = await Promise.all(relatedPeople.map(async (rp) => _.deep(rp, "billing.autoPay") && await rp.connectedBankAccount() || await rp.connectedCreditCard())),
				relatedBankOnFilePeopleResults = await Promise.all(relatedPeople.map(async (rp) => await rp.connectedBankAccount())),
				relatedAutopayPeople = relatedPeople.filter((_, index) => relatedAutopayPeopleResults[index]),
				currentOrg = _.find(orgs, o => o._id == person.orgId),
				relatedBankOnFilePeople = currentOrg.hasCustomization("billing/queueAutopayments/enabled") &&
					relatedPeople.filter((_, index) => relatedBankOnFilePeopleResults[index]);

			const enrollmentStatus = relatedAutopayPeople.length > 0;
			let detail = "";

			await Promise.all(relatedPeople.map(async (rap) => {
				const nameLabel = rap.firstName + " " + rap.lastName + " - ";
				if (await rap.connectedBankAccount()) {
					detail += nameLabel + " - Bank Account" + ((rap.billing.autoPay == "bank_account" && " - Autopay Enrolled" + " - " + new moment(rap.billing.autoPayUpdatedAt).format("M/DD/YY")) || "") + "<br/>";
				}
				if (await rap.connectedCreditCard()) {
					detail += nameLabel + " - Credit Card" + ((rap.billing.autoPay == "card" && " - Autopay Enrolled" + " - " + new moment(rap.billing.autoPayUpdatedAt).format("M/DD/YY")) || "") + "<br/>";
				}
			}));

			if (relatedBankOnFilePeople)
				relatedBankOnFilePeople.forEach(rap => {
					detail += rap.firstName + " " + rap.lastName + " - Bank Account - Autopay Enrolled<br/>";
				});
			return {
				_id: person._id,
				lastName: person.lastName,
				firstName: person.firstName,
				orgId: person.orgId,
				orgName: currentOrg.name,
				enrollmentStatus,
				detail,
				designations: person.designations || []
			};
		}));

		return results.filter(person => !options.enrollmentStatus || (options.enrollmentStatus == "enrolled" && person.enrollmentStatus) || (options.enrollmentStatus == "not_enrolled" && !person.enrollmentStatus));
	},
	'billingLedgerReport': async function (options) {
		this.unblock();
		// console.log("billingLedgerReport called with options", options);
		const currentUser = await Meteor.userAsync();
		const currentPerson = currentUser && await currentUser.fetchPerson(),
			currentOrg = await Orgs.current();

		if (!currentUser || currentPerson.type != "admin")
			throw new Meteor.Error(403, "Access denied");

		let orgQuery = {};
		if (options.orgIds && options.orgIds.length > 0) {
			const orgsScope = await currentPerson.findScopedOrgs(),
				orgsScopeList = orgsScope && _.pluck(orgsScope, "_id"),
				validatedOrgIds = _.intersection(orgsScopeList, options.orgIds);
			orgQuery["_id"] = { "$in": validatedOrgIds };
		} else {
			orgQuery["_id"] = currentOrg._id;
		}

		let itemGroups = [];
		const allOrgs = await Orgs.find().fetchAsync();
		const currentOrgs = await Orgs.find(orgQuery).fetchAsync();
		const orgsMap = ReportAggregation.orgHierarchyMap(allOrgs, currentOrgs);

		for (const org of currentOrgs) {
			// console.log("working org:", org.name);
			moment.tz.setDefault(org.getTimezone());
			const startDateMoment = new moment(options.startDate, 'MM/DD/YYYY');
			const startDate = startDateMoment.valueOf();
			const endDateMoment = new moment(options.endDate, 'MM/DD/YYYY').add(1, 'days');
			const endDate = endDateMoment.valueOf();
			const periodStartDate =
				options.periodStartDate &&
				new moment(options.periodStartDate, 'MM/DD/YYYY').valueOf();
			const periodStartDateEnd =
				periodStartDate &&
				options.periodStartDateEnd &&
				new moment(options.periodStartDateEnd, 'MM/DD/YYYY').valueOf();
			const numberOfDays = options.groupByDate ? endDateMoment.diff(startDateMoment, 'days') : 1;

			// console.log("numberOfDays", numberOfDays);
			for (let i = 0; i < numberOfDays; i++) {
				var thisDate = startDateMoment.clone().add(i, "days"),
					thisEndDate = options.groupByDate ? thisDate.clone().add(1, "days").valueOf() : endDate;

				// console.log("working thisDate", thisDate.format("MM/DD/YYYY"));

				let thisItemGroups = [];
				if (options.useHistoricalData) {
					const historicalItemGroups = await AuditLedgerBatchesHistorical.findOneAsync({
						date: thisDate.format("YYYY-MM-DD"),
						orgId: org._id
					});
					if (historicalItemGroups) {
						thisItemGroups = historicalItemGroups.data;
					}
				} else {
					thisItemGroups = await MappedLedgerEntriesForRange({
						startDate: thisDate.valueOf(),
						endDate: thisEndDate,
						orgId: org._id,
						includeLinkedDetails: options.includeLinkedDetails,
						periodStartDate,
						periodStartDateEnd,
						cashOffsetFilter: options.cashOffsetFilter,
						includeLocationUser: options.includeLocationUser,
					});
				}

				itemGroups = itemGroups.concat(
					thisItemGroups.map(ig => {
						ig.txDate = thisDate.format("MM/DD/YYYY");
						ig.orgId = org._id;
						ig.orgName = _.deep(org, "billing.netSuite.suppressLocation") ? "" : org.name;
						ig.subsidiaryLabel = _.deep(org, 'billing.netSuite.subsidiary');
						ig.sageLocationCode =
							_.deep(org, 'billing.sage.locationCode') ||
							(
								(
									org?.whiteLabel?.smsPostfix?.includes('sshouse') ||
									org?.whiteLabel?.smsPostfix?.includes('questzone') ||
									org?.whiteLabel?.smsPostfix?.includes('foundations') ||
									org?.whiteLabel?.smsPostfix?.includes('cnmoments')
								) &&
								org.name.substring(0, 3)
							) ||
							'';
						ig.sageCompanyCode = (org?.whiteLabel?.smsPostfix?.includes('cnmoments') && 'CNI');
						ig.cashDepositAccount = ig.description.toLowerCase() === 'payroll deduction' ? '1103' : '1035';
						//ig.cashDepositAccount = _.deep(org, "billing.sage.cashAccountCode") || "";
						if (ig.accountNameInternal == 'accountsReceivable' && (ig.linkedDetails || []).length > 0) {
							ig.linkedDetails.forEach(lig => {
								if (numberOfDays > 1) {
									lig.date = thisDate.format('MM/DD/YYYY');
								}
							}
							);
						}

						return ig;
					})
				);
			}
		}

		if (options.debitOrCreditOnly) {
			for (const itemGroup of itemGroups) {
				if (itemGroup.debitAmount > itemGroup.creditAmount) {
					itemGroup.debitAmount = itemGroup.debitAmount - itemGroup.creditAmount;
					itemGroup.creditAmount = 0;
				} else if (itemGroup.debitAmount < itemGroup.creditAmount) {
					itemGroup.creditAmount = itemGroup.creditAmount - itemGroup.debitAmount;
					itemGroup.debitAmount = 0;
				} else {
					itemGroup.creditAmount = 0;
					itemGroup.debitAmount = 0;
				}
			}
		}
		// if (currentOrgs.length > 1) {
		// 	for (const ig of itemGroups) {
		// 		for (const pOrg of orgsMap[ig.orgId] || []) {
		// 			let summary = itemGroups.find((loopIg) => {
		// 				return ig.accountName === loopIg.accountName &&
		// 					ig.groupName === loopIg.groupName &&
		// 					ig.description === loopIg.description &&
		// 					loopIg.orgName === pOrg
		// 			});
		// 			if (!summary) {
		// 				itemGroups.push({
		// 					accountName: ig.accountName,
		// 					groupName: ig.groupName,
		// 					orgName: pOrg,
		// 					description: ig.description,
		// 					debitAmount: 0,
		// 					creditAmount: 0,
		// 					isRollup: true
		// 				})
		// 				summary = itemGroups[itemGroups.length - 1];
		// 			}
		// 			summary.debitAmount += ig.debitAmount;
		// 			summary.creditAmount += ig.creditAmount;
		// 		}
		// 	}
		// 	itemGroups.sort((ig1, ig2) => {
		// 		if (ig1.isRollup !== ig2.isRollup) {
		// 			return ig2.isRollup < ig1.isRollup ? -1 : 1;
		// 		}
		// 		if (ig1.orgName !== ig2.orgName ) {
		// 			return ig1.orgName.localeCompare(ig2.orgName);
		// 		}
		// 		if (ig1.groupName !== ig2.groupName) {
		// 			return ig1.groupName.localeCompare(ig2.groupName);
		// 		}
		// 		if (ig1.description !== ig2.description) {
		// 			return ig1.description.localeCompare(ig2.description);
		// 		}
		// 		return ig1.accountName.localeCompare(ig2.accountName);
		// 	});
		// }

		return itemGroups
			.filter(ig => (!options.ledgerCode || (ig.accountName || "").toLowerCase().includes(options.ledgerCode.toLowerCase())) &&
				((options.planIds || []).length == 0 || _.contains(options.planIds, ig.accountPlanOrItemId)));
	},
	'generateTransactionData': async function (options) {
		this.unblock();
		return await MappedLedgerEntriesForRange(options);
	},
	'billingBankDepositReport': async function (options) {
		this.unblock();
		const currentUser = await Meteor.userAsync();
		const currentPerson = currentUser && await currentUser.fetchPerson(),
			currentOrg = await Orgs.current();

		if (!currentUser || currentPerson.type != "admin")
			throw new Meteor.Error(403, "Access denied");

		moment.tz.setDefault(currentOrg.getTimezone());
		const startDate = new moment(options.startDate, "MM/DD/YYYY").valueOf();
		const endDate = new moment(options.endDate, "MM/DD/YYYY").add(1, "days").valueOf();
		console.log("billing bank deposit range", startDate, endDate);

		const itemGroups = await MappedLedgerEntriesForRange({
			startDate,
			endDate,
			orgId: currentUser.orgId,
			includeLinkedDetails: true,
			includeBankDeposits: true
		}),
			undepositedAccountName = _.deep(currentOrg, "billing.billingMaps.undepositedFunds.accountName"),
			filterCategories = ["Settlements", "Voided Payments", "Manual Deposits"],
			filterDescriptions = undepositedAccountName ? [] : ["Payer Receipts"],
			filteredGroups = _.filter(itemGroups, ig => _.include(filterCategories, ig.groupName) || _.include(filterDescriptions, ig.description)),
			resultItems = [];

		_.each(filteredGroups, ig => {
			_.each(ig.linkedDetails, ld => {
				ld.itemTypeDescription = _.include(filterDescriptions, ig.description) ? ig.description : ig.groupName;
				if (ld.itemTypeDescription == "Payer Receipts")
					ld.itemTypeDescriptionDetail = ld.creditPayerSource;
				resultItems.push(ld);
			});
		});

		return resultItems;
	},
	async getUserFromPersonId(personId) {
		this.unblock();
		const currentUser = await Meteor.userAsync();
		if (!currentUser) {
			throw new Meteor.Error('Access denied', 'Cannot access user');
		}
		const userTarget = await Meteor.users.findOneAsync({
			$or: [
				{ personId: personId },
				{
					membership: {
						$elemMatch: { personId: { $eq: personId } }
					}
				}
			]
		});
		return userTarget;
	},
	'withdrawPerson': async function (options) {
		this.unblock();
		var currentUser = await Meteor.userAsync();
		const cuser = await currentUser.fetchPerson();
		const currentOrg = await Orgs.current();
		var personTarget = await People.findOneAsync({ orgId: currentUser.orgId, _id: options.personId });
		if (!currentUser ||
			(cuser?.type != "admin") ||
			!personTarget)
			throw new Meteor.Error(403, "Access denied");

		let query = { $set: {} };
		if (currentOrg.profileDataPrefix()) {
			query["$set"][`${currentOrg.profileDataPrefix()}.withdrawDate`] = options.withdrawDate
		} else {
			query["$set"]["withdrawDate"] = options.withdrawDate;
		}

		await People.updateAsync({ _id: personTarget._id }, query);
		return { result: "success" };
	},
	'isPersonAdmin': async function (personId, orgId) {
		const person = await People.findOneAsync({ _id: personId, orgId: orgId });
		return person.type === 'admin';
	},
	'deactivatePerson': async function (options) {
		this.unblock();
		await processPermissions({
			assertions: [{ context: "people/deactivateUsers", action: "edit" }],
				evaluator: (person) => person.type === "admin",
				throwError: true
			});

		const currentUser = await Meteor.userAsync();
		const currentOrg = await Orgs.current();
		const personTarget = await People.findOneAsync({ orgId: currentUser.orgId, _id: options.personId });
		const org = await Orgs.findOneAsync({ _id: currentUser.orgId });

		if (!personTarget) {
			throw new Meteor.Error('403', "Access denied");
		}

		const previousState = { ...personTarget };
		const updateQuery = {
			inActive: true,
			deactivationReason: options.deactivationReason,
			deactivatedAt: new Date().valueOf()
		};

		if (personTarget.checkedIn) {
			Object.assign(updateQuery, {
				checkedIn: false,
				previousClassroomGroupId: null,
				checkInGroupId: null,
				checkInGroupName: null
			});
		}

		let executeDeactivation = false;
		const withdrawDateNum = options.withdrawDate && new moment.tz(options.withdrawDate, "YYYY-MM-DD", org.getTimezone()).valueOf();

		if (personTarget.type === "person") {
			if (!withdrawDateNum) {
				throw new Meteor.Error('500', "You must specify a withdraw date in order to deactivate this person.");
			}

			const prefix = currentOrg.profileDataPrefix();
			const withdrawDateField = (prefix ? prefix + "." : "") + "withdrawDate";
			updateQuery[withdrawDateField] = withdrawDateNum;

			let updatedPlans = [];
			_.each(personTarget.billing?.enrolledPlans, (ep) => {
				if (ep?.enrollmentDate <= withdrawDateNum && (!ep.expirationDate || ep.expirationDate > withdrawDateNum)) {
					ep.expirationDate = withdrawDateNum;
				}
				if (ep?.enrollmentDate <= withdrawDateNum) {
					updatedPlans.push(ep);
				}
			});

			if (updatedPlans.length > 0) {
				updateQuery['billing.enrolledPlans'] = updatedPlans;
			}

			const relationships = await Relationships.find({ orgId: currentUser.orgId, targetId: personTarget._id }).fetchAsync();
			let couldDeactivateRelationships = [];
			let couldNotDeactivateRelationships = [];

			for (const rel of relationships) {
				const relatedPerson = await People.findOneAsync({ orgId: currentUser.orgId, _id: rel.personId });
				if (relatedPerson && !relatedPerson.inActive) {
					const secondaryRelCount = await Relationships.find({
						orgId: currentUser.orgId,
						personId: relatedPerson._id,
						targetId: { "$ne": personTarget._id }
					}).countAsync({ "relatedPerson.inActive": { "$ne": true } });

					rel.relatedPerson = relatedPerson;
					if (secondaryRelCount === 0) {
						couldDeactivateRelationships.push(rel);
					} else {
						couldNotDeactivateRelationships.push(rel);
					}
				}
			};

			if (options.deactivateAssociatedRelationships) {
				const deactivateQuery = {
					orgId: currentUser.orgId,
					_id: { "$in": couldDeactivateRelationships.map((r) => r.personId) }
				};
				await People.updateAsync(deactivateQuery, {
					"$set": {
						deactivationReason: "Related Person Was Withdrawn",
						inActive: true,
						deactivatedAt: new Date().valueOf()
					}
				}, { multi: true });

				for (const rel of couldDeactivateRelationships) {
					const relatedPersonPreviousState = await People.findOneAsync({ _id: rel.personId });
					const relatedPersonCurrentState = { ...relatedPersonPreviousState, inActive: true };

					await Meteor.callAsync('logHistory', {
						changeType: HistoryAuditChangeTypes.EDIT,
						details: `Deactivated due to related person withdrawal: ${personTarget._id}`,
						previousState: relatedPersonPreviousState,
						currentState: relatedPersonCurrentState,
						performedByUser: currentUser,
						callbackString: HistoryAuditRecordTypes.STATUS
					});
				};

				executeDeactivation = true;
			} else if (options.suppressSuggestedDeactivations || couldDeactivateRelationships.length === 0) {
				executeDeactivation = true;
			} else {
				return { couldDeactivateRelationships, couldNotDeactivateRelationships };
			}
		} else {
			executeDeactivation = true;
		}

		if (executeDeactivation) {
			await People.updateAsync({ _id: personTarget._id }, { $set: updateQuery });

			const currentState = await People.findOneAsync({ _id: personTarget._id });

			await Meteor.callAsync('logHistory', {
				changeType: HistoryAuditChangeTypes.EDIT,
				details: `User deactivated person with reason: ${options.deactivationReason}`,
				previousState,
				currentState,
				performedByUser: currentUser,
				callbackString: HistoryAuditRecordTypes.STATUS
			});

			if (personTarget.type === "person") {
				const reservationsToRemove = await Reservations.find({
					selectedPerson: personTarget._id,
					recurringFrequency: { "$ne": null },
					scheduledDate: { "$gt": withdrawDateNum }
				}).fetchAsync();

				for (const reservation of reservationsToRemove) {
					await Meteor.callAsync('logHistory', {
						changeType: HistoryAuditChangeTypes.DELETE,
						details: 'Reservation removed due to person deactivation',
						previousState: reservation,
						currentState: null,
						performedByUser: currentUser,
						callbackString: HistoryAuditRecordTypes.SCHEDULE
					});

					await Reservations.removeAsync({ _id: reservation._id });
				}

				const reservationsToUpdate = await Reservations.find({
					selectedPerson: personTarget._id,
					recurringFrequency: { "$ne": null },
					"$or": [
						{ scheduledEndDate: { "$eq": null } },
						{ scheduledEndDate: { "$gt": withdrawDateNum } }
					]
				}).fetchAsync();

				const update = {
					"$set": { scheduledEndDate: withdrawDateNum }
				}

				for (const reservation of reservationsToUpdate) {
					await Meteor.callAsync('logHistory', {
						changeType: HistoryAuditChangeTypes.EDIT,
						details: 'Reservation end date updated due to person deactivation',
						previousState: reservation,
						currentState: { ...reservation, scheduledEndDate: withdrawDateNum },
						performedByUser: currentUser,
						callbackString: HistoryAuditRecordTypes.SCHEDULE
					});

					await Reservations.updateAsync({ _id: reservation._id }, update);
				}

				await Meteor.callAsync('crmDeactivateChild', personTarget._id);
			}

			await deactivatePersonUpdateUser({ person: personTarget });

			if (personTarget.type === 'admin') {
				const associatedUser = await personTarget.findAssociatedUser();
				await Meteor.callAsync('sendManageUserUpdates', associatedUser);
			}

			await Meteor.callAsync('updateZkTecoPerson', personTarget._id);
			return { result: "success" };
		}
	},

	'reactivatePerson': async function (personId) {
		await processPermissions({
			assertions: [{ context: "people/deactivateUsers", action: "edit" }],
			evaluator: (person) => person.type === "admin",
			throwError: true
		});

		const currentUser = await Meteor.userAsync();
		const cuser = await currentUser.fetchPerson();
		const personTarget = await People.findOneAsync(personId);

		if (!currentUser ||
			cuser?.type !== "admin" ||
			personTarget.orgId !== currentUser.orgId) {
			throw new Meteor.Error('403', "Access denied");
		}

		const previousState = { ...personTarget };

		await People.updateAsync(personId, { $set: { inActive: false } });

		const currentState = await People.findOneAsync(personId);

		await Meteor.callAsync('logHistory', {
			changeType: HistoryAuditChangeTypes.EDIT,
			details: 'User reactivated person',
			previousState,
			currentState,
			performedByUser: currentUser,
			callbackString: HistoryAuditRecordTypes.STATUS
		});

		if (personTarget.type === 'admin') {
			const associatedUser = await personTarget.findAssociatedUser();
			await Meteor.callAsync('sendManageUserUpdates', associatedUser);
		}

		Meteor.callAsync('setAutoPin', personTarget._id).then(async (res) => {
			if (!res) {
				await Meteor.callAsync('updateZkTecoPerson', personTarget._id);
			}
		}).catch((err) => {
			Log.error('Error setting auto pin', err);
		});
	},

	'lightbridgePeekData': async function () {
		const currentUser = await Meteor.userAsync();
		const currentPerson = currentUser && await currentUser.fetchPerson(),
			currentOrg = currentUser && await currentUser.fetchOrg();

		if (!currentOrg || !currentOrg.hasCustomization("customer/lightbridge/default"))
			throw new Meteor.Error(403, "Access denied");

		let matchedGroupIds = [];
		if (currentPerson.type == "family") {
			const personCursor = await currentPerson.findInheritedRelationships().fetchAsync();
			const matchedGroupIdsSet = new Set();

			for (const rel of personCursor) {
				const relPerson = await People.findOneAsync(rel.targetId);
				if (relPerson && relPerson.defaultGroupId) {
					matchedGroupIdsSet.add(relPerson.defaultGroupId);
				}
			}

			matchedGroupIds = Array.from(matchedGroupIdsSet);
		} else if (currentPerson.type == "staff") {
			if (currentPerson.defaultGroupId) matchedGroupIds = [currentPerson.defaultGroupId];
		} else if (currentPerson.type == "admin") {
			matchedGroupIds = _.chain(await Groups.find({ orgId: currentOrg._id }).fetchAsync()).map(g => g._id).value();
		}
		const today = new moment(),
			periodStart = today.day() <= 5 ? new moment().startOf("week").valueOf() : new moment().add(1, "week").startOf("week").valueOf(),
			periodEnd = today.day() <= 5 ? new moment().endOf("week").valueOf() : new moment().add(1, "week").endOf("week").valueOf(),
			matchedThemes = await CurriculumThemes.find({ selectedGroups: { "$in": matchedGroupIds }, selectedDays: { "$gte": periodStart, "$lt": periodEnd } }).fetchAsync();

		for (const mt of matchedThemes) {
			const themeGroupIds = _.intersection(mt.selectedGroups, matchedGroupIds);
			mt.groups = await Groups.find({ "_id": { "$in": themeGroupIds } }, { fields: { name: 1 } }).fetchAsync();
			const curriculumData = await Curriculums.find({ "curriculumThemeId": mt._id }).fetchAsync();
			mt.activities = _.uniq(curriculumData, false, (item) => ((item.selectedTypes || []).length > 0 ? item.selectedTypes[0] : "") + "|" + item.headline + "|" + item.message);
			mt.startDay = _.min(mt.selectedDays);
			mt.endDay = _.max(mt.selectedDays);
		}

		return matchedThemes;
	},

	'generatePeekHtml': async function (opts) {
		const currentUser = await Meteor.userAsync();
		const currentPerson = currentUser && await currentUser.fetchPerson(),
			currentOrg = currentUser && await currentUser.fetchOrg();

		if (!currentOrg || !currentOrg.hasCustomization("customer/lightbridge/default"))
			throw new Meteor.Error(403, "Access denied");

		return await generatePeekHtmlPopOut(opts);

	},
	'generateLessonPlansSummaryHtml': async function (options) {
		const currentOrg = options.org;
		const groupId = options.selectedGroup;
		let group = null;
		const query = { selectedDays: { "$gte": options.periodStart, "$lt": options.periodEnd }, orgId: currentOrg._id };
		if (groupId) {
			query.selectedGroups = { "$in": [groupId] };
			group = await Groups.findOneAsync({ _id: groupId });
		}
		let matchedThemes = await CurriculumThemes.find(query).fetchAsync();

		for (const mt of matchedThemes) {
			const curriculumThemes = await Curriculums.find({ "curriculumThemeId": mt._id }).fetchAsync();
			const activities = _.uniq(curriculumThemes, false, (item) => ((item.selectedTypes || []).length > 0 ? item.selectedTypes[0] : "") + "|" + item.headline + "|" + item.message);
			let actArr = [];
			const groupedActivities = _.groupBy(activities, (activity) => activity.selectedTypes[0]);
			for (let [key, value] of Object.entries(groupedActivities)) {

				actArr.push({ title: formatStandardName(key), activities: value })
			}
			actArr = actArr.reduce(function (result, value, index, array) {
				if (index % 3 === 0)
					result.push(array.slice(index, index + 3));
				return result;
			}, []);

			mt.activities = actArr;
			mt.startDay = _.min(mt.selectedDays);
			mt.endDay = _.max(mt.selectedDays);
		}

		SSR.compileTemplate("lessonPlansSummary", await Assets.getTextAsync('lesson_plans_summary/lesson_plans_summary_v1.html'));

		const whiteLabelLogo = _.deep(currentOrg, "whiteLabel.assets.emailLogo") || "/media/svg/icons/ll_brandmark.svg",
			whiteLabelPrimaryColor = _.deep(currentOrg, "whiteLabel.primaryColor") || "#AC52DB",
			whiteLabelSecondaryColor = _.deep(currentOrg, "whiteLabel.secondaryColor") || "#EBE8EB";

		Blaze.Template.lessonPlansSummary.helpers({
			"getPrimaryColor": function () {
				return whiteLabelPrimaryColor;
			},
			"getSecondaryColor": function () {
				return whiteLabelSecondaryColor;
			},
			"getLogoSrc": function () {
				return whiteLabelLogo;
			}
		});
		let cssPath = __meteor_bootstrap__.serverDir.replace('/server', '/web.browser');
		cssPath = "/" + fs.readdirSync(cssPath).find(file => file.slice(-4) == ".css");
		const lessonsData = {
			matchedThemes,
			groupName: group ? group.name || '' : 'All Groups',
			startDate: new moment.tz(options.periodStart, currentOrg.timezone).format("MMM Do"),
			endDate: new moment.tz(options.periodEnd, currentOrg.timezone).format("MMM Do, YYYY"),
			cssPath,
		};
		var html_string = SSR.render("lessonPlansSummary", lessonsData);
		return html_string;

	},
    'fetchCurriculum': async function (options = {}) {
		check(options, Object);
	  
		if (!this.userId) {
		  throw new Meteor.Error('unauthorized', 'You must be logged in');
		}
	  
		const user = await Meteor.users.findOneAsync(this.userId);
		if (!user) {
		  throw new Meteor.Error('user-not-found');
		}
	  
		const org = await Orgs.findOneAsync({ _id: user.orgId });
		if (!org) {
		  throw new Meteor.Error('org-not-found');
		}
	  
		const timezone = org.getTimezone();
		let rangeStart, rangeEnd;
	  
		try {
		  rangeStart = options.startDate ?
			moment.tz(options.startDate, "MM/DD/YYYY", timezone).startOf('day').valueOf() :
			moment.tz(timezone).startOf("day").valueOf();
	  
		  rangeEnd = options.endDate ?
			moment.tz(options.endDate, "MM/DD/YYYY", timezone).startOf('day').valueOf() :
			moment.tz(timezone).add(1, "day").startOf("day").valueOf();
		} catch (e) {
		  console.error('Invalid date format', e);
		  throw new Meteor.Error('invalid-date', 'Invalid date format');
		}
	  
		const query = { 
		  orgId: user.orgId,
		  scheduledDate: { "$gte": rangeStart, "$lte": rangeEnd }
		};
	  
		if (options.groupId) {
			query.$or = [
			  { selectedGroups: [] },
			  { selectedGroups: { $in: [options.groupId] } }
			];
		  } else if (user.type === "family") {
			const userPerson = await user.fetchPerson();
			const connectedGroups = await getRelationshipGroupIds(userPerson);
			query.$or = [
			  { selectedGroups: [] },
			  { selectedGroups: { $in: connectedGroups } }
			];
		}
	  
		const results = await Curriculums.find(query, { readPreference: "secondaryPreferred" }).fetchAsync();
		const enhancedResults = results.map(curriculum => ({
			...curriculum,
			sendToAll: curriculum.selectedGroups.length === 0,
			recipientGroups: curriculum.selectedGroups || []
		  }));
		return enhancedResults;
	},
	'fetchCurriculumThemes': async function (options = {}) {
		check(options, Object);
	
		if (!this.userId) {
		  throw new Meteor.Error('unauthorized', 'You must be logged in');
		}
	
		const user = await Meteor.users.findOneAsync(this.userId);
		if (!user) {
		  throw new Meteor.Error('user-not-found');
		}
	
		const org = await Orgs.findOneAsync({ _id: user.orgId });
		if (!org) {
		  throw new Meteor.Error('org-not-found');
		}
	
		const userPerson = await user.fetchPerson();
		const timezone = org.getTimezone();
		const query = { orgId: user.orgId };
	
		if (options.themeId) {
		  query._id = options.themeId;
		} else if (options.activityId) {
		  const theActivity = await Curriculums.findOneAsync({ orgId: org._id, _id: options.activityId });
		  query._id = theActivity?.curriculumThemeId;
		} else {
		  const rangeStart = options.startDate
			? moment.tz(options.startDate, "MM/DD/YYYY", timezone).startOf("day").valueOf()
			: moment.tz(timezone).startOf("day").valueOf();
	
		  const rangeEnd = options.endDate
			? moment.tz(options.endDate, "MM/DD/YYYY", timezone).startOf("day").valueOf()
			: moment.tz(timezone).add(1, "day").startOf("day").valueOf();
	
		  query.selectedDays = { $elemMatch: { $gte: rangeStart, $lte: rangeEnd } };
	
		  if (userPerson?.type === "family") {
			const connectedGroups = await getRelationshipGroupIds(userPerson);
			query.$or = [
			  { selectedGroups: [] },
			  { selectedGroups: { $in: connectedGroups } },
			];
		  }
		}
	
		const themes = await CurriculumThemes.find(query, {
		  readPreference: "secondaryPreferred"
		}).fetchAsync();
	
		return themes;
	},
	async immunizationsDueReport(options) {
		var currentUser = await Meteor.userAsync();
		const cuser = await currentUser.fetchPerson();
		if (!currentUser ||
			cuser?.type != "admin")
			throw new Meteor.Error(403, "Access denied");

		const currentOrg = await Orgs.current();
		const peopleQuery = { orgId: currentUser.orgId, type: "person", inActive: { "$ne": true }, designations: { $nin: ["Wait List"] } };
		const prefix = currentOrg.profileDataPrefix();
		const val = prefix ? prefix + ".birthday" : "birthday";
		peopleQuery[val] = { $exists: true };
		if (options.selectedGroupId)
			peopleQuery.defaultGroupId = options.selectedGroupId;

		const allPeople = await People.find(peopleQuery).fetchAsync(),
			output = [];
		allPeople.forEach(person => {
			const personImmunizations = person.groupedImmunizations({ prefix, currentOrg });
			personImmunizations.filter(pi => pi.overdue || (!options.showOverdueOnly && pi.dueSoon)).forEach(pi => {
				output.push({
					name: person.lastName + ", " + person.firstName,
					immunizationType: pi.type,
					status: pi.overdue ? "Overdue" : "Due Soon",
					dueDate: pi.dueDate
				});
			});
		});
		console.log("output====", output);
		return output;
	},
	"createNewRelease": async function (options) {
		this.unblock();
		var currentUser = await Meteor.userAsync();
		var currentPerson = currentUser && await currentUser.fetchPerson();
		if (!currentUser || currentPerson.type != "admin" || !currentPerson.superAdmin) {
			throw new Meteor.Error(403, "Access Denied");
		}

		const setDateData = {};
		if (options.mobileRelease) setDateData.mobileBadgeDate = new Date().valueOf();
		if (options.webRelease) setDateData.webReleaseDate = new Date().valueOf();


		const releaseLinks = {};
		if (options.webLink) releaseLinks["data.$.webLink"] = options.webLink;
		if (options.mobileLink) releaseLinks["data.$.mobileLink"] = options.mobileLink;

		if (!_.isEmpty(setDateData)) {
			await SharedConfigs.updateAsync({ key: "appHelpInfo" }, { $set: setDateData });
		}

		if (!_.isEmpty(releaseLinks)) {
			await SharedConfigs.updateAsync({ key: "appHelpInfo", "data.type": "releaseInfo" }, { $set: releaseLinks });
		}

		return { success: true };
	},
	"propagateOrgSettings": async function ({ fromOrg, toOrg }) {
		this.unblock();
		const currentUser = await Meteor.userAsync();
		const currentPerson = currentUser && await currentUser.fetchPerson();

		if (!currentUser || currentPerson.type !== USER_TYPES.ADMIN || !currentPerson.superAdmin) {
			throw new Meteor.Error(403, "Access Denied");
		}

		const orgWithSettings = await Orgs.findOneAsync({ _id: fromOrg });

		if (!orgWithSettings) {
			throw new Meteor.Error(404, "Source organization not found");
		}

		const newOrgSettings = OrgCreationService.superAdminCopyOrg(orgWithSettings);

		// Validate destination org exists before updating
		const destinationOrg = await Orgs.findOneAsync({ _id: toOrg });
		if (!destinationOrg) {
			throw new Meteor.Error(404, "Destination organization not found");
		}

		// Update all settings at once
		await Orgs.updateAsync({ _id: toOrg }, { $set: newOrgSettings });

		return {
			success: true,
			propagatedFields: Object.keys(newOrgSettings).length,
			message: `Successfully propagated ${Object.keys(newOrgSettings).length} settings from ${orgWithSettings.name || fromOrg} to ${destinationOrg.name || toOrg}`
		};
	},
	"superAdminFindByEmail": async function ({ emailAddress }) {
		this.unblock();
		var currentUser = await Meteor.userAsync();
		var currentPerson = currentUser && await currentUser.fetchPerson();
		if (!currentUser || currentPerson.type != "admin" || !currentPerson.superAdmin) {
			throw new Meteor.Error(403, "Access Denied");
		}
		let users = [], associatedPeople = [], otherPeople = [], userInvitations = [];

		const user = await usersRepository.findUserByEmail(emailAddress);
		let userPeopleIds = [];
		const orgIds = [];
		if (user) {
			users.push(user);
			if (user.orgId) orgIds.push(user.orgId);
			userPeopleIds.push(user.personId);
			if (user.membership) {
				_.each(user.membership, (u) => {
					userPeopleIds.push(u.personId);
					orgIds.push(u.orgId);
				})
			}
			associatedPeople = await People.find({ _id: { $in: userPeopleIds } }, { fields: { orgId: 1, profileEmailAddress: 1, email: 1, inActive: 1, firstName: 1, lastName: 1 } }).fetchAsync();
		}

		otherPeople = await People.find({ profileEmailAddress: emailAddress, _id: { $nin: userPeopleIds } }, { fields: { orgId: 1, profileEmailAddress: 1, email: 1, inActive: 1, firstName: 1, lastName: 1 } }).fetchAsync();


		var peopleIds = associatedPeople.concat(otherPeople).map((p) => p._id)
		userInvitations = await UserInvitations.find({ $or: [{ personId: { $in: peopleIds } }, { email: emailAddress }] }).fetchAsync();

		_.each(associatedPeople, (ap) => orgIds.push(ap.orgId));
		_.each(otherPeople, (op) => orgIds.push(op.orgId));
		_.each(userInvitations, (ui) => orgIds.push(ui.orgId));

		const orgs = await Orgs.find({ _id: { $in: orgIds } }, { fields: { name: 1 } }).fetchAsync();

		_.each(associatedPeople, (ap) => {
			const o = _.find(orgs, (org) => org._id == ap.orgId);
			if (o) ap.orgName = o.name;
		});

		_.each(otherPeople, (op) => {
			const o = _.find(orgs, (org) => org._id == op.orgId);
			if (o) op.orgName = o.name;
		});

		_.each(userInvitations, (ui) => {
			const o = _.find(orgs, (org) => org._id == ui.orgId);
			if (o) ui.orgName = o.name;
		});

		_.each(users, (u) => {
			const o = _.find(orgs, (org) => org._id == u.orgId);
			if (o) u.orgName = o.name;
			_.each(u.membership, (m) => {
				const om = _.find(orgs, (org) => org._id == m.orgId);
				if (om) m.orgName = om.name
			});
		});

		return {
			users,
			associatedPeople,
			otherPeople,
			userInvitations
		};
	},
	"superAdminDeletePersonEmail": async function (personId) {
		this.unblock();
		var currentUser = await Meteor.userAsync();
		var currentPerson = currentUser && await currentUser.fetchPerson();
		if (!currentUser || currentPerson.type != "admin" || !currentPerson.superAdmin) {
			throw new Meteor.Error(403, "Access Denied");
		}

		await People.updateAsync({ _id: personId }, { $unset: { profileEmailAddress: 1 } });
		return { status: "success" };
	},
	"superAdminDeleteInvitation": async function (invitationId) {
		this.unblock();
		var currentUser = await Meteor.userAsync();
		var currentPerson = currentUser && await currentUser.fetchPerson();
		if (!currentUser || currentPerson.type != "admin" || !currentPerson.superAdmin) {
			throw new Meteor.Error(403, "Access Denied");
		}

		await UserInvitations.removeAsync({ _id: invitationId });
		return { status: "success" };
	},
	"updateProfileDefinition": async function (curId, prefix, profileDefinition) {
		this.unblock();
		var currentUser = await Meteor.userAsync();
		const cuser = await currentUser.fetchPerson();
		if (!currentUser ||
			cuser?.type != "admin" ||
			!cuser.superAdmin ||
			!_.contains(['inquiryProfileFields', 'staffProfileFields', 'familyProfileFields', 'pinCodeCheckinFields', 'profileFields', 'deactivationReasons', 'deactivationReasonsStaff'], prefix))
			throw new Meteor.Error(403, "Access Denied");

		var query = { $set: {} };
		var accessor = "valueOverrides." + prefix;
		query["$set"][accessor] = profileDefinition
		await Orgs.updateAsync({ _id: curId }, query);
	},
	"changeCustomerProperty": async function (curId, options, requireSuperAdmin = false) {
		Log.info("changeCustomerProperty", curId, options);
		this.unblock();
		const currentUser = await Meteor.userAsync();
		const acceptableAreas = Object.values(CustomizableOrgProperties);
		const cuser = await currentUser.fetchPerson();
		if (!currentUser ||
			cuser?.type !== "admin" ||
			(requireSuperAdmin && !cuser.superAdmin) ||
			!acceptableAreas.includes(options.area))
			throw new Meteor.Error(403, "Access Denied");
		const query = {};
		let action;

		action = options.value ? "$set" : "$unset";

		if (!options.value && !_.contains(['inactive'], options.area)) {
			options.value = "";
		}

		if (options.area === "valueOverrides.availablePermissionsContexts" && _.contains(options.value, "none")) {
			action = "$unset";
		}

		if (options.area === "valueOverrides.availablePermissionsRoles" && _.contains(options.value, "none")) {
			action = "$unset";
		}

		query[action] = {};
		switch (options.area) {
			case "customizations":
				const custValue = options.value == "true" ? true : false;
				query[action]["customizations." + options.key] = custValue;
				break;
			default:
				query[action][options.area] = options.value;
				break;
		}

		Log.info("changeCustomerProperty", query)

		await Orgs.updateAsync({ _id: curId }, query);
	},
	'sendAccountingEmail': async function (options) {
		try {
			return await OrgCreationService.sendAccountingEmail(options);
		} catch (e) {
			Log.error('Error sending account email: ', e.reason || e.message || e);
			throw new Meteor.Error(500, 'Error sending account email: ' + e.reason || e.message || e);
		}
	},
	'createNewOrg': async function (options) {
		try {
			return await OrgCreationService.createOrg(options);
		} catch (e) {
			Log.error('Error creating new Org: ', e.reason || e.message || e);
			throw new Meteor.Error(500, 'Error creating new Org: ' + e.reason || e.message || e);
		}
	},
	'getOrg': async function (orgId) {
		this.unblock();
		try {
			return await OrgCreationService.getOrg(orgId);
		} catch (e) {
			Log.error('Error fetching Org: ', e.reason || e.message || e);
			throw new Meteor.Error(500, 'Error fetching Org: ' + e.reason || e.message || e);
		}
	},
	'orgNameExists': async function (orgName, currentOrgId) {
		try {
			return await OrgCreationService.orgNameExists(orgName, currentOrgId);
		} catch (e) {
			Log.error('Error validating Org: ', e.reason || e.message || e);
			throw new Meteor.Error(500, 'Error validating Org: ' + e.reason || e.message || e);
		}
	},
	'copyOrg': async function (options) {
		try {
			return await OrgCreationService.copyOrg(options);
		} catch (e) {
			Log.error('Error copying Org: ', e.reason || e.message || e);
			throw new Meteor.Error(500, 'Error copying Org: ' + e.reason || e.message || e);
		}
	},
	'switchOrg': async function (orgId) {
		this.unblock();
		logger.info('>>> switchOrg start', { orgId });
		var currentUser = await Meteor.userAsync();
		const cuser = await currentUser.fetchPerson()
		if (!currentUser ||
			cuser?.type != "admin" ||
			!cuser.superAdmin)
			throw new Meteor.Error(403, "Access denied");
		await Meteor.users.updateAsync({ _id: (await Meteor.userAsync())._id }, { $set: { orgId: orgId } });
		await People.updateAsync({ _id: currentUser.personId }, { $set: { orgId: orgId } });
		Session.set('switchableSites', null);
		if (cuser?.type === 'admin') {
			await Meteor.callAsync('sendManageUserUpdates', currentUser, false, true);
		}
		logger.info('<<< switchOrg finished', { orgId });
	},
	'resetPins': async function (orgId) {
		var currentUser = await Meteor.userAsync();
		const cuser = await currentUser.fetchPerson();
		if (!currentUser ||
			cuser?.type != "admin" ||
			!cuser.superAdmin)
			throw new Meteor.Error(403, "Access denied");

		const allPeople = await People.find({ orgId: orgId, superAdmin: { $ne: true }, $or: [{ type: "family" }, { type: "staff" }, { type: "admin" }] }).fetchAsync();
		for (const person of allPeople) {
			let success = false;
			while (!success) {
				var pin = Math.floor(Math.random() * 999999); // Math.random returns a number between 0 and 1, multiply to get a number 0 - 999999
				pin = pin.toString().padStart(6, "0"); // pad with leading zeros to make 6 digits
				while (People.find({ pinCode: pin }).count() > 0) {
					// if the pin is already in use, generate a new one
					pin = Math.floor(Math.random() * 999999);
					pin = pin.toString().padStart(6, "0");
				}

				await People.updateAsync({ _id: person._id }, { $set: { pinCode: pin, pinCodeSupplemental: "" } });
				try {
					await Meteor.callAsync('updateZkTecoPerson', person._id, true);
					success = true;
				} catch (e) {
				}
			}
			// Do not send emails or texts to inactive people
			if (person.inActive) {
				continue;
			}

			// this a bit paranoid, but since we're changing an authentication method I want to make extra sure
			// we're grabbing the PIN that is currently committed in the database.
			const updatedPerson = await People.find({ _id: person._id }).fetchAsync()[0];

			const orgs = await Orgs.find({ _id: person.orgId }).fetchAsync();
			const subjectOrg = orgs[0];
			if (person.profileEmailAddress != null) {
				SSR.compileTemplate('pinResetEmail', await Assets.getTextAsync('email_templates/v2021/pin_reset_email.html'));
				var emailData = {
					person: updatedPerson,
					org: subjectOrg
				};

				const whiteLabel = generateEmailWhiteLabelData(subjectOrg);
				emailData.whiteLabel = whiteLabel
				emailData.backgroundColor = `${whiteLabel.primaryColor}1A`;
				emailData.headerOrgNameColor = "#8E8E93";
				emailData.headerBgColor = whiteLabel.primaryColor;
				emailData.secondaryColor = whiteLabel.secondaryColor;
				emailData.assetPrefix = `emailAssets/${whiteLabel.emailAssetPrefix}`;
				emailData.currentYear = new moment().format("YYYY");

				const activeUserMails = await updatedPerson.getActiveUserEmailAddress();
				const emailOptions = {
					from: "LineLeader support <<EMAIL>>",
					to: activeUserMails,
					subject: `You have a new PIN at ${subjectOrg.name}`,
					html: SSR.render('pinResetEmail', emailData)
				};
				const getOrg = await updatedPerson.findOrg();
				if (getOrg.replyToAddress) emailOptions.replyTo = getOrg.replyToAddress;
				if (getOrg.fromAddress) emailOptions.from = getOrg.fromAddress;
				try {
					await Email.sendAsync(emailOptions);
				} catch (e) {
					console.log(e);
				}
			}

			const pinSmsMessage = `Hello ${updatedPerson.firstName}! PIN codes at ${subjectOrg.name} have been reset. Your new six-digit PIN code is: ${updatedPerson.pinCode}. If you have any questions, please contact the school--do not reply to this text. Thank you.`;

			// based on person's type, the primary phone number will be stored in different fields
			if (updatedPerson.phonePrimary != null) {
				sendSMSMessage(updatedPerson.phonePrimary, pinSmsMessage, subjectOrg.originationNumber);
			}
			if (updatedPerson.profileData.phonePrimary != null) {
				sendSMSMessage(updatedPerson.profileData.phonePrimary, pinSmsMessage, subjectOrg.originationNumber);
			}
		}
	},
	'setCurriculumBank': async function (options) {
		this.unblock();
		const user = await Meteor.userAsync();
		const currentPerson = await user?.fetchPerson?.();
		var currentUser = user;
		if (!currentPerson ||
			currentPerson.type != "admin" ||
			!currentPerson.superAdmin)
			throw new Meteor.Error(403, "Access denied");

		await Orgs.updateAsync({ _id: options.orgId }, { $set: { curriculumBankId: options.bankId } });
	},
	'setWmgCenterId': async function (options) {
		this.unblock();
		const user = await Meteor.userAsync();
		const currentPerson = await user?.fetchPerson?.();
		if (!currentPerson ||
			currentPerson.type != "admin" ||
			!currentPerson.superAdmin)
			throw new Meteor.Error(403, "Access denied");

		await Orgs.updateAsync({ _id: options.orgId }, { $set: { wmgCenterId: options.wmgCenterId } });
	},
	'syncWmgCenterState': async function (options) {
		this.unblock();
		const user = await Meteor.userAsync();
		const currentPerson = await user?.fetchPerson?.();
		if (!currentPerson ||
			currentPerson.type != "admin" ||
			!currentPerson.superAdmin)
			throw new Meteor.Error(403, "Access denied");

		sendWmgStateResetMessage(options.orgId);
	},
	'getWmgConfigToken': async function (options) {
		this.unblock();
		const user = await Meteor.userAsync();
		const currentPerson = await user?.fetchPerson?.();
		if (!currentPerson ||
			currentPerson.type != "admin" ||
			!currentPerson.superAdmin)
			throw new Meteor.Error(403, "Access denied");

		//we only sync kidz and families ... so just find a family and create a token with scopes
		const person = await People.findOneAsync({ orgId: options.orgId, inActive: { $ne: true }, type: "family" });
		const org = await Orgs.findOneAsync({ _id: options.orgId });
		const token = signWmgConfigToken(org.wmgCenterId, person._id);

		return { token, url: `${Meteor.settings.wmgPresenceUrl}/v1/embed/settings` };
	},
	'createWmgEngine': async function (options) {
		const user = await Meteor.userAsync();
		const currentPerson = await user?.fetchPerson?.();
		if (!currentPerson ||
			currentPerson.type != "admin" ||
			!currentPerson.superAdmin)
			throw new Meteor.Error(403, "Access denied");

		const org = await Orgs.findOneAsync({ _id: options.orgId });
		const token = signWmgEngineToken(org.wmgCenterId);
		await createWmgEngineApi(org.wmgCenterId, token);
		return true;
	},
	'showGroupsForCameraConfig': async function (orgId) {
		this.unblock();
		const user = await Meteor.userAsync();
		const currentPerson = await user?.fetchPerson?.();
		if (!currentPerson ||
			currentPerson.type != "admin" ||
			!currentPerson.superAdmin)
			throw new Meteor.Error(403, "Access denied");

		const groupList = [];
		const allGroups = await Groups.find({ orgId }, { fields: { _id: 1, name: 1 } }).fetchAsync();
		allGroups.forEach(function (g) {
			groupList.push(`<span>${g._id}: ${g.name}</span>`);
		});
		return groupList;
	},
	'adminSwitchOrg': async function (orgId) {
		this.unblock();
		logger.info('>>> adminSwitchOrg start', { orgId });
		var currentUser = await Meteor.userAsync();
		var currentPerson = currentUser && await currentUser.fetchPerson();

		if (!currentUser ||
			currentPerson.type != "admin" ||
			!currentPerson.masterAdmin)
			throw new Meteor.Error(403, "Access denied - improper permissions");

		const currentOrg = await Orgs.current(),
			currentParentOrgId = (currentOrg.enableSwitchOrg && currentOrg._id) || currentOrg.parentOrgId,
			desiredOrg = await Orgs.findOneAsync({ _id: orgId }),
			parentOrg = await Orgs.findOneAsync({ _id: currentParentOrgId }),
			relatedOrgs = await OrgsUtil.getSwitchableSites(currentOrg, currentPerson),
			relatedOrgList = relatedOrgs.map(o => o._id);

		if (!_.contains(relatedOrgList, orgId))
			throw new Meteor.Error(403, "Access denied - org destination mismatch");

		if (!parentOrg || !parentOrg.enableSwitchOrg)
			throw new Meteor.Error(403, "Org switching not available");

		await Meteor.users.updateAsync({ _id: currentUser._id }, { $set: { orgId: orgId } });
		await People.updateAsync({ _id: currentUser.personId }, { $set: { orgId: orgId } })
		await Meteor.callAsync('updateZkTecoPerson', currentUser.personId);
		if (currentPerson.type === 'admin') {
			await Meteor.callAsync('sendManageUserUpdates', currentUser, false, true)
		}
		logger.info('<<< adminSwitchOrg stop', { orgId });
	},
	'adminSwitchOrgForDashboard': async function (orgId) {
		this.unblock();
		logger.info('>>> adminSwitchOrgForDashboard start', { orgId });
		var currentUser = await Meteor.userAsync();
		var currentPerson = currentUser && await currentUser.fetchPerson();

		if (!currentUser ||
			currentPerson.type != "admin" ||
			!currentPerson.masterAdmin)
			throw new Meteor.Error(403, "Access denied - improper permissions");

		const currentOrg = await Orgs.current(),
			currentParentOrgId = (currentOrg.enableSwitchOrg && currentOrg._id) || currentOrg.parentOrgId,
			parentOrg = await Orgs.findOneAsync({ _id: currentParentOrgId });

		if (!parentOrg || !parentOrg.enableSwitchOrg)
			throw new Meteor.Error(403, "Org switching not available");

		await Meteor.users.updateAsync({ _id: currentUser._id }, { $set: { orgId: orgId } });
		await People.updateAsync({ _id: currentUser.personId }, { $set: { orgId: orgId } })
		await Meteor.callAsync('updateZkTecoPerson', currentUser.personId);
		if (currentPerson.type === 'admin') {
			await Meteor.callAsync('sendManageUserUpdates', currentUser, false, true);
		}
		logger.info('>>> adminSwitchOrgForDashboard stop', { orgId });
	},

	'getTopLevelOrg': async function () {
		this.unblock();
		const curOrg = await Orgs.current();
		const currentOrg = await Orgs.findOneAsync({ _id: curOrg._id });

		if (!currentOrg) {
			throw new Meteor.Error(403, "Invalid orgId");
		}

		return await currentOrg.findTopOrg();
	},
	'getTopLevelPersonOrg': async function (currentPersonId) {
		this.unblock();
		const currentPerson = await People.findOneAsync({ _id: currentPersonId });

		if (!currentPerson) {
			throw new Meteor.Error(403, "Invalid personId");
		}

		return await currentPerson.topmostOrg();
	},
	async getOrgHierarchyAsLevels() {
		this.unblock();
		const currentOrg = await Orgs.current();

		if (!currentOrg) {
			throw new Meteor.Error(403, "Invalid orgId");
		}

		return await OrgsUtil.getOrgHierarchyAsLevels(currentOrg);
	},
	'adminSwitchOrgForInvoiceId': async function (invoiceId) {
		this.unblock();
		const matchingInvoice = await Invoices.findOneAsync({ _id: invoiceId });

		if (!matchingInvoice || !matchingInvoice.orgId) {
			throw new Meteor.Error(403, "Invalid invoice or no orgId associated");
		}

		const orgId = matchingInvoice.orgId;
		try {
			Meteor.apply('switchOrg', [orgId], { returnStubValue: true });
		} catch (error) {
			// Handle the error here, maybe rethrow it
			console.error(error);
			throw new Meteor.Error(500, "Failed to switch org");
		}
	},
	async 'recordAppUsage'(options) {
		this.unblock();
		const user = await Meteor.userAsync();

		Meteor.defer(async function () {
			if (user) {

				const e = {
					orgId: user.orgId,
					type: "mobile",
					subType: options.event,
					createdBy: user.personId,
					createdAt: new Date().valueOf(),
				}
				var eId = await Engagements.insertAsync(e);
			}
		});
	},

	async attendanceReport(options) {
		this.unblock();
		const currentUser = await Meteor.userAsync();
		const currentPerson = await currentUser.fetchPerson();
		const org = await Orgs.current();

		await processPermissions({
			assertions: [{ context: "reports/standard", action: "read" }],
			evaluator: (person) => person.type === "admin" || person.type === "staff",
			throwError: true
		});
		try {
			return await ReportGeneralAttendanceService.generateReport(currentUser, currentPerson, org, options);
		} catch (e) {
			console.log('Error generating General Attendance Report: ', e);
			throw new Meteor.Error(e.error, e.reason, e.details);
		}
	},

	async getAttendanceReportData(options) {
		this.unblock();
		const currentUser = await Meteor.userAsync();
		const currentPerson = await currentUser.fetchPerson();
		const org = await Orgs.current();

		await processPermissions({
			assertions: [{ context: "reports/standard", action: "read" }],
			evaluator: (person) => person.type === "admin" || person.type === "staff",
			throwError: true
		});
		try {
			return await ReportGeneralAttendanceService.getRawReportData(currentUser, currentPerson, org, options);
		} catch (e) {
			console.log('Error generating General Attendance Report Data: ', e);
			throw new Meteor.Error(e.error, e.reason, e.details);
		}
	},

	async processAttendanceData(options) {
		this.unblock();
		const org = await Orgs.current();

		await processPermissions({
			assertions: [{ context: "reports/standard", action: "read" }],
			evaluator: (person) => person.type === "admin" || person.type === "staff",
			throwError: true
		});
		try {
			return await ReportGeneralAttendanceService.getOutputData(org, options);
		} catch (e) {
			console.log('Error processing General Attendance Report Data: ', e);
			throw new Meteor.Error(e.error, e.reason, e.details);
		}
	},

	async attendanceReportCalifornia(options) {
		this.unblock();

		const currentUser = await Meteor.userAsync(),
			currentPerson = await currentUser.fetchPerson(),
			org = await Orgs.current();

		await processPermissions({
			assertions: [{ context: "reports/standard", action: "read" }],
			evaluator: (person) => person.type == "admin" || person.type == "staff",
			throwError: true
		});

		const startDate = moment.tz(options.startDate, "MM/DD/YYYY", org.getTimezone()).startOf('day'), startDateNum = startDate.valueOf();
		const endDate = moment.tz(options.endDate, "MM/DD/YYYY", org.getTimezone()).endOf('day'), endDateNum = endDate.valueOf();
		const numDays = endDate.diff(startDate, "days");
		const weekdays = AVAILABLE_WEEKDAYS.map(day => day.toLowerCase());

		let daysInMonth = [];
		for (let i = 0; i <= numDays; i++) {
			const newDate = startDate.clone().add(i, "days");
			if (newDate.day() > 0 && newDate.day() < 6)
				daysInMonth.push({
					dateStamp: newDate.valueOf(),
					dateEndStamp: newDate.endOf("day").valueOf(),
					dayOfWeek: newDate.day(),
					dayOfMonth: newDate.date(),
					dayLabel: weekdays[newDate.day()],
					blocks: [],
					totalElapsedTime: 0
				});
		}

		const query = {
			$and: [
				{ sortStamp: { $gte: startDateNum, $lte: endDateNum } },
				{ momentType: { $in: ["checkin", "checkout"] } },
			]
		},
			peopleQuery = { orgId: currentUser.orgId, type: "person" };
		if (!options.includeAll)
			peopleQuery["profileData.certified"] = "Yes";

		var peopleIds = _.map(await People.find(peopleQuery, { fields: { _id: 1 } }).fetchAsync(),
			function (p) { return p._id; });

		query["$and"].push({ owner: { $in: peopleIds } });

		const moments = await Moments.find(query).fetchAsync();

		var groupedMoments = _.groupBy(moments, m => m.owner);
		var output = [];
		for (const k in groupedMoments) {
			let m = groupedMoments[k];
			var pid = k;
			var person = await People.findOneAsync({ _id: pid });
			const personDays = JSON.parse(JSON.stringify(daysInMonth));

			const schedule = person.getRecurringSchedule();

			var checkInTimes = _.chain(m)
				.filter(function (i) { return i.momentType == "checkin"; })
				.sortBy(function (i) { return i.sortStamp; })
				.value();
			_.each(checkInTimes, function (checkInTime) {
				var checkOutTime = _.chain(m)
					.filter(function (i) { return i.momentType == "checkout" && i.sortStamp > checkInTime.sortStamp; })
					.sortBy(function (i) { return i.sortStamp; })
					.first()
					.value();

				if (checkInTime && checkOutTime) {
					var elapsedTime = ((moment(checkOutTime.time, "HH:mm a") - moment(checkInTime.time, "HH:mm a")) / 1000 / 60 / 60).toFixed(2);

					const matchedDay = _.find(personDays, pd => checkOutTime.sortStamp >= pd.dateStamp && checkOutTime.sortStamp <= pd.dateEndStamp);
					matchedDay.blocks.push([checkInTime, checkOutTime]);
					matchedDay.totalElapsedTime += elapsedTime;
				}
			});

			const invoicesForPeriod = await Invoices.find({ personId: person._id, "lineItems.periodStartDate": { "$gte": startDateNum, "$lt": endDateNum }, voided: { "$ne": true } },
				{ sort: { "lineItems.enrolledPlan.enrollmentDate": 1 } }).fetchAsync(),
				enrollmentPlan = invoicesForPeriod.length > 0 && _.find(invoicesForPeriod[0].lineItems, li => li.type == "plan"),
				enrollmentDate = enrollmentPlan ? enrollmentPlan.enrolledPlan.enrollmentDate : person.createdAt,
				withdrawalDate = person.deactivatedAt;

			let usedEnrollmentDate = enrollmentDate < startDateNum, usedWithdrawalDate = false;
			_.each(personDays, pd => {
				if (pd.blocks.length > 0) pd.attended = true;
				if (pd.dateStamp >= enrollmentDate && (!withdrawalDate || pd.dateStamp < withdrawalDate)) pd.enrolled = true;
				if (!usedWithdrawalDate && pd.dateStamp >= withdrawalDate) {
					usedWithdrawalDate = true;
					pd.dayCode = "L";
				}
				if (!usedEnrollmentDate && pd.dateStamp >= enrollmentDate) {
					usedEnrollmentDate = true;
					pd.dayCode = "E";
				}
				if (pd.dateStamp < enrollmentDate || pd.dateStamp >= withdrawalDate) pd.dayCode = "-";
				if (!pd.dayCode && pd.attended) pd.dayCode = " ";
				if (!pd.dayCode && schedule && schedule.recurringDays) {
					if (_.contains(schedule.recurringDays, weekdays[pd.dayOfWeek]))
						pd.dayCode = "A";
					else
						pd.dayCode = "X";
				}
				if (!pd.dayCode) pd.dayCode = "A";
				if (pd.attended || _.contains(["A"], pd.dayCode))
					pd.enrolled = true;
			});
			const row = {
				id: pid,
				attendeeName: person ? person.firstName + " " + person.lastName : "",
				attendeeFirstName: person && person.firstName,
				attendeeLastName: person && person.lastName,
				personDays,
				daysEnrolled: _.filter(personDays, pd => pd.enrolled).length,
				daysAttended: _.filter(personDays, pd => pd.attended).length,
				certified: _.deep(person, "profileData.certified") == "Yes"
			};
			const ageInMonths = await person.calcAge("months");
			if (_.deep(person, "profileData.severelyHandicapped") == "Yes") {
				row.category = "Severely handicapped"; row.order = 7;
			} else if (_.deep(person, "profileData.childAtRisk") == "Yes") {
				row.category = "Children at risk of abuse or neglect"; row.order = 6;
			} else if (_.deep(person, "profileData.limitedOrNoEnglish") == "Yes") {
				row.category = "Limited and non-English proficient"; row.order = 5;
			} else if (_.deep(person, "profileData.exceptionalNeeds") == "Yes") {
				row.category = "Exceptional Needs"; row.order = 4;
			} else if (ageInMonths >= 36) {
				row.category = "Three years and older"; row.order = 3;
			} else if (ageInMonths >= 18 && ageInMonths < 36) {
				row.category = "Toddlers"; row.order = 2;
			} else if (ageInMonths < 18 && _.deep(person, "profileData.fcch") == "Yes") {
				row.category = "FCCH Infants"; row.order = 1;
			} else if (ageInMonths < 18) {
				row.category = "Infants"; row.order = 0;
			} else
				row.category = "NO AGE AVAILABLE";

			if (!schedule) {
				row.subcategory = "Full time";
			} else {
				const timespan = ((moment(schedule.scheduledEndTime, "HH:mm a") - moment(schedule.scheduledTime, "HH:mm a")) / 1000 / 60 / 60).toFixed(2);
				if (timespan < 4)
					row.subcategory = "One-half time";
				else if (timespan < 6.5)
					row.subcategory = "Three-quarter time";
				else if (timespan < 10.5)
					row.subcategory = "Full time";
				else
					row.subcategory = "Full time plus";
			}
			const endOfYearDateNum = endDate.endOf("year").valueOf(),
				paymentsForPeriod = await Invoices.find({
					personId: person._id,
					"credits": { "$elemMatch": { voidedAt: { "$exists": 0 }, type: "payment", createdAt: { "$gte": startDateNum, "$lte": endDateNum } } },
					"lineItems.periodStartDate": { "$lt": endOfYearDateNum }, voided: { "$ne": true }
				}).fetchAsync();
			const paymentPeriod = paymentsForPeriod.map(i =>
				i.credits
					.filter(c => !c.voidedAt && c.type == "payment" && c.createdAt >= startDateNum && c.createdAt <= endDateNum)
					.map(c => {
						if (c.adyenInfo)
							c.receiptNumber = c.adyenInfo.pspReference;
						else if (c.cashnetInfo)
							c.receiptNumber = c.cashnetInfo.tx;
						return c;
					})
			).flat();
			row.payments = paymentPeriod;
			row.feesDueTotal = _.reduce(invoicesForPeriod, (memo, i) => memo + i.originalAmount, 0);
			row.feesReceivedTotal = _.reduce(paymentPeriod, (memo, p) => memo + p.amount, 0);
			output.push(row);
		}
		const reportData = {
			rows: output,
			daysInMonth
		};

		if (options.summarize) {
			const categorySummaries = {};
			reportData.rows.forEach(rr => {
				const catLabel = rr.category + " " + rr.subcategory,
					certifiedLabel = rr.certified ? "certified" : "noncertified";
				if (!categorySummaries[catLabel]) categorySummaries[catLabel] = { "certified": 0, "noncertified": 0 };
				categorySummaries[catLabel][certifiedLabel] += rr.daysEnrolled;
			});
			reportData.categorySummaries = categorySummaries;
		}
		return reportData;
	},
	async populationReportCalifornia(options) {
		this.unblock();
		const currentUser = await Meteor.userAsync(),
			currentPerson = await currentUser.fetchPerson(),
			org = await Orgs.current();

		await processPermissions({
			assertions: [{ context: "reports/standard", action: "read" }],
			evaluator: (person) => person.type == "admin" || person.type == "staff",
			throwError: true
		});

		const startDate = moment.tz(options.startDate, "MM/DD/YYYY", org.getTimezone()).startOf('day'), startDateNum = startDate.valueOf(),
			endDate = moment.tz(options.endDate, "MM/DD/YYYY", org.getTimezone()).endOf('day'), endDateNum = endDate.valueOf();

		const query = {
			$and: [
				{ orgId: org._id },
				{ sortStamp: { $gte: startDateNum, $lte: endDateNum } },
				{ momentType: { $in: ["checkin", "checkout"] } },
			]
		},
			peopleQuery = { orgId: currentUser.orgId, type: "person", "profileData.certified": "Yes" };

		const moments = await Moments.find(query, { "fields": { owner: 1 } }).fetchAsync();

		const peopleIds = _.chain(moments).map(m => m.owner).uniq().value();
		peopleQuery["_id"] = { "$in": peopleIds };

		console.log(peopleQuery);
		const people = await People.find(peopleQuery).fetchAsync();

		return people;

	},

	async RosterReportCalifornia(options) {
		this.unblock();
		const currentUser = await Meteor.userAsync(),
			currentPerson = await currentUser.fetchPerson();
		await processPermissions({
			assertions: [{ context: "reports/standard", action: "read" }],
			evaluator: (person) => person.type == "admin" || person.type == "staff",
			throwError: true
		});
		return await Roster.Generate();
	},

	async profileReport(options) {
		this.unblock();
		const currentUser = await Meteor.userAsync();
		const currentPerson = await currentUser.fetchPerson(),
			org = await Orgs.current();

		await processPermissions({
			assertions: [{ context: "reports/standard", action: "read" }],
			evaluator: (person) => person.type == "admin" || person.type == "staff",
			throwError: true
		});

		if (!options.fieldTypes || options.fieldTypes.length === 0) {
			if (options.personType !== 'familyAndChild') {
				return;
			}
			if (!options.fieldTypesChild || options.fieldTypesChild.length === 0) {
				return;
			}
		}

		const result = await ProfileReportService.getProfileReport(options, org, currentPerson);
		return result
	},



	async billingAgingReport(options) {
		this.unblock();
		const currentUser = await Meteor.userAsync();
		options.currentPerson = await currentUser.fetchPerson();
		options.org = await Orgs.current();

		await processPermissions({
			assertions: [{ context: "billing/invoices", action: "read" }],
			evaluator: (person) => person.type === "admin",
			throwError: true
		});

		try {
			return await BillingReportAgingService.generateAgingReport(options);
		} catch (err) {
			if (err.reason) {
				throw new Meteor.Error(err.error, err.reason, err.details);
			} else {
				console.error(err);
				throw new Meteor.Error('500', err.message, '');
			}
		}
	},
	async orgSetAutoPin(orgId) {
		this.unblock();
		const people = await People.find({ orgId, inActive: { $ne: true }, type: { $in: ['family', 'staff', 'admin'] } }).fetchAsync();
		for (const person of people) {
			await PinService.generateAutomaticPin(person._id);
		}
	},
	async setAutoPin(personId) {
		return await PinService.generateAutomaticPin(personId);
	},
	async emailPin(personId) {
		await PinService.sendPinEmail(personId);
	},
	async pinCodeValidation(pinCode) {
		this.unblock();
		var currentUser = await Meteor.userAsync();
		var currentPerson = currentUser && await currentUser.fetchPerson();

		if (!currentUser || !currentPerson)
			throw new Meteor.Error(403, "Access Denied");

		if (!pinCode || pinCode.trim() == "") {
			throw new Meteor.Error(500, "PIN code cannot be blank");
		}

		let validatePerson = await People.findOneAsync({ orgId: currentUser.orgId, pinCode });
		const currentOrg = await Orgs.current();
		const pinLength = currentOrg.pinLength();
		if (!currentOrg.pinUniqueAcrossOrgs() && (validatePerson?.pinCodeSupplemental?.length > 0 || pinCode.length > pinLength)) {
			validatePerson = await People.findOneAsync({
				orgId: currentUser.orgId,
				pinCode: pinCode.substring(0, pinLength),
				pinCodeSupplemental: pinCode.substring(pinLength)
			});
		}

		if (!validatePerson) {
			throw new Meteor.Error(404, "Not Found");
		}

		if (validatePerson._id != currentPerson._id) {
			throw new Meteor.Error(403, "PinCode is incorrect");
		}

		return true;
	},
	async mobilePinCodeValidationWithUserIds({ pin, userIds }) {
		// message center pass thru for true/false
		if (_.isEmpty(userIds)) {
			return await Meteor.callAsync("pinCodeValidation", pin);
		}

		const currentUser = await Meteor.userAsync();
		const currentPerson = currentUser && await currentUser.fetchPerson();

		if (!currentUser || !currentPerson) {
			throw new Meteor.Error(403, "Access Denied");
		}

		if (!pin || pin.trim() == "") {
			throw new Meteor.Error(500, "PIN code cannot be blank");
		}

		const userData = {};
		const users = await Meteor.users.find({ _id: { $in: userIds } }).fetchAsync();
		for (const user of users) {
			let person = await People.findOneAsync({ _id: user.personId, pinCode: pin });
			/*
				* MP-149 ignores pinCodeSupplemental field verification for pin based validation
				* used for on-device profile switching for already-authenticated users
			*/
			if (person) {
				userData._id = user._id;
				userData.personId = person._id;
				break;
			}
		}

		if (_.isEmpty(userData)) {
			throw new Meteor.Error(500, "Invalid Entry - Enter Email/Password or Try Again");
		}

		return { userData }
	},
	async search(options) {
		const user = await Meteor.userAsync();
		const person = user && await user.fetchPerson();
		if (!person) return;

		if (options && options.query) {
			// Clean up the query
			options.query = options.query.trim().replace(/\s+/g, ' ');
		}

		var query = options.query;
		// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Guide/Regular_Expressions#escaping
		query = query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');

		let scopedOrgs, orgList = [user.orgId];
		if (options.expandSearch) {
			scopedOrgs = await person.findScopedOrgs(options);
			if (scopedOrgs.length > 0)
				orgList = scopedOrgs.map(o => o._id);
		}
		const results = await searchPeople({ person, orgList, primaryOrgId: person.orgId, query, includeInactive: options.includeInactive ? true : false, searchCrm: true });

		return _.sortBy(results, "rank").reverse();
	},
	async getReportQbAuditLog(options) {
		this.unblock();
		await processPermissions({
			assertions: [{ context: "billing/configuration/system", action: "read" }],
			evaluator: (person) => person.type == "admin",
			throwError: true
		});

		const currentUser = await Meteor.userAsync();
		const currentPerson = await currentUser.fetchPerson();

		const orgIds = options.orgIds,
			org = orgIds && orgIds.length > 0 && await Orgs.findOneAsync(orgIds[0]),
			tz = org ? org.getTimezone() : "America/New_York",
			startDateNum = new moment.tz(options.startDate, "MM/DD/YYYY", tz).startOf('day').valueOf(),
			endDateNum = new moment.tz(options.endDate, "MM/DD/YYYY", tz).add(1, 'days').startOf('day').valueOf(),
			query = {
				createdAt: { "$gte": startDateNum, "$lt": endDateNum }
			};

		if (orgIds && orgIds.length > 0 && (currentPerson.masterAdmin || currentPerson.superAdmin)) {
			let orgsScope;
			orgsScope = await currentPerson.findScopedOrgs();
			const orgsScopeList = orgsScope && _.pluck(orgsScope, "_id"),
				filteredOrgIds = _.intersection(orgsScopeList, orgIds);

			query["orgId"] = { "$in": filteredOrgIds }
		}
		else if (!currentPerson.superAdmin || !options.superAdmin)
			query["orgId"] = currentPerson.orgId;

		const data = await AuditLedgerBatches.find(query).fetchAsync();

		for (const row of data) {
			const rowOrg = await Orgs.findOneAsync(row.orgId);
			if (rowOrg) row.orgName = rowOrg.name;
		};

		return data;
	},
	'getOtherTimeCards': async function (timeCard) {
		this.unblock();
		let currentUser = await Meteor.userAsync();
		let currentPerson = (currentUser) ? await currentUser.fetchPerson() : { type: null };
		if (!currentUser || currentPerson.type != "admin")
			throw new Meteor.Error(403, "Access denied");

		return await TimeCards.find({
			orgId: timeCard.orgId,
			personId: timeCard.personId,
			checkInDate: timeCard.checkInDate,
			void: { $ne: true },
			_id: { $ne: timeCard._id }
		}).fetchAsync();
	},
	async voidTimeCard(options) {
		let currentUser = await Meteor.userAsync();
		let currentPerson = (currentUser) ? await currentUser.fetchPerson() : { type: null };
		if (!currentUser || currentPerson.type != "admin") {
			throw new Meteor.Error(403, "Access denied");
		}
		await TimeCards.voidWithReason({
			timeCardId: options.timeCardId,
			voidedByUserId: currentUser._id,
			voidedByPersonName: `${currentPerson.firstName} ${currentPerson.lastName}`,
			voidReason: options.voidReason,
		})
	},
	async timeCardToKinderConnect(options) {
		this.unblock();
		const user = await Meteor.userAsync();
		const currentPerson = await user?.fetchPerson?.();
		if (currentPerson?.type != "admin")
			throw new Meteor.Error(403, "Access denied");

		const tc = await TimeCards.findOneAsync({ _id: options.timeCardId });
		if (!tc) throw new Meteor.Error(404, "Time Card Data Not Found");

		const person = await People.findOneAsync({ _id: tc.personId });
		if (!person || !person?.profileData?.kinderConnectChildId) {
			throw new Meteor.Error(404, "KinderConnect ChildID Missing");
		}

		const org = await Orgs.current();
		const agencyId = org.kinderConnect.agencyId;
		const providerId = org.kinderConnect.providerId;
		const code = org.kinderConnect.code;
		const uploadDate = new moment(tc.checkInDate).format("YYYY-MM-DD");

		const attendanceList = [
			{
				ChildID: parseInt(person?.profileData?.kinderConnectChildId),
				AttendanceType: "TimeInOut",
				AttendanceDate: uploadDate,
				TimeIn: new moment(tc.checkInTime, "h:mm a").format("HH:mm:ss"),
				TimeOut: new moment(tc.checkOutTime, "h:mm a").format("HH:mm:ss")
			}
		];

		await KinderConnect.uploadAttendance({ agencyId, providerId, code, attendanceList })
	},

	'familyCheckin': async function (checkInData) {
		this.unblock();
		const userData = await Meteor.userAsync();
		const currentPerson = await userData?.fetchPerson?.();

		if (!currentPerson ||
			!await Relationships.findOneAsync({ personId: currentPerson._id, targetId: checkInData.personId, relationshipType: "family" })
		) {
			throw new Meteor.Error(403, "Access denied");
		}

		const org = await Orgs.current();
		var timezone = org.getTimezone();

		const existingPerson = await People.findOneAsync(checkInData.personId);


		let familyCheckInData = {
			checkInTime: new moment().tz(timezone).valueOf(),
			checkedInById: currentPerson._id,
			formFieldData: {}
		};
		if (checkInData.checkinFormFields && checkInData.checkinFormFields.hasOwnProperty('attending')) {
			familyCheckInData.absent = !checkInData.checkinFormFields.attending;
			if (checkInData.checkinFormFields.attendingAbsenceReason) {
				familyCheckInData.absentReason = checkInData.checkinFormFields.attendingAbsenceReason;
			}
			if (checkInData.checkinFormFields.attendingAbsenceComment) {
				familyCheckInData.absentComment = checkInData.checkinFormFields.attendingAbsenceComment;
			}
			//forcing the attending data onto the saved formfielddata so edit functionality can work in app
			familyCheckInData.formFieldData["attending"] = (familyCheckInData.absent) ? `No - ${familyCheckInData.absentReason}` : "Yes";
		}

		const checkInOutlook = [];
		for (const field of org.pinCodeCheckinFields()) {
			familyCheckInData.formFieldData[field.dataId] = checkInData.checkinFormFields[field.dataId];
			if (field.outlook || field.dataId === "notes") {
				checkInOutlook.push(`${field.label}: ${checkInData.checkinFormFields[field.dataId]}`)
			}
		}

		const existingTimeEstimate = existingPerson?.familyCheckIn?.dropOffTimeEstimate
		if (checkInData?.checkinFormFields?.dropOffTimeEstimate) {
			const arrivalEstimate = expressDriveUpArrivalTime[checkInData.checkinFormFields.dropOffTimeEstimate];
			if (arrivalEstimate) {
				familyCheckInData.dropOffTimeEstimate = new moment().tz(timezone).add(arrivalEstimate, 'minutes').valueOf();
			} else if (arrivalEstimate != null && existingTimeEstimate) {
				familyCheckInData.dropOffTimeEstimate = existingTimeEstimate;
			}
		} else if (existingTimeEstimate) {
			familyCheckInData.dropOffTimeEstimate = existingTimeEstimate;
		}

		const lastInformedArrival = {
			source: "familyCheckin",
			createdAt: familyCheckInData.checkInTime,
			checkedInById: familyCheckInData.checkedInById,
			fieldValues: familyCheckInData.formFieldData,
			absent: familyCheckInData.absent,
			absentReason: familyCheckInData.absentReason
		};

		await People.updateAsync(checkInData.personId, {
			$set: {
				checkInOutlook,
				familyCheckIn: familyCheckInData,
				lastInformedArrival: lastInformedArrival
			}
		});

		//Cancel Reservation
		/**
		 * Currently the mobile app does not have access to the reservation data when marking a child as absent. This will
		 * be in a future mobile release. When this is added, the logic below should take in an array of reservation IDs that
		 * come from the mobile app that we can use to cancel the reservations. The temporary solution for multiple schedules
		 * on one day will be to cancel any matched reservations.
		 */
		Meteor.defer(async function () {
			scheduleGroupDashboardRecalculation(existingPerson?.defaultGroupId);
			if (!familyCheckInData.absent) {
				return;
			}
		
			const reservationService = new ReservationService();
			await reservationService.handleFamilyCheckinCancellation(
				existingPerson._id,
				familyCheckInData,
				currentPerson._id
			);
		})
	},
	"expressDriveUpTimeEstimate": async function (options) {
		const userData = await Meteor.userAsync();
		const currentPerson = await userData?.fetchPerson?.();
		const relationship = await Relationships.findOneAsync({ personId: currentPerson?._id, targetId: options.personId, relationshipType: "family" });
		if (!currentPerson || !relationship) {
			throw new Meteor.Error(403, "Access denied");
		}

		const org = await Orgs.current();
		const timezone = org.getTimezone();

		const timeEstimate = new moment().tz(timezone).add(options.minutes, 'minutes').valueOf();
		const updateValue = {};
		if (options.checkIn) {
			updateValue["$set"] = { "familyCheckIn.dropOffTimeEstimate": timeEstimate };
		} else if (options.pickUp) {
			updateValue["$set"] = { "familyCheckOut.pickUpTimeEstimate": timeEstimate };
		}

		if (!_.isEmpty(updateValue)) await People.updateAsync(options.personId, updateValue);
		return true;
	},
	"expressDriveUpImHere": async function (options) {
		const userData = await Meteor.userAsync();
		const currentPerson = await userData?.fetchPerson?.();
		const relationship = await Relationships.findOneAsync({ personId: currentPerson?._id, targetId: options.personId, relationshipType: "family" });
		if (!currentPerson || !relationship) {
			throw new Meteor.Error(403, "Access denied");
		}

		const org = await Orgs.current();
		const timezone = org.getTimezone();
		const now = new moment().tz(timezone).valueOf();
		const updateValue = {};
		if (options.dropOff) {
			updateValue["$set"] = { "familyCheckIn.dropOffArrival": true, "familyCheckIn.arrivalTime": now };
			updateValue["$unset"] = { "familyCheckIn.dropOffTimeEstimate": 1 };
		} else if (options.pickUp) {
			updateValue["$set"] = { "familyCheckOut.pickUpArrival": true, "familyCheckOut.arrivalTime": now };
			updateValue["$unset"] = { "familyCheckOut.pickUpTimeEstimate": 1 };
		}

		if (!_.isEmpty(updateValue)) await People.updateAsync(options.personId, updateValue);
		return true;
	},
	"expressDriveUpCancel": async function (options) {
		const userData = await Meteor.userAsync();
		const currentPerson = await userData?.fetchPerson?.();
		const relationship = await Relationships.findOneAsync({ personId: currentPerson?._id, targetId: options.personId, relationshipType: "family" });
		if (!currentPerson || !relationship) {
			throw new Meteor.Error(403, "Access denied");
		}

		const person = await People.findOneAsync(options.personId);
		if (!person) return false;
		if (person.checkedIn) {
			await People.updateAsync({ _id: options.personId }, { $set: { "familyCheckOut": {} } });
		} else {
			await People.updateAsync({ _id: options.personId }, { $unset: { "familyCheckIn.dropOffTimeEstimate": 1 } })
		}

		return true;
	},

	async copyCurriculumThemeBank(options) {
		this.unblock();
		const userData = await Meteor.userAsync();
		const userPerson = await userData?.fetchPerson?.();
		if (!userPerson) throw new Meteor.Error(403, "Access denied");

		const totalDays = options?.selectedDays?.length || 0;
		if (totalDays <= 0) throw new Meteor.Error(400, "Select at least 1 day to copy theme");

		const org = await Orgs.findOneAsync(userPerson.orgId);
		const bankIds = (org.hasCustomization("curriculumBank/globalAndLocal") && org.curriculumBankId) ?
			[org._id, org.curriculumBankId] :
			[org.curriculumBankId || org._id];
		const themeId = options.themeId;

		const theme = await CurriculumThemeBanks.findOneAsync({ _id: themeId, bankId: { $in: bankIds } });
		if (!theme || !theme?.published || !theme?.sourceData) throw new Meteor.Error(404, "Theme Not Found");

		let orgsToCopy = [org._id];
		if (userPerson.isMasterAdmin() && options?.orgIds?.length > 0) {
			orgsToCopy = options.orgIds;
		}

		let themeCount = 0;
		for (const orgId of orgsToCopy) {
			const selectedGroups = (await Groups.find({ orgId, activitiesAgeGroup: theme.sourceData.selectedAgeGroup }).fetchAsync()).map(g => g._id);
			if (_.isEmpty(selectedGroups)) continue;

			const org = await Orgs.findOneAsync(orgId);
			if (!org) continue;

			const timezone = org.getTimezone();
			const selectedDays = options.selectedDays.map((d) => { return DateTimeUtils.getDatestampInTimezone(d, timezone) })
			//insert theme for org
			const curriculumThemeId = await CurriculumThemes.insertAsync({
				name: theme.sourceData.name,
				description: theme.sourceData.description,
				orgId,
				selectedGroups,
				themeBankSourceId: theme._id,
				selectedDays,
				createdByPersonId: userPerson._id,
				createdAt: new Date().valueOf()
			});
			++themeCount;

			const user = await Meteor.userAsync();
			for (let x = 0; x < selectedDays.length; x++) {
				const day = selectedDays[x];
				const activities = theme?.sourceData?.selectedDays?.[x];

				if (!activities) continue;
				for (const activityId of activities) {
					const bankActivity = await CurriculumBanks.findOneAsync({ _id: activityId });
					if (!bankActivity || !bankActivity?.published) continue;
					const curriculumId = await Curriculums.insertAsync({
						headline: bankActivity.sourceData.headline,
						type: bankActivity.sourceData.type,
						message: bankActivity.sourceData.message || bankActivity.sourceData.notes,
						materials: bankActivity.sourceData.materials,
						homework: bankActivity.sourceData.homework,
						scheduledDate: day,
						selectedGroups: selectedGroups,
						selectedStandards: bankActivity.sourceData.selectedStandards,
						selectedTypes: bankActivity.sourceData.selectedTypes,
						selectedAgeGroup: bankActivity.sourceData.selectedAgeGroup,
						curriculumThemeId,
						curriculumBankSourceId: activityId,
						teacherNotes: bankActivity.sourceData.teacherNotes,
						internalNotes: bankActivity.sourceData.internalNotes,
						internalLink: bankActivity.sourceData.internalLink,
						orgId,
						createdBy: user._id,
						createdAt: Date.now(),
						mediaFiles: bankActivity.sourceData.mediaFiles,
					});
				}
			}
		}

		return { message: `Successfully created ${themeCount} themes` };
	},
	async propagateCurriculumThemeBankUpdate(options) {
		this.unblock();
		const { themeId, user, internalOnly, propagate } = options;

		if (!internalOnly || !user) {
			throw new Meteor.Error(403, "Bad Caller Data");
		}

		const sourceTheme = await CurriculumThemeBanks.findOneAsync(themeId);
		if (!sourceTheme) {
			throw new Meteor.Error(404, "Not Found");
		}

		const themeQuery = {
			themeBankSourceId: sourceTheme._id,
		};

		// Loop over each individual org level theme that uses this master source theme
		const allCurriculumThemes = await CurriculumThemes.find(themeQuery).fetchAsync();
		for (const theme of allCurriculumThemes) {
			const org = await Orgs.findOneAsync({ _id: theme.orgId });
			if (!org) {
				continue;
			}

			// Sort the theme's selected days datetime array and get the first and last days
			const timezone = org.getTimezone();
			const today = new moment.tz(timezone).startOf('day').valueOf();
			const selectedDays = theme.selectedDays.sort(function (a, b) { return a - b; });
			const length = selectedDays?.length ?? 1;
			const firstDay = selectedDays?.[0] ?? 0;
			const lastDay = selectedDays?.[length - 1] ?? 0;

			// Determine if any updates need to be made based on first and last days, propagate setting, and current date
			if (propagate === "current" && (lastDay < today || firstDay > today)) {
				return;
			} else if (propagate === "currentFuture" && lastDay < today) {
				return;
			}

			const allGroups = await Groups.find({ orgId: theme.orgId, activitiesAgeGroup: sourceTheme.sourceData.selectedAgeGroup }).fetchAsync();
			const selectedGroups = allGroups.map(group => group._id);

			// Update the theme with new name, description, and selected groups.
			const update = {
				name: sourceTheme.sourceData.name,
				description: sourceTheme.sourceData.description,
				selectedGroups
			}
			await CurriculumThemes.updateAsync({ _id: theme._id }, { $set: update });

			for (let x = 0; x < theme.selectedDays.length; x++) {
				const day = theme.selectedDays[x];
				let activities = null;
				if (sourceTheme?.sourceData?.selectedDays?.length % theme.selectedDays.length === 0) {
					activities = sourceTheme?.sourceData?.selectedDays?.[x % sourceTheme.sourceData.selectedDays.length];
				} else {
					activities = sourceTheme?.sourceData?.selectedDays?.[x];
				}
				let currentDailyActivityIdsData = await Curriculums.find({ curriculumThemeId: theme._id, scheduledDate: day }).fetchAsync();
				let currentDailyActivityIds = currentDailyActivityIdsData.map(activity => activity.curriculumBankSourceId);

				if (!activities) {
					if (currentDailyActivityIds?.length) {
						// TODO: handling for increasing/decreasing # of days on the theme itself doesn't exist yet. That might need to be a feature request.
						// This removes the activities from days that no longer exist in the source theme. But it doesn't remove the day from the theme itself.
						await Curriculums.removeAsync({ curriculumThemeId: theme._id, scheduledDate: day });
					}
					continue;
				}

				if (!activities?.length && currentDailyActivityIds?.length) {
					// Remove whole day of activities if they no longer exist in source.
					await Curriculums.removeAsync({ curriculumThemeId: theme._id, scheduledDate: day });
				}

				for (const activityId of activities) {
					currentDailyActivityIds = _.without(currentDailyActivityIds, activityId);
					const existingCurriculum = await Curriculums.findOneAsync({ curriculumBankSourceId: activityId, curriculumThemeId: theme._id, scheduledDate: day });
					const bankActivity = await CurriculumBanks.findOneAsync({ _id: activityId });

					const newData = {
						headline: bankActivity.sourceData.headline,
						type: bankActivity.sourceData.type,
						message: bankActivity.sourceData.message || bankActivity.sourceData.notes,
						materials: bankActivity.sourceData.materials,
						homework: bankActivity.sourceData.homework,
						scheduledDate: day,
						selectedGroups: selectedGroups,
						selectedStandards: bankActivity.sourceData.selectedStandards,
						selectedTypes: bankActivity.sourceData.selectedTypes,
						selectedAgeGroup: bankActivity.sourceData.selectedAgeGroup,
						curriculumThemeId: theme._id,
						curriculumBankSourceId: activityId,
						internalNotes: bankActivity.sourceData.internalNotes,
						internalLink: bankActivity.sourceData.internalLink,
						orgId: theme.orgId,
						mediaFiles: bankActivity.sourceData.mediaFiles,
					};
					if (existingCurriculum) {
						await Curriculums.updateAsync({ _id: existingCurriculum._id }, { $set: newData });
					} else {
						newData.createdBy = user._id;
						newData.createdAt = new moment().valueOf();
						await Curriculums.insertAsync(newData);
					}
				}

				// Remove any existing curriculums that are no longer in the source theme for that day
				await Curriculums.removeAsync({ curriculumBankSourceId: { $in: currentDailyActivityIds }, curriculumThemeId: theme._id, scheduledDate: day });
			}
		};
	},
	"mobileCalendarAgenda": async function (options) {
		this.unblock();
		const user = await Meteor.userAsync();
		const currentPerson = await user?.fetchPerson?.();
		if (!currentPerson) throw new Meteor.Error(403, "Access denied");

		const org = await Orgs.current();

		const rangeStartDateMoment = (options && new moment.tz(options.currentDate, org.getTimezone())) || new moment.tz(org.getTimezone()),
			rangeStartDate = rangeStartDateMoment.startOf("day"),
			rangeStartDateLabel = rangeStartDate.format("MM/DD/YYYY"),
			rangeStartDateValue = rangeStartDate.valueOf(),
			rangeEndDate = rangeStartDate.clone().add(1, "day").startOf("day"),
			rangeEndDateLabel = rangeEndDate.format("MM/DD/YYYY"),
			rangeEndDateValue = rangeEndDate.valueOf();

		let connectedGroups = [];

		//START ANNOUNCEMENTS
		let announcementQuery = {
			"$and": [
				{ orgId: currentPerson.orgId },
				{
					"$or": [
						{ scheduledEndDate: { "$gte": rangeStartDateValue }, scheduledDate: { "$lt": rangeEndDateValue } },
						{ scheduledEndDate: { $exists: false }, scheduledDate: { "$gte": rangeStartDateValue, "$lte": rangeEndDateValue } }
					]
				}
			]
		}, orQuery;

		if (_.contains(["admin", "staff"], currentPerson.type)) {
			orQuery = {
				"$or": [
					{ selectedRoles: currentPerson.type }
				]
			};
			if (currentPerson.checkInGroupId) {
				orQuery["$or"].push({ selectedGroups: currentPerson.checkInGroupId });
			} else if (currentPerson.defaultGroupId) {
				orQuery["$or"].push({ selectedGroups: currentPerson.defaultGroupId });
			}
		}

		if (currentPerson.type == "family") {
			const relationships = await Relationships.find({ orgId: currentPerson.orgId, personId: currentPerson._id, relationshipType: "family" }).fetchAsync();

			for (const r of relationships) {
				const targetPerson = await People.findOneAsync({ orgId: currentPerson.orgId, _id: r.targetId });
				if (targetPerson && targetPerson.defaultGroupId) {
					connectedGroups.push(targetPerson.defaultGroupId);
				}
			}
			orQuery = {
				"$or": [
					{ selectedGroups: { $in: connectedGroups } },
					{ selectedGroups: { $in: [[], null] }, selectedRoles: 'family' }
				]
			};
		}
		if (orQuery) {
			orQuery["$or"].push({ $and: [{ selectedGroups: { $in: [[], null] } }, { selectedRoles: { $in: [[], null] } }] });
			announcementQuery["$and"].push(orQuery);
		}

		const announcements = await Announcements.find(announcementQuery).fetchAsync();
		//END ANNOUNCEMENTS

		//START CURRICULUMS
		const currEndDateValue = rangeStartDate.clone().endOf('day').valueOf();
		let curriculumQuery = { orgId: currentPerson.orgId, scheduledDate: { $gte: rangeStartDateValue, $lte: currEndDateValue } };
		if (currentPerson.type == "family") {
			curriculumQuery["$or"] = [{ selectedGroups: [] },
			{ selectedGroups: { $in: connectedGroups } }
			];
		}

		if (currentPerson.type == "staff" && (currentPerson.checkInGroupId || currentPerson.defaultGroupId)) {
			curriculumQuery["$or"] = [{ selectedGroups: [] },
			{ selectedGroups: { $in: [currentPerson.checkInGroupId ? currentPerson.checkInGroupId : currentPerson.defaultGroupId] } }
			];
		}

		const curriculum = await Curriculums.find(curriculumQuery).fetchAsync();
		//END CURRICULUMS

		//START Curriculum Themes
		let ctQuery = { orgId: currentPerson.orgId };
		ctQuery["selectedDays"] = { "$elemMatch": { "$gte": rangeStartDateValue, "$lte": rangeEndDateValue } };
		if (currentPerson.type == "family") {
			ctQuery["$or"] = [{ selectedGroups: [] },
			{ selectedGroups: { $in: connectedGroups } }
			];
		}

		if (currentPerson.type == "staff" && (currentPerson.checkInGroupId || currentPerson.defaultGroupId)) {
			ctQuery["$or"] = [{ selectedGroups: [] },
			{ selectedGroups: { $in: [currentPerson.checkInGroupId ? currentPerson.checkInGroupId : currentPerson.defaultGroupId] } }
			];
		}

		const curriculumThemes = await CurriculumThemes.find(ctQuery).fetchAsync();
		//END Curriculum Themes

		//START Food
		let groupsQuery = null;
		if (currentPerson.type == "family") {
			groupsQuery = [{ selectedGroups: [] },
			{ selectedGroups: { $in: connectedGroups } }
			];
		}

		if (currentPerson && (currentPerson.type == "staff" || currentPerson.type == "admin") && (currentPerson.checkInGroupId || currentPerson.defaultGroupId)) {
			groupsQuery = [{ selectedGroups: [] },
			{ selectedGroups: { $in: [currentPerson.checkInGroupId ? currentPerson.checkInGroupId : currentPerson.defaultGroupId] } }
			];
		}

		const foodQuery = { orgId: currentPerson.orgId };
		if (groupsQuery) foodQuery["$or"] = groupsQuery
		let food = await Foods.findWithRecurrence({ startDateValue: rangeStartDateValue, endDateValue: rangeEndDateValue, query: foodQuery }) || [];
		food = _.map(food, (f) => {
			f.pd = f.processedDescription();
			return f;
		})
		//END Food
		return { announcements, curriculum, curriculumThemes, food, connectedGroups };
	},
	"mediaGallery": async function ({ startDate, endDate, mediaType }) {
		const user = await Meteor.userAsync();
		const userPerson = await user?.fetchPerson?.();
		if (!userPerson || userPerson?.type != "admin") throw new Meteor.Error(403, "Access denied");

		const org = await Orgs.current();
		const allowedPeopleQuery = { orgId: userPerson.orgId, type: "person", inActive: { $ne: true } };
		if (mediaType == "internal") {
			allowedPeopleQuery["$or"] = [
				{ "profileData.mediaRequirements.mediaRelease": "Internal" },
				{ "profileData.mediaRequirements.mediaRelease": "Internal and External" },
				{ "mediaRequirements.mediaRelease": "Internal" },
				{ "mediaRequirements.mediaRelease": "Internal and External" }
			];
		} else if (mediaType == "external") {
			allowedPeopleQuery["$or"] = [
				{ "profileData.mediaRequirements.mediaRelease": "Internal and External" },
				{ "mediaRequirements.mediaRelease": "Internal and External" }
			];
		}

		const allowedPeople = (await People.find(allowedPeopleQuery, { fields: { _id: 1 } }).fetchAsync()).map(p => p._id);
		const remainingPeople = (await People.find({ orgId: userPerson.orgId, type: "person", _id: { $nin: allowedPeople } }, { fields: { _id: 1 } }).fetchAsync()).map(p => p._id);


		const startDateNum = new moment.tz(startDate, "MM/DD/YYYY", org.getTimezone()).startOf('day').valueOf();
		const endDateNum = new moment.tz(endDate, "MM/DD/YYYY", org.getTimezone()).endOf('day').valueOf();
		const momentsQuery = {
			orgId: userPerson.orgId,
			mediaFiles: { $exists: true, $ne: [] },
			sortStamp: { $gte: startDateNum, $lte: endDateNum },
			$and: [{ taggedPeople: { $in: allowedPeople } }, { taggedPeople: { $nin: remainingPeople } }],
		}

		let output = [];
		const allMoments = await Moments.find(momentsQuery, { sort: { sortStamp: -1 }, fields: { mediaFiles: 1 } }).fetchAsync();
		allMoments.forEach(m => {
			output = output.concat(m.attachedMedia());
		});

		return { results: output };
	},
	"saveMpSurvey": async function (data) {
		const user = await Meteor.userAsync();
		const userPerson = await user?.fetchPerson?.();
		if (!userPerson) throw new Meteor.Error(403, "Access denied");
		const org = await userPerson.findOrg();
		const checkInGroupId = userPerson.checkInGroupId || null;

		const now = new Date();
		const record = {
			orgName: org.name,
			personName: `${userPerson.firstName} ${userPerson.lastName}`,
			personId: userPerson._id,
			personType: userPerson.type,
			checkInGroupId,
			happy: data.happy,
			soso: data.soso,
			sad: data.sad,
			happyText: data.happyText,
			sadText: data.sadText,
			happyUpNext: data.happyUpNext.active,
			happyOutlook: data.happyOutlook.active,
			happyEdu: data.happyEdu.active,
			happyAnnouncements: data.happyAnnouncements.active,
			sadUpNext: data.sadUpNext.active,
			sadOutlook: data.sadOutlook.active,
			sadEdu: data.sadEdu.active,
			sadAnnouncements: data.sadAnnouncements.active,
			deviceOem: data.deviceInfo.oem,
			deviceType: data.deviceInfo.type,
			deviceId: data.deviceInfo.id,
			deviceOsVersion: data.deviceInfo.osVersion,
			deviceUniqueId: data.deviceInfo.uniqueId,
			createdAt: now.valueOf(),
			friendlyDate: now,
		};

		await MPSurveysEMA.insertAsync(record)

	},
	'getReservationsForBillingReview': async function (options) {
		/*
			All kids with schedule start dates within the next 30 days (with the ability to look further similar to the Recent Chargebacks)
			All kids with schedule end dates within the next 30 days (with the ability to look further similar to the Recent Chargebacks)
		*/

		await processPermissions({
			assertions: [{ context: "reservations", action: "edit" }],
			evaluator: (person) => person.type == "admin",
			throwError: true
		});

		const user = await Meteor.userAsync();
		const currentPerson = await user?.fetchPerson?.();
		if (!currentPerson) {
			throw new Meteor.Error(403, "Access denied");
		}

		const allFoundOrgs = await Meteor.callAsync("getSwitchableOrgs", { failSilently: true });
		let allOrgs = [];
		if (!allFoundOrgs) {
			allOrgs = [currentPerson.orgId];
		} else {
			allOrgs = allFoundOrgs.map(o => o._id);
			allOrgs.push(currentPerson.orgId);
		}

		const allOrgsData = await Orgs.find({ _id: { "$in": allOrgs } }, { "name": 1 }).fetchAsync();

		const currentOrg = await Orgs.current();
		const timezone = currentOrg.getTimezone();
		const startDateValue = (options?.startDate) ? new moment.tz(options.startDate, "MM/DD/YYYY", timezone).startOf('day').valueOf() : new moment.tz(timezone).startOf('day').valueOf();
		const endDateValue = (options?.endDate) ? new moment.tz(options.endDate, "MM/DD/YYYY", timezone).endOf('day').valueOf() : new moment.tz(timezone).add(30, 'days').endOf('day').valueOf();


		const startingQuery = {
			orgId: { $in: allOrgs },
			scheduledDate: { $gte: startDateValue, $lte: endDateValue },
			recurringFrequency: { $exists: true },
			needsBillingReview: { $ne: false },
		};

		const endingQuery = {
			orgId: { $in: allOrgs },
			recurringFrequency: { $exists: true },
			scheduledEndDate: { $gte: startDateValue, $lte: endDateValue },
			needsBillingReview: { $ne: false },
		};

		// The query for the reservations that are currently running, outside of the time range, and there is no billing plan on the person
		const peopleIdsMissingTuitionPlanData = await People.find({
			type: 'person',
			orgId: { $in: allOrgs },
			inActive: { $ne: true },
			$or: [
				{ 'billing': { $exists: false } },
				{ 'billing.enrolledPlans': { $exists: false } },
				{ 'billing.enrolledPlans': [] }
			]
		}).fetchAsync();
		const peopleIdsMissingTuitionPlan = peopleIdsMissingTuitionPlanData.map(p => p._id);
		const currentQuery = {
			orgId: { $in: allOrgs },
			recurringFrequency: { $exists: true },
			scheduledDate: { $lte: startDateValue },
			needsBillingReview: { $ne: false },
			recurrenceId: { $exists: false },
			$or: [
				{ scheduledEndDate: null },
				{ scheduledEndDate: { $gte: endDateValue } }
			],
			selectedPerson: { $in: peopleIdsMissingTuitionPlan }
		};

		// The query for reservations that are currently running, and the person was billed last outside of allowed invoice interval
		const missedInvoiceInterval = currentOrg && _.deep(currentOrg.billing, 'scheduling.missedInvoiceInterval');
		let missedIntervalQuery = null;
		if (missedInvoiceInterval) {
			// The child needs to be created before the interval threshold date value and have an invoice before the same value
			const intervalThreshold = new moment.tz(timezone).subtract(missedInvoiceInterval, 'days').valueOf();
			const peopleIdsPastInvoiceIntervalData = await People.find({
				type: 'person',
				orgId: { $in: allOrgs },
				inActive: { $ne: true },
				createdAt: { $lte: intervalThreshold },
				$or: [
					{ 'billing': { $exists: false } },
					{ 'billing.lastInvoiced': { $exists: false } },
					{ 'billing.lastInvoiced': { $lte: intervalThreshold } }
				]
			}).fetchAsync();
			const peopleIdsPastInvoiceInterval = peopleIdsPastInvoiceIntervalData.map(p => p._id);
			missedIntervalQuery = {
				orgId: { $in: allOrgs },
				recurringFrequency: { $exists: true },
				scheduledDate: { $lte: intervalThreshold },
				needsBillingReview: { $ne: false },
				recurrenceId: { $exists: false },
				$or: [
					{ scheduledEndDate: null },
					{ scheduledEndDate: { $gte: startDateValue } }
				],
				selectedPerson: { $in: peopleIdsPastInvoiceInterval }
			};
		}



		const peopleLookup = {
			from: "people",
			localField: "selectedPerson",
			foreignField: "_id",
			as: "person"
		};

		const currentOrgPrefix = currentOrg.profileDataPrefix(),
			birthdayProfileField = (currentOrgPrefix ? currentOrgPrefix + "." : "") + "birthday";

		let startingReservations = [];
		let endingReservations = [];
		if (options.limit) {
			startingReservations = await Reservations.rawCollection().aggregate([
				{ $match: startingQuery },
				{ $limit: options.limit },
				{ $lookup: peopleLookup },
			]).toArray();
			endingReservations = await Reservations.rawCollection().aggregate([
				{ $match: endingQuery },
				{ $limit: options.limit },
				{ $lookup: peopleLookup },
			]).toArray();
		} else {
			startingReservations = await Reservations.rawCollection().aggregate([
				{ $match: startingQuery },
				{ $lookup: peopleLookup },
			]).toArray();
			endingReservations = await Reservations.rawCollection().aggregate([
				{ $match: endingQuery },
				{ $lookup: peopleLookup },
			]).toArray();
		}

		let currentReservations = [],
			missedIntervalReservations = [];
		if (options.fromWidget) {
			if (options.limit) {
				currentReservations = await Reservations.rawCollection().aggregate([
					{ $match: currentQuery },
					{ $limit: options.limit },
					{ $lookup: peopleLookup },
				]).toArray();
			} else {
				currentReservations = await Reservations.rawCollection().aggregate([
					{ $match: currentQuery },
					{ $lookup: peopleLookup },
				]).toArray();
			}

			if (missedInvoiceInterval) {
				if (options.limit) {
					missedIntervalReservations = await Reservations.rawCollection().aggregate([
						{ $match: missedIntervalQuery },
						{ $limit: options.limit },
						{ $lookup: peopleLookup },
					]).toArray();
				} else {
					missedIntervalReservations = await Reservations.rawCollection().aggregate([
						{ $match: missedIntervalQuery },
						{ $lookup: peopleLookup },
					]).toArray();
				}
			}
		}

		const widgetArr1 = startingReservations?.slice?.(0, 5) ?? [];
		const widgetArr2 = endingReservations?.slice?.(0, 5) ?? [];
		const widgetArr4 = currentReservations?.slice?.(0, 5) ?? [];
		const widgetArr5 = missedIntervalReservations?.slice?.(0, 5) ?? [];

		let combinedArr = widgetArr1.concat(widgetArr2).concat(widgetArr4).concat(widgetArr5);
		if (options.fromWidget) {
			// Filter out reservations for the same person; they don't need to appear multiple times
			const mappedPersonIds = new Map();
			combinedArr = combinedArr.filter(reservation => {
				if (mappedPersonIds.has(reservation.selectedPerson)) {
					return false;
				}
				mappedPersonIds.set(reservation.selectedPerson, true);
				return true;
			});
		}
		const widgetDataArr = combinedArr?.slice?.(0, 5) ?? [];

		const orgNameMap = (allFoundOrgs) ? allFoundOrgs.map(o => { return { _id: o._id, name: o.name } }) : { _id: currentOrg._id, name: currentOrg.name };

		let orgScheduleMap = currentOrg?.valueOverrides?.scheduleTypes ?? [];
		if (allFoundOrgs) {
			for (const o of allFoundOrgs) {
				orgScheduleMap = orgScheduleMap.concat(o?.valueOverrides?.scheduleTypes ?? []);
			}
		}

		const allOrgGroups = await Groups.find({ orgId: { $in: allOrgs } }, { fields: { name: 1 } }).fetchAsync();
		let allReservations = _.union(startingReservations, endingReservations, currentReservations, missedIntervalReservations);

		if (options.showResolvedSchedules) {
			const resolvedReservationsQuery = {
				orgId: { $in: allOrgs },
				recurringFrequency: { $exists: true },
				$or: [
					{ scheduledDate: { $gte: startDateValue, $lte: endDateValue } },
					{ scheduledEndDate: { $gte: startDateValue, $lte: endDateValue } },
				],
				needsBillingReview: false,
			};
			allReservations = await Reservations.rawCollection().aggregate([
				{ $match: resolvedReservationsQuery },
				{ $lookup: peopleLookup },
			]).toArray();
		}
		if (!_.isEmpty(options.eventTypes) && !_.contains(options.eventTypes, "Schedule Changes")) allReservations = [];

		const prefixedWithdrawDateFieldName = (currentOrgPrefix ? `${currentOrgPrefix}.` : "") + "withdrawDate",
			additionalPeopleQuery = {
				orgId: { $in: allOrgs },
				_id: { "$nin": allReservations.map(r => r.selectedPerson) },
				inActive: { "$ne": true },
				"$or": []
			};
		if (_.isEmpty(options.eventTypes) || _.contains(options.eventTypes, "Withdraws"))
			additionalPeopleQuery["$or"].push({ [`${prefixedWithdrawDateFieldName}`]: { "$gte": startDateValue, "$lte": endDateValue } });

		if (_.isEmpty(options.eventTypes) || _.contains(options.eventTypes, "Birthdays")) {
			for (let yearNum = 0; yearNum <= 10; yearNum++) {
				const birthdayStartDate = new moment(startDateValue).add(-1 * yearNum, "years").valueOf(),
					birthdayEndDate = new moment(endDateValue).add(-1 * yearNum, "years").valueOf(),
					currentYearQuery = { [`${birthdayProfileField}`]: { "$gte": birthdayStartDate, "$lte": birthdayEndDate } };
				additionalPeopleQuery["$or"].push(currentYearQuery);
			}
		}

		if (!options.showResolvedSchedules)
			additionalPeopleQuery["needsBillingReview"] = { "$ne": false };

		const additionalPeople = additionalPeopleQuery["$or"].length > 0 ? (await People.find(additionalPeopleQuery).fetchAsync()) : [];

		allReservations = allReservations.concat(additionalPeople.map(ap => ({
			orgId: ap.orgId,
			person: [ap],
		})));
		allReservations = _.chain(allReservations)
			.map(r => {
				const resPerson = r.person && r.person[0],
					enrolledPlans = _.deep(resPerson, "billing.enrolledPlans");
				let voucherEndDates = [];
				_.each(enrolledPlans, ep => {
					_.each(ep.allocations, allocation => {
						if (allocation.payerEndDate) {
							voucherEndDates.push(new moment(allocation.payerEndDate).format("M/D/YYYY"));
						}
					});
				});
				r.voucherEndDates = voucherEndDates.join(",");
				r._reportWithdrawalDate = resPerson && _.deep(resPerson, prefixedWithdrawDateFieldName)
				return r;
			})
			.sortBy(r => r.scheduledEndDate ? r.scheduledEndDate : Number.MAX_VALUE)
			.value();

		allReservations = _.sortBy(allReservations, (r) => {
			const useDate = (r.scheduledEndDate) ? r.scheduledEndDate : Number.MAX_VALUE;
			if (options.sortBy == "org") {
				const org = allOrgsData.find(o => o._id == r.orgId);
				return `${org.name}|${useDate}`;
			} else
				return `|${useDate}`;
		});

		return {
			allOrgGroups,
			orgScheduleMap,
			orgNameMap,
			reservations: allReservations,
			widgetDataArr,
		}
	},

	'getNewEnrollments': async function (options) {
		await processPermissions({
			assertions: [{ context: "reservations", action: "edit" }],
			evaluator: (person) => person.type == "admin",
			throwError: true
		});
		const user = await Meteor.userAsync();
		const currentPerson = await user?.fetchPerson?.();
		if (!currentPerson) {
			throw new Meteor.Error(403, "Access denied");
		}
		const allFoundOrgs = await Meteor.callAsync("getSwitchableOrgs", { failSilently: true });
		let allOrgs = [];
		if (!allFoundOrgs) {
			allOrgs = [currentPerson.orgId];
		} else if (!_.isEmpty(options.orgIds || [])) {
			const allowedOrgs = allFoundOrgs.map(o => o._id);
			allowedOrgs.push(currentPerson.orgId);
			allOrgs = _.intersection(options.orgIds, allowedOrgs);
		} else {
			allOrgs = allFoundOrgs.map(o => o._id);
			allOrgs.push(currentPerson.orgId);
		}
		const currentOrg = await Orgs.current();
		const timezone = currentOrg.getTimezone();
		const startDateValue = (options?.startDate) ? new moment.tz(options.startDate, "MM/DD/YYYY", timezone).startOf('day').valueOf() : new moment.tz(timezone).subtract(30, 'days').startOf('day').valueOf();
		const endDateValue = (options?.endDate) ? new moment.tz(options.endDate, "MM/DD/YYYY", timezone).endOf('day').valueOf() : new moment.tz(timezone).endOf('day').valueOf();
		const peopleQuery = { orgId: { "$in": allOrgs }, "$or": [{ designations: "Wait List", createdAt: { "$gte": startDateValue, "$lt": endDateValue } }] };
		if (options.showResolvedRegistrations)
			peopleQuery["$or"].push({ waitlistRemovedAt: { "$gte": startDateValue, "$lt": endDateValue } });
		if (options.showUnpaidOnly)
			peopleQuery["waitlistAddedDate"] = { "$eq": null };
		let peopleSubQuery = { fields: { firstName: 1, lastName: 1, orgId: 1, createdAt: 1, waitlistAddedDate: 1, birthday: 1 }, sort: { createdAt: -1 } };
		if (options.limit) {
			peopleSubQuery = { ...peopleSubQuery, limit: options.limit };
		}
		let enrolledPeople = await People.find(peopleQuery, peopleSubQuery).fetchAsync();
		const query = {
			orgId: { "$in": allOrgs },
			actionedDate: { $exists: false },
			isApproved: { $eq: false },
			isDenied: { $eq: false }
		};
		if (options.showOnHoldRegistrations && !options.showResolvedRegistrations) {
			let onHoldRegistrations = [];
			const onHoldQuery = { ...query, "data.districtEmail": { $exists: false } };
			if (options.limit) {
				onHoldRegistrations = await OnHoldRegistrations.find(onHoldQuery, { sort: { addedStamp: -1 }, limit: options.limit }).fetchAsync();
			} else {
				onHoldRegistrations = await OnHoldRegistrations.find(onHoldQuery, { sort: { addedStamp: -1 } }).fetchAsync();
			}
			for (const reg of onHoldRegistrations) {
				enrolledPeople.push(reg);
			}
		}
		if (options.showDistrictEmailOnly) {
			const onHoldQuery = { ...query, "data.districtEmail": { $exists: true } };
			if (options.limit) {
				enrolledPeople = await OnHoldRegistrations.find(onHoldQuery, { sort: { addedStamp: -1 }, limit: options.limit }).fetchAsync();
			} else {
				enrolledPeople = await OnHoldRegistrations.find(onHoldQuery, { sort: { addedStamp: -1 } }).fetchAsync();
			}
		}
		const orgCache = {};
		for (const ep of enrolledPeople) {
			if (!orgCache[ep.orgId]) {
				const org = await Orgs.findOneAsync(ep.orgId);
				orgCache[ep.orgId] = org.name;
			}
			ep.orgName = orgCache[ep.orgId];
		};
		return { enrolledPeople }
	},

	async "resolveEnrollment"(options) {
		this.unblock();
		await processPermissions({
			assertions: [{ context: "reservations", action: "edit" }],
			evaluator: (person) => person.type == "admin",
			throwError: true
		});

		const user = await Meteor.userAsync();
		const currentPerson = await user?.fetchPerson?.();
		if (!currentPerson) {
			throw new Meteor.Error(403, "Access denied");
		}

		const allFoundOrgs = await Meteor.callAsync("getSwitchableOrgs", { failSilently: true });
		let allOrgs = [];
		if (!allFoundOrgs) {
			allOrgs = [currentPerson.orgId];
		} else {
			allOrgs = allFoundOrgs.map(o => o._id);
			allOrgs.push(currentPerson.orgId);
		}

		const updatePerson = await People.findOneAsync({ orgId: { "$in": allOrgs }, _id: options.personId });
		if (!updatePerson)
			throw new Meteor.Error(403, "You do not have permissions to update this person's registration status");

		await People.updateAsync({ _id: options.personId }, {
			"$pull": { "designations": "Wait List" },
			"$set": {
				"waitlistRemovedAt": new Date().valueOf(),
				"waitlistRemovedBy": currentPerson._id
			}
		});
	},

	"sendTwoFactorCode": async function () {
		const user = await Meteor.userAsync();
		await processTwoFactorEmail({ user });
		return true;
	},
	"superadminAddLegalEntity": async function (options) {
		this.unblock();
		const user = await Meteor.userAsync();
		const currentPerson = await user?.fetchPerson?.();
		if (!currentPerson ||
			currentPerson.type != "admin" ||
			!currentPerson.superAdmin)
			throw new Meteor.Error(403, "Access denied");

		const newEntity = {
			"business_name": options["business-name"],
			"business_tax_id": AES.encrypt(options["business-tax-id"], Meteor.settings.mpEntityKey).toString(),
			"address": options["address"],
			"city": options["city"],
			"state": options["state"],
			"zipcode": options["zipcode"],
		};

		if (!_.isEmpty(options["start-date"]))
			newEntity["startDate"] = options["start-date"];
		if (!_.isEmpty(options["end-date"]))
			newEntity["endDate"] = options["end-date"];

		await Orgs.updateAsync({ _id: options.orgId }, { "$push": { "billing.legalEntityHistory": newEntity } });
	},
	"superadminRemoveLegalEntity": async function (options) {
		const user = await Meteor.userAsync();
		const currentPerson = await user?.fetchPerson?.();
		if (!currentPerson ||
			currentPerson.type != "admin" ||
			!currentPerson.superAdmin)
			throw new Meteor.Error(403, "Access denied");

		const existing = _.deep(await Orgs.findOneAsync({ _id: options.orgId }), "billing.legalEntityHistory");
		existing.splice(options.index, 1);
		await Orgs.updateAsync({ _id: options.orgId }, { "$set": { "billing.legalEntityHistory": existing } });
	},
	"getQrCode": async function () {
		this.unblock();
		const currentUser = await Meteor.userAsync();
		const currentPerson = await currentUser?.fetchPerson(),
			currentOrg = await Orgs.current();

		if (!(currentPerson && (
			currentPerson.type == "admin" || currentPerson.type == "staff")))
			throw new Meteor.Error(403, "Access denied");

		const qrTokenString = tokenString();

		// We now return { authString, qrCodeId, qrCode} as a result
		// of insertWithTokenString.
		// We return these up-stream so that expiration can be checked client-side
		// and a new QR code can be retrieved if necessary.

		const { authString, qrCodeId, qrCode } = await QrCodes.insertWithTokenString({
			orgId: currentOrg._id,
			tokenString: qrTokenString,
			createdByPersonId: currentPerson._id
		})

		return { authString, qrCodeId, qrCode };

	},
	'phoneSearch': async function (phoneString) {
		this.unblock();
		const org = await Orgs.current();
		const phoneNumber = phoneString.replace(/[^0-9]/g, '');
		if (phoneNumber.length !== 10) {
			return [];
		}
		console.log({ phoneNumber });
		const possibles = await People.find({ orgId: org._id, type: { $in: ['family', 'staff', 'admin'] } }).fetchAsync();
		const matches = [];
		for (const possible of possibles) {
			const pPhone = (possible.profileData?.phonePrimary || possible.phonePrimary || '').replace(/[^0-9]/g, '');
			console.log({ pPhone });
			if (pPhone === phoneNumber) {
				matches.push(possible);
			}
		}
		return matches;
	},
	'hasAdpEnabled': async function () {
		this.unblock();
		const currentOrg = await Orgs.current();
		const topOrg = await currentOrg.findTopOrg();
		return topOrg.ftpCredentials && topOrg.ftpCredentials.adp && topOrg.ftpCredentials.adp.url;
	},
	'pinCodeCheckin': async function (checkInData) {
		this.unblock();
		console.log("Pincode Start: includePeople: ", checkInData.includePeople?.join(","), Date.now())
		// When checking in with PIN, checkInData looks like this:
		/*
		{
			"pinCode": "NUMBER",
			"wasPinCodeCheckin": true,
			"requestTime": 1624735200000,
			"peopleConstraint": false
		}
		*/
		// When checking in with QR, checkInData looks like this:
		/*
		{
			...,
			"qrdata": "hashed-qr-code-data"
		}
		*/

		// const delay = ms => new Promise(res => setTimeout(res, ms));
		// await delay(125000);
		// The above is code that can be used to simulate a connection delay
		const currentUser = await Meteor.userAsync();
		const org = await Orgs.current(), currentPerson = await currentUser.fetchPerson();

		if (!Cache.has(`${org._id}-${CACHE_KEYS.ORGS_CUSTOMIZATION_PEOPLE_QR_CODE_CHECK_IN_ENABLED}`)) {
			const getQrCodeCheckinEnabled = org.hasCustomization("people/qrCodeCheckin/enabled")
			Cache.set(`${org._id}-${CACHE_KEYS.ORGS_CUSTOMIZATION_PEOPLE_QR_CODE_CHECK_IN_ENABLED}`, getQrCodeCheckinEnabled);
		}
		const orgHasCustomizationPeopleQrCodeCheckinEnabled = Cache.get(`${org._id}-${CACHE_KEYS.ORGS_CUSTOMIZATION_PEOPLE_QR_CODE_CHECK_IN_ENABLED}`);

		if (!Cache.has(`${org._id}-${CACHE_KEYS.ORGS_CUSTOMIZATION_MOMENTS_CHECKOUT_SHOW_STAFF_CERTIFICATION_MESSAGE}`)) {
			const getShowStaffCertificationMessage = org.hasCustomization("moments/checkout/showStaffCertificationMessage")
			Cache.set(`${org._id}-${CACHE_KEYS.ORGS_CUSTOMIZATION_MOMENTS_CHECKOUT_SHOW_STAFF_CERTIFICATION_MESSAGE}`, getShowStaffCertificationMessage);
		}
		const orgHasCustomizationMomentsCheckoutShowStaffCertificationMessage = Cache.get(`${org._id}-${CACHE_KEYS.ORGS_CUSTOMIZATION_MOMENTS_CHECKOUT_SHOW_STAFF_CERTIFICATION_MESSAGE}`);

		if (!Cache.has(`${org._id}-${CACHE_KEYS.ORGS_CUSTOMIZATION_MOMENTS_CHECKIN_REQUIRE_COMPANION_SELECTION}`)) {
			const getRequireCompanionSelection = org.hasCustomization("moments/checkin/requireCompanionSelection")
			Cache.set(`${org._id}-${CACHE_KEYS.ORGS_CUSTOMIZATION_MOMENTS_CHECKIN_REQUIRE_COMPANION_SELECTION}`, getRequireCompanionSelection);
		}
		const orgHasCustomizationMomentsCheckinRequireCompanionSelection = Cache.get(`${org._id}-${CACHE_KEYS.ORGS_CUSTOMIZATION_MOMENTS_CHECKIN_REQUIRE_COMPANION_SELECTION}`);

		if (!Cache.has(`${org._id}-${CACHE_KEYS.ORGS_CUSTOMIZATION_MOMENTS_CHECKIN_CHECKINORDER}`)) {
			const getCheckinorder = org.hasCustomization("moments/checkin/checkinorder")
			Cache.set(`${org._id}-${CACHE_KEYS.ORGS_CUSTOMIZATION_MOMENTS_CHECKIN_CHECKINORDER}`, getCheckinorder);
		}
		const orgHasCustomizationMomentsCheckinCheckinorder = Cache.get(`${org._id}-${CACHE_KEYS.ORGS_CUSTOMIZATION_MOMENTS_CHECKIN_CHECKINORDER}`);

		let currentPersonPinCode = currentPerson.pinCode;
		const usedPin = checkInData.wasPinCodeCheckin;
		if (currentPerson?.pinCodeSupplemental?.length > 0) {
			currentPersonPinCode = currentPersonPinCode + currentPerson.pinCodeSupplemental;
		}

		if (!currentPerson ||
			!(currentPerson.type === "admin" || currentPerson.type === "staff" ||
				(orgHasCustomizationPeopleQrCodeCheckinEnabled && currentPerson.type === "family" && currentPersonPinCode === checkInData.pinCode)))
			throw new Meteor.Error(403, "Access denied");

		const now = moment.tz(org.getTimezone()).valueOf();
		if (checkInData.requestTime && !Number.isNaN(checkInData.requestTime) && (now - checkInData.requestTime > 120000)) {
			throw new Meteor.Error('400', 'Connection Issue: It looks like your device is having a hard time connecting. Please try again.');
		}

		if (!checkInData.pinCode || checkInData.pinCode.trim() === "")
			throw new Meteor.Error(500, "PIN code cannot be blank");

		if (orgHasCustomizationPeopleQrCodeCheckinEnabled && checkInData.qrdata) {
			// Previously we were checking here to see if the currentPerson.type === "family".
			// This was basically checking to see if the mobile app was being used, which maybe
			// could have an explicit flag.
			//
			// Instead now we are looking for any existence of a qrdata field, which is only
			// present when a QR code is scanned.  This is a more reliable way to determine
			// if the mobile app is being used.
			//
			const qrdata = checkInData.qrdata;

			const qrdataMatch = await QrCodes.retrieveFromQrData(org._id, qrdata); // Returns the QrCode object

			if (!qrdataMatch) {
				throw new Meteor.Error(400, "Unauthorized. QR code not found.");
			}

			// QrCodes.checkForExpiration verifies that org.hasCustomization("people/checkInCheckOutQrCodesExpire/enabled")
			// is set correctly (there will be no error if expiration is disabled).
			const { error } = await QrCodes.checkForExpiration(org._id, qrdataMatch._id);

			if (error) {
				throw new Meteor.Error(400, error);
			}
		}

		const peopleConstraint = checkInData.peopleConstraint;
		const condition = {
			orgId: currentUser.orgId,
			pinCode: checkInData.pinCode
		};
		if (peopleConstraint && peopleConstraint.length > 0) {
			condition._id = {
				$in: peopleConstraint.map(p => p._id)
			}
		}
		let checkinPerson = await People.findOneAsync(condition);
		const pinLength = org.pinLength();
		if (checkinPerson?.pinCodeSupplemental?.length > 0 || checkInData.pinCode.length > pinLength) {
			const condition = {
				orgId: currentUser.orgId,
				pinCode: checkInData.pinCode.substring(0, pinLength),
				pinCodeSupplemental: checkInData.pinCode.substring(pinLength)
			}
			if (peopleConstraint && peopleConstraint.length > 0) {
				condition._id = {
					$in: peopleConstraint.map(p => p._id)
				}
			}
			checkinPerson = await People.findOneAsync(condition);
		}

		if (!checkinPerson && peopleConstraint && peopleConstraint.length > 0) {
			throw new Meteor.Error(500, 'Sorry, that is not the correct PIN code');
		}
		if (!checkinPerson) throw new Meteor.Error(500, "PIN code not found. Please try again.");

		const activeRelationships = [];

		if (checkinPerson.type === "admin" || checkinPerson.type === "staff")
			activeRelationships.push(checkinPerson);

		if (orgHasCustomizationMomentsCheckoutShowStaffCertificationMessage &&
			(checkinPerson.type === "staff" || checkinPerson.type === "admin") && checkinPerson.checkedIn && !checkInData.affirmed)
			return {
				needsStaffAffirmation: true
			};

		let checkInCount = 0, showInterstitial = false, checkInRemindersCount = 0;
		await PinCodeCheckinService.eachActiveRelationshipsArray(
			await Relationships.find({
				orgId: currentUser["orgId"],
				personId: checkinPerson._id,
				relationshipType: { "$in": ["family", "authorizedPickup"] }
			}).fetchAsync(),
			function (relTarget) {
				if (!relTarget.checkedIn) checkInCount++;
				if (relTarget.checkedIn && relTarget.checkInReminders) checkInRemindersCount++;
				activeRelationships.push(relTarget);
			}
		);

		const filterToIndividualChild = activeRelationships.filter((person, index, self) => { return index === self.findIndex(p => p._id === person._id); });

		const today = new moment(); const startOfDay = today.startOf("day").valueOf(), endOfDay = today.add(1, "days").startOf("day").valueOf();
		const lastCheckinOrOut = _.min(_.pluck(filterToIndividualChild, "checkedInOutTime"));
		const messageMoments = await Moments.find({ orgId: currentUser["orgId"], taggedPeople: { $in: _.pluck(filterToIndividualChild, "_id") }, momentType: "alert", sortStamp: { $gte: lastCheckinOrOut }, alertShowCheckin: true }).fetchAsync();

		if ((org.pinCodeCheckinFields() ||
			messageMoments.length > 0 ||
			await Announcements.findOneAsync({ scheduledDate: { "$gte": startOfDay, "$lt": endOfDay }, selectedGroups: [] }))
			&& (checkInCount > 0 || checkInRemindersCount > 0))
			showInterstitial = true;

		const verifyCompanion = orgHasCustomizationMomentsCheckinRequireCompanionSelection,
			authorizedCompanions = [];
		if (verifyCompanion && filterToIndividualChild.length === 1) {
			await PinCodeCheckinService.eachAuthorizedCompanionsArray(
				await Relationships.find({
					targetId: filterToIndividualChild[0]._id,
					relationshipType: { "$in": ["family", "authorizedPickup"] }
				}).fetchAsync(),
				function (person) {
					authorizedCompanions.push({ name: person.lastName + ", " + person.firstName, _id: person._id });
				}
			);
		}
		if (filterToIndividualChild.length === 0)
			throw new Meteor.Error(500, "No active relationships for that PIN code");
		else if ((filterToIndividualChild.length > 1 || showInterstitial || (verifyCompanion && filterToIndividualChild.length === 1 && authorizedCompanions.length > 1))
			&& !checkInData.includePeople) {
			return {
				checkinCompleted: false,
				promptToInclude: _.map(filterToIndividualChild, function (relTarget) {
					let checkInReminders = [];
					_.each(org.pinCodeCheckinFields(), (checkInField) => {
						if (checkInField.remind && relTarget.checkInReminders && relTarget.checkInReminders[checkInField.dataId]) checkInReminders.push({
							label: checkInField.label,
							value: relTarget.checkInReminders[checkInField.dataId]
						});
					});
					if (org.pinCodeCheckinFields() && relTarget.checkInReminders && relTarget.checkInReminders.comment)
						checkInReminders.push({
							label: "Notes from check-in",
							value: relTarget.checkInReminders.comment
						});
					return {
						_id: relTarget._id,
						name: relTarget.firstName + " " + relTarget.lastName,
						checkedIn: relTarget.checkedIn,
						checkInReminders: checkInReminders,
						familyCheckin: relTarget.currentFamilyCheckin() && _.map(relTarget.currentFamilyCheckin().formFieldData, (ffValue, ffKey) => {
							return {
								label: ffKey,
								value: ffValue
							};
						}),
						messageMoments: _.filter(messageMoments, (m) => { return _.contains(m.taggedPeople, relTarget._id) && m.sortStamp >= relTarget.checkedInOutTime; })
					};
				}),
				companionSelection: verifyCompanion && authorizedCompanions.length > 1 && authorizedCompanions,
				checkInCount: checkInCount
			};
		} else {
			const includePeople = (filterToIndividualChild.length === 1) ? [filterToIndividualChild[0]._id] : checkInData.includePeople;
			let checkoutPersonIds = []
			let fn = (personId) => {
				return new Promise(async (resolve, reject) => {
					const activeTarget = _.find(filterToIndividualChild, function (r) { return r._id === personId; });
					let companionId = checkinPerson._id;
					if (activeTarget) {
						if (verifyCompanion && filterToIndividualChild.length === 1 && authorizedCompanions.length > 1) {
							if (!_.contains(_.pluck(authorizedCompanions, "_id"), checkInData.companionId)) {
								reject(new Meteor.Error(500, "Person is not authorized to pick up or drop off"));
							}
							companionId = checkInData.companionId;
						}
						if (!activeTarget.checkedIn) {
							console.log("Checkin Initiated:", activeTarget._id, Date.now());
							const checkInArgs = {
								personId: activeTarget._id,
								pinCodeFormFields: checkInData.pinCodeFormFields && checkInData.pinCodeFormFields[activeTarget._id],
								checkInDefaultGroup: true,
								checkedInById: companionId,
								pinCode: checkInData.pinCode,
								usedPin: usedPin,
								isKioskMode: checkInData.isKioskMode ?? false
							}
							if (checkInData && checkInData.earlyLateReason && checkInData.earlyLateReason[activeTarget._id]) {
								checkInArgs["earlyLateReason"] = checkInData.earlyLateReason[activeTarget._id];
							}
							Meteor.callAsync("checkIn", checkInArgs).then(async () => {
								console.log("Checkin Returned:", activeTarget._id, Date.now());
								let checkInText = "Checked in " + activeTarget.firstName + " " + activeTarget.lastName;
								if (orgHasCustomizationMomentsCheckinCheckinorder && activeTarget.type === "person") {
									const targetPersonCheckInOrder = await People.findOneAsync({ _id: activeTarget._id }, { fields: { checkInOrder: 1 } });
									checkInText += " <b><u>(Guest #" + targetPersonCheckInOrder.checkInOrder + ")</u></b>";
								}
								resolve(checkInText);
							}).catch((error) => {
								console.log("Checkin Returned:", activeTarget._id, Date.now());
								reject(error);
							});
						} else {
							const targetPerson = await People.findOneAsync(activeTarget._id),
								currentGroup = targetPerson.checkInGroupId && await Groups.findOneAsync(targetPerson.checkInGroupId);
							const checkOutArgs = {
								personId: activeTarget._id,
								checkedOutById: companionId,
								pinCode: checkInData.pinCode,
								usedPin: usedPin,
								multipleCheckoutEmail: true,
								isKioskMode: checkInData.isKioskMode ?? false
							}
							if (checkInData && checkInData.earlyLateReason && checkInData.earlyLateReason[activeTarget._id]) {
								checkOutArgs["earlyLateReason"] = checkInData.earlyLateReason[activeTarget._id];
							}
							Meteor.callAsync("checkOut", checkOutArgs).then(() => {
								checkoutPersonIds.push(personId);
								setTimeout(async () => {
									let checkOutText = "Checked out " + activeTarget.firstName + " " + activeTarget.lastName + (currentGroup ? " from " + currentGroup.name : "");
									if (orgHasCustomizationMomentsCheckinCheckinorder && activeTarget.type === "person") {
										const targetPersonCheckInOrder = await People.findOneAsync({ _id: activeTarget._id }, { fields: { checkInOrder: 1, checkedIn: 1 } });
										checkOutText += " <b><u>(Guest #" + targetPersonCheckInOrder.checkInOrder + ")</u></b>";
									}
									resolve(checkOutText);
								}, 500);
							}).catch((error) => {
								setTimeout(() => {
									reject(error);
								}, 500);
							});
						}
					}
				});
			}
			const actions = includePeople.map(fn);

			return Promise.all(actions)
				.then((ck) => {
					const timezone = org.getTimezone();
					const result = {
						checkinCompleted: true,
						checkinList: ck,
						checkinTimestamp: new moment.tz(timezone).format("h:mm A")
					};
					Meteor.defer(async function () {
						if (new Date().valueOf() > new moment().tz(timezone).startOf('day')) {
							for (let personId of checkoutPersonIds) {
								await processSummaryMail2021(personId, "");
							}
						}
					})
					console.log("Pincode End: includePeople: ", checkInData.includePeople?.join(","), Date.now())
					return result
				})
				.catch((error) => {
					throw new Meteor.Error(error.error || 'Error in pinCodeCheckin', error.reason || error.message || 'There was an error in pinCodeCheckin')
				});
		}
	},
	"getIntercomHash": async function () {
		this.unblock();
		const user = await Meteor.userAsync();
		if (!user) return;
		const secret = Meteor.settings.intercomSecret;
		if (!secret) return;
		const hash = CryptoJS.HmacSHA256(user._id, secret).toString(CryptoJS.enc.Hex)
		/*const hash = createHmac('sha256', secret)
			   .update(user._id)
			   .digest('hex');*/
		console.log("hash", hash);
		return hash;

	},
	async updateTimeCardsLockedDate(options) {
		this.unblock();
		const user = await Meteor.userAsync();
		const currentPerson = await user?.fetchPerson?.();
		if (!currentPerson || currentPerson.type != "admin")
			throw new Meteor.Error(403, "Access denied");

		const enabled = options.enabled;
		const lockedDate = options.lockedDate;
		const currentOrg = await Orgs.current();

		let orgIds = [currentOrg._id];
		let timecardLockOrgs = options.timecardLockOrgs;
		if (timecardLockOrgs && timecardLockOrgs.length) {
			const availableOrgs = await Meteor.callAsync('getSwitchableOrgs');
			// make sure no funny business is coming over from the server
			timecardLockOrgs = OrgsUtil.filterOrgs(timecardLockOrgs, availableOrgs);
			orgIds = timecardLockOrgs;
		}
		await Orgs.updateAsync({ _id: { $in: orgIds } }, { $set: { "customizations.people/timeCardsLocked/enabled": enabled } }, { multi: true });
		if (lockedDate) {
			await Orgs.updateAsync({ _id: { $in: orgIds } }, { $set: { "valueOverrides.timeCardsLockedDate": lockedDate } }, { multi: true });

		} else {
			await Orgs.updateAsync({ _id: { $in: orgIds } }, { $unset: { "valueOverrides.timeCardsLockedDate": 1 } }, { multi: true })
		}
	},
	async "approveTimecards"(options) {
		this.unblock();
		const user = await Meteor.userAsync();
		const currentPerson = await user?.fetchPerson?.();

		if (!["admin", "staff"].includes(currentPerson?.type)) {
			throw new Meteor.Error(403, "Access denied");
		}

		// Previously we were using the currentOrg:
		// const currentOrg = await Orgs.findOneAsync({ _id: currentPerson.orgId });
		// This may be the right approach. For now, we use the current person's orgId (feels incorrect)

		await TimeCards.approveForPerson({
			orgId: currentPerson.orgId,
			personId: currentPerson._id,
			timecardIds: options.timecardIds || [],
			approvedByPersonId: currentPerson._id
		})
	},
	startPayerSession: async ({ payer, startDate, endDate }) => {
		const user = await Meteor.userAsync();
		const currentPerson = await user?.fetchPerson?.();
		if (!["admin", "staff"].includes(currentPerson?.type))
			throw new Meteor.Error(403, "Access denied");
		const currentOrg = await Orgs.current();
		await PayerUtils.clearSession(currentPerson._id, currentOrg._id);
		await PayerUtils.startSession(currentPerson._id, currentOrg._id, payer, startDate, endDate);
	},
	// Used only in client side
	clearPayerSession: async () => {
		const user = await Meteor.userAsync();
		const currentPerson = await user?.fetchPerson?.();
		if (!["admin", "staff"].includes(currentPerson?.type))
			throw new Meteor.Error(403, "Access denied");
		const currentOrg = await Orgs.current();
		await PayerUtils.clearSession(currentPerson._id, currentOrg._id);
	},
	checkConflictingSessions: async ({ payer, startDate, endDate }) => {
		const user = await Meteor.userAsync();
		const currentPerson = await user?.fetchPerson?.();
		if (!["admin", "staff"].includes(currentPerson?.type))
			throw new Meteor.Error(403, "Access denied");
		const currentOrg = await Orgs.current();
		return await PayerUtils.getConflictingSessions(currentPerson._id, currentOrg._id, payer, startDate, endDate);
	},
	saveDraft: async (draftType, draftObject, draftName) => {
		const user = await Meteor.userAsync();
		const currentPerson = await user?.fetchPerson?.();
		if (!["admin", "staff"].includes(currentPerson?.type))
			throw new Meteor.Error(403, "Access denied");
		const currentOrg = await Orgs.current();
		return await DraftUtils.saveDraft(draftType, draftObject, draftName, currentPerson._id, currentOrg._id);
	},
	async retrieveDrafts(draftType) {
		this.unblock();
		const user = await Meteor.userAsync();
		const currentPerson = await user?.fetchPerson?.();
		if (!["admin", "staff"].includes(currentPerson?.type))
			throw new Meteor.Error(403, "Access denied");
		const currentOrg = await Orgs.current();
		return await DraftUtils.getDrafts(draftType, currentPerson._id, currentOrg._id);
	},
	async deleteDraft(draftId) {
		this.unblock();
		await SavedDrafts.removeAsync({ _id: draftId });
	},
	"adjustAssessment": async function (options) {
		this.unblock();
		const user = await Meteor.userAsync();
		const currentPerson = user && await user.fetchPerson();
		if (!currentPerson || !_.contains(["staff", "admin"], currentPerson.type))
			throw new Meteor.Error(403, "Access denied");

		const targetPerson = await People.findOneAsync({ "orgId": user.orgId, "_id": options.personId });
		if (!targetPerson)
			throw new Meteor.Error(500, "Target person not found");

		const selectedStandard = (await Curriculums.getStandards()).find(s => s.standardId == options.targetAdjustment);
		if (!selectedStandard)
			throw new Meteor.Error(500, "Selected standard not found");
		console.log("options", options);
		const level = parseInt(options['adjusted-measurement']);
		if (isNaN(level))
			throw new Meteor.Error(500, "You must select a valid level for the adjustment.");

		const updateObj = {};
		updateObj[`assessmentOverrides.${options.targetAdjustment}`] = {
			level,
			createdByPersonId: currentPerson._id,
			createdAt: new Date().valueOf()
		}

		await People.updateAsync({ _id: targetPerson._id }, { "$set": updateObj });
	},
	async checkForIntegrationSourceIds(options) {
		this.unblock();
		const org = await Orgs.current();
		if (!org) {
			return null;
		}

		const person = await People.findOneAsync({ orgId: org._id, _id: options.personId });
		if (!person || person.type !== 'person') {
			return null;
		}
		return await PeopleUtils.getPersonHubspotAndAirslateIds(person);
	},
	async getBenchmarkStats(orgId) {
		this.unblock();
		return await BenchmarkService.getStats(orgId);
	},
	sendManageUserUpdates(manageUser, isRemoving = false, isChangingOrg = false, password = null) {
		this.unblock();
		if (manageUser) {
			const staffApiService = new StaffApiService();
			staffApiService.updateEnrollStaff(manageUser, isRemoving, isChangingOrg, password);
		}
	},
	/**
	 * Set the order of the activities in the theme/curriculum.
	 *
	 * @param options
	 */
	async setCurriculumActivitiesOrder(options = {}) {
		this.unblock();
		const user = await Meteor.userAsync();
		const currentPerson = user && await user.fetchPerson();
		if (!currentPerson || !_.contains(['staff', 'admin'], currentPerson.type)) {
			throw new Meteor.Error('403', 'Access denied. Cannot order the activities in the theme');
		}

		if (!options?.activityIds?.length) {
			throw new Meteor.Error('400', 'Please pass in some activities to order.');
		}

		await CurriculumUtils.setCurriculumActivitiesOrder(currentPerson.orgId, options.activityIds);
	},
	async getNameFromId(personId) {
		this.unblock();
		const person = await People.findOneAsync({ _id: personId });
		if (person) {
			const { firstName, lastName } = person;
			const fullName = `${firstName} ${lastName}`;
			return fullName.trim();
		} else {
			return null;
		}
	},
	async checkAllowed(currentPerson, profileId) {
		this.unblock();
		return await RelUtils.checkAllowedProfile(currentPerson, profileId);
	},
	"fetchRelationships": async function () {
		this.unblock();
		const user = await Meteor.userAsync();
		const currentPerson = user && await user.fetchPerson();
		if (currentPerson) {
			let query = { orgId: user.orgId };
			if (currentPerson.type == "family") {
				query.relationshipType = "family";
			}
			return await Relationships.find(query, { readPreference: "secondaryPreferred" }).fetchAsync();
		}
		return [];
	},
	/**
	 * Update "numberOfRegistrations" for coupon when saved, this is used for the "Max # of registrant" check
	 * @param orgId {string}
	 * @param code {string} "Coupon Code"
	 */
	async updateCouponCodeNumOfRegistrants({ orgId, code }) {
		const peoples = await (await People.aggregate([
			{ "$match": { orgId: orgId, inActive: { $ne: true }, type: "person", "billing.enrolledPlans": { "$exists": true, $ne: [] } } },
			{
				"$project": { "billing.enrolledPlans": 1 }
			}])).toArray()

		let numOfTimesAllocationUsed = 0;
		peoples.forEach((person) => {
			const enrolledPlans = person.billing.enrolledPlans;
			enrolledPlans.forEach((enrolledPlan) => {
				if (enrolledPlan?.allocations && enrolledPlan.allocations.length > 0) {
					enrolledPlan.allocations.forEach((allocation) => {
						if (allocation.allocationType === 'coupon') {
							if (allocation.discountType === code) {
								numOfTimesAllocationUsed++;
							}
						}
					})
				}
			})
		})

		await Orgs.updateAsync({ _id: orgId, "billing.couponCodes.code": code }, { $set: { "billing.couponCodes.$.numberOfRegistrations": numOfTimesAllocationUsed } });
	},

	/**
	 * get service accounts from user
	 * @param orgId {string}
	 * @return [users]
	 */
	async getServiceAccounts(orgId) {
		let response = [];
		const users = await Meteor.users.find({ orgId: orgId, isServiceAccount: true }, { _id: 1, "emails.address": 1, "services.password": 1, }).fetchAsync();
		if (users.length > 0) {
			users.map((i) => response.push({
				_id: i._id,
				email: (i?.emails?.[0]?.address) ? (i?.emails?.[0]?.address) : "",
				password: (i?.services?.password?.bcrypt) ? (i?.services?.password?.bcrypt) : ""
			}));
		}
		return response
	},

	/**
	 * insert service accounts to user collection
	 * @param {string} email
	 * @param {string} password
	 * @return [users]
	 */
	async insertServiceAccount(email, password) {
		try {
			const currentUser = await Meteor.userAsync();
			const personObj = {
				type: "serviceAccount",
				orgId: currentUser ? currentUser.orgId : '',
				email: email,
				inActive: false,
				createdBy: currentUser ? currentUser._id : '',
				createdAt: new moment(new Date().valueOf()).toISOString()
			};
			const userObj = {
				email: email,
				password: password
			};
			const userUpdateObj = {
				isServiceAccount: true,
				orgId: currentUser ? currentUser.orgId : '',
				createdBy: currentUser ? currentUser._id : '',
				createdAt: new moment(new Date().valueOf()).toISOString()
			};
			const emailExist = await Meteor.callAsync("checkEmailExistsForOrg", userObj.email);

			if (!emailExist) {
				const personId = await People.insertAsync(personObj);
				const userId = await Accounts.createUserAsync(userObj);
				if (userId && personId) {
					userUpdateObj.personId = personId;
					await Meteor.users.updateAsync({ _id: userId }, { $set: userUpdateObj });
				}
				// AWS code to add admincreateuser and adminsetpassword
				userObj.orgId = userUpdateObj.orgId;
				userObj.userId = userId;
				await cognitoService.createCognitoUser(userObj).then(data => {
					console.log('User created and password set successfully:', data);
				})
					.catch(err => {
						console.error('Error creating user or setting password:', err);
						throw new UnknownProblem(err.error || err.reason || err.message);
					});
				await HistoryAuditService.logPersonChange({
					changeType: HistoryAuditChangeTypes.ADD,
					performedByUser: currentUser,
					previousState: null,
					currentState: { ...personObj, _id: personId },
				});
				return userId;
			} else { return new Meteor.Error('409', 'User already exist') }
		} catch (err) { return new Meteor.Error(err.error, err.message) }
	},

	/**
	 * update service account's password
	 * @param {string} id
	 * @param {string} password
	 */
	async updatePasswordServiceAccount(id, password, email) {
		this.unblock();
		try {
			await Accounts.setPasswordAsync(id, password);
			const userObj = {
				email: email,
				password: password
			}
			await cognitoService.updateCognitoUserPassword(userObj).then(data => {
				console.log('User created and password set successfully:', data);
			})
				.catch(err => {
					console.error('Error creating user or setting password:', err);
					throw new UnknownProblem(err.error || err.reason || err.message);
				});
		} catch (err) {
			Log.error(err.error);
		}
	},

	/**
	 * update service account's password
	 * @param {string} id
	 */
	async deleteServiceAccount(id) {
		try {
			const user = await Meteor.users.findOneAsync({ _id: id });
			await People.removeAsync(user.personId);
			await Meteor.users.removeAsync(id);
			//todo remove user from cognito.
			const userCog = await cognitoService.searchUsers('username', user.emails[0].address);
			if (userCog.length) {
				await cognitoService.deleteUser(userCog[0].Username);
			}
			return 'service account removed successfully.'
		} catch (err) {
			Log.error(err.error);
		}
	},
	logHistory: async function (options) {
		this.unblock();
		options.performedByUser = await Meteor.userAsync();
		if (options.callback) {
			options.callback(options);
		} else if (options.callbackString) {
			/** { HistoryAuditRecordTypes } options.callbackString */
			switch (options.callbackString) {
				case HistoryAuditRecordTypes.PERSON:
					await HistoryAuditService.logPersonChange(options);
					break;
				case HistoryAuditRecordTypes.SCHEDULE:
					await HistoryAuditService.logScheduleChange(options);
					break;
				case HistoryAuditRecordTypes.STATUS:
					await HistoryAuditService.logStatusChange(options);
					break;
				case HistoryAuditRecordTypes.PAYMENT_METHOD:
					await HistoryAuditService.logPaymentChange(options);
					break;
				case HistoryAuditRecordTypes.RELATIONSHIP:
					await HistoryAuditService.logRelationshipChange(options);
					break;
			}
		} else {
			await HistoryAuditService.logHistory(options);
		}
	},

	async findTimeCardsOnSpecificDayForPerson(orgId, date, personId) {
		this.unblock();
		return await TimeCards.find(
			{
				orgId,
				personId,
				$or: [
					{ checkInDate: date },
					{ checkOutDate: date },
				],
				$and: [
					{
						$or: [
							{ void: false },
							{ void: { $exists: false } }
						]
					}
				]
			},
			{
				fields: { _id: 1, checkInTime: 1, checkInDate: 1, checkOutTime: 1, checkOutDate: 1 },
				sort: { checkInDate: 1, checkInTime: 1 }
			}
		).fetchAsync();
	},
	announcementsForToday: async function (orgId, groupId, type) {
		// Get the local timezone offset in milliseconds
		const timezoneOffset = new Date().getTimezoneOffset() * 60 * 1000;

		// Get the start of the day (12:00 AM local time) in milliseconds
		const localStartRange = new Date().setHours(0, 0, 0, 0);

		// Adjust startRange to UTC equivalent (to match the epoch stored in the DB)
		const startRange = localStartRange - timezoneOffset;

		// Set the end of the day (11:59:59.999 PM local time) in milliseconds
		const localEndRange = new Date().setHours(24, 0, 0, 0);

		// Adjust endRange to UTC equivalent
		const endRange = localEndRange - timezoneOffset;

		const q = {
			orgId: orgId,
			scheduledDate: {
				$gte: startRange,
				$lt: endRange
			},
			$and: [
				{ $or: [{ selectedGroups: [] }, { selectedGroups: groupId }, { selectedGroups: null }] },
				{ $or: [{ selectedRoles: [] }, { selectedRoles: null }, { selectedRoles: type === "person" ? "family" : type }] }
			],
		};

		return await Announcements.find(q).fetchAsync();
	},
	'thePeopleCount': async function (query) {
		this.unblock();
		const user = await Meteor.users.findOneAsync(this.userId), currentPerson = user && await user.fetchPerson();
		if (user && currentPerson) {
			var orgQuery = { "orgId": user["orgId"] }
		}

		if (currentPerson.type == "family") {
			var includeList = [currentPerson._id];
			includeList = includeList.concat(await Relationships.find({ $or: [{ personId: currentPerson._id }, { targetId: currentPerson._id }], relationshipType: "family" }).mapAsync(
				function (m, index) { return m.targetId; }));
			query["_id"] = { $in: includeList };
			query["inActive"] = { $ne: true };
		}

		if (currentPerson.type == "person") {
			var includeList = [currentPerson._id];
			includeList = includeList.concat(await Relationships.find({ $or: [{ personId: currentPerson._id }, { targetId: currentPerson._id }], relationshipType: "family" }).mapAsync(
				function (m, index) { return m.personId; }));
			query["_id"] = { $in: includeList };
			delete query.inActive;
		}

		const mergedQuery = { ...orgQuery, ...query };
		return await People.find(mergedQuery).countAsync();
	},
	async setPrimarySiteForPerson(personId) {
		this.unblock();
		await AbleToWorkAtService.setPrimarySite(personId);
	},
	async updateWorkableOrgsForStaff(personId, membershipOrgIds, primaryOrgId) {
		this.unblock();
		const person = await People.findOneAsync({ _id: personId });
		await AbleToWorkAtService.updateStaffWorkableOrgs(person, membershipOrgIds, primaryOrgId);
	},
	scheduleGroupDashboardRecalculation,
	afterInsertMomentTimeCard,
	afterUpdateMomentTimeCard,
	afterDeleteMomentTimeCard,
	async getAttendanceGridByDate(
		{
			orgId: desiredOrgId,
			reportStartDate: desiredForDate, // required, formatted as MM/DD/YYYY
			selectedGroup: desiredGroup, // optional, group id
			scheduleTypes: desiredScheduleTypes, // optional, array of schedule type ids
			groupByScheduleType: desiredGroupByScheduleType // optional, boolean
		}
	) {
		const currentOrg = await Orgs.findOneAsync(desiredOrgId);

		if (!currentOrg) {
			throw new Meteor.Error(400, "Invalid orgId");
		}

		if (!desiredForDate || typeof desiredForDate !== "string") {
			throw new Meteor.Error(400, "Invalid reportStartDate");
		}

		if (desiredGroup && typeof desiredGroup !== "string") {
			throw new Meteor.Error(400, "Invalid selectedGroup");
		}

		if (desiredScheduleTypes && !Array.isArray(desiredScheduleTypes)) {
			throw new Meteor.Error(400, "Invalid scheduleTypes");
		}

		if (typeof desiredGroupByScheduleType !== "boolean") {
			throw new Meteor.Error(400, "Invalid groupByScheduleType");
		}

		return await AttendanceGridByDateService.execute(
			currentOrg,
			desiredForDate,
			desiredGroup,
			desiredScheduleTypes,
			desiredGroupByScheduleType
		);
	},
	'editRelationship': async function (relationshipData) {
		try {
			await processPermissions({
				assertions: [{ context: "people/relationships", action: "edit" }],
				evaluator: (thisPerson) => thisPerson.type == "admin",
				throwError: true
			});

			const currentUser = await Meteor.userAsync();

			const r = await Relationships.findOneAsync(relationshipData.relationshipId);

			if (!r) {
				throw new Meteor.Error(403, "missing relationship");
			}

			if (!(r["orgId"] === currentUser["orgId"]) || !(_.contains["family", "authorizedPickup"], relationshipData.relationshipType)) {
				throw new Meteor.Error(403, "Access denied");
			}

			let newId = null;
			if (r?.personId && r?.targetId) {
				newId = await RelUtils.setAllRelationships(
					r.personId,
					r.targetId,
					currentUser.orgId,
					relationshipData.primaryCaregiver,
					relationshipData.relationshipType,
					relationshipData.relationshipDescription
				);
			}

			await Meteor.callAsync('updateZkTecoPerson', r.personId);

			return { relationshipId: newId || relationshipData.relationshipId };
		} catch (error) {
			throw error;
		}
	},
	'deleteRelationship': async function (relationshipId) {
		try {
			await processPermissions({
				assertions: [{ context: "people/relationships", action: "edit" }],
				evaluator: (thisPerson) => thisPerson.type == "admin",
				throwError: true
			});

			var currentUser = await Meteor.userAsync();

			var r = await Relationships.findOneAsync(relationshipId);

			const currentPerson = await r?.fetchPerson();

			if (!(currentPerson["orgId"] == currentUser["orgId"])) {
				throw new Meteor.Error(403, "Access denied");
			}

			await removeAllRelationships(r.personId, r.targetId);

			await Meteor.callAsync('updateZkTecoPerson', r.personId);

		} catch (error) {
			throw error;
		}
	},
	"triggerKinderConnect": async function () {
	try {
		if (!this.userId) {
			throw new Meteor.Error("not-authorized");
		}		
		const user = await Meteor.users.findOneAsync(this.userId);
		if (!user) throw new Meteor.Error("user-not-found");
		const uploadDate = new moment.utc().subtract(0, 'day').format('MM/DD/YYYY');
		console.log('Starting Kinderconnect attendance report Job for', uploadDate);
		await KinderConnect.processAttendance({ uploadDate });
	} catch (error) {
		throw error;
	}
	},
	"triggerPrecomputeCronjobForOrgHierarchy": async function () {
		try {
			if (!this.userId) {
				throw new Meteor.Error("not-authorized");
			}		
			const user = await Meteor.users.findOneAsync(this.userId);
			if (!user) throw new Meteor.Error("user-not-found");
			await Metrics.precomputeOrgTree(user.orgId);
		} catch (error) {
			throw error;
		}
	},
	"getReservations": async function (options) {
		const user = await scheduleBookingUtils.fetchUser(this.userId);
		return scheduleBookingUtils.getReservations(options, user);
	},
	'triggerPrecomputeCronjob': async function(){
		try{
			await Metrics.precompute();
		} catch (error) {
			throw error;
		}
	},
	/**
	 * Refreshes the person data to update reactivity with Redis Oplog
	 * This forces UI components to update when data changes without a page refresh
	 * 
	 * @param {string} personId - The ID of the person to refresh
	 */
	refreshPersonData(personId) {
		check(personId, String);
		
		// This forces Redis Oplog to notify clients that the person has changed
		// We use $set with the current timestamp to ensure the update is detected
		People.update({ _id: personId }, { $set: { lastRefreshed: new Date().getTime() } });
		
		return true;
	}
})

async function removeAllRelationships(personId, targetId) {
	await Relationships.removeAsync({ personId, targetId });
}

var tokenString = function () {
	var chars = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXTZabcdefghiklmnopqrstuvwxyz";
	var string_length = 20;
	var randomstring = '';
	for (var i = 0; i < string_length; i++) {
		var rnum = Math.floor(Math.random() * chars.length);
		randomstring += chars.substring(rnum, rnum + 1);
	}
	return randomstring;
};

var createNewS3Client = function () {
	const credentials = new AWS.Credentials(Meteor.settings.AWSAccessKeyId, Meteor.settings.AWSSecretAccessKey);

	const s3 = new AWS.S3({
		region: "us-east-1",
		endpoint: "https://s3.us-east-1.amazonaws.com",
		credentials,
		signatureVersion: 'v4'
	});
	return s3;
}

function formatStandardName(name) {
	return name.replace("`", " ");
}

const dayMap = { "sun": "Su", "mon": "M", "tue": "Tu", "wed": "W", "thu": "Th", "fri": "F", "sat": "Sa" };
