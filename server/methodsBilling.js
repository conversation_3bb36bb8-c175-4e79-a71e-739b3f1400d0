import { Meteor } from 'meteor/meteor';
import { Random } from 'meteor/random';
import { AdyenProvider } from './card_providers/adyenProvider';
import { AdyenBalancePaymentProvider } from './card_providers/adyenBalancePaymentProvider';
import AES from 'crypto-js/aes';
import {Metrics} from './analytics/metrics';
import Papa from 'papaparse';
import moment from "moment-timezone";
import {
	CreditReasons,
	ITEM_TYPE,
	ledgerAccountTypes,
	PUNCH_CARD_TYPE
} from '../lib/constants/billingConstants';
import { AvailableCustomizations } from '../lib/customizations';
import {ReportExportService} from "./reportExportService";
import { LedgerDetailService } from "./ledgerDetailService";
import { PunchCardService } from './punchCardService';
import { AwsBillingService } from './awsBillingService';
import Utf8 from "crypto-js/enc-utf8";
import { BillingUtils } from '../lib/util/billingUtils';
import { Log } from '../lib/util/log';
import { People } from '../lib/collections/people';
import { LedgerDetailServiceUtils } from '../lib/util/ledgerDetailServiceUtils';
import { Invoices } from '../lib/collections/invoices';
import { Orgs } from '../lib/collections/orgs';
import { processPermissions } from '../lib/permissions';
import _ from '../lib/util/underscore';
import { PayerFunds } from '../lib/collections/payerFunds';
import { Deposits } from '../lib/collections/deposits';
import { processBillingEmail } from './processBillingEmail';
import { Relationships } from '../lib/collections/relationships';
import { TwoFactorCodes } from '../lib/collections/twoFactorCodes';
import { OrgAdditionalDatas } from './collections/orgAdditionalDatas';
import { PaymentBatches } from './collections/paymentBatches';
import { CardProviders } from "./card_providers/cardProviders";
import { USER_TYPES } from '../lib/constants/profileConstants';
import { AvailableActionTypes, AvailablePermissions } from '../lib/constants/permissionsConstants';
import { InvoiceModificationService } from './invoices/invoiceModificationService';
import { PayerReconciliationUtils } from '../lib/util/payerReconciliationUtils';
import { BillingServerUtils } from './billingServerUtils';
import { PayerReconciliationBatches } from '../lib/collections/payerReconciliationBatches';

const MAX_CSV_DB_SIZE = 100 * 1024;

function generateReconciliationCsv(report, batchLabel, startDate, endDate, payer, totalDays, totalAmount) {
    let csvData = [];
    
    csvData.push('Name,Invoice #,Covered Day(s),# Day(s),Amount');
    report.forEach(item => {
        const quotedName = `"${item.personName?.replace(/"/g, '""') || ''}"`;
        const coveredDays = item.coveredDaysLabel?.includes(',') ? 
            `"${item.coveredDaysLabel.replace(/"/g, '""')}"` : (item.coveredDaysLabel || '');
        
        csvData.push(`${quotedName},${item.invoiceNumber || ''},${coveredDays},${item.coveredDaysCount || 0},$${item.amount.toFixed(2)}`);
    });
    
    csvData.push(`"Total for batch",,,${totalDays},$${totalAmount.toFixed(2)}`);
    return csvData.join('\n');
}

Meteor.methods({
	async orgBusinessTaxId(options) {
		this.unblock();
		const org = await Orgs.findOneAsync({ _id: options.orgId }, {fields: { 'billing.legalEntity.business_tax_id': 1 }});

		if (org.billing?.legalEntity?.business_tax_id) {
			return AES.decrypt( org.billing.legalEntity.business_tax_id, Meteor.settings.mpEntityKey).toString(Utf8)
		}

		return null;
	},
    'getPlatformFees': async function(accountType, personId) {
        this.unblock();
        let peopleRelationships = [];
        const todayDate = new moment().startOf('day').valueOf();
        const currentOrg = await Orgs.current();
        const orgPaymentFees = currentOrg.billing.paymentFees;

        const getEnabledBillingPlans = async (child, todayDate, currentOrg) => {
            return await child.enabledBillingPlans(todayDate, false, currentOrg);
        };

        const getPersonBillingPlans = async (personId) => {
            return await People.findOneAsync(
                personId,
                { fields: { _id: 1, firstName: 1, lastName: 1, 'billing.enrolledPlans': 1 } }
            );
        };

        const allRelationships = await Relationships.find({ relationshipType: "family", personId }).fetchAsync();
		for (const r of allRelationships) {
			const child = await getPersonBillingPlans(r.targetId);
			if (child) {
				for (const plan of await getEnabledBillingPlans(child, todayDate, currentOrg)) {
					const merchantFeeCalculated = child.calculateMerchantFee(accountType, plan, orgPaymentFees);
		
					peopleRelationships.push({
						personName: `${child.firstName} ${child.lastName}`,
						merchantFee: numeral(merchantFeeCalculated).format('$0,0.00'), // Format as currency
						totalAmount: numeral(plan.calculatedAmount).format('$0,0.00')  // Format as currency
					});
				}
			}
		}

		return {
			peopleRelationships,
			hasRecurringCharges: peopleRelationships.length > 0
		};

	},
	async updateRegFeeConfig(config) {
		this.unblock();
		await processPermissions({
			assertions: [{ context: "billing/configuration/system", action: "edit"}],
			evaluator: (person) => person.type==="admin",
			throwError: true
		});
		const currentUser = await Meteor.userAsync();
		await Orgs.updateAsync({_id: currentUser.orgId}, {"$set": {"billing.regFeeConfig": config}});
	},

	async updateBillingLegalEntity(options) {
		this.unblock();
		await processPermissions({
			assertions: [{ context: "billing/configuration/system", action: "edit"}],
			evaluator: (person) => person.type=="admin",
			throwError: true
		});

		const currentUser = await Meteor.userAsync();

		const updateData = {
			"business_name": options.business_name,
			"business_tax_id": AES.encrypt( options.business_tax_id, Meteor.settings.mpEntityKey).toString(),
			"address": options.address,
			"city": options.city,
			"state": options.state,
			"zipcode": options.zipcode
		};
		await Orgs.updateAsync({_id: currentUser.orgId}, {"$set": {"billing.legalEntity": updateData}});
	},

	async removeCustomerSource(options) {
		this.unblock();
		const currentUser = await Meteor.userAsync();

		if (!currentUser) {
			throw new Meteor.Error(403, "Access denied");
		}

		const currentUserPerson = await currentUser.fetchPerson();
		const isAdmin = currentUserPerson.type === "admin";
		const isFamilyWithAccess = currentUserPerson.type === "family" && currentUser.personId === options.personId;

		if (!isAdmin && !isFamilyWithAccess) {
			throw new Meteor.Error(403, "Access denied");
		}

		const currentPerson = await People.findOneAsync({orgId: currentUser.orgId, _id: options.personId});

		if (!await currentPerson.availablePayMethodAction("remove", options.type, await Orgs.current())) {
			throw new Meteor.Error(500, "This particular payment method cannot be removed.");
		}

		const paymentOptions = {
			personId: currentPerson._id,
			paymentType: options.type,
			performedByUser: currentUser,
			lastFour: BillingUtils.getLastFourByPaymentType(currentPerson, options.type)
		};

		try {
			await AdyenProvider.removeCustomerSourceByType( paymentOptions );
		} catch (error) {
			Log.error(error);
			throw new Meteor.Error(500, "Issue with bank account removal: " + error.message);
		}
	},

	async payInvoice(options) {
		const user = await Meteor.userAsync();
		const currentUser = options.fromRegistration ? null : user;
		if (currentUser) {
			options.userId = currentUser._id;
		}
		options.orgId = options.orgId || currentUser.orgId;
		if (!options.fromRegistration) {
			await processPermissions({
				assertions: [{ context: 'billing/payments', action: 'edit' }, { context: 'billing/payments/create', action: 'edit' }],
				evaluator: (person) => person.type === 'admin' || (person.type === 'family' && currentUser.personId === options.personId),
				throwError: true
			});
		}
		try {
			await AwsBillingService.payInvoice(options);
		} catch (error) {
			throw new Meteor.Error('Error during payment', error.message);
		}
	},

	async refundCharge(options) {
		const currentUser =  await Meteor.userAsync();
		if (!_.deep(await Orgs.current(), "billing.cashnetEnabled") && !_.deep(await Orgs.current(), "billing.adyenInfo") ) {
			throw new Meteor.Error(403, "Access denied");
		}

		await processPermissions({
			assertions: [{ context: "billing/payments", action: "edit" }, { context: "billing/payments/refund", action: "edit" }],
			evaluator: (person) => person.type=="admin",
			throwError: true
		});

		const invoice = await Invoices.findOneAsync({_id: options.invoiceId});

		if (options.refund_amount > invoice.getRefundableAmount())
			throw new Meteor.Error(500, "Either this amount exceeds the original charge amount or existing refunds would cause this refund to exceed the original charge");

		options.creditedByPersonId = currentUser.personId;
		options.currentUser = currentUser;

		try {
			const paymentProvider = await CardProviders.getPaymentProvider(options.charge_id);

			const refundLine = await paymentProvider.refundCharge(options);
			await processBillingEmail({emailType: 'refund', invoiceId: options.invoiceId, refundLine});
		} catch (error) {
			console.log(error);
			throw new Meteor.Error(500, "Problem with refund: " + error.message);
		}
	},

	async creditPayerMultiple (options) {
		const currentUser = await Meteor.userAsync();
		const currentPerson = currentUser && await currentUser.fetchPerson();

		await processPermissions({
			assertions: [{ context: "billing/payments", action: "edit" }],
			evaluator: (person) => person.type=="admin",
			throwError: true
		});

		const payerReconciliationBatchLabel = new moment().format() + " - " + currentPerson.firstName + " " + currentPerson.lastName;

		let batch = [], creditMemoBatch = [], report = [], totalNumberDays = 0, totalAmount = 0.0;
		for(const a of options.allocations){
			let amount = BillingUtils.roundToTwo(parseFloat(a.amount));
			if (!a.invoiceId) {
				await PayerFunds.insertAsync({
					orgId: currentPerson.orgId,
					createdAt: new Date().valueOf(),
					createdBy: currentPerson._id,
					payerSource: options.payer,
					amount: amount,
					destination: a.overpaymentDestination,
					checkNumber: options.checkNumber,
					payerReconciliationBatchLabel: payerReconciliationBatchLabel
				});
				report.push({
					isNonFamilyFunding: true,
					amount
				});
				totalAmount += amount;
				continue;
			}
			const invoice = await Invoices.findOneAsync({orgId: currentUser.orgId, _id: a.invoiceId});

			const creditLine = {
				type: "credit",
				amount,
				lineItemId: a.lineItemId,
				payerReconciliationBatchLabel,
				createdAt: new Date().valueOf(),
				creditedBy: currentUser.personId,
				creditReason: "reimbursable",
				creditNote: options.credit_note,
			};
			if (!a.invoiceOnlyAmount && a.hasOwnProperty('daySelections'))
				creditLine.coveredDays = a.daySelections;

			const amountAdjustment = creditLine.amount;
			creditLine.creditReason = "reimbursable";
			creditLine.creditPayerSource = options.payer;
			creditLine.checkNumber = options.checkNumber;

			const openPayerAmount = invoice.openAmountForPayer(creditLine.creditPayerSource)
			if (amountAdjustment > openPayerAmount) {
				let overpaymentDestination = a.overpaymentDestination
				if (!overpaymentDestination)
					throw new Meteor.Error(500, "The payment for invoice #" + invoice.invoiceNumber + " is more than its open amount for requested payer and no overpayment destination was selected.");

				const overpaymentAmount = amountAdjustment - openPayerAmount;
				if (overpaymentDestination.startsWith("family-credit-memo")) {
					creditLine.payerOverpaymentFamilyPersonId = overpaymentDestination.replace("family-credit-memo-", "");
					overpaymentDestination = "family-credit-memo";
				} else if (overpaymentDestination == "payer-credit-memo") {
					const personRelationships = await Relationships.find({orgId: currentUser.orgId, targetId: invoice.personId, relationshipType:"family"}).mapAsync(r => r.personId),
						aFamilyPerson = await People.findOneAsync({orgId: currentUser.orgId, _id: {"$in": personRelationships}, inActive:{"$ne":true}});
					if (!aFamilyPerson)
						throw new Meteor.Error(500, "There is not a valid family person for invoice #" + invoice.invoiceNumber + " to apply a payer credit memo.");
					creditLine.payerOverpaymentFamilyPersonId = aFamilyPerson._id;
				}
				creditLine.amount = openPayerAmount;
				creditLine.payerOverpaymentAmount = overpaymentAmount;
				creditLine.payerOverpaymentDestination = overpaymentDestination;
				creditLine.payerTotalReceiptAmount = amountAdjustment;

				if (creditLine.payerOverpaymentFamilyPersonId) {
					const newCreditMemoType = overpaymentDestination == "payer-credit-memo" ? `prepaid_${options.payer}` : `excess_${options.payer}`,
						newCreditMemoId = Random.id();

					creditMemoBatch.push( [
						{_id: creditLine.payerOverpaymentFamilyPersonId},
						{
							_id: newCreditMemoId,
							type: newCreditMemoType,
							createdAt: new moment().valueOf(),
							createdBy: currentPerson._id,
							notes: "Payer overpayment on invoice " + invoice.invoiceNumber,
							openAmount: parseFloat(overpaymentAmount),
							originalAmount: parseFloat(overpaymentAmount),
							overpaymentDestinationPersonId: creditLine.payerOverpaymentFamilyPersonId,
							overpaymentSourceInvoiceId: invoice._id
						} ] );

					creditLine.payerOverpaymentCreditMemoId = newCreditMemoId;
				}
			}

			const query = [
				{_id: invoice._id},
				{"$push": {credits: creditLine}}
			];
			batch.push(query);

			const person = await People.findOneAsync(invoice.personId);
			const actualProcessedAmount = creditLine.payerTotalReceiptAmount || creditLine.amount;
			report.push({
				personName: person.lastName + ", " + person.firstName,
				invoiceNumber: invoice.invoiceNumber,
				amount: actualProcessedAmount,
				coveredDaysCount: (a.daySelections && a.daySelections.length) || 0,
				coveredDaysLabel: (a.daySelections && a.daySelections.join(", ")) || ""
			});
			totalAmount += actualProcessedAmount;
			totalNumberDays += (a.daySelections && a.daySelections.length) || 0;
		};

		_.each(batch, batchItem => {
			Invoices.updateByComplexQueryWithJournalEntry(batchItem[0], batchItem[1], {
				userId: currentUser._id,
				personId: currentUser.personId,
				orgId: currentUser.orgId,
				reason: `Batch item update`,
				reasonLocation: `server/methodsBilling.js:creditPayerMultiple`,
			});
		});

		for (const batchItem of creditMemoBatch) {
			await People.updateAsync(batchItem[0], { "$push": { "billing.creditMemos": batchItem[1] } });
		}

		const batchId = await PayerReconciliationBatches.insertAsync({
			orgId: currentUser.orgId,
			batchLabel: payerReconciliationBatchLabel,
			date: new Date().valueOf(),
			payer: options.payer,
			payerName: options.payerName || options.payer,
			checkNumber: options.checkNumber,
			totalAmount,
			totalNumberDays,
			startDate: options.startDate,
			endDate: options.endDate,
			createdAt: new Date().valueOf(),
			createdBy: currentUser.personId,
			creatorName: currentPerson.firstName + " " + currentPerson.lastName,
			supportingDocumentId: options.supportingDocument?.documentId,
			fileName: options.supportingDocument?.fileName,
			fileType: options.supportingDocument?.fileType,
			documentUrl: options.supportingDocument?.documentUrl,
			allocations: report
		});

		return {
			report: report,
			data: report,
			batchLabel: payerReconciliationBatchLabel,
			startDate: options.startDate,
			endDate: options.endDate,
			dateRange: `${options.startDate} - ${options.endDate}`,
			payer: options.payer,
			totalNumberDays,
			totalAmount,
			batchId
		};
	},

	async creditInvoice(options) {
		const currentUser = await Meteor.userAsync();
		const creditReason = options.credit_reason;
		const assertions = [{ context: AvailablePermissions.BILLING_PAYMENTS, action: AvailableActionTypes.EDIT }];

		if (creditReason === CreditReasons.BAD_DEBT || creditReason === CreditReasons.PAYROLL_DEDUCTION || creditReason === CreditReasons.AGENCY_WRITE_OFF || creditReason === CreditReasons.COLLECTIONS_WRITE_OFF) {
			assertions.push({ context: AvailablePermissions.BILLING_PAYMENTS_CREDIT_BAD_DEBT, action: AvailableActionTypes.EDIT });
		}

		if (creditReason === CreditReasons.OTHER && options.existingCreditIndex) {
			assertions.push({ context: AvailablePermissions.BILLING_OTHER_CREDITS_MODIFY, action: AvailableActionTypes.EDIT });
		}

		await processPermissions({
			assertions,
			evaluator: (person) => person.type === USER_TYPES.ADMIN,
			throwError: true
		});

		const invoice = await Invoices.findOneAsync({_id: options.invoiceId});

		if (!invoice) {
			throw new Meteor.Error(500, "Invoice not found");
		}

		let creditLine;
		let query;
		let amountAdjustment = 0.0;
		const isModifiedCredit = parseInt(options.existingCreditIndex) >= 0;

		if (isModifiedCredit) {
			const result = InvoiceModificationService.getUpdateCreditLineAndQueryForModifiedCredit(invoice, options, currentUser);
			creditLine = result.creditLine;
			query = result.query;
			amountAdjustment = result.amountAdjustment;
		} else {
			creditLine = {
				type: "credit",
				amount: BillingUtils.roundToTwo(parseFloat(options.credit_amount)),
				createdAt: new Date().valueOf(),
				creditedBy: currentUser.personId,
				creditReason: creditReason,
				creditNote: options.credit_note
			};
			amountAdjustment = creditLine.amount;
			query = {
				"$push": {credits: creditLine}
			};
		}

		if (creditReason === CreditReasons.OTHER) {
			if (!options.line_item) {
				throw new Meteor.Error(500, "For credit type 'other', you must select a line item for applying this credit.");
			}

			const lineItemIndex = parseInt(options.line_item);
			const amountCreditedToLineItem = invoice.credits?.filter(c => c.creditLineItemIndex === lineItemIndex).reduce((memo, c) => memo + c.amount, 0) ?? 0;

			const lineItem = invoice.lineItems[lineItemIndex];

			if (!lineItem) {
				throw new Meteor.Error(500, "Invalid line item specified");
			}

			const totalCreditAmountForLineItem = amountCreditedToLineItem + amountAdjustment;

			if (totalCreditAmountForLineItem > lineItem.amount ) {
				throw new Meteor.Error(500, "The credit amount specified would exceed the original amount of the line item minus existing credits.");
			}

			creditLine.creditLineItemIndex = lineItemIndex;
			creditLine.creditLineItemOriginal = lineItem;

			// Punch card logic for other credits
			if (lineItem.type === ITEM_TYPE && lineItem.originalItem?.type === PUNCH_CARD_TYPE) {
				const numberOfDays = parseInt(lineItem.originalItem?.numberOfDays) * lineItem.quantity;
				if (amountCreditedToLineItem + creditLine.amount === lineItem.amount && numberOfDays > 0) {
					await PunchCardService.addPunchCardDays(invoice.personId, numberOfDays);
				}
			}
		}

		if (creditReason.startsWith("reimbursable")) {
			creditLine.creditReason = "reimbursable";
			creditLine.creditPayerSource = creditReason.split("_")[1];

			if (amountAdjustment > invoice.openAmountForPayer(creditLine.creditPayerSource)) {
				throw new Meteor.Error(500, "An invoice cannot be credited for more than its open amount.");
			}
		} else {
			if (amountAdjustment > BillingUtils.roundToTwo(invoice.openAmount)) {
				throw new Meteor.Error(500, "An invoice cannot be credited for more than its open amount.");
			}

			if (creditReason.startsWith("manual_payment")) {
				creditLine.creditReason = "manual_payment";

				if (creditReason.includes("_fpid_")) {
					const reasonMatches = creditReason.match(/payment_(.*)_fpid_(.*)/);
					creditLine.paidBy = reasonMatches[2];
					const payingPerson = await People.findOneAsync({orgId: currentUser.orgId, _id: creditLine.paidBy});

					if (payingPerson) {
						if (amountAdjustment > BillingUtils.roundToTwo(invoice.amountDueForFamilyMember(payingPerson._id))) {
							throw new Meteor.Error(500, "An invoice cannot be credited for more than this person's open amount.");
						}

						creditLine.paidByDesc = payingPerson.firstName + " " + payingPerson.lastName;
					}

					creditLine.creditManualPaymentMethod = reasonMatches[1];
				} else {
					creditLine.creditManualPaymentMethod = creditReason.replace("manual_payment_", "");
				}

				creditLine.checkNumber = options.check_number ? options.check_number : null;
			}
			if (creditReason.startsWith("security_deposit_refund")) {
				creditLine.creditReason = "security_deposit_refund";
				const payerMatches = creditReason.match(/refund_fpid_(.*)/);
				creditLine.paidBy = payerMatches[1];
				const payingPerson = await People.findOneAsync({orgId: currentUser.orgId, _id: creditLine.paidBy});

				if (payingPerson) {
					if (amountAdjustment > BillingUtils.roundToTwo(invoice.amountDueForFamilyMember(payingPerson._id))) {
						throw new Meteor.Error(500, "An invoice cannot be credited for more than this person's open amount.");
					}

					creditLine.paidByDesc = payingPerson.firstName + " " + payingPerson.lastName;
				}
			}

			query["$inc"] = {openAmount: -1 * BillingUtils.roundToTwo(amountAdjustment)};
		}

		Log.info('credit invoice', options.invoiceId, query);

		await Invoices.updateByIdWithJournalEntry(options.invoiceId, query, {
			userId: currentUser._id,
			personId: currentUser.personId,
			orgId: currentUser.orgId,
			reason: `Credit applied to invoice for $${amountAdjustment} (${creditReason})`,
			reasonLocation: `server/methodsBilling.js:creditInvoice`,
		});

		if (creditReason.startsWith("manual_payment")) {
			processPostPaymentLogic({invoiceId: invoice._id, paymentLine: creditLine});
			await processBillingEmail({emailType: 'receipt', invoiceId: options.invoiceId, personId: creditLine.paidBy, paymentLine: creditLine});
		}
	},

	async billingTransactionReport(options) {
		const currentUser =  await Meteor.userAsync();

		await processPermissions({
			assertions: [{ context: "billing/payments", action: "read" }],
			evaluator: (person) => person.type=="admin",
			throwError: true
		});

		if ( _.deep((await Orgs.current()), "billing.cashnetEnabled") ) {
			const startDate = new moment(options.startDate, "MM/DD/YYYY").valueOf(),
				endDate = new moment(options.endDate, "MM/DD/YYYY").add(1, "days").valueOf(),
				transactions = [];
			let totalAmount = 0.0;
			const invoiceData = await Invoices.find({
				orgId: currentUser.orgId,
				"credits": {"$elemMatch": {
						"createdAt": {"$gte": startDate, "$lt": endDate },
						"type": "payment",
						"payment_type": "card"
					}}
			}).fetchAsync();
			invoiceData.forEach( (invoice) => {

				invoice.credits
					.filter( (c) => c.createdAt >= startDate && c.createdAt < endDate && c.type=="payment" && c["payment_type"] == "card" && c.cashnetInfo)
					.forEach( (c) => {
						totalAmount = totalAmount + c.amount;
						transactions.push({
							created: c.createdAt,
							type: c.type.capitalizeFirstLetter(),
							desc: "Payment via Cashnet",
							id: c.cashnetInfo.tx,
							amount: c.amount,
							invoiceNumber: invoice.invoiceNumber
						});
					});
			});

			return {
				data: transactions,
				totalAmount
			};
		} else {
			try {
				const outputData = [];
				let totalAmount = 0;
				const currentOrg = await Orgs.current();
				if (_.deep(currentOrg, 'billing.adyenInfo.accountCode')) {
					const adyenClassicTransactions = await AdyenProvider.billingTransactionReport(options);
					outputData.push(...adyenClassicTransactions.data);
					totalAmount += adyenClassicTransactions.totalAmount;
				}
				if (_.deep(currentOrg, 'billing.adyenInfo.balanceAccountId')) {
					const adyenBalanceTransactions = await AdyenBalancePaymentProvider.billingTransactionReport(options);
					outputData.push(...adyenBalanceTransactions.data);
					totalAmount += adyenBalanceTransactions.totalAmount;
				}

				return {
					data: outputData.sort((a, b) => b.arrival_date - a.arrival_date),
					totalAmount,
				};
			} catch (error) {
				console.log(error);
				throw new Meteor.Error(500, "Problem with report: " + error.message);
			}

		}
	},

	async billingChargebacksReport(options) {
		const currentUser =  await Meteor.userAsync();
		const org = await currentUser.fetchOrg();
		const userPerson = await currentUser.fetchPerson();
		if (!currentUser ||
			(userPerson.type != "admin") ||
			!(_.deep(org, "billing.adyenInfo")) ) {
			throw new Meteor.Error(403, "Access denied");
		}

		let orgIds = [org._id], allOrgs;
		if (options.orgIds && options.orgIds.length > 0) {
			const orgsScope = await userPerson.findScopedOrgs(),
				orgsScopeList = orgsScope && _.pluck(orgsScope, "_id");
			orgIds = _.intersection(orgsScopeList, options.orgIds);
			allOrgs = await Orgs.find({_id:{"$in": orgIds}}).fetchAsync();
		}

		const startDateNum = new moment.tz(options.startDate, "MM/DD/YYYY", org.getTimezone()).startOf("day").valueOf(),
			endDateNum = new moment.tz(options.endDate, "MM/DD/YYYY", org.getTimezone()).endOf("day").valueOf();
		const output = [];
		const invoiceData = await Invoices.find({orgId: {"$in": orgIds},
			"credits": {"$elemMatch": {"type": "chargeback", createdAt: {"$gte":startDateNum, "$lte":endDateNum} }}} ).fetchAsync();
			for (const i of invoiceData) {
				const filteredCredits = _.filter(i.credits, c => c.type == "chargeback" && c.createdAt >= startDateNum && c.createdAt <= endDateNum && (options.showResolved || !c.chargebackResolvedAt));
				for (const fc of filteredCredits) {
					const person = await People.findOneAsync({ orgId: i.orgId, _id: i.personId });
					output.push({
						personId: i.personId,
						invoiceId: i._id,
						invoiceNumber: i.invoiceNumber,
						personName: person.firstName + ' ' + person.lastName,
						chargebackAmount: fc.amount,
						chargebackDate: fc.createdAt,
						chargebackPspId: _.deep(fc, "adyenInfo.pspReference"),
						chargebackReason: _.deep(fc, "adyenInfo.reason"),
						status: fc.chargebackResolvedAt ? "Resolved" : "Open",
						orgName: allOrgs && allOrgs.find(ao => ao._id == i.orgId)?.name,
						orgId: i.orgId
					});
				}
			}

		return output;
	},

	async resolveChargeback(options) {
		const currentUser =  await Meteor.userAsync();
		await processPermissions({
			assertions: [{ context: "billing/invoices", action: "edit"}],
			evaluator: (person) => person.type=="admin",
			throwError: true
		});
		const query = {
			orgId:currentUser.orgId,
			_id:options.chargeback.invoiceId,
			"credits.adyenInfo.pspReference": options.chargeback.chargebackPspId
		};
		await Invoices.updateAsync(query, {"$set": {
				"credits.$.chargebackResolvedAt": new Date().valueOf(),
				"credits.$.chargebackResolvedBy": currentUser.personId
			}});

	},

	async billingRefundsReport(options) {
		console.log("options", options);
		const currentUser =  await Meteor.userAsync();
		const org = await currentUser.fetchOrg();
		const userPerson = await currentUser.fetchPerson();
		if (!currentUser ||
			(userPerson.type != "admin") ||
			!(_.deep((await Orgs.current()), "billing.adyenInfo")) ) {
			throw new Meteor.Error(403, "Access denied");
		}

		let orgIds = [org._id],allOrgs;
		if (options.orgIds && options.orgIds.length > 0) {
			const orgsScope = await userPerson.findScopedOrgs(),
				orgsScopeList = orgsScope && _.pluck(orgsScope, "_id");
			orgIds = _.intersection(orgsScopeList, options.orgIds);
			allOrgs = await Orgs.find({_id:{"$in": orgIds}}).fetchAsync();
		}

		const startDateNum = new moment.tz(options.startDate, "MM/DD/YYYY", org.getTimezone()).startOf("day").valueOf(),
			endDateNum = new moment.tz(options.endDate, "MM/DD/YYYY", org.getTimezone()).endOf("day").valueOf();
		console.log("startDateNum", startDateNum, "endDateNum", endDateNum)
		const output = [];
		if (!options.refundType || options.refundType == "onlinePayment") {
			const invoiceData = await Invoices.find({orgId: {"$in": orgIds},
				"credits": {"$elemMatch": {"type": "refund", createdAt: {"$gte":startDateNum, "$lte":endDateNum} }}} ).fetchAsync();
			for (const i of invoiceData) {
				const filteredCredits = _.filter(i.credits, c => c.type == "refund" && c.createdAt >= startDateNum && c.createdAt <= endDateNum && (!options.showPendingOnly || !c.refundConfirmed));
				for (const fc of filteredCredits) {
					const person = await People.findOneAsync({ orgId: i.orgId, _id: i.personId });
					output.push({
						personId: i.personId,
						invoiceId: i._id,
						invoiceNumber: i.invoiceNumber,
						personName: person.firstName + ' ' + person.lastName,
						refundAmount: fc.amount,
						refundDate: fc.createdAt,
						status: fc.refundConfirmed ? "Paid " + new moment(fc.refundConfirmedAt).format("MM/DD/YYYY") : "Pending",
						typeLabel: "Payment Refund",
						type: "onlinePayment",
						orgName: allOrgs ? allOrgs.find(ao => ao._id == i.orgId)?.name : orgIds.find(ao => ao == i.orgId)?.name,
					});
				}
			}
		}

		if (!options.refundType || options.refundType == "manualCreditMemo") {
			_.each( await People.find({orgId: {"$in": orgIds},
				"billing.creditMemos": {"$elemMatch": {refundedAt: {"$gte":startDateNum, "$lte":endDateNum} }}} ).fetchAsync(),
				p => {
					const filteredCreditMemos = _.filter(p.billing.creditMemos, c => c.refundedAt >= startDateNum && c.refundedAt <= endDateNum);
					_.each(filteredCreditMemos, fcm => {
						output.push( {
							personId: p._id,
							invoiceId: null,
							invoiceNumber: null,
							personName: p.firstName + ' ' + p.lastName,
							refundAmount: fcm.refundedAmount,
							refundDate: fcm.refundedAt,
							status: "Manual Refund CM",
							typeLabel: "Credit Memo Refund",
							type: "manualCreditMemo",
							orgName: allOrgs ? allOrgs.find(ao => ao._id == i.orgId)?.name : orgIds.find(ao => ao == i.orgId)?.name,
						});
					});
			});
		}

		return _.sortBy(output, entry => entry.refundDate);
	},

	async billingPayoutsReport(options) {
		console.log('starting billing payouts reports for orgs')
		const currentUser =  await Meteor.userAsync();
		const cuser = await currentUser.fetchPerson();
		if (!currentUser ||
			(cuser.type != "admin") ||
			!(_.deep((await Orgs.current()), "billing.adyenInfo")) ) {
			throw new Meteor.Error(403, "Access denied");
		}

		const outputData = [];

        const orgIds = options.orgIds && options.orgIds.length > 0;
		try {
			const currentOrg = await Orgs.current();
            options.orgIds = orgIds ? options.orgIds : [currentUser.orgId];
			const adyenClassicPayouts = await AdyenProvider.billingPayoutsReport(options);
			outputData.push(...adyenClassicPayouts.data);

			return {
				data: outputData.sort((a, b) => b.arrival_date - a.arrival_date)
			};
		} catch (error) {
			console.log(error);
			throw new Meteor.Error(500, "Problem with report: " + error.message);
		}
	},

	async billingPayoutDetailReport(options) {
		const currentUser =  await Meteor.userAsync();
		const cuser = await currentUser.fetchPerson();
		if (!currentUser ||
			(cuser.type != "admin") ||
			!(_.deep((await Orgs.current()), "billing.adyenInfo")) ) {
			throw new Meteor.Error(403, "Access denied");
		}

		try {
			if (!options.orgId) {
				options.orgId = currentUser.orgId;
			}
			const paymentProvider = await CardProviders.getPayoutProvider(options.payoutId);

			let output;
			if (options.type === "TOP_UP") {
				output = await AdyenProvider.billingPayoutDetailTopUp(options);
			} else {
				output = await paymentProvider.billingPayoutDetailReport(options);
			}
			return output;
		} catch (error) {
			console.log(error);
			throw new Meteor.Error(500, "Problem with report: " + error.message);
		}

	},

	async quickBooksPayoutsReport(payouts) {
		try {
			const exportData = await ReportExportService.quickBooksCsvExport(payouts)
			return exportData;
		} catch (error) {
			console.log(error)
		}
	},

	async fixLineItemsWithoutVoidReport(invoices) {
		try {
			const csvData = await ReportExportService.lineItemsWithoutVoidCsvExport(invoices)
			return csvData;
		} catch (error) {
			console.log(error, 'error')
		}
	},

	async billingPaymentsDetailReport(options) {
		const currentUser =  await Meteor.userAsync();
		const currentOrg = await Orgs.current();
		const dataCache = {};

		await processPermissions({
			assertions: [{ context: "billing/payments", action: "read" }],
			evaluator: (person) => person.type=="admin",
			throwError: true
		});

		moment.tz.setDefault(currentOrg.getTimezone());
		const startDate = new moment(options.startDate, "MM/DD/YYYY").valueOf();
		const endDate = new moment(options.endDate, "MM/DD/YYYY").add(1, "days").valueOf();
		const query = options.query;

		let runningTotal = 0.0, payments = [];
		const allInvoices = await Invoices.find({orgId: currentUser.orgId, "credits.createdAt": {"$gte": startDate, "$lt": endDate}}).fetchAsync();
		const selectedPaymentTypes = options.paymentType.length > 0 ? options.paymentType : [""];

		for(const paymentType of selectedPaymentTypes) {
			for( const i of  allInvoices) {

				const filteredCredits = _.filter(i.credits, (c) => {
					const filterTimeframe = c.createdAt >= startDate && c.createdAt < endDate,
						filterType = (
								(paymentType.startsWith("payer_") && c.creditReason === "reimbursable" && c.creditPayerSource == paymentType.replace("payer_", "")) ||
								(paymentType === "manual_cash" && (
										(c.creditReason === "manual_payment" && c.creditManualPaymentMethod === "cash") ||
										(c.type === "payment" && c.payment_type === "credit_memo" && c.creditMemoType === "cash") ) ) ||
								(paymentType === "manual_check" && (
										(c.creditReason === "manual_payment" && c.creditManualPaymentMethod === "check") ||
										(c.type === "payment" && c.payment_type === "credit_memo" && c.creditMemoType === "check") ) ) ||
								(paymentType === "manual_card" && (
										(c.creditReason === "manual_payment" && c.creditManualPaymentMethod === "card") ||
										(c.type === "payment" && c.payment_type === "credit_memo" && c.creditMemoType === "manualCard") ) ) ||
								(paymentType === "manual_ach" && (
										(c.creditReason === "manual_payment" && c.creditManualPaymentMethod === "ach") ||
										(c.type === "payment" && c.payment_type === "credit_memo" && c.creditMemoType === "manualAch") ) ) ||
								(paymentType === "payment_ach" && c.type === "payment" && c.payment_type === "bank_account") ||
								(paymentType === "payment_creditmemo" && c.type === "payment" && c.payment_type === "credit_memo") ||
								(paymentType === "payment_card" && c.type === "payment" && c.payment_type === "card") ||
								(paymentType === "security_deposit_refund" && c.type === "security_deposit_refund") ||
								(paymentType === "security_deposit_auto" && c.type === "payment" && c.payment_type === "credit_memo" && c.creditMemoType === "securityDepositAuto")
							);

					if (paymentType !== "" && paymentType !== "other" && filterType && !c.paymentType) {
						c.paymentType = paymentType;
					}

					return filterTimeframe && ( filterType || paymentType === "" || paymentType === "other");
				});

				const invoicePerson = await People.findOneAsync({_id: i.personId, orgId: i.orgId}, {fields:{firstName:1, lastName:1}});

				for( const c of filteredCredits) {

					let typeDescription = "", description = "", payoutInfo;
					switch (c.creditReason || c.type) {
						case "reimbursable":
							typeDescription = "Payer - " + c.creditPayerSource;
							description = (invoicePerson ? "For: " + invoicePerson.lastName + ", " + invoicePerson.firstName : "") +
								(c.creditNote ? (invoicePerson ? " - " : "") + c.creditNote : "");
							break;
						case "manual_payment":
							typeDescription = "Manual payment"
								+ (c.creditManualPaymentMethod ? " - " + c.creditManualPaymentMethod : "");
							description = (c.paidByDesc ? c.paidByDesc : "")
								+ (c.creditNote ? " - " + c.creditNote : "");
							break;
						case "security_deposit_refund":
							typeDescription = "Security deposit refund";
							description = (c.paidByDesc ? c.paidByDesc : "")
								+ (c.creditNote ? " - " + c.creditNote : "");
							break;
						case "bad_debt":
							description = "Write Off - Bad debt"
								+ (c.paidByDesc ? " - " + c.paidByDesc : "")
								+ " - " + c.creditNote;
							break;
						case "agency_write_off":
							description = "Write Off - Agency"
								+ (c.paidByDesc ? " - " + c.paidByDesc : "")
								+ " - " + c.creditNote;
							break;
						case "collections_write_off":
							description = "Write Off - Collections"
								+ (c.paidByDesc ? " - " + c.paidByDesc : "")
								+ " - " + c.creditNote;
							break;
						case "payroll_deduction":
							description = "Payroll deduction"
								+ (c.paidByDesc ? " - " + c.paidByDesc : "")
								+ " - " + c.creditNote;
							break;
						case "payment":
							typeDescription = "Payment - "
							const paymentProvider = await CardProviders.getPaymentProvider(c.adyenInfo?.pspReference);
							switch (c.payment_type) {
								case "bank_account":
									typeDescription += "ACH";
									payoutInfo = options.includePayouts && paymentProvider && await paymentProvider.findPayoutForPayment({invoiceId: i._id, payment: c, dataCache});
									break;
								case "credit_memo":
									typeDescription += "Credit Memo"
									if (c.creditMemoType) {
										const currentType = _.find((await Orgs.current()).availableCreditMemoTypes(), (cmt) => { return cmt.type==c.creditMemoType; })
										if (currentType) typeDescription += " - " + currentType.description;
									}
									break;
								default:
									typeDescription += "Credit Card";
									payoutInfo = options.includePayouts && paymentProvider && await paymentProvider.findPayoutForPayment({invoiceId: i._id, payment: c, dataCache});

							}
							description = c.paidByDesc + (c.creditNote ? " - " + c.creditNote : "");
							break;
						default:
							typeDescription = "Other";
							description = c.creditNote;
							break;
					}

					if (selectedPaymentTypes[0]=="" ||
							(_.contains(selectedPaymentTypes, "other") && typeDescription == "Other") ||
							c.paymentType) {

						const payment = {
							createdAt: c.createdAt,
							type: typeDescription,
							description: description,
							invoiceNumber: i.invoiceNumber,
							invoiceId: i._id,
							coversPeriodDescription: i.coversPeriodDescription(),
							amount: c.amount,
							payoutInfo,
							personName: invoicePerson?.lastName + ", " + invoicePerson?.firstName,
							personId: i.personId,
						}
						payment.checkNumber = c.checkNumber ?? null;
						if (query) {
							const excluded = ['createdAt', 'invoiceId', 'personId'];
							const regexp = new RegExp(query, 'gi');
							for (const prop in payment) {
								if (!excluded.includes(prop) && regexp.test(payment[prop])) {
									payments.push(payment)
									runningTotal += c.amount ? c.amount : 0;
									break;
								}
							}
						} else {
							payments.push(payment);
							runningTotal += c.amount ? c.amount : 0;
						}
	
					}

				};
			};
		};
		return {
			entries: _.sortBy(payments, "createdAt"),
			totalAmount: runningTotal
		};
	},

	async billingPayerReconciliationTransactions(options) {
			const currentOrg = await Orgs.current();
			await processPermissions({
					assertions: [{ context: "billing/payments", action: "edit" }],
					evaluator: (person) => person.type === "admin",
					throwError: true
			});

			// Validate required options
			if (!options.payer || !options.startDate || !options.endDate) {
					throw new Meteor.Error(500, "You must specify all filter options.");
			}

			// Set up date handling
			moment.tz.setDefault(currentOrg.getTimezone());
			const startDateValue = new moment(options.startDate, "MM/DD/YYYY");
			const startDate = startDateValue.format("YYYY-MM-DD");
			const endDateValue = new moment(options.endDate, "MM/DD/YYYY");
			const endDate = endDateValue.format("YYYY-MM-DD");

			// Build query to find relevant invoices
			const query = PayerReconciliationUtils.buildPayerInvoiceQuery(
					currentOrg,
					options.payer,
					startDate,
					endDate,
					startDateValue,
					endDateValue
			);

			// Fetch invoices
			const invoices = await Invoices.find(query).fetchAsync();

			// Group invoices by person
		const invoicesGroupedByPerson = _.groupBy(invoices, "personId");

		// Process each person's invoices

				const groupedInvoices = await PayerReconciliationUtils.processAllPersons(
					invoicesGroupedByPerson,
					startDate,
					endDate,

							startDateValue,
					endDateValue,
					options
			);

			// Return filtered results

		return groupedInvoices.filter(p => p.name && (!options.hidePaidItems || p.matchedLineItems.length > 0)
			);
	},

	async applySecurityDeposit(options) {
		const currentUser =  await Meteor.userAsync(),
			currentOrg = await Orgs.current();

		await processPermissions({
			assertions: [{ context: "billing/payments", action: "edit" }],
			evaluator: (person) => person.type=="admin",
			throwError: true
		});

		const targetPerson = await People.findOneAsync({orgId: currentOrg._id, _id: options.personId}),
			openSecurityDeposits = targetPerson && await targetPerson.openSecurityDeposits(),
			securityDeposit = _.find(openSecurityDeposits, sd => sd.creditMemoId == options.creditMemoId);

		if (!securityDeposit)
			throw new Meteor.Error(500, "Invalid security deposit specified.");

		if (securityDeposit.openAmount < options.amount)
			throw new Meteor.Error(500, "Amount specified is greater than open security deposit amount.");

		const findOwnedRelationships = await targetPerson.findOwnedRelationships();
		if (!_.contains(findOwnedRelationships.map( r=> r.personId), securityDeposit.paidForByPersonId))
			throw new Meteor.Error(500, "Invalid payer selected.");

		const paymentLine = await payInvoiceWithCreditMemo({
			invoiceId: options.invoiceId,
			personId: securityDeposit.paidForByPersonId,
			creditMemoId: options.creditMemoId,
			payment_amount: options.amount
		});

		return paymentLine;
	},

	async applyUnappliedCash(options) {
		const currentUser =  await Meteor.userAsync(),
			currentOrg = await Orgs.current();
		await processPermissions({
			assertions: [{ context: "billing/payments", action: "edit" }],
			evaluator: (person) => person.type=="admin",
			throwError: true
		});

		const targetPerson = await People.findOneAsync({orgId: currentOrg._id, _id: options.personId});
		const openPaymentInvoice = await Invoices.findOneAsync({_id: options.invoiceId});
			const invoice = await Invoices.findOneAsync({ _id: options.creditInvoiceId })

		const unappliedCashCredit = invoice.credits[options.creditIndex];

			if (!unappliedCashCredit) {
				throw new Meteor.Error(500, "Invalid unapplied cash credit specified.");
			}
		const heldAmount = unappliedCashCredit.payerOverpaymentAmountRemaining !== undefined && unappliedCashCredit.payerOverpaymentAmountRemaining !== null ? unappliedCashCredit.payerOverpaymentAmountRemaining : unappliedCashCredit.payerOverpaymentAmount;

			if (heldAmount < options.amount ) {
				throw new Meteor.Error(500, "Amount specified is greater than unapplied cash credit amount.");
			}
			if (openPaymentInvoice.openAmount < options.amount) {
				throw new Meteor.Error(500, "Amount specified is greater than open invoice amount.");
			}
		const paymentLine = await payInvoiceWithUnappliedCash({
			invoiceId: options.invoiceId,
			creditInvoiceId: options.creditInvoiceId,
			personId: options.personId,
			creditIndex: options.creditIndex,
			payment_amount: options.amount,
			creditPayerSource: options.creditPayerSource
		});
			return paymentLine;
	},

	async resolveSecurityDeposit(options) {
		const currentUser =  await Meteor.userAsync(),
		currentOrg = await Orgs.current();
		console.log("in resolve with", options);
		await processPermissions({
			assertions: [{ context: "billing/payments", action: "edit" }],
			evaluator: (person) => person.type=="admin",
			throwError: true
		});

		const targetPerson = await People.findOneAsync({orgId: currentOrg._id, _id: options.personId}),
			openSecurityDeposits = targetPerson && await targetPerson.openSecurityDeposits(),
			securityDepositInvoice = _.find(openSecurityDeposits, sd => sd.invoiceId == options.invoiceId),
			manualResolveCreditMemo = !options.invoiceId && options.manualResolveCreditMemoId && _.find(openSecurityDeposits, osd => osd.creditMemoId == options.manualResolveCreditMemoId);

		if ((!securityDepositInvoice || securityDepositInvoice.length == 0) && !options.manualResolveCreditMemoId)
			throw new Meteor.Error(500, "Invalid security deposit specified or security deposit already resolved.");

		const secDepositQuery = {
			orgId: currentOrg._id,
			"billing.creditMemos": {
				"$elemMatch": {
					"voidedAt": {'$exists': false},
					"type": "securityDepositAuto",
					"openAmount": {"$gte": 0}
				}
			}
		};

		if (options.invoiceId)
			secDepositQuery["billing.creditMemos"]["$elemMatch"]["paidForInvoiceId"] = options.invoiceId;
		else
			secDepositQuery["billing.creditMemos"]["$elemMatch"]["_id"] = manualResolveCreditMemo.creditMemoId;
console.log("secDepositQuery", secDepositQuery);
		const openSecurityDepositCredits = _.chain( await People.find(secDepositQuery).fetchAsync())
			.map( p => p.billing.creditMemos
				.filter( cm => !cm.voidedAt && cm.type == "securityDepositAuto" && (cm.paidForInvoiceId == options.invoiceId || cm._id == manualResolveCreditMemo.creditMemoId) && cm.openAmount > 0)
				.map( cm => { cm.paidByPersonName = p.firstName + " " + p.lastName; cm.paidByPersonId = p._id; return cm; })
			)
			.flatten()
			.value();
console.log("opendeposits", openSecurityDepositCredits);
		if (openSecurityDepositCredits.length > 0) {
			if (options.checkStatus)
				return {rejectionReason: "openCreditMemos", openSecurityDepositCredits};
			else if (options.action) {
				console.log("options to deal:", options);
				if (options.action == "manual_refund") {
					for (const c of openSecurityDepositCredits) {
						console.log("trying to update query", { _id: c.paidByPersonId, "billing.creditMemos._id": c._id });
						const creditMemoSetQuery = {
							"billing.creditMemos.$.openAmount": 0.0,
							"billing.creditMemos.$.refundedAmount": c.openAmount,
							"billing.creditMemos.$.refundedNotes": options.notes,
							"billing.creditMemos.$.refundedAt": new Date().valueOf(),
							"billing.creditMemos.$.refundedByPersonId": currentUser.personId
						};
						if (manualResolveCreditMemo) {
							creditMemoSetQuery["billing.creditMemos.$.manuallyResolvedAt"] = new Date().valueOf();
							creditMemoSetQuery["billing.creditMemos.$.manuallyResolvedByPersonId"] = currentUser.personId;
						}
						await People.updateAsync(
							{ _id: c.paidByPersonId, "billing.creditMemos._id": c._id },
							{ "$set": creditMemoSetQuery }
						);
					}
				} else if (options.action == "void" && !currentOrg.isLedgerExportable()) {
					for (c of openSecurityDepositCredits) {
						const result = await Meteor.callAsync("voidCreditMemo", {personId: c.paidByPersonId, creditMemoId:c._id, notes: options.notes, manuallyResolvedSecurityDeposit: true});
					}
				}
			}
			else
				throw new Meteor.Error(500, "Open credit memos must be closed before security deposit can be resolved.");
		}

		if (!manualResolveCreditMemo) {
			await Invoices.updateAsync({_id: options.invoiceId, "lineItems.originalItem.refundableDeposit": true},
				{
					"$set": {
						"lineItems.$.resolvedBy": currentUser.personId,
						"lineItems.$.resolvedAt": new Date().valueOf()
					}
				});
		}
	},

	async seizeSecurityDeposit(creditMemo) {
		if (creditMemo.openAmount <= 0) {
			throw new Meteor.Error(500, "Security deposit must be open to seize.");
		}
		const paidByPerson = await People.findOneAsync({_id: creditMemo.paidByPersonId});
		const timezone = (await Orgs.current()).getTimezone();
		if (!paidByPerson) {
			throw new Meteor.Error(500, "Cannot find person who paid security deposit.");
		}
		await People.updateAsync(
			{
				"_id": paidByPerson._id,
				"billing.creditMemos._id": creditMemo.creditMemoId
			},
			{
				"$set": {
					"billing.creditMemos.$.openAmount": 0,
					"billing.creditMemos.$.seizedAmount": creditMemo.openAmount,
				},
				"$push": {
					"billing.creditMemos.$.securityActions": {
						"action": "seizedSecurityDeposit",
						"seizedAmount": creditMemo.openAmount,
						"seizedAt": new moment().tz(timezone).valueOf(),
						"seizedBy": creditMemo.seizedBy
					}
				}
			}
		);
		return 'success';
	},

	async reinstateSeizedSecurityDeposit(creditMemo) {
		if (creditMemo.seizedAmount <= 0) {
			throw new Meteor.Error(500, "Seized Amount must be greater than zero to reinstate.");
		}
		const paidByPerson = await People.findOneAsync({_id: creditMemo.paidByPersonId});
		const timezone = (await Orgs.current()).getTimezone();
		if (!paidByPerson) {
			throw new Meteor.Error(500, "Cannot find person who paid security deposit.");
		}
		await People.updateAsync(
			{
				"_id": paidByPerson._id,
				"billing.creditMemos._id": creditMemo.creditMemoId
			},
			{
				"$set": {
					"billing.creditMemos.$.openAmount": creditMemo.seizedAmount,
					"billing.creditMemos.$.reinstatedAmount": creditMemo.seizedAmount,
					"billing.creditMemos.$.seizedAmount": 0,
				},
				"$push": {
					"billing.creditMemos.$.securityActions": {
						"action": "reinstatedSecurityDeposit",
						"reinstatedAmount": creditMemo.seizedAmount,
						"seizeReinstatedAt": new moment().tz(timezone).valueOf(),
						"seizeReinstatedBy": creditMemo.seizeReinstatedBy
					}
				}
			}
		);
		return 'success';
	},

	async billingSecurityDepositsReport(options) {
		const currentUser = await Meteor.userAsync(),
			currentOrg = await Orgs.current();

		await processPermissions({
			assertions: [{context: "billing/payments", action: "read"}],
			evaluator: (person) => person.type == "admin",
			throwError: true
		});

		options = options || {};
		options.includeOpenInvoices = true;
		return await Metrics.getMetric("upcomingWithdrawals", options);
	},

	async manualRefundCreditMemo(options) {
		await processPermissions({
			assertions: [{ context: "billing/payments", action: "edit" }],
			evaluator: (person) => person.type=="admin",
			throwError: true
		});

		const currentUser = await Meteor.userAsync();
		const person = await People.findOneAsync({orgId: currentUser.orgId, _id: options.personId});
		if (!person) throw new Meteor.Error(500, "Cannot find matching person");

		const matchedCreditMemo = _.chain(person)
			.deep("billing.creditMemos")
			.find( cm => cm._id == options.creditMemoId)
			.value();

		if (!matchedCreditMemo) throw new Meteor.Error(500, "Cannot find specified credit memo.");

		return await People.updateAsync({_id: person._id, "billing.creditMemos._id": options.creditMemoId}, {$set:
				{
					"billing.creditMemos.$.openAmount": 0,
					"billing.creditMemos.$.refundedByPersonId": currentUser.personId,
					"billing.creditMemos.$.refundedAt": new moment().valueOf(),
					"billing.creditMemos.$.refundedAmount": parseFloat(matchedCreditMemo.openAmount),
					"billing.creditMemos.$.refundedNotes": options.notes
				}
		});
	},

	async billingUndepositedPayments(options) {

		const currentUser =  await Meteor.userAsync();
		const org = await currentUser.fetchOrg();

		await processPermissions({
			assertions: [{ context: "billing/payments", action: "read" }],
			evaluator: (person) => person.type=="admin",
			throwError: true
		});

		options = options || {};
		const startDateNum = new moment.tz(options.startDate, org.getTimezone()).startOf("day").valueOf(),
			endDateNum = new moment.tz(options.endDate, org.getTimezone()).endOf("day").valueOf();
		const output = [],
			queryFragment = {
				"type": "credit",
				"voidedAt": {"$exists": false},
				"depositBatchId": {"$exists": false},
				"createdAt": {"$gte": startDateNum, "$lt": endDateNum}
			};
		if (!_.isEmpty(options.selectedTypes)) {
			queryFragment["creditManualPaymentMethod"] = {"$in":options.selectedTypes};
			queryFragment["creditReason"] = "manual_payment";
		} else {
			queryFragment["creditReason"] = {"$in": ["manual_payment", "reimbursable"]};
		}
		let query;
		const nonFamilyFundingQuery = {
			depositBatchId: { $exists: false },
			createdAt: { $gte: startDateNum, $lt: endDateNum },
			orgId: org._id
		};
		if (options.depositId) {
			query = {
				orgId: org._id,
				"credits.depositBatchId": options.depositId
			};
			nonFamilyFundingQuery.depositBatchId = options.depositId;
		} else {
			query = {
				orgId: currentUser.orgId,
				"credits": {
					"$elemMatch": queryFragment
				}
			}
		}
		const queryLimit = options.overview && 5;
		const invoiceData = await Invoices.find(query, { sort: { createdAt: -1 }, limit: queryLimit }
		).fetchAsync();
		for (const i of invoiceData) {
			const filteredCredits = _.chain(i.credits)
				.map((c, idx) => { c.creditIndex = idx; return c; })
				.filter(c => (options.depositId && c.depositBatchId === options.depositId) || (!options.depositId
					&& c.type === "credit"
					&& !c.depositBatchId
					&& !c.voidedAt
					&& (c.creditReason || !c.adyenInfo)
					&& c.createdAt >= startDateNum && c.createdAt < endDateNum
					&& (
						(_.isEmpty(options.selectedTypes) && _.contains(["manual_payment", "reimbursable"], c.creditReason))
						||
						(c.creditReason === "manual_payment" && _.contains(options.selectedTypes, c.creditManualPaymentMethod))
					)))
				.value();
		
			for (const fc of filteredCredits) {
				const person = await People.findOneAsync({ orgId: currentUser.orgId, _id: i.personId });
				output.push({
					personId: i.personId,
					invoiceId: i._id,
					invoiceNumber: i.invoiceNumber,
					personName: person.firstName + ' ' + person.lastName,
					creditAmount: fc.amount + (fc.payerOverpaymentAmount ? fc.payerOverpaymentAmount : 0),
					creditDate: fc.createdAt,
					paidByName: fc.creditReason === "manual_payment" ? fc.paidByDesc : fc.creditPayerSource,
					paymentMethod: fc.creditManualPaymentMethod,
					creditIndex: fc.creditIndex,
					note: fc.creditNote,
					depositId: fc.depositBatchId
				});
			}
		}
		// Find credit memos
		let fPeople = await People.find({
			orgId: currentUser.orgId,
			"billing.creditMemos.createdAt": { $gte: startDateNum, $lt: endDateNum },
		}).fetchAsync();

		for (let p of fPeople){
			const filteredCreditMemos = _.filter(p.billing.creditMemos, (cm) => {
				return (
					(_.isEmpty(options.selectedTypes) || _.contains(options.selectedTypes, cm.type)) &&
					cm.createdAt >= startDateNum &&
					cm.createdAt < endDateNum &&
					!cm.voidedBy &&
					cm.type !== "systemOverpayment" &&
					((options.depositId && cm.depositBatchId === options.depositId) ||
						(!options.depositId &&
							!cm.depositBatchId &&
							!cm.voidedAt &&
							(cm.creditReason || !cm.adyenInfo))))
			});
			for (let cm of filteredCreditMemos) {
				const cmType = _.find((await Orgs.current()).availableCreditMemoTypes(), (cmt) => {
					return cmt.type === cm.type;
				});
	
				output.push({
					invoiceId: cm._id,
					creditAmount: cm.originalAmount,
					creditDate: cm.createdAt,
					payerId: p._id,
					paidByName: p.firstName + " " + p.lastName,
					paymentMethod: "Credit Memo" + (cmType ? " - " + cmType.description : ""),
					creditIndex: cm.creditIndex,
					note: cm.notes || "",
					depositId: cm.depositBatchId,
	
				});
				output.sort((a, b) => b.creditDate - a.creditDate);
			}
		}
		// Find non-family funding
		if (org.hasCustomization(AvailableCustomizations.NON_FAMILY_FUNDS_ENABLED)) {
			const funds = await PayerFunds.find(nonFamilyFundingQuery, { sort: { createdAt: -1 }, limit: queryLimit }).fetchAsync();
			for (const fund of funds) {
				const payerName = (org.availablePayerSources(true) || []).find(ps => ps.type === fund.payerSource)?.description ?? '';
				let payerLongName = payerName;
				if (payerName) {
					payerLongName += ' Non-Family Funds';
				}
				output.push({
					fundId: fund._id,
					personName: payerLongName,
					creditAmount: fund.amount,
					creditDate: fund.createdAt,
					paidByName: payerName,
					depositId: fund.depositBatchId
				});
			}
			output.sort((a, b) => b.creditDate - a.creditDate);
			if (queryLimit) {
				output.slice(0, queryLimit);
			}
		}
		let depositInfo;
		if (options.depositId)
			depositInfo = await Deposits.findOneAsync({orgId: org._id, _id: options.depositId});
		return {transactions: output, depositInfo};
	},

	async upsertDeposit(options) {
		await processPermissions({
			assertions: [{ context: "billing/payments", action: "edit" }],
			evaluator: (person) => person.type=="admin",
			throwError: true
		});

        options.currentUser = await Meteor.userAsync();
        options.org = await options.currentUser.fetchOrg();

        await BillingServerUtils.upsertDeposit(options);
	},

	async billingDepositsReport(options) {

		const currentUser =  await Meteor.userAsync(),
			org = await currentUser.fetchOrg();

		await processPermissions({
			assertions: [{ context: "billing/payments", action: "read" }],
			evaluator: (person) => person.type=="admin",
			throwError: true
		});

		const startDate = new moment.tz(options.startDate, org.getTimezone()).startOf("day").format("YYYY-MM-DD"),
			endDate = new moment.tz(options.endDate, org.getTimezone()).endOf("day").format("YYYY-MM-DD");

		const result = await Deposits.find({orgId: org._id, depositDate:{"$gte": startDate, "$lte": endDate}}).fetchAsync();

		return result;
	},

	async removeDeposit(options) {

		const currentUser =  await Meteor.userAsync(),
			org = await currentUser.fetchOrg();

		await processPermissions({
			assertions: [{ context: "billing/payments", action: "edit" }],
			evaluator: (person) => person.type=="admin",
			throwError: true
		});
		await Invoices.updateAsync({orgId: org._id, "credits.depositBatchId": options.depositId}, {
			"$unset": {
				"credits.$.depositBatchId": 1,
				"credits.$.depositBatchIdAssignedAt": 1
			}
		});
		await People.updateAsync({orgId: org._id, "billing.creditMemos.depositBatchId": options.depositId}, {
			"$unset": {
				"billing.creditMemos.$.depositBatchId": 1,
				"billing.creditMemos.$.depositBatchIdAssignedAt": 1
			}
		});
		await Deposits.removeAsync({orgId: org._id, _id: options.depositId});

	},

	async quickEntryInsertSecurityDeposit(options) {

		const currentUser =  await Meteor.userAsync(),
			org = await currentUser.fetchOrg();

		const creditToPerson = await People.findOneAsync({orgId: org._id, _id: options.selectedFamilyId}),
			relationship = await Relationships.findOneAsync({orgId:org._id, targetId: options.selectedPersonId, personId: options.selectedFamilyId, relationshipType:"family"}),
			creditForPerson = relationship && await People.findOneAsync({orgId: org._id, _id: options.selectedPersonId});

		if (!creditToPerson || !creditForPerson)
			throw new Meteor.Error(500, "Invalid relationship specified to create security deposit.");

		await People.updateAsync({_id: creditToPerson._id}, {$push: {"billing.creditMemos": {
			_id: Random.id(),
			type: "securityDepositAuto",
			createdAt: new moment().valueOf(),
			createdBy: creditToPerson._id,
			notes: "Security deposit payment manually created for " + creditForPerson.firstName + " " + creditForPerson.lastName,
			paidForPersonId: creditForPerson._id,
			openAmount: parseFloat(options.amount),
			originalAmount: parseFloat(options.amount),
			sourceQuickEntry: true
		}}});
		const timestamp = new moment().format("M/D/YY h:mm a");
		return `Created security deposit entry of ${numeral(options.amount).format("$0.00")} for ${creditForPerson.firstName + " " + creditForPerson.lastName} at ${timestamp}.`;
 	},

 	async onlineRefundCreditMemo(options) {

		const currentUser =  await Meteor.userAsync(),
			org = await currentUser.fetchOrg();

		await processPermissions({
			assertions: [{ context: "billing/payments", action: "edit" }],
			evaluator: (person) => person.type=="admin",
			throwError: true
		});

    	const person = await People.findOneAsync({orgId: org._id, _id: options.personId}),
			creditMemo = person.billing.creditMemos.find( cm => cm._id == options.creditMemoId),
			paymentInvoice = await Invoices.findOneAsync({_id: creditMemo.paidForInvoiceId}),
			refundableCharges = paymentInvoice.getRefundableCharges().charges.filter( c => c.paidByPersonId == person._id);

		if (!options.refund_amount) {
			return {paymentInvoice, refundableCharges};
		} else {
			const refundedCharge = refundableCharges.find(rc => rc.id == options.charge_id);

			if (!refundedCharge || options.refundAmount > refundedCharge.amount) {
				throw new Meteor.Error(500, "Cannot specify more than the original charge");
			}

			if (options.refundAmount > creditMemo.openAmount) {
				throw new Meteor.Error(500, "Cannot specify more than the open securty deposit amount");
			}
			const paymentProvider = await CardProviders.getPaymentProvider(options.charge_id);

			const refundLine = await paymentProvider.refundCharge({
				refund_amount: options.refund_amount,
				noChangeOpenAmount: true,
				creditedByPersonId: currentUser.personId,
				charge_id: options.charge_id,
				refund_reason: "online_security_deposit_refund",
				invoiceId: paymentInvoice._id,
				refund_note: options.refund_note,
				currentUser,
			});
			await People.updateAsync({ _id:person._id, "billing.creditMemos._id": options.creditMemoId},
				{"$inc": {
						"billing.creditMemos.$.openAmount": -1 * options.refund_amount
					}});
		}
	},

	async getQueuedPaymentsInfo(options) {
		const currentUser =  await Meteor.userAsync();
		const userPerson = await currentUser.fetchPerson(),
			org = await currentUser.fetchOrg();

		await processPermissions({
			assertions: [{ context: "billing/payments/achGeneration", action: "edit" }],
			evaluator: (person) => person.type=="admin",
			throwError: true
		});

		if (!org.hasCustomization("billing/queueAutopayments/enabled"))
			throw new Meteor.Error(403, "You are not authorized to generate a payment file.");

		const autoPaymentInvoices = await Invoices.find({
			orgId: org._id,
			"credits":{"$elemMatch": {"queueInfo": {"$exists": true}, "queueInfo.processed": {"$ne": true}, "payment_type": "bank_account"}}
		}).fetchAsync();

		let transactionCount = 0, transactionTotal = 0;
		for (invoice of autoPaymentInvoices) {
			for ([paymentIdx, queuedPayment] of invoice.credits.filter( c => c.queueInfo && !c.queueInfo.processed && c.payment_type == "bank_account").entries()) {
				transactionCount += 1;
				transactionTotal += queuedPayment.amount;
			}
		}
		const recentBatches = await PaymentBatches.find({orgId: org._id}, {sort:{createdAt:-1}, limit:10}).fetchAsync();
		return {
			recentBatches,
			transactionCount,
			transactionTotal
		}
	},

	async processPaymentTypeImport(options) {
		const currentUser =  await Meteor.userAsync();
		const userPerson = await currentUser.fetchPerson(),
			org = await currentUser.fetchOrg();

		await processPermissions({
			assertions: [ { context: "billing/payments/achGeneration", action: "edit" }],
			evaluator: (person) => person.type=="admin",
			throwError: true
		});

		let orgIds = [org._id];

		const orgsScope = await userPerson.findScopedOrgs(),
			orgsScopeList = orgsScope && _.pluck(orgsScope, "_id");
		orgIds = orgIds.concat(orgsScopeList);

		const orgs = await Orgs.find({_id:{"$in": orgIds}}).fetchAsync();

		const data = Papa.parse(options.fileContents);

		if (!data.data || data.data.length == 0) {
			throw new Meteor.Error(500, "There was no data found in file.");
		}

		if (data.data[0].length != 7) {
			throw new Meteor.Error(500, "Invalid number of columns in file. Should be 7.");
		}

		const processAccounts = async function() {
			let results = [];
			for (let i = 0; i < data.data.length; i++) {
				const rec = data.data[i],
					recLabel = `${i}: ${rec[0]}:`,
					orgName = rec[6],
					foundOrg = orgs.find( o => o.name == orgName);
				if (!foundOrg) {
					results.push(`${recLabel} org not matched to current available orgs`);
					continue;
				}
				const matchedPerson = await People.findOneAsync({orgId: foundOrg._id, profileEmailAddress: rec[3]});
				if (!matchedPerson) {
					results.push(`${recLabel} person record not found with email '${rec[3]}'`);
					continue;
				}
				if (!_.isEmpty(matchedPerson?.billing?.adyenInfo?.sources?.bank_account)) {
					results.push(`${recLabel} person already has bank account (skipped)`);
					continue;
				}
				if (options.importMode == "live") {
					const useOwnerName = rec[5] || rec[0],
						ownerNameSplits = useOwnerName.split(","),
						ownerName = (ownerNameSplits.length > 0 ? `${ownerNameSplits[1]} ${ownerNameSplits[0]}` : ownerNameSplits[0]).trim();

					const paymentMethodResult = await AdyenProvider.addBankAccount({
						orgId: foundOrg._id,
						personId: matchedPerson._id,
						bankAccountNumber: rec[1],
						bankLocationId: rec[2],
						ownerName
					});
					if (paymentMethodResult?.error)
						results.push(`${recLabel} failed to add payment method: ${paymentMethodResult.error}`);

					else
						results.push(`${recLabel} added payment method successfully`);
				} else {
					results.push(`${recLabel} passed verification`);
				}
			}
			return results;
		}

		if (options.importMode == "live") {
			Meteor.defer( async function () {
				const accountsResults = await processAccounts();
				const emailAdd = userPerson.getEmailAddress();

				const emailOptions = {
					from: "LineLeader support <<EMAIL>>",
					to: emailAdd,
					subject: `Your account upload is now complete`,
					text: "Here are the results from your account upload:\n\n" + accountsResults.join("\n"),
				};

				try {
					await Email.sendAsync(emailOptions);
				} catch (err) {
					console.log("Error sending payment batch email", err);
				}
				console.log("Live card import processing completed.");
			});
			return { results: "Your file has been submitted successfully for processing.  You will receive an email when it is complete."}
		} else {
			const accountsResults = await processAccounts();
			return { results: accountsResults.join("\n")}
		}


	},
	async getPayerReconciliations(options) {
		const currentUser = await Meteor.userAsync();

		await processPermissions({
			assertions: [{ context: "billing/payments", action: "read" }],
			evaluator: (person) => person.type=="admin",
			throwError: true
		});

		// Convert date strings to timestamps for query
		const startDate = new moment(options.startDate, "YYYY-MM-DD").startOf('day').valueOf();
		const endDate = new moment(options.endDate, "YYYY-MM-DD").endOf('day').valueOf();

		// Query the PayerReconciliationBatches collection
		const reconciliations = await PayerReconciliationBatches.find({
			orgId: currentUser.orgId,
			date: { $gte: startDate, $lte: endDate }
		}, {
			sort: { date: -1 }
		}).fetchAsync();

		// Map the results to the expected format
		const results = reconciliations.map(rec => ({
			_id: rec._id,
			batchId: rec._id, // Using _id as batchId for direct lookup
			date: rec.date,
			batchLabel: rec.batchLabel,
			payer: rec.payer,
			payerName: rec.payerName,
			amount: rec.totalAmount,
			checkNumber: rec.checkNumber,
			csvUrl: rec.csvUrl,
			csvContent: rec.csvContent,
			csvStorageError: rec.csvStorageError,
			documentUrl: rec.documentUrl,
			supportingDocumentId: rec.supportingDocumentId,
			fileName: rec.fileName,
			fileType: rec.fileType
		}));

		return {
			results,
			dateRange: {
				startDate: options.startDate,
				endDate: options.endDate
			}
		};
	},

	async generateReconciliationCsv({ batchId }) {
		const currentUser = await Meteor.userAsync();
		if (!currentUser) {
			throw new Meteor.Error('not-authorized', 'You must be logged in to generate CSV files');
		}
		
		const batch = await PayerReconciliationBatches.findOneAsync({ 
			_id: batchId,
			orgId: currentUser.orgId 
		});
		
		if (!batch) {
			console.error('Reconciliation batch not found:', { batchId, orgId: currentUser.orgId });
			throw new Meteor.Error('not-found', 'Reconciliation batch not found');
		}

		// If CSV already exists, return it
		if (batch.csvContent) {
			return { 
				csvContent: batch.csvContent,
				downloadUrl: batch.csvUrl 
			};
		}
		
		// Generate CSV data
		const csvData = generateReconciliationCsv(
			batch.allocations || [],
			batch.batchLabel,
			batch.startDate,
			batch.endDate,
			batch.payerName || batch.payer,
			batch.totalNumberDays,
			batch.totalAmount
		);
		
		try {
			const metaContext = { tokenId: Random.id() };
			const uploader = new Slingshot.Upload("mySupportingDocumentUploads", metaContext);
			
			const fileName = `reconciliations/${currentUser.orgId}/${batch.batchLabel.replace(/[^a-zA-Z0-9-_]/g, '_')}_${moment().format('YYYY-MM-DD-HHmmss')}.csv`;
			const downloadUrl = await uploader.send(csvData, fileName);
			await PayerReconciliationBatches.updateAsync(
				{ _id: batchId },
				{ 
					$set: { 
						csvUrl: downloadUrl,
						csvContent: csvData,
						csvContentSize: csvData.length,
						csvGeneratedAt: new Date().valueOf()
					}
				}
			);
			
			return { csvContent: csvData, downloadUrl };
			
		} catch (uploadError) {
			console.error("Error uploading CSV to cloud storage:", uploadError);
			try {
				await PayerReconciliationBatches.updateAsync(
					{ _id: batchId },
					{ 
						$set: { 
							csvContent: csvData,
							csvContentSize: csvData.length,
							csvStorageError: uploadError.message,
							csvGeneratedAt: new Date().valueOf()
						}
					}
				);
				
				return { 
					csvContent: csvData, 
					downloadUrl: null,
					storageError: uploadError.message 
				};
				
			} catch (dbError) {
				console.error("Error saving CSV to database:", dbError);
				throw new Meteor.Error('csv-generation-failed', 'Failed to generate and store CSV file: ' + dbError.message);
			}
		}
	},

	async generateAchFile(options) {
		const currentUser =  await Meteor.userAsync();
		const userPerson = await currentUser.fetchPerson(),
			org = await currentUser.fetchOrg();

		await processPermissions({
			assertions: [ { context: "billing/payments/achGeneration", action: "edit" }],
			evaluator: (person) => person.type=="admin",
			throwError: true
		});

		const orgAddlConfig = await OrgAdditionalDatas.findForOrg(org._id);
		if (!orgAddlConfig?.data?.achGeneration)
			throw new Meteor.Error(500, "No configuration data on file for batch file generation.");
		const clientIP = this.connection.clientAddress;
		console.log("generate ach file called from IP:", clientIP);

		//if (!_.contains(orgAddlConfig.data.achGeneration.ipWhitelist, clientIP))
		//	throw new Meteor.Error(500, `Address ${clientIP} is not whitelisted for running batch file generation.`);

		const authCode = options["auth-code"],
			cutoff = new Date().valueOf() - (20 * 60 * 1000),
			matchingTwoFactor = await TwoFactorCodes.findOneAsync({userId: currentUser._id, code: authCode, createdAt:{"$gt": cutoff}});
		await TwoFactorCodes.removeAsync({userId: currentUser._id});
		if (!matchingTwoFactor)
			throw new Meteor.Error(500, "No matching authorization code found. Please try sending again.");

		if (!org.hasCustomization("billing/queueAutopayments/enabled"))
			throw new Meteor.Error(403, "You are not authorized to generate a payment file.");

		if (!options["payment-date"])
			throw new Meteor.Error(500, "A valid payment date must be selected.");

		const companyRouting = options["company-routing"],
			companyAccountNumber = options["company-account"],
			companyBankName = options["account-name"],
			companyIdentifier = options["company-identifier"],
			companyName = options["company-name"],
			paymentDate = new moment(options["payment-date"], "YYYY-MM-DD").format("YYMMDD"),
			publicKeyArmored = orgAddlConfig.data.achGeneration.publicKey,
			saveData = options["save-data"] == "on";

		if (!publicKeyArmored)
			throw new Meteor.Error(500, "A valid PGP encryption key is required.");

		const openpgp = require("openpgp");
		const publicKey = await openpgp.readKey({ armoredKey: publicKeyArmored });

		if (saveData) {
			await OrgAdditionalDatas.updateAsync({_id: orgAddlConfig._id}, {"$set": {
				"data.achGeneration.companyRouting": companyRouting,
				"data.achGeneration.companyAccountNumber": companyAccountNumber,
				"data.achGeneration.companyBankName": companyBankName,
				"data.achGeneration.companyIdentifier": companyIdentifier,
				"data.achGeneration.companyName": companyName
			}});
		}

		let orgIds = [org._id];
		if (options.orgIds && options.orgIds.length > 0) {
			const orgsScope = await userPerson.findScopedOrgs();
			const orgsScopeList = orgsScope && _.pluck(orgsScope, "_id");
			orgIds = _.intersection(orgsScopeList, options.orgIds);
		}

		const autoPaymentInvoices = await Invoices.find({
			orgId: {"$in": orgIds},
			"credits":{"$elemMatch": {"queueInfo": {"$exists": true}, "queueInfo.processed": {"$ne": true}, "payment_type": "bank_account"}}
		}).fetchAsync();

		if (autoPaymentInvoices.length === 0) {
			throw new Meteor.Error(500, "There are currently no transactions available to generate a file.");
		}
		Meteor.defer( async function() {
			try {
				console.log("beginning ach file generation");
				let transactions = [], issues = [];
				for (invoice of autoPaymentInvoices) {
					console.log("on invoice:", invoice._id);
					for ( i = 0; i < invoice.credits.length; i++) { invoice.credits[i].paymentIdx = i; }
					for (queuedPayment of invoice.credits.filter( c => c.queueInfo && !c.queueInfo.processed && c.payment_type == "bank_account")) {
						const payerPerson = await People.findOneAsync({orgId: {$in: orgIds}, _id: queuedPayment.paidBy });
						if (!payerPerson) {
							issues.push('Could not find payer for invoice id ' + invoice._id + ' credit ' + queuedPayment.paidByDesc);
						} else {
							//retrieve account info from Adyen
							const subscription = await AdyenProvider.retrieveSubscriptionInfo({personId:payerPerson._id, paymentType: "bank_account"});
							if (!subscription || !subscription.bank || !subscription.bank.bankLocationId || subscription.bank.bankLocationId.length != 9) {
								issues.push('No active adyen subscription for person id ' + payerPerson._id + ' on invoice id ' + invoice._id);
							} else {
								transactions.push({
									paymentIdx: queuedPayment.paymentIdx,
									queuedPayment,
									subscription,
									invoice,
									payerPerson
								});
							}

						}
					}
				}

				if (transactions.length > 0) {
					let outputFile = "";
					let counter = 0, sumTotal = 0, hashTotal = 0;

					const batchId = await PaymentBatches.insertAsync({
						orgId: org._id,
						orgIds,
						createdAt: new Date().valueOf(),
						createdBy: userPerson._id
					});

				//file header record
				outputFile += '101'
					+ ' *********'
					+ getPaddedField(companyIdentifier, 10, 'left', ' ')
					+ new moment().format("YYMMDDHHmm")
					+ 'A'
					+ '094101'
					+ getPaddedField("OAKSTAR BANK", 23, 'left', ' ')
					+ getPaddedField(companyName, 23, 'left', ' ')
					+ "\n";

				//batch header record
				outputFile += '5200'
					+ getPaddedField(companyName, 16, 'left', ' ')
					+ getPaddedField(' ', 20, 'left', ' ')
					+ getPaddedField(companyIdentifier, 10, 'left', ' ')
					+ 'PPD'
					+ getPaddedField('Payment', 10, 'left', ' ')
					+ paymentDate
					+ paymentDate
					+ '   1'
					+ '********'
					+ '0000001'
					+ "\n";

				for (transaction of transactions) {
					//entry detail record
					outputFile += '627'
						+ getPaddedField(transaction.subscription.bank.bankLocationId, 9)
						+ getPaddedField(transaction.subscription.bank.bankAccountNumber, 17, 'left', ' ')
						+ getPaddedField(Math.round(transaction.queuedPayment.amount * 100), 10, 'right', '0')
						+ getPaddedField(transaction.payerPerson._id, 15, 'left', ' ')
						+ getPaddedField(transaction.payerPerson.lastName + ", " + transaction.payerPerson.firstName, 22, 'left', ' ')
						+ '  '
						+ '*********'
						+ getPaddedField(counter + 1, 7, 'right', '0')
						+ "\n";

					counter += 1;
					sumTotal += transaction.queuedPayment.amount * 100;
					hashTotal += parseInt(transaction.subscription.bank.bankLocationId.slice(0,8));
					const creditUpdateSetQuery = {},
						creditUpdateKey = `credits.${transaction.paymentIdx}.queueInfo`;
					creditUpdateSetQuery[`${creditUpdateKey}.processed`] = true;
					creditUpdateSetQuery[`${creditUpdateKey}.paymentBatchId`] = batchId;
					console.log("credit update query", JSON.stringify(creditUpdateSetQuery, null, 2));
					await Invoices.updateAsync({_id: transaction.invoice._id}, {"$set": creditUpdateSetQuery});
				}

				// payee record
				outputFile += '622'
					+ getPaddedField(companyRouting, 9)
					+ getPaddedField(companyAccountNumber, 17, 'left', ' ')
					+ getPaddedField(Math.round(sumTotal), 10, 'right', '0')
					+ getPaddedField('', 15, 'left', ' ')
					+ getPaddedField(companyBankName, 22, 'left', ' ')
					+ '  '
					+ '*********'
					+ getPaddedField(counter + 1, 7, 'right', '0')
					+ "\n";
				hashTotal += parseInt(companyRouting.slice(0,8));

				// company/batch control record
				const entryHash = hashTotal.toString().slice(-10);
				outputFile += '8200'
					+ getPaddedField(counter, 6, 'right', '0')
					+ getPaddedField(entryHash, 10, 'right', '0')
					+ getPaddedField(Math.round(sumTotal), 12, 'right', '0')
					+ getPaddedField(Math.round(sumTotal), 12, 'right', '0')
					+ companyIdentifier
					+ getPaddedField(' ', 24, 'left', ' ')
					+ "*********"
					+ "0000001"
					+ "\n";

				// file control record
				outputFile += "*************"
					+ getPaddedField(counter, 8, 'right', '0')
					+ getPaddedField(entryHash, 10, 'right', '0')
					+ getPaddedField(Math.round(sumTotal), 12, 'right', '0')
					+ getPaddedField(Math.round(sumTotal), 12, 'right', '0')
					+ "\n";

				const datetimestamp = new moment().format("YYYYMMDDHHmmss");
				const fileKey = "ach_tx_" + batchId + "_" + datetimestamp + ".txt";

					const encrypted = await openpgp.encrypt({
						message: await openpgp.createMessage({ text: outputFile}),
						encryptionKeys: publicKey
					});

					const emailAdd = await userPerson.getEmailAddress();
					const emailOptions = {
						from: "LineLeader support <<EMAIL>>",
						to: emailAdd,
						subject: `Your batch file is now complete`,
						html: "Please find attached the recent batch you requested to be generated.",
						attachments: [
							{
								filename: fileKey,
								content: encrypted //outputFile
							}
						]
					};

					try {
						await Email.sendAsync(emailOptions);
						console.log("email sent with", emailOptions);
					} catch (err) {
						console.log("Error sending payment batch email", err);
					}

					await PaymentBatches.updateAsync({_id: batchId}, {"$set": {
						"uploadedAt": new Date().valueOf(),
						"fileKey": fileKey,
						"itemCount": counter,
						"sumTotalInCents": sumTotal,
						issues
					}});
					console.log("ach file generation complete");
				}
			} catch (err) {
				console.log("Error within ach file generation", err);
			}
		});

	}
});

function getPaddedField(val, len, justify, char) {
	const truncated = val.toString().slice(0, len),
		justified = justify == "right" ? truncated.padStart(len, char) : truncated.padEnd(len, char);
	return justified;
}

export const payInvoiceWithCashnet = async (options) => {
	const currentPerson = await People.findOneAsync({ _id: options.personId});
	const invoice = await Invoices.findOneAsync({_id: options.invoiceId});
	const currentOrg = currentPerson && await Orgs.findOneAsync({_id:currentPerson.orgId});

	const paidByDesc = currentPerson.firstName + " " + currentPerson.lastName;

	if (!currentPerson || !invoice || !currentOrg)
		throw new Meteor.Error(500, "Invalid request.");

	let original_payment_amount = options.payment_amount;

	// handle overpayment
	let overpayment_amount = 0.00, applied_amount = original_payment_amount;
	if (original_payment_amount > invoice.openAmount) {
		//applied_amount = invoice.openAmount;
		//overpayment_amount = roundToTwo(original_payment_amount - invoice.openAmount);
		throw new Meteor.Error(500, "Cannot overpay an invoice.");
	}

	const paymentLine = {
		type: "payment",
		payment_type: options.cashnetResponse.pmttype == "CC" ? "card" : "bank_account",
		amount: applied_amount,
		overpaymentAmount: overpayment_amount,
		createdAt: new Date().valueOf(),
		paidBy: options.personId,
		paidByDesc: paidByDesc,
		cashnetInfo: options.cashnetResponse
	};
	await Invoices.updateAsync({_id: options.invoiceId},
		{$set: {openAmount: invoice.openAmount - applied_amount},
			$push: {credits: paymentLine}
		},
	);
	processPostPaymentLogic({invoiceId: options.invoiceId, paymentLine});
	if (overpayment_amount > 0) {
		await People.updateAsync({_id: options.personId}, {$push: {"billing.creditMemos": {
			_id: Random.id(),
			type: "systemOverpayment",
			createdAt: new moment().valueOf(),
			createdBy: currentPerson._id,
			notes: "Overpayment on invoice " + invoice.invoiceNumber,
			openAmount: parseFloat(overpayment_amount),
			originalAmount: parseFloat(overpayment_amount)
		}}});
	}

	Meteor.defer( async function() {
		await processBillingEmail({emailType: 'receipt', invoiceId: options.invoiceId, personId: options.personId, paymentLine});
    });

	return paymentLine;
}

export const payInvoiceWithCreditMemo = async (options) => {
	const currentPerson = await People.findOneAsync({ _id: options.personId});
	const invoice = await Invoices.findOneAsync({_id: options.invoiceId});
	const currentOrg = options.orgId ? await Orgs.findOneAsync({_id:options.orgId}) : await Orgs.current();
	const creditMemoId = options.creditMemoId;

	const paidByDesc = currentPerson.firstName + " " + currentPerson.lastName;

	//verify available credit balance
	const creditMemo = _.find(currentPerson.availableCreditMemos(), (cm)=>{return cm._id==creditMemoId});

	const creditAmount = BillingUtils.roundToTwo(creditMemo.openAmount > options.payment_amount ? options.payment_amount : creditMemo.openAmount);

	if (creditAmount <= 0) {
		return;
	}
	//create credit line
	let paymentLine = {
		type: "payment",
		payment_type: "credit_memo",
		amount: creditAmount,
		createdAt: new Date().valueOf(),
		paidBy: options.personId,
		paidByDesc: paidByDesc,
		creditMemoId: options.creditMemoId,
		creditNote: creditMemo.notes,
		creditMemoType : creditMemo.type
	};

	if (creditMemo.type.startsWith("prepaid_") || (currentOrg.hasCustomization("billing/applyExcessSubsidyOnlyToPayer") && creditMemo.type.startsWith("excess_"))) {
		paymentLine.creditReason = "reimbursable";
		paymentLine.creditPayerSource = creditMemo.type.replace("prepaid_", "").replace("excess_", "");
		delete paymentLine.paidBy;
		paymentLine.type = "reimbursement";
		paymentLine.paidByDesc = creditMemo.type;
	}

	let query = {$push: {credits: paymentLine}};
	if (paymentLine.creditReason !== "reimbursable") {
		query["$inc"] = { openAmount: BillingUtils.roundToTwo(-1.0 * options.payment_amount) };
	}
	Log.info('payInvoiceWithCreditMemo', options.invoiceId, query);
	await Invoices.updateAsync({_id: options.invoiceId}, query);

	//reduce available credit balance
	await People.updateAsync({_id: currentPerson._id, "billing.creditMemos._id": creditMemoId }, {"$inc": {"billing.creditMemos.$.openAmount": BillingUtils.roundToTwo(-1.0 * creditAmount)}});
	return paymentLine;
}

const payInvoiceWithUnappliedCash = async (options) => {
	const invoice = await Invoices.findOneAsync({_id: options.invoiceId});
	const currentOrg = options.orgId ? await Orgs.findOneAsync({_id:options.orgId}) : await Orgs.current();
	const creditMemoIndex = options.creditIndex;
	const paidByDesc = "Unapplied Cash";
	const creditPayerSource = options.creditPayerSource;

	const creditMemo = (await Invoices.findOneAsync({_id: options.creditInvoiceId})).credits[creditMemoIndex];

	const cashAmount = creditMemo.payerOverpaymentAmount ? creditMemo.payerOverpaymentAmount : creditMemo.payerOverpaymentAmountRemaining;
	const creditAmount = BillingUtils.roundToTwo(cashAmount > options.payment_amount ? options.payment_amount : cashAmount);
	const newCreditMemoId = Random.id();
	let paymentLine = {
		type: "payment",
		payment_type: "credit_memo",
		amount: creditAmount,
		payerOverpaymentAmount: creditAmount,
		createdAt: new Date().valueOf(),
		paidBy: 'unappliedCash',
		paidByDesc: paidByDesc,
		creditMemoIndex: creditMemoIndex,
		creditMemoType : 'unappliedCashAuto',
		creditMemoId: newCreditMemoId,
	};

	let query = {$push: {credits: paymentLine}};
	if (paymentLine.creditReason != "reimbursable") {
		query["$inc"] = { openAmount: BillingUtils.roundToTwo(-1.0 * options.payment_amount) };
	}
	Log.info('payInvoiceWithUnappliedCash', options.invoiceId, query);
	await Invoices.updateAsync({_id: options.invoiceId}, query);
	const creditIndex = options.creditIndex;
	const amountToSubtract = creditAmount;

	const creditInvoice = await Invoices.findOneAsync({_id: options.creditInvoiceId});
	if (creditInvoice.credits[creditIndex].payerOverpaymentAmountRemaining) {
		const newRemainingAmount = BillingUtils.roundToTwo(creditInvoice.credits[creditIndex].payerOverpaymentAmountRemaining - amountToSubtract);
		await Invoices.updateAsync(
			{ _id: options.creditInvoiceId },
			{ $set: { ["credits." + creditIndex + ".payerOverpaymentAmountRemaining"]: newRemainingAmount } }
		);
	} else {
		const newRemainingAmount = BillingUtils.roundToTwo(creditInvoice.credits[creditIndex].payerOverpaymentAmount - amountToSubtract);
		await Invoices.updateAsync(
			{ _id: options.creditInvoiceId },
			{ $set: { ["credits." + creditIndex + ".payerOverpaymentAmountRemaining"]: newRemainingAmount } }
		);
	}
	return paymentLine;
}

export const MappedLedgerEntriesForRange = async function(options) {
	const org = await Orgs.findOneAsync(options.orgId);
	const undepositedAccountName = _.deep(org,"billing.billingMaps.undepositedFunds.accountName");
	const includeBankDeposits = (undepositedAccountName && true ) || options.includeBankDeposits;

	const ledgerEntryGroups = await LedgerDetailService.LedgerEntriesForRange({
		startDate:options.startDate,
		endDate: options.endDate,
		orgId: options.orgId,
		includeTransactions: options.includeTransactions,
		includeLinkedDetails: options.includeLinkedDetails,
		includeBankDeposits,
		periodStartDate: options.periodStartDate,
		periodStartDateEnd: options.periodStartDateEnd,
		includeLocationUser: options.includeLocationUser
	});

	const arAccountName = _.deep(org,"billing.billingMaps.accountsReceivable.accountName");
	const cashAccountName = _.deep(org,"billing.billingMaps.manualPaymentDeposits.accountName") || "Cash";
	const customerPayableAccountName = _.deep(org,"billing.billingMaps.customerLiabilityPayable.accountName") || "Customer Liability/Payable";
	let outputTransactions = [];
	let arInvoiceEntry = {
		accountName:  arAccountName || "1205",
		accountNameInternal: "accountsReceivable",
		memo: "",
		groupName: "Other",
		description: "Accounts Receivable",
		customerName: "MomentPath",
		debitAmount: 0,
		creditAmount: 0,
		linkedDetails: []
	};

	const includeCash = options?.cashOffsetFilter !== "no-cash";
	const includeNonCash = options?.cashOffsetFilter !== "cash-only";
	const cashOnly = options?.cashOffsetFilter === "cash-only";
	const noCash = options?.cashOffsetFilter === "no-cash";
	const excludeAR = cashOnly || noCash;

	_.each(ledgerEntryGroups, (leg, legKey) => {
		_.each(leg.itemsGroup, (ig, igKey) => {
			let entry = {
				accountName: ig.ledgerAccount ? ig.ledgerAccount.accountName : "",
				accountDescription: ig.ledgerAccountDetail ? ig.ledgerAccountDetail.description : "",
				accountPlanOrItemId: ig.accountPlanOrItemId,
				debitAmount: 0,
				creditAmount: 0,
				groupName: legKey,
				description: igKey,
				transactions: ig.transactions,
				linkedDetails: ig.linkedDetails
			};

			if (!ig.ledgerAccount) {
				return;
			}
			entry.glImportIgnore = ig.ledgerAccount.glImportIgnore;
			//non-cash transactions
			if (includeNonCash) {
				if (((!leg.reversal && ig.ledgerAccount.type === ledgerAccountTypes.REVENUE) || (leg.reversal && ig.ledgerAccount.type === ledgerAccountTypes.DISCOUNT))) {
					arInvoiceEntry.debitAmount += BillingUtils.roundToTwo(ig.amount);
					entry.creditAmount = BillingUtils.roundToTwo(ig.amount);
					LedgerDetailServiceUtils.pushAccountReceivableEntry(entry, "debit", arInvoiceEntry);
				}
				if (((!leg.reversal && ig.ledgerAccount.type === ledgerAccountTypes.DISCOUNT) || (leg.reversal && ig.ledgerAccount.type === ledgerAccountTypes.REVENUE))) {
					arInvoiceEntry.creditAmount += BillingUtils.roundToTwo(ig.amount);
					entry.debitAmount = BillingUtils.roundToTwo(ig.amount);
					LedgerDetailServiceUtils.pushAccountReceivableEntry(entry, "credit", arInvoiceEntry);
				}
				if (ig.ledgerAccount.type === ledgerAccountTypes.PAYMENT_MANUAL) {
					arInvoiceEntry.creditAmount += BillingUtils.roundToTwo(ig.amount);
					entry.debitAmount = BillingUtils.roundToTwo(ig.amount);
					LedgerDetailServiceUtils.pushAccountReceivableEntry(entry, "credit", arInvoiceEntry);
				}

				if (ig.ledgerAccount.type === ledgerAccountTypes.PAYROLL_DEDUCTION) {
					arInvoiceEntry.creditAmount += BillingUtils.roundToTwo(ig.amount);
					entry.debitAmount = BillingUtils.roundToTwo(ig.amount);
					entry.cashOffset = true;
					LedgerDetailServiceUtils.pushAccountReceivableEntry(entry, "credit", arInvoiceEntry);
				}
				if (ig.ledgerAccount.type === ledgerAccountTypes.PAYMENT_ADJUSTED) {
					arInvoiceEntry.debitAmount += BillingUtils.roundToTwo(ig.amount);
					entry.creditAmount = BillingUtils.roundToTwo(ig.amount);
					LedgerDetailServiceUtils.pushAccountReceivableEntry(entry, "debit", arInvoiceEntry);
				}
				if (ig.ledgerAccount.type === ledgerAccountTypes.PAYMENT_MANUAL_REFUNDED) {
					arInvoiceEntry.debitAmount += BillingUtils.roundToTwo(ig.amount);
					entry.creditAmount = BillingUtils.roundToTwo(ig.amount);
					LedgerDetailServiceUtils.pushAccountReceivableEntry(entry, "debit", arInvoiceEntry);
				}
				if (ig.ledgerAccount.type === ledgerAccountTypes.PAYMENT_SETTLEMENT_FEE) {
					if (ig.amount > 0) {
						arInvoiceEntry.creditAmount += BillingUtils.roundToTwo(ig.amount);
						entry.debitAmount = BillingUtils.roundToTwo(ig.amount);
						LedgerDetailServiceUtils.pushAccountReceivableEntry(entry, "credit", arInvoiceEntry);
					} else {
						arInvoiceEntry.debitAmount += BillingUtils.roundToTwo(ig.amount * -1);
						entry.creditAmount = BillingUtils.roundToTwo(ig.amount * -1);
						ig.amount = ig.amount * -1;
						LedgerDetailServiceUtils.pushAccountReceivableEntry(entry, "debit", arInvoiceEntry);
					}
				}
				if (ig.ledgerAccount.type === ledgerAccountTypes.PAYMENT_REFUNDED) {
					arInvoiceEntry.creditAmount += BillingUtils.roundToTwo(ig.amount);
					entry.debitAmount = BillingUtils.roundToTwo(ig.amount);
					LedgerDetailServiceUtils.pushAccountReceivableEntry(entry, "credit", arInvoiceEntry);
				}
				if (ig.ledgerAccount.type === ledgerAccountTypes.PAYMENT_VOIDED) {
					arInvoiceEntry.debitAmount += BillingUtils.roundToTwo(ig.amount);
					entry.creditAmount = BillingUtils.roundToTwo(ig.amount);
					LedgerDetailServiceUtils.pushAccountReceivableEntry(entry, "debit", arInvoiceEntry);
				}
				if (ig.ledgerAccount.type === ledgerAccountTypes.WRITE_OFF_BAD_DEBT) {
					entry.debitAmount = BillingUtils.roundToTwo(ig.amount);
					if (ig.ledgerAccount.offsetAccountName) {
						let payerOffsetEntry = {
							accountName: ig.ledgerAccount.offsetAccountName,
							accountDescription: ig.ledgerAccount.offsetAccountName,
							debitAmount: 0 ,
							creditAmount: BillingUtils.roundToTwo(ig.amount),
							description: igKey,
							groupName: "Other",
							transactions: ig.transactions,
							linkedDetails: ig.linkedDetails,
						};
						outputTransactions.push(payerOffsetEntry);
					} else {
						arInvoiceEntry.creditAmount += BillingUtils.roundToTwo(ig.amount);
						LedgerDetailServiceUtils.pushAccountReceivableEntry(entry, "credit", arInvoiceEntry);
					}
				}
				if (ig.ledgerAccount.type === ledgerAccountTypes.WRITE_OFF_AGENCY) {
					entry.debitAmount = BillingUtils.roundToTwo(ig.amount);
					if (ig.ledgerAccount.offsetAccountName) {
						let payerOffsetEntry = {
							accountName: ig.ledgerAccount.offsetAccountName,
							accountDescription: ig.ledgerAccount.offsetAccountName,
							debitAmount: 0 ,
							creditAmount: BillingUtils.roundToTwo(ig.amount),
							description: igKey,
							groupName: "Other",
							transactions: ig.transactions,
							linkedDetails: ig.linkedDetails,
						};
						outputTransactions.push(payerOffsetEntry);
					} else {
						arInvoiceEntry.creditAmount += BillingUtils.roundToTwo(ig.amount);
						LedgerDetailServiceUtils.pushAccountReceivableEntry(entry, "credit", arInvoiceEntry);
					}
				}
				if (ig.ledgerAccount.type === ledgerAccountTypes.WRITE_OFF_COLLECTIONS) {
					entry.debitAmount = BillingUtils.roundToTwo(ig.amount);
					if (ig.ledgerAccount.offsetAccountName) {
						let payerOffsetEntry = {
							accountName: ig.ledgerAccount.offsetAccountName,
							accountDescription: ig.ledgerAccount.offsetAccountName,
							debitAmount: 0 ,
							creditAmount: BillingUtils.roundToTwo(ig.amount),
							description: igKey,
							groupName: "Other",
							transactions: ig.transactions,
							linkedDetails: ig.linkedDetails,
						};
						outputTransactions.push(payerOffsetEntry);
					} else {
						arInvoiceEntry.creditAmount += BillingUtils.roundToTwo(ig.amount);
						LedgerDetailServiceUtils.pushAccountReceivableEntry(entry, "credit", arInvoiceEntry);
					}
				}

				if (ig.ledgerAccount.type === ledgerAccountTypes.WRITE_OFF_BAD_DEBT_REVERSAL) {
					entry.creditAmount = BillingUtils.roundToTwo(ig.amount);
					if (ig.ledgerAccount.offsetAccountName) {
						let payerOffsetEntry = {
							accountName: ig.ledgerAccount.offsetAccountName,
							accountDescription: ig.ledgerAccount.offsetAccountName,
							debitAmount: BillingUtils.roundToTwo(ig.amount),
							creditAmount: 0,
							description: igKey + ' - Offset Reversal',
							groupName: "Other",
							transactions: ig.transactions,
							linkedDetails: ig.linkedDetails,
						};
						outputTransactions.push(payerOffsetEntry);
					} else {
						arInvoiceEntry.debitAmount += BillingUtils.roundToTwo(ig.amount);
						LedgerDetailServiceUtils.pushAccountReceivableEntry(entry, "debit", arInvoiceEntry);
					}
				}

				if (ig.ledgerAccount.type === ledgerAccountTypes.WRITE_OFF_AGENCY_REVERSAL) {
					entry.creditAmount = BillingUtils.roundToTwo(ig.amount);
					if (ig.ledgerAccount.offsetAccountName) {
						let payerOffsetEntry = {
							accountName: ig.ledgerAccount.offsetAccountName,
							accountDescription: ig.ledgerAccount.offsetAccountName, //ig.ledgerAccountDetail ? ig.ledgerAccountDetail.description : "",
							debitAmount: BillingUtils.roundToTwo(ig.amount),
							creditAmount: 0,
							description: igKey + ' - Offset Reversal',
							groupName: "Other",
							transactions: ig.transactions,
							linkedDetails: ig.linkedDetails,
						};
						outputTransactions.push(payerOffsetEntry);
					} else {
						arInvoiceEntry.debitAmount += BillingUtils.roundToTwo(ig.amount);
						LedgerDetailServiceUtils.pushAccountReceivableEntry(entry, "debit", arInvoiceEntry);
					}
				}
				if (ig.ledgerAccount.type === ledgerAccountTypes.WRITE_OFF_COLLECTIONS_REVERSAL) {
					entry.creditAmount = BillingUtils.roundToTwo(ig.amount);
					if (ig.ledgerAccount.offsetAccountName) {
						let payerOffsetEntry = {
							accountName: ig.ledgerAccount.offsetAccountName,
							accountDescription: ig.ledgerAccount.offsetAccountName, //ig.ledgerAccountDetail ? ig.ledgerAccountDetail.description : "",
							debitAmount: BillingUtils.roundToTwo(ig.amount),
							creditAmount: 0,
							description: igKey + ' - Offset Reversal',
							groupName: "Other",
							transactions: ig.transactions,
							linkedDetails: ig.linkedDetails,
						};
						outputTransactions.push(payerOffsetEntry);
					} else {
						arInvoiceEntry.debitAmount += BillingUtils.roundToTwo(ig.amount);
						LedgerDetailServiceUtils.pushAccountReceivableEntry(entry, "debit", arInvoiceEntry);
					}
				}
				//legacy sec deposit credits
				if (ig.ledgerAccount.type === ledgerAccountTypes.SECURITY_DEPOSIT_REFUND) {
					arInvoiceEntry.creditAmount += BillingUtils.roundToTwo(ig.amount);
					entry.debitAmount = BillingUtils.roundToTwo(ig.amount);
					LedgerDetailServiceUtils.pushAccountReceivableEntry(entry, "credit", arInvoiceEntry);
				}
				//new sec deposit refunds
				if (ig.ledgerAccount.type === ledgerAccountTypes.SECURITY_DEPOSIT_REFUND_AUTO) {
					entry.debitAmount = BillingUtils.roundToTwo(ig.amount);
					let payerOffsetEntry = {
						accountName: customerPayableAccountName,
						accountDescription: customerPayableAccountName, //ig.ledgerAccountDetail ? ig.ledgerAccountDetail.description : "",
						debitAmount: 0,
						creditAmount: BillingUtils.roundToTwo(ig.amount),
						description: "Customer Liability/Payable",
						groupName:"Other",
						transactions: ig.transactions,
						linkedDetails: ig.linkedDetails
					};

					outputTransactions.push(payerOffsetEntry);
				}
				if (ig.ledgerAccount.type === ledgerAccountTypes.SECURITY_DEPOSIT_APPLIED) {
					arInvoiceEntry.creditAmount += BillingUtils.roundToTwo(ig.amount);
					entry.debitAmount = BillingUtils.roundToTwo(ig.amount);
					LedgerDetailServiceUtils.pushAccountReceivableEntry(entry, "credit", arInvoiceEntry);
				}
				if (ig.ledgerAccount.type === ledgerAccountTypes.UNAPPLIED_CASH_APPLIED) {
					arInvoiceEntry.creditAmount += BillingUtils.roundToTwo(ig.amount);
					entry.debitAmount = BillingUtils.roundToTwo(ig.amount);
					LedgerDetailServiceUtils.pushAccountReceivableEntry(entry, "credit", arInvoiceEntry);
				}
				if (ig.ledgerAccount.type === ledgerAccountTypes.CREDIT_MEMO_REFUNDED) {
					arInvoiceEntry.debitAmount += BillingUtils.roundToTwo(ig.amount);
					entry.creditAmount = BillingUtils.roundToTwo(ig.amount);
					LedgerDetailServiceUtils.pushAccountReceivableEntry(entry, "debit", arInvoiceEntry);
				}
				if (ig.ledgerAccount.type === ledgerAccountTypes.SECURITY_DEPOSIT_FORFEITURE) {
					entry.creditAmount = BillingUtils.roundToTwo(ig.amount);
					let forfeitureOffsetEntry = {
						accountName: ig.ledgerAccount.liabilityAccountName,
						debitAmount: BillingUtils.roundToTwo(ig.amount),
						creditAmount: 0,
						description: "Security Deposit Liability/Offset",
						groupName: "Security Deposit Liability/Offset",
						linkedDetails: ig.linkedDetails,
					};
					outputTransactions.push(forfeitureOffsetEntry);
				}
				if (ig.ledgerAccount.type === ledgerAccountTypes.SECURITY_DEPOSIT_LIABILITY) {
					entry.creditAmount = BillingUtils.roundToTwo(ig.amount);
					let liabilityOffsetEntry = {
						accountName: ig.ledgerAccount.forfeitAccountName,
						debitAmount: BillingUtils.roundToTwo(ig.amount),
						creditAmount: 0,
						description: "Security Deposit Forfeiture - Misc. Rev",
						groupName: "Security Deposit Forfeiture - Misc. Rev",
						linkedDetails: ig.linkedDetails,
					};
					outputTransactions.push(liabilityOffsetEntry);
				}

				if (ig.ledgerAccount.type === ledgerAccountTypes.CREDIT_MODIFIED) {
					const creditAmount = ig.amountIncreased ?? 0;
					const debitAmount = ig.amountDecreased ?? 0;
					// Not if else because amounts can change on both debit and credit.
					if (debitAmount) {
						arInvoiceEntry.debitAmount += Math.abs(debitAmount);
						entry.creditAmount = Math.abs(debitAmount)
					}

					if (creditAmount) {
						arInvoiceEntry.creditAmount += Math.abs(creditAmount)
						entry.debitAmount = Math.abs(creditAmount)
					}

					LedgerDetailServiceUtils.pushAccountReceivableEntry(entry, "both", arInvoiceEntry);
				}
			}
			// cash transactions
			if (includeCash) {
				if (ig.ledgerAccount.type === ledgerAccountTypes.BANK_WITHDRAWAL) {
					arInvoiceEntry.debitAmount += BillingUtils.roundToTwo(ig.amount);
					arInvoiceEntry.cashOffset = true;
					entry.creditAmount = BillingUtils.roundToTwo(ig.amount);
					entry.cashOffset = true;
					if (!cashOnly) LedgerDetailServiceUtils.pushAccountReceivableEntry(entry, "debit", arInvoiceEntry);
				}
				if (ig.ledgerAccount.type === ledgerAccountTypes.PAYMENT_SETTLEMENT_CASH) {
					arInvoiceEntry.creditAmount += BillingUtils.roundToTwo(ig.amount);
					arInvoiceEntry.cashOffset = true;
					entry.debitAmount = BillingUtils.roundToTwo(ig.amount);
					entry.cashOffset = true;
					if (!cashOnly) LedgerDetailServiceUtils.pushAccountReceivableEntry(entry, "credit", arInvoiceEntry);
				}
                if (ig.ledgerAccount.type === ledgerAccountTypes.MANUAL_DEPOSITS) {
                    outputTransactions.push(LedgerDetailServiceUtils.getMappedLedgerEntryForDeposit(ig, entry, cashOnly, cashAccountName));
                }
				if (ig.ledgerAccount.type === ledgerAccountTypes.PAYMENT_PAYER) {

					entry.creditAmount = BillingUtils.roundToTwo(ig.amount);
					let payerOffsetEntry = {
						accountName: ig.ledgerAccount.offsetAccountName || undepositedAccountName || cashAccountName,
						accountDescription: ig.ledgerAccount.offsetAccountName || undepositedAccountName || cashAccountName, //ig.ledgerAccountDetail ? ig.ledgerAccountDetail.description : "",
						debitAmount: BillingUtils.roundToTwo(ig.amount),
						creditAmount: 0,
						description: "Payer Receipts",
						groupName:"Other",
						glImportIgnore: true,
						transactions: ig.transactions,
						linkedDetails: ig.linkedDetails
					};
					entry.cashOffsetExclude = excludeAR;
					outputTransactions.push(payerOffsetEntry);
				}
				if (ig.ledgerAccount.type === ledgerAccountTypes.PAYER_OVERPAYMENT) {
					let payerOffsetEntry = {
						accountName: undepositedAccountName || cashAccountName,
						accountDescription: undepositedAccountName || cashAccountName,
						debitAmount: BillingUtils.roundToTwo(ig.amount),
						creditAmount: 0,
						description: "Payer Receipts",
						groupName:"Other",
						transactions: ig.transactions,
						glImportIgnore: true,
						linkedDetails: ig.linkedDetails
					};
					entry.cashOffsetExclude = excludeAR;
					outputTransactions.push(payerOffsetEntry);
					entry.creditAmount = BillingUtils.roundToTwo(ig.amount);
				}
				if (ig.ledgerAccount.type === ledgerAccountTypes.PAYER_OVERPAYMENT_CREDIT_MEMO) {
					entry.creditAmount = BillingUtils.roundToTwo(ig.amount);
					let payerOffsetEntry = {
						accountName: ig.ledgerAccount.offsetAccountName || undepositedAccountName || cashAccountName,
						accountDescription: ig.ledgerAccount.offsetAccountName || undepositedAccountName || cashAccountName,
						debitAmount: BillingUtils.roundToTwo(ig.amount),
						creditAmount: 0,
						description: "Payer Receipts",
						groupName:"Other",
						glImportIgnore: true,
						transactions: ig.transactions,
						linkedDetails: ig.linkedDetails
					};

					entry.cashOffsetExclude = excludeAR;
					outputTransactions.push(payerOffsetEntry);
				}
				if (ig.ledgerAccount.type === ledgerAccountTypes.CREDIT_MEMO) {

					entry.creditAmount = BillingUtils.roundToTwo(ig.amount);
					let payerOffsetEntry = {
						accountName: cashAccountName,
						accountDescription: cashAccountName,
						debitAmount: BillingUtils.roundToTwo(ig.amount),
						creditAmount: 0,
						description: "Credit Memos",
						groupName:"Other",
						glImportIgnore: true,
						transactions: ig.transactions,
						linkedDetails: ig.linkedDetails,
					};
					entry.cashOffsetExclude = excludeAR;
					outputTransactions.push(payerOffsetEntry);

				}
				if (ig.ledgerAccount.type === ledgerAccountTypes.CREDIT_MEMO_VOID) {
					entry.debitAmount = BillingUtils.roundToTwo(ig.amount);
					let payerOffsetEntry = {
						accountName: cashAccountName,
						accountDescription: cashAccountName,
						debitAmount: 0 ,
						creditAmount: BillingUtils.roundToTwo(ig.amount),
						description: "Credit Memos",
						groupName:"Other",
						glImportIgnore: true,
						transactions: ig.transactions,
						linkedDetails: ig.linkedDetails,
					};
					entry.cashOffsetExclude = excludeAR;
					outputTransactions.push(payerOffsetEntry);
				}
			}

			if (ig.ledgerAccount.type === ledgerAccountTypes.DISCOUNT_MODIFIED && (ig.ledgerAccount.amountIncrease || ig.ledgerAccount.amountDecrease)) {
				const creditAmount = ig.ledgerAccount.amountIncrease;
				const debitAmount = ig.ledgerAccount.amountDecrease;
				// Not if else because amounts can change on both debit and credit.
				if (debitAmount) {
					arInvoiceEntry.debitAmount += Math.abs(debitAmount);
					entry.creditAmount = Math.abs(debitAmount)
				}

				if (creditAmount) {
					arInvoiceEntry.creditAmount += Math.abs(creditAmount)
					entry.debitAmount = Math.abs(creditAmount)
				}

				LedgerDetailServiceUtils.pushAccountReceivableEntry(entry, "both", arInvoiceEntry);
			}

			if (ig.ledgerAccount.type === ledgerAccountTypes.REALLOCATION_TO_PAYER) {
				arInvoiceEntry.creditAmount += BillingUtils.roundToTwo(ig.amount);
				entry.debitAmount = BillingUtils.roundToTwo(ig.amount);
				LedgerDetailServiceUtils.pushAccountReceivableEntry(entry, "credit", arInvoiceEntry);
			}
			if (ig.ledgerAccount.type === ledgerAccountTypes.REALLOCATION_TO_PAYER_REVERSAL) {
				arInvoiceEntry.debitAmount += BillingUtils.roundToTwo(ig.amount);
				entry.creditAmount = BillingUtils.roundToTwo(ig.amount);
				LedgerDetailServiceUtils.pushAccountReceivableEntry(entry, "debit", arInvoiceEntry);
			}

			if (!entry.cashOffsetExclude && (entry.creditAmount > 0 || entry.debitAmount > 0)) {
				outputTransactions.push(entry);
			}
		});
	});

	if (arInvoiceEntry.debitAmount > 0 || arInvoiceEntry.creditAmount > 0) {
		arInvoiceEntry.debitAmount = BillingUtils.roundToTwo(arInvoiceEntry.debitAmount);
		arInvoiceEntry.creditAmount = BillingUtils.roundToTwo(arInvoiceEntry.creditAmount);
		if (!cashOnly) outputTransactions.push(arInvoiceEntry);
	}

	return outputTransactions;
}

export const processPostPaymentLogic = async function(options) {

	const invoice = await Invoices.findOneAsync(options.invoiceId);
	const securityDepositItems = invoice && _.filter(invoice.lineItems, li => {
			return li.type === "item" && li?.originalItem?.refundableDeposit;
		} );
	const punchCardItems = invoice && invoice.lineItems.filter(li => li.type === ITEM_TYPE && li.originalItem?.type === PUNCH_CARD_TYPE);

	if (securityDepositItems?.length > 0) {
		const totalDepositItemAmount = _.reduce(securityDepositItems, (num, sdi) => { return sdi.amount + num;}, 0.0),
			paymentPercent = options.paymentLine.amount / invoice.originalAmount,
			creditAmount = BillingUtils.roundToTwo(totalDepositItemAmount * paymentPercent),
			invoicePerson = await People.findOneAsync(invoice.personId),
			invoicePersonName = invoicePerson && (invoicePerson.firstName + " " + invoicePerson.lastName);

		console.log("issuing sec deposit credit", options.paymentLine.paidBy, "amount", creditAmount);
console.log('Another ',securityDepositItems?.[0]?._id);
		await People.updateAsync({_id: options.paymentLine.paidBy}, {$push: {"billing.creditMemos": {
			_id: securityDepositItems[0]._id,
			type: "securityDepositAuto",
			createdAt: new moment().valueOf(),
			createdBy: options.paymentLine.paidBy,
			notes: "Security deposit payment invoice #" + invoice.invoiceNumber + " for " + invoicePersonName,
			paidForPersonId: invoice.personId,
			paidForInvoiceId: invoice._id,
			openAmount: parseFloat(creditAmount),
			originalAmount: parseFloat(creditAmount)
		}}});
	}
	// add punch card days to person at time of payment
	if (punchCardItems?.length > 0) {
		const totalDaysToAdd = punchCardItems.reduce((total, item) => total + parseInt(item.originalItem?.numberOfDays) * item.quantity, 0);
		await PunchCardService.addPunchCardDays(invoice.personId, totalDaysToAdd);
	}
}