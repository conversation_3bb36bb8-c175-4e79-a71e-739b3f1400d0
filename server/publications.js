import { Meteor } from 'meteor/meteor';
import { publishComposite } from 'meteor/reywood:publish-composite';
import { MomentTypes } from "../lib/constants/momentTypeConstants";
import { PublicationsUtils } from "./publicationsUtils";
import { theCheckinStatsCount } from '../lib/collections/counts';
import { RelUtils } from "../lib/util/relUtils";
import { People } from '../lib/collections/people';
import { ChildcareCrmAccounts } from '../lib/collections/childcareCrmAccounts';
import { Orgs } from '../lib/collections/orgs';
import { OrgSettingChanges } from '../lib/collections/orgSettingChanges';
import { Groups } from '../lib/collections/groups';
import { processPermissions } from '../lib/permissions';

import { MomentDefinitions } from '../lib/collections/momentDefinitions';
import { PermissionsRoles } from '../lib/collections/permissionsRoles';
import { Reservations } from '../lib/collections/reservations';
import { CheckinRatios } from '../lib/collections/checkinRatios';
import { Moments } from '../lib/collections/moments';
import { Counts } from '../lib/collections/counts';
import { Relationships } from '../lib/collections/relationships';
import { Invoices } from '../lib/collections/invoices';
import { MediaReviews } from '../lib/collections/mediaReview';
import { Foods } from '../lib/collections/food';
import { PeopleDataValidations } from '../lib/collections/peopleDataValidation';
import { Messages } from '../lib/collections/messages';
import { Announcements } from '../lib/collections/announcements';
import moment from 'moment-timezone';
import _ from "../lib/util/underscore";
import { AuditTrails } from '../lib/collections/auditTrail';
import { TimeCards } from '../lib/collections/timeCards';
import { UserInvitations } from '../lib/collections/userInvitations';
import { GroupDashboards } from '../lib/collections/groupDashboard';
import { MetaMoments } from '../lib/collections/metaMoments';

const peopleProjections = {
  "firstName": 1,
  "lastName": 1,
  "checkedIn": 1,
  "checkInGroupName": 1,
  "defaultGroupId": 1,
  "checkInGroupId": 1,
  "type": 1,
  "familyCheckIn": 1,
  "familyCheckOut": 1,
  "checkInOutlook": 1,
  "standardOutlook": 1,
  "designations": 1,
  "lastInformedArrival": 1,
  "lastMomentByType": 1,
  "documentItems": 1,
  "profileData.standardOutlook": 1,
  "forceUpdateField": 1,
  "timestamps": 1
};

Meteor.publish('theOrgSettingChanges', async function (options) {
  this.unblock();
  if (this.userId) {
    const user = await Meteor.users.findOneAsync(this.userId);
    return OrgSettingChanges.find({ userId: user._id, orgId: user.orgId, applied: null });
  } else {
    this.ready()
  }
});

Meteor.publish('theGroupDashboard', async function (options) {
  this.unblock();
  if (this.userId) {
    const user = await Meteor.users.findOneAsync(this.userId);
    const userPerson = await user?.fetchPerson?.();
    if (userPerson?.type == "staff" || userPerson?.type == "admin") {
      const groupId = userPerson?.checkInGroupId || userPerson?.defaultGroupId;
      return GroupDashboards.find({ _id: groupId }, { readPreference: "secondaryPreferred" });
    } else {
      this.ready();
    }
  } else {
    this.ready();
  }
})

Meteor.publish('theMediaReview', async function () {
  this.unblock();
  if (this.userId) {
    const user = await Meteor.users.findOneAsync(this.userId);
    const userPerson = await user?.fetchPerson?.();
    if (userPerson) {
      return MediaReviews.find({ orgId: userPerson.orgId }, { readPreference: "secondaryPreferred" });
    } else {
      this.ready();
    }
  } else {
    this.ready();
  }
})


Meteor.publish('theMobilePeople', async function (options) {
  this.unblock();
  let handle = null;
  var self = this;
  if (this.userId) {
    const user = await Meteor.users.findOneAsync(this.userId), currentPerson = user && await user.fetchPerson();
    if (user && currentPerson) {
      const query = { orgId: user.orgId };
      if (currentPerson.type == "family") {
        let includeList = [currentPerson._id];
        const relationships = await Relationships.find({
          $or: [{ personId: currentPerson._id }, { targetId: currentPerson._id }],
          relationshipType: "family"
        }).fetchAsync();

        includeList = includeList.concat(relationships.map(m => m.targetId));
        query["_id"] = { $in: includeList };
      } else {
        query["type"] = { $in: ["person", "admin", "staff"] };
      }

      handle = People.find(query, { fields: peopleProjections }).observe({
        added: function (p) {
          self.added('staffAdminFetch', p._id, { dt: new Date().valueOf() });
        },

        changed: function (p) {
          self.changed('staffAdminFetch', p._id, { dt: new Date().valueOf() });
        },

        removed: function (p) {
          self.removed('staffAdminFetch', p._id);
        }
      });
      this.ready();
    } else {
      this.ready()
    }
  } else {
    this.ready()
  }

  this.onStop(function () {
    if (handle && typeof handle.stop === 'function') {
      handle.stop();
    }
  });
})

Meteor.publish('thePeopleList', async function (options) {
  this.unblock()
  const handles = [];
  var self = this;
  if (this.userId) {
    const user = await Meteor.users.findOneAsync(this.userId), currentPerson = user && await user.fetchPerson();
    if (user && currentPerson) {
      var query = { "orgId": user["orgId"] }, projection;

      if (currentPerson.type == "family") {
        var includeList = [currentPerson._id];
        includeList = includeList.concat(await Relationships.find({ $or: [{ personId: currentPerson._id }, { targetId: currentPerson._id }], relationshipType: "family" }).mapAsync(
          function (m, index) { return m.targetId; }));
        query["_id"] = { $in: includeList };
        delete query.inActive;
      }

      if (currentPerson.type == "person") {
        var includeList = [currentPerson._id];
        includeList = includeList.concat(await Relationships.find({ $or: [{ personId: currentPerson._id }, { targetId: currentPerson._id }], relationshipType: "family" }).mapAsync(
          function (m, index) { return m.personId; }));
        query["_id"] = { $in: includeList };
        delete query.inActive;
      }

      if (currentPerson.type == "staff") {
        projection = { pinCode: 0, pinCodeSupplemental: 0 };
        if (!options || !options.personId) {
          projection["profileData"] = 0;
        }
      }

      if (currentPerson.type != "admin") {
        let roles = ["prospect", "person", "staff", "family"];
        for(let currentRole of roles){
          const excludedFields = await People.excludedProfileFieldsForType(currentRole);
          if (excludedFields && excludedFields.length > 0 && projection && projection["profileData"] != 0) {
            projection = projection || {};
            _.each(excludedFields, (ef) => {
              const fieldKey = "profileData." + ef.fieldPath;
              projection[fieldKey] = 0;
            });
          }
        }
      }

      // the lightweight option is a mobile-only tag.  If we have lightweight w/o an _id then we should include the things we need to support ActivityList outlook
      // trying not to boil the ocean on mobile data architecture for this feature (while still being reactive)
      if (options && options.lightweight && !query["_id"]) {
        projection = {};
        const lightweightFields = ["firstName", "lastName", "preferredName", "checkedIn", "checkInGroupName", "defaultGroupId", "checkInGroupId", "avatarPath", "type", "lastMomentByType", "familyCheckIn", "familyCheckOut", "checkInOutlook", "standardOutlook", "timestamps"];
        _.each(lightweightFields, lf => {
          if (!projection[lf])
            projection[lf] = 1;
        });

        if (currentPerson.type == "staff" || currentPerson.type == "admin") {
          for (let currentRole of ["prospect", "person", "staff", "family"]) {
            const exclusionProjection = {};
            if (currentPerson.type == "staff") {
              exclusionProjection.pinCode = 0;
              exclusionProjection.pinCodeSupplemental = 0;
              if (currentRole == "staff") exclusionProjection.profileData = 0;
            }
            const excludedFields = (currentRole != "staff") ? await People.excludedProfileFieldsForType(currentRole) : [];
            if (excludedFields && excludedFields.length > 0) {
              _.each(excludedFields, (ef) => {
                const fieldKey = "profileData." + ef.fieldPath;
                exclusionProjection[fieldKey] = 0;
              });
            }
            const handle = People.find({ ...query, type: currentRole }, { fields: exclusionProjection, readPreference: "secondaryPreferred" }).observe({
              added: function (p) {
                self.added('people', p._id, p);
              },

              changed: function (p) {
                self.changed('people', p._id, p);
              },

              removed: function (p) {
                self.removed('people', p._id);
              }
            });
            handles.push(handle);
          }
        }
      }

      const skip = options?.skip || 0;
      const limit = options?.limit || 50;
      const sort = options?.sort;
      const queryPeople = {...options?.query, ...query};
      if (handles.length > 0) {
        this.ready();
      } else {
        if (projection) {
          return People.find(queryPeople, { fields: projection, readPreference: "secondaryPreferred", skip, limit, sort });
        } else {
          return People.find(queryPeople, { readPreference: "secondaryPreferred", skip, limit, sort });
        }
      }
    } else {
      this.ready()
    }
  } else {
    this.ready()
  }

  this.onStop(function () {
    if (handles && handles.length > 0) {
      for (let x = 0; x < handles.length; x++) {
        handles[x].stop();
      }
    }
  });
});

Meteor.publish('thePeopleListByIds', async function (options) {
  const user = await Meteor.userAsync();
  var currentUseOrg = user?.orgId;
  const currentPerson = await user?.fetchPerson();
  if (!currentPerson) {
    this.ready();
    return;
  }
  if (currentUseOrg) {
    let peopleIds = [...(options.peopleIds || [])];
    if (!['admin', 'staff'].includes(currentPerson.type)) {
      const relIds = await RelUtils.getRelatedProfiles(currentPerson);
      peopleIds = peopleIds.filter(id => relIds.includes(id));
    }
    return People.find({"orgId": currentUseOrg, _id:{$in:peopleIds}});
  } else {
    this.ready();
  }
});

Meteor.publish('thePerson', async function (options) {
  const user = await Meteor.users.findOneAsync(this.userId), currentPerson = user && await user?.fetchPerson();
  var query = { "orgId": user?.orgId }
  var projection;
  options = options || {};
  options.personById = options.personById || currentPerson?._id;
  if (currentPerson && options.personById) {
    const personSelected = await People.findOneAsync({_id: options.personById, orgId:user?.orgId})

    if (currentPerson.type == "family") {
      var includeList = [currentPerson._id];
      includeList = includeList.concat(await Relationships.find({ $or: [{ personId: currentPerson._id }, { targetId: currentPerson._id }], relationshipType: "family" }).mapAsync(
        function (m, index) { return m.targetId; }));
      query["_id"] = { $in: includeList };
      delete query.inActive;
    }
    else if (currentPerson.type == "person") {
      var includeList = [currentPerson._id];
      includeList = includeList.concat(await Relationships.find({ $or: [{ personId: currentPerson._id }, { targetId: currentPerson._id }], relationshipType: "family" }).mapAsync(
        function (m, index) { return m.personId; }));
      query["_id"] = { $in: includeList };
      delete query.inActive;
    }
    else if (currentPerson.type == "staff") {
      projection = { pinCode: 0, pinCodeSupplemental: 0 };
      if (!options || !options.personById) {
        projection["profileData"] = 0;
      }
      var includeList = [options.personById];
      const personSelectedCursor = personSelected.findInheritedRelationships();
      const findOwnedRelationships = await personSelected.findOwnedRelationships();
      const personSelectedFamilyPeople = await personSelected.findRelatedFamilyPeople();
      includeList = includeList.concat(await personSelectedCursor.mapAsync(function (m, index) { return m.targetId; }));
      includeList = includeList.concat(findOwnedRelationships.map(function (m, index) { return m.personId; }));
      includeList = includeList.concat(await personSelectedFamilyPeople.mapAsync(function (m, index) { return m._id; }));
      query["_id"] =  { $in: [...new Set(includeList)] };
    }
    else {
      var includeList = [options.personById];
      const personSelectedCursor = personSelected.findInheritedRelationships();
      const findOwnedRelationships = await personSelected.findOwnedRelationships();
      const personSelectedFamilyPeople = await personSelected.findRelatedFamilyPeople();
      includeList = includeList.concat(await personSelectedCursor.mapAsync(function (m, index) { return m.targetId; }));
      includeList = includeList.concat(findOwnedRelationships.map(function (m, index) { return m.personId; }));
      includeList = includeList.concat(await personSelectedFamilyPeople.mapAsync(function (m, index) { return m._id; }));
      query["_id"] =  { $in: [...new Set(includeList)] };
    }
    if (currentPerson.type != "admin") {
      for (let currentRole of ["prospect", "person", "staff", "family"]) {
        const excludedFields = await People.excludedProfileFieldsForType(currentRole);
        if (excludedFields && excludedFields.length > 0 && projection && projection["profileData"] != 0) {
          projection = projection || {};
          _.each(excludedFields, (ef) => {
            const fieldKey = "profileData." + ef.fieldPath;
            projection[fieldKey] = 0;
          });
        }
      }
    }
    if (projection) {
      return People.find(query, { fields: projection, readPreference: "secondaryPreferred" });
    }
    else {
      return People.find(query);
    }
  } else {
    this.ready();
  }
});

Meteor.publish('thePeopleAssociateExisting', async function (options) {
  const user = await Meteor.userAsync();
  var currentUseOrg = user?.orgId;
  if (currentUseOrg) {
    var projection = { orgId:1, firstName: 1, lastName: 1, preferredName: 1, type: 1, inActive: 1, checkedIn: 1, familyCheckIn: 1, checkInGroupId: 1, defaultGroupId: 1, engagement: 1, sType: 1, avatarPath: 1, avatarUrl: 1, personInitials: 1, isAbsentToday: 1, standardOutlook: 1, "profileData.standardOutlook": 1, absentComment: 1, absentReason: 1, recurringDays: 1, profileEmailAddress: 1, email:1, "profileData.homePhone": 1, homePhone:1, "profileData.cellPhone": 1, cellPhone:1, phonePrimary: 1, "profileData.phonePrimary": 1, address:1, "profileData.address": 1, "profileData.city": 1, "profileData.state": 1, "profileData.zipcode": 1 };
    return People.find({"orgId": currentUseOrg,type: {$ne: "person"}, inActive: {$ne: true}, _id: { $nin: Object.keys(options.alreadySelectedRelationships) }}, {sort: {lastName: 1, firstName: 1}, fields: projection});
    // return People.find({_id: {$in:ids}});
  } else {
    this.ready();
  }
});

Meteor.publish('thePeopleRoster', async function (options) {
  const user = await Meteor.userAsync();
  var currentUseOrg = user?.orgId;
  if (currentUseOrg) {
    // This projection is too strict, and it causes temporary missing data at times
    // var projection = { orgId:1, firstName: 1, lastName: 1, preferredName: 1, type: 1, inActive: 1, checkedIn: 1, familyCheckIn: 1, checkInGroupId: 1, defaultGroupId: 1, engagement: 1, sType: 1, avatarPath: 1, personInitials: 1, isAbsentToday: 1, standardOutlook: 1, "profileData.standardOutlook": 1, absentComment: 1, absentReason: 1, recurringDays: 1 };
    return People.find({ "orgId": currentUseOrg, $or: [{ checkInGroupId: options.groupId }, { defaultGroupId: options.groupId }] });
  } else {
    this.ready();
  }
});
Meteor.publish('thePeopleStaffAndAdminForOrg', async function (options) {
  const user = await Meteor.userAsync();
  var currentUseOrg = user?.orgId;
  if (currentUseOrg) {
    return People.find({"orgId": currentUseOrg, type: { "$in": ["staff", "admin"] }, inActive: { $ne: true }});
  } else {
    this.ready();
  }
});

Meteor.publish('thePeople', async function (options) {
  this.unblock()
  const handles = [];
  var self = this;
  if (this.userId) {
    const user = await Meteor.users.findOneAsync(this.userId), currentPerson = user && await user.fetchPerson();
    if (user && currentPerson) {
      var query = { "orgId": user["orgId"] }, projection;
      if (options && options.onlyActive) {
        query.inActive = false;
      }

      if (currentPerson.type == "family") {
        var includeList = [currentPerson._id];
        includeList = includeList.concat(await Relationships.find({ $or: [{ personId: currentPerson._id }, { targetId: currentPerson._id }], relationshipType: "family" }).mapAsync(
          function (m, index) { return m.targetId; }));
        if (options?.isPersonProfile) {
          query["$or"] = [
            { "_id": { $in: includeList } },
            { "type": { $in: ["staff", "admin"] } }
          ];
        } else {
          query["_id"] = { $in: includeList };
        }
        delete query.inActive;
      }

      if (currentPerson.type == "person") {
        var includeList = [currentPerson._id];
        includeList = includeList.concat(await Relationships.find({ $or: [{ personId: currentPerson._id }, { targetId: currentPerson._id }], relationshipType: "family" }).mapAsync(
          function (m, index) { return m.personId; }));
        if (options?.isPersonProfile) {
          query["$or"] = [
            { "_id": { $in: includeList } },
            { "type": { $in: ["staff", "admin"] } }
          ];
        } else {
          query["_id"] = { $in: includeList };
        }
        delete query.inActive;
      }

      if (currentPerson.type == "staff") {
        projection = { pinCode: 0, pinCodeSupplemental: 0 };
        if (!options || !options.personId) {
          projection["profileData"] = 0;
        } else if (options && options.personId && options.personId != currentPerson._id) {
          const checkPerson = await People.findOneAsync({ orgId: user["orgId"], _id: options.personId });
          if (!checkPerson || !_.contains(["person", "family"], checkPerson.type)) {
            projection["profileData"] = 0;
          } else if (checkPerson && checkPerson.type == "person") {
            // we want to force include family relationships on the person
            var includeList = [options.personId];
            includeList = includeList.concat(await Relationships.find({ $or: [{ personId: options.personId }, { targetId: options.personId }], relationshipType: "family" }).mapAsync(
              function (m, index) { return m.personId; }));
            if (options?.isPersonProfile) {
              query["$or"] = [
                { "_id": { $in: includeList } },
                { "type": { $in: ["staff", "admin"] } }
              ];
            } else {
              query["_id"] = { $in: includeList };
            }
          }
        }
      }

      if (currentPerson.type != "admin") {
        for (let currentRole of ["prospect", "person", "staff", "family"]) {
          const excludedFields = await People.excludedProfileFieldsForType(currentRole);
          if (excludedFields && excludedFields.length > 0 && projection && projection["profileData"] != 0) {
            projection = projection || {};
            _.each(excludedFields, (ef) => {
              const fieldKey = "profileData." + ef.fieldPath;
              projection[fieldKey] = 0;
            });
          }
        }
      }

      // the lightweight option is a mobile-only tag.  If we have lightweight w/o an _id then we should include the things we need to support ActivityList outlook
      // trying not to boil the ocean on mobile data architecture for this feature (while still being reactive)
      if (options && options.lightweight && !query["_id"]) {
        projection = {};
        const lightweightFields = ["firstName", "lastName", "preferredName", "checkedIn", "checkInGroupName", "defaultGroupId", "checkInGroupId", "avatarPath", "type", "lastMomentByType", "familyCheckIn", "familyCheckOut", "checkInOutlook", "standardOutlook", "timestamps"];
        _.each(lightweightFields, lf => {
          if (!projection[lf])
            projection[lf] = 1;
        });

        if (currentPerson.type == "staff" || currentPerson.type == "admin") {
          for (let currentRole of ["prospect", "person", "staff", "family"]) {
            const exclusionProjection = {};
            if (currentPerson.type == "staff") {
              exclusionProjection.pinCode = 0;
              exclusionProjection.pinCodeSupplemental = 0;
              if (currentRole == "staff") exclusionProjection.profileData = 0;
            }
            const excludedFields = (currentRole != "staff") ? await People.excludedProfileFieldsForType(currentRole) : [];
            if (excludedFields && excludedFields.length > 0) {
              _.each(excludedFields, (ef) => {
                const fieldKey = "profileData." + ef.fieldPath;
                exclusionProjection[fieldKey] = 0;
              });
            }
            const handle = People.find({ ...query, type: currentRole }, { fields: exclusionProjection, readPreference: "secondaryPreferred" }).observe({
              added: function (p) {
                self.added('people', p._id, p);
              },

              changed: function (p) {
                self.changed('people', p._id, p);
              },

              removed: function (p) {
                self.removed('people', p._id);
              }
            });
            handles.push(handle);
          }
        }
      }

      if (options && options.personId && !query["_id"] && (currentPerson.type == "staff" || currentPerson.type == "admin")) {
        query["_id"] = options.personId;
      }

      if (handles.length > 0) {
        this.ready();
      } else {
        if (projection) {
          return People.find(query, { fields: projection, readPreference: "secondaryPreferred" });
        } else {
          return People.find(query, { readPreference: "secondaryPreferred" });
        }
      }
    } else {
      this.ready()
    }
  } else {
    this.ready()
  }

  this.onStop(function () {
    if (handles && handles.length > 0) {
      for (let x = 0; x < handles.length; x++) {
        handles[x].stop();
      }
    }
  });

});
Meteor.publish("thePeopleDirectory", async function (options) {
  if (this.userId) {
    var user = await Meteor.users.findOneAsync(this.userId);
    const currentPerson = user && await user.fetchPerson();
    if (user && currentPerson) {
      var query = { "orgId": user["orgId"] }, projection;
      projection = { firstName: 1, lastName: 1, preferredName: 1, type: 1, inActive: 1 };

      if (currentPerson.type == "family" || currentPerson.type == "person") {
        var includeList = [currentPerson._id];
        includeList = includeList.concat(await Relationships.find({ $or: [{ personId: currentPerson._id }, { targetId: currentPerson._id }], relationshipType: "family" }).mapAsync(
          function (m, index) { return m.targetId; }));
        query["$or"] = [
          { "_id": { $in: includeList } },
          { "type": { $in: ["staff", "admin"] } }
        ];
      }
      return People.find(query, { fields: projection, readPreference: "secondaryPreferred" });
    } else {
      this.ready()
    }
  } else {
    this.ready()
  }
});
Meteor.publish("thePeopleWithFamilyDirectory", async function (options) {
  if (this.userId) {
    const user = await Meteor.users.findOneAsync(this.userId);
    const currentPerson = user && await user.fetchPerson();
    if (user && currentPerson) {
      const query = { 'orgId': user['orgId'] };
      const projection = {
        firstName: 1,
        lastName: 1,
        preferredName: 1,
        type: 1,
        inActive: 1,
        checkedIn: 1,
        checkInGroupId: 1,
        checkInGroupName: 1,
        defaultGroupId: 1
      };

      if (currentPerson.type === 'family' || currentPerson.type === 'person') {
        let includeList = [currentPerson._id];
        const currentPersonCursor = await currentPerson.findRelatedFamilyPeople(true);
        includeList = includeList.concat(await currentPersonCursor.mapAsync((p) => p._id));

        query['$or'] = [
          { '_id': { $in: includeList } },
          { 'type': { $in: ['staff', 'admin'] } }
        ];
      }
      return People.find(query, { fields: projection, readPreference: 'secondaryPreferred' });
    } else {
      this.ready()
    }
  } else {
    this.ready()
  }
});
Meteor.publish('personMoments', async function (personId, limit, startDate, endDate) {
  if (this.userId) {
    var user = await Meteor.users.findOneAsync(this.userId);
    var person = await People.findOneAsync(personId);
    if (user && person) {
      return await personMomentsWithOptions({ personId, limit, startDate, endDate, user, person });
    } else {
      this.ready()
    }
  } else {
    this.ready();
  }
});
Meteor.publish('personMomentsWithOptions', async function (options) {
  if (this.userId) {
    var user = await Meteor.users.findOneAsync(this.userId);
    var person = await People.findOneAsync(options.personId);
    if (user && person) {
      options.user = user; options.person = person;
      return await personMomentsWithOptions(options);
    } else {
      this.ready()
    }
  } else {
    this.ready();
  }
});
Meteor.publish('dashboardMoments', async function (limit, showAll, momentId) {
  if (this.userId) {
    showAll = showAll || false;
    var user = await Meteor.users.findOneAsync(this.userId);
    const userPerson = user && await user.fetchPerson();
    if (user && userPerson) {
      return await dashboardMomentsWithOptions({ limit, showAll, momentId, user, userPerson });
    } else {
      this.ready()
    }
  } else {
    this.ready()
  }
});
Meteor.publish('dashboardMomentsWithOptions', async function (options) {
  if (this.userId) {
    options.showAll = options.showAll || false;
    const user = await Meteor.users.findOneAsync(this.userId);
    const userPerson = user && await user.fetchPerson();
    if (user && userPerson) {
      options.user = user; options.userPerson = userPerson;
      return await dashboardMomentsWithOptions(options);
    } else {
      this.ready()
    }
  } else {
    this.ready()
  }
});
Meteor.publish('groupMoments', async function (limit, groupId) {
  if (this.userId) {
    var user = await Meteor.users.findOneAsync(this.userId), userPerson = user && await user.fetchPerson();

    if (userPerson && (userPerson.type == "staff" || userPerson.type == "admin")) {
      return await groupMomentsWithOptions({ limit, groupId, user, userPerson });
    } else {
      this.ready()
    }
  } else {
    this.ready();
  }
});
Meteor.publish('groupMomentsWithOptions', async function (options) {
  if (this.userId) {
    var user = await Meteor.users.findOneAsync(this.userId), userPerson = user && await user.fetchPerson();
    if (userPerson && (userPerson.type == "staff" || userPerson.type == "admin")) {
      options.user = user; options.userPerson = userPerson;
      return await groupMomentsWithOptions(options);
    } else {
      this.ready()
    }
  } else {
    this.ready()
  }
});
Meteor.publish('attendanceReportMoments', async function (personId, startDate, endDate, groupId, personType) {
  if (this.userId) {
    var user = await Meteor.users.findOneAsync(this.userId);
    var person = user && await user.fetchPerson();
    if ((person.type == "staff" || person.type == "admin")) {
      var org = await Orgs.findOneAsync(user.orgId)

      var startDateNum = moment.tz(startDate, "MM/DD/YYYY", org.getTimezone()).startOf('day').valueOf();
      var endDateNum = moment.tz(endDate, "MM/DD/YYYY", org.getTimezone()).endOf('day').valueOf();

      // console.log(startDateNum, endDateNum);
      var query = {
        $and: [
          { sortStamp: { $gte: startDateNum, $lte: endDateNum } },
          { momentType: { $in: ["checkin", "checkout"] } },
          { orgId: user.orgId }
        ]
      };
      if (personId && personId != "") query["$and"].push({ owner: personId });
      if (groupId && groupId != "") {
        var peopleIds = _.map(await People.find({ orgId: user.orgId, defaultGroupId: groupId }, { fields: { _id: 1 } }).fetchAsync(),
          function (p) { return p._id; });
        query["$and"].push({ owner: { $in: peopleIds } });
      }
      if (personType && personType != "") {
        var peopleIds = _.map(await People.find({ orgId: user.orgId, type: personType }, { fields: { _id: 1 } }).fetchAsync(),
          function (p) { return p._id; });
        query["$and"].push({ owner: { $in: peopleIds } });
      }

      return Moments.find(query, { readPreference: "secondaryPreferred" });
    } else {
      this.ready();
    }
  } else {
    this.ready();
  }
});

Meteor.publish('mealsDetailsReportMoments', async function (startDate, endDate) {
  if (this.userId) {
    const user = await Meteor.users.findOneAsync(this.userId);
    const person = user && await user.fetchPerson();
    if ((person.type == "staff" || person.type == "admin")) {
      const org = await Orgs.findOneAsync(user.orgId)
      const startDateNum = moment.tz(startDate, "MM/DD/YYYY", org.getTimezone()).startOf('day').valueOf();
      const endDateNum = moment.tz(endDate, "MM/DD/YYYY", org.getTimezone()).endOf('day').valueOf();
      const query = {
        $and: [
          { sortStamp: { $gte: startDateNum, $lte: endDateNum } },
          { momentType: { $in: ["checkin", "checkout", "food"] } },
          { orgId: user.orgId }
        ]
      };
      return Moments.find(query, { readPreference: "secondaryPreferred" });
    } else {
      this.ready();
    }
  } else {
    this.ready();
  }
});

Meteor.publish('reservationsReportMoments', async function (personId, startDate, endDate, groupId) {
  if (this.userId) {
    var user = await Meteor.users.findOneAsync(this.userId);
    var person = await user.fetchPerson();
    if ((person.type == "staff" || person.type == "admin")) {
      var startDateNum = moment(startDate).startOf('day').valueOf();
      var endDateNum = moment(endDate).endOf('day').valueOf();
      var query = {
        $and: [
          { sortStamp: { $gte: startDateNum, $lte: endDateNum } },
          { momentType: { $in: ["checkin", "checkout"] } },
          { orgId: user.orgId }
        ]
      };
      if (personId && personId != "") query["$and"].push({ owner: personId });
      if (groupId && groupId != "") {
        var peopleIds = _.map(await People.find({ orgId: user.orgId, defaultGroupId: groupId, inActive: { $ne: true } }, { fields: { _id: 1 } }).fetchAsync(),
          function (p) { return p._id; });
        query["$and"].push({ owner: { $in: peopleIds } });
      }
      return Moments.find(query, { readPreference: "secondaryPreferred" });
    } else {
      this.ready()
    }
  } else {
    this.ready()
  }
});
Meteor.publish('reservationsReportReservations', async function (personId, startDate, endDate, groupId) {
  if (this.userId) {
    var user = await Meteor.users.findOneAsync(this.userId);
    var person = await user.fetchPerson();
    if ((person.type == "staff" || person.type == "admin")) {
      var startDateNum = moment(startDate).startOf('day').valueOf();
      var endDateNum = moment(endDate).endOf('day').valueOf();

      //if (personId && personId != "") query["$and"].push({owner: personId});
      //var checkins = Moments.find(query);

      var query = {
        $and: [
          { scheduledDate: { $gte: startDateNum, $lte: endDateNum } },
          { orgId: user.orgId },
          { reservationType: "person" },
          { recurringFrequency: { $exists: 0 } }
        ]
      };
      if (personId && personId != "") query["$and"].push({ selectedPerson: personId });
      if (groupId && groupId != "") {
        var peopleIds = _.map(await People.find({ orgId: user.orgId, defaultGroupId: groupId, inActive: { $ne: true } }, { fields: { _id: 1 } }).fetchAsync(),
          function (p) { return p._id; });
        query["$and"].push({ selectedPerson: { $in: peopleIds } });
      }
      return Reservations.find(query, { readPreference: "secondaryPreferred" });
    } else {
      this.ready()
    }
  } else {
    this.ready();
  }

});
Meteor.publish('theGroups', async function () {
  if (this.userId) {
    var user = await Meteor.users.findOneAsync(this.userId);
    if (user) {
      return Groups.find({ "orgId": user["orgId"] });
    } else {
      this.ready()
    }
  } else {
    this.ready()
  }
});
Meteor.publish("theUsers", async function () {
  if (this.userId) {
    var user = await Meteor.users.findOneAsync(this.userId);
    if (user) {
      var person = await user.fetchPerson();
      if (!(person.type == "staff" || person.type == "admin")) {
        return Meteor.users.find({ orgId: user["orgId"], _id: this.userId });
      } else {
        return Meteor.users.find({ "$or": [{ "orgId": user["orgId"] }, { "membership.orgId": user["orgId"] }] }, { fields: { "emails.address": 1, "personId": 1, "pending": 1, "membership": 1 } });
      }
    } else {
      this.ready()
    }
  } else {
    this.ready()
  }
});
Meteor.publish('theLoggedinPeople', async function () {
  const user = await Meteor.userAsync();
  var currentUser = user?.personId;
  if (currentUser) {
    return People.find({_id: currentUser});
  } else {
    this.ready();
  }
});
Meteor.publish('userData', function () {
  var currentUser = this.userId;
  if (currentUser) {
    return Meteor.users.find({
      _id: currentUser
    }, {
      fields: {
        "emails": 1,
        "personId": 1,
        "type": 1,
        "orgId": 1,
        "uiOptions": 1,
        "membership": 1,
        "isServiceAccount": 1
      }
    });
  } else {
    this.ready();
  }
});
Meteor.publish("theRelationships", async function () {
  if (this.userId) {
    var user = await Meteor.users.findOneAsync(this.userId);
    if (user) {
      let query = { "orgId": user["orgId"] };
      const cuser = await user.fetchPerson();
      if (cuser.type == "family") query["relationshipType"] = "family";
      return Relationships.find(query, { readPreference: "secondaryPreferred" });
    } else {
      this.ready()
    }
  } else {
    this.ready();
  }
});

Meteor.publish('theRoles', async function () {
  if (this.userId) {
    var user = await Meteor.users.findOneAsync(this.userId);
    if (user) {
      return Roles.getAllRoles();
    } else {
      this.ready()
    }
  } else {
    this.ready()
  }
});
Meteor.publish('theAnnouncements', async function (options) {
  //console.log("ANNOUNCEMENT OPTIONS", options);
  options = options || {};
  if (this.userId) {
    const user = await Meteor.users.findOneAsync(this.userId), currentPerson = user && await user.fetchPerson();
    if (currentPerson) {
      const org = await Orgs.findOneAsync({ _id: user.orgId });

      const rangeStart = options.rangeStart ?
        new moment.tz(options.rangeStart, org.getTimezone()).startOf('day').valueOf() :
        new moment.tz(org.getTimezone()).startOf("day").valueOf();
      const rangeEnd = options.rangeEnd ?
        new moment.tz(options.rangeEnd, org.getTimezone()).endOf('day').valueOf() :
        new moment.tz(org.getTimezone()).endOf("day").valueOf();

      let query = {
        "$and": [
          { orgId: user.orgId },
          {
            "$or": [
              { scheduledEndDate: { "$gte": rangeStart }, scheduledDate: { "$lt": rangeEnd } },
              { scheduledEndDate: { $exists: false }, scheduledDate: { "$gte": rangeStart, "$lte": rangeEnd } }
            ]
          }
        ]
      }, orQuery;

      if (_.contains(["admin", "staff"], currentPerson.type) && !(currentPerson.type == "admin" && options.expand)) {
        orQuery = {
          "$or": [
            { selectedRoles: currentPerson.type }
          ]
        };
        if (currentPerson.defaultGroupId) orQuery["$or"].push({ selectedGroups: currentPerson.defaultGroupId });
      }

      if (currentPerson.type == "family") {
        const connectedGroups = await getRelationshipGroupIds(currentPerson);
        orQuery = {
          "$or": [
            { selectedGroups: { $in: connectedGroups } },
            { selectedGroups: { $in: [[], null] }, selectedRoles: 'family' }
          ]
        };
      }
      if (orQuery) {
        orQuery["$or"].push({ $and: [{ selectedGroups: { $in: [[], null] } }, { selectedRoles: { $in: [[], null] } }] });
        query["$and"].push(orQuery);
      }

      //console.log("ANNOUNCEMENT QUERY:", JSON.stringify( query ));
      return Announcements.find(query, { readPreference: "secondaryPreferred" });
    } else {
      this.ready();
    }
  } else {
    this.ready()
  }

});
Meteor.publish("theFood", async function (options) {
  options = options || {};
  if (this.userId) {
    const user = await Meteor.users.findOneAsync(this.userId);
    if (user) {
      const userPerson = await user.fetchPerson();
      const org = await Orgs.findOneAsync({ _id: user.orgId });
      const rangeStart = options.startDate ?
        new moment.tz(options.startDate, "MM/DD/YYYY", org.getTimezone()).startOf('day').valueOf() :
        new moment.tz(org.getTimezone()).startOf("day").valueOf();
      const rangeEnd = options.endDate ?
        new moment.tz(options.endDate, "MM/DD/YYYY", org.getTimezone()).startOf('day').valueOf() :
        new moment.tz(org.getTimezone()).add(1, "day").startOf("day").valueOf();

      let query = {
        orgId: user.orgId,
        $and: [{ "$or": [{ recurringFrequency: { $exists: true } }, { scheduledDate: { "$gte": rangeStart, "$lte": rangeEnd } }] }]
      };
      if (userPerson.type == "family") {
        const connectedGroups = await getRelationshipGroupIds(userPerson);
        query["$and"].push({
          "$or": [{ selectedGroups: [] },
            { selectedGroups: { $in: connectedGroups } }
          ]
        });
      }

      return Foods.find(query, { readPreference: "secondaryPreferred" });
    } else {
      this.ready()
    }
  } else {
    this.ready()
  }
});
Meteor.publish('theReservations', async function (options) {
  options = options || {};
  if (this.userId) {
    var user = await Meteor.users.findOneAsync(this.userId);
    if (user) {
      const currentPerson = await user.fetchPerson();
      const query = await PublicationsUtils.getTheReservationsQuery(options, currentPerson, user.orgId);
      return Reservations.find(query, { readPreference: "secondaryPreferred" });
    } else {
      this.ready()
    }
  } else {
    this.ready()
  }
});

Meteor.publish("theOrg", async function () {
  if (this.userId) {
    const user = await Meteor.users.findOneAsync(this.userId);
    if (user) {
      return [Orgs.find({ _id: user.orgId }), await PermissionsRoles.findByOrgId(user.orgId)];
    } else {
      this.ready()
    }
  } else {
    this.ready()
  }
});
Meteor.publish('theReservationsForStaff', async function (options) {
  options = options || {};
  if (this.userId) {
    const user = await Meteor.users.findOneAsync(this.userId);
    if (user) {
      const currentPerson = await user.fetchPerson();
      const query = await PublicationsUtils.getTheReservationsQuery(options, currentPerson, user.orgId);
      return Reservations.find(query, {
        fields: {
          scheduledDate: 1,
          scheduledTime: 1,
          reservationType: 1,
          selectedPerson: 1,
          groupId: 1,
          recurringFrequency: 1,
          recurringDays: 1
        },
        readPreference: "secondaryPreferred"
      });
    } else {
      this.ready();
    }
  } else {
    this.ready();
  }
});

Meteor.publish("theRegistrationOrg", async function (orgId) {
  if (this.userId) {
    const user = await Meteor.users.findOneAsync(this.userId);
    if (user) {
      return Orgs.find({ _id: user.orgId });
    } else {
      this.ready()
    }
  } else {
    if (orgId) {
      return Orgs.find({ _id: orgId });
    }
    this.ready()
  }
});

publishComposite('orgHasEnroll', {
  async find() {
    const user = await Meteor.users.findOneAsync(this.userId);
    if (!user) {
      return;
    }
    const person = await user.fetchPerson();
    if (person && (person.type === "admin" || person.superAdmin)) {
      return Orgs.find({ _id: user.orgId });
    }
  },
  children: [
    {
      find(org) {
        return ChildcareCrmAccounts.find({ "centers.orgId": org._id });
      }
    }
  ]
});

Meteor.publish("allChildcareCrmAccounts", async function () {
  if (this.userId) {
    const user = await Meteor.users.findOneAsync(this.userId);
    const person = user && await user.fetchPerson();
    if (user && person && person.type === "admin" && person.superAdmin) {
      return ChildcareCrmAccounts.find();
    } else {
      this.ready();
    }
  } else {
    this.ready();
  }
});

Meteor.publish("allOrgs", async function (options) {
  options = options || {};
  if (this.userId) {
    const user = await Meteor.users.findOneAsync(this.userId);
    const person = user && await user.fetchPerson();
    if (user && person && person.type == "admin" && person.superAdmin) {
      const query = options.orgId ? { _id: options.orgId } : {};
      return Orgs.find(query, { readPreference: "secondaryPreferred" });
    } else {
      this.ready();
    }
  } else {
    this.ready();
  }
});

Meteor.publish("scopedOrgs", async function () {
  if (this.userId) {
    var user = await Meteor.users.findOneAsync(this.userId),
      person = user && await user.fetchPerson();
    //  console.log("scopedOrgs", person.findScopedOrgs())
    const personScopedOrgs = await person.findScopedOrgs();
    if (user && person) {
      return Orgs.find({ "_id": { "$in": personScopedOrgs.map(o => o._id) } }, { fields: { name: 1, enableSwitchOrg: 1, parentOrgId: 1 }, readPreference: "secondaryPreferred" });
    } else {
      this.ready();
    }
  } else {
    this.ready();
  }
});

Meteor.publish("theMomentDefinitions", async function () {
  if (this.userId) {
    var user = await Meteor.users.findOneAsync(this.userId);
    if (user) {
      const cuser = await user.fetchPerson();
      if (cuser.type == "admin" && cuser.superAdmin)
        return MomentDefinitions.find();
      else {
        var org = await Orgs.findOneAsync({ _id: user.orgId });
        var orgMomentTypes = org.enabledMomentTypes || [];
        return MomentDefinitions.find({ momentType: { $in: orgMomentTypes } }, { readPreference: "secondaryPreferred" });
      }
    } else {
      this.ready()
    }
  } else {
    this.ready()
  }
});
Meteor.publish("theInvoices", async function (options) {
  if (this.userId) {
    var user = await Meteor.users.findOneAsync(this.userId);
    if (user) {
      const userPerson = await user.fetchPerson();

      let query = { orgId: user.orgId };

      if (options && options.status && options.status == "open")
        query["openAmount"] = { "$gt": 0 };
      if (options && options.status && options.status == "pastdue") {
        query["openAmount"] = { "$gt": 0 };
        query["dueDate"] = { "$lte": new moment().valueOf() };
      }
      if (options && options.dateSince) {
        switch (options.dateSince) {
          case "last14":
            query["createdAt"] = { "$gt": new moment().subtract(14, "days").valueOf() };
            break;
          case "last30":
            query["createdAt"] = { "$gt": new moment().subtract(30, "days").valueOf() };
            break;
          case "last90":
            query["createdAt"] = { "$gt": new moment().subtract(90, "days").valueOf() };
            break;
        }
      }

      if (options && (options.startDate || options.endDate)) query["createdAt"] = {};
      if (options && options.startDate)
        query["createdAt"]["$gte"] = options.startDate;
      if (options && options.endDate)
        query["createdAt"]["$lt"] = options.endDate;
      if (options && options.invoiceId)
        query["_id"] = options.invoiceId;

      const targetPerson = options && options.targetPersonId && await People.findOneAsync({ orgId: user.orgId, _id: options.targetPersonId });

      const adminAccess = await processPermissions({
        assertions: [{ context: "billing/invoices", action: "read" }],
        evaluator: (person) => person.type == "admin",
      });

      if (userPerson.type == "family") {
        const connectedPeople = _.map(
          await Relationships.find({ orgId: user.orgId, personId: user.personId, relationshipType: "family" }).fetchAsync(),
          (r) => { return r.targetId; }
        );
        if (options && options.targetPersonId && _.contains(connectedPeople, options.targetPersonId)) {
          query["personId"] = options.targetPersonId;
        } else
          query["personId"] = { $in: connectedPeople };
      } else if (options && options.targetPersonId && adminAccess) {
        if (targetPerson && targetPerson.type == "family") {
          const connectedPeople = _.map(
            await Relationships.find({ orgId: user.orgId, personId: targetPerson._id, relationshipType: "family" }).fetchAsync(),
            (r) => { return r.targetId; }
          );
          query["personId"] = { $in: connectedPeople };
        } else
          query["personId"] = options.targetPersonId;
      }

      if ((adminAccess && options) || userPerson.type == "family") {
        console.log("query: ", query);
        const res = Invoices.find(query);
        const inv = await Invoices.find(query).fetch()
        console.log("Res: ", inv.length);
        return res;
      } else {
        this.ready();
      }
    } else {
      this.ready()
    }
  } else {
    this.ready()
  }
});

Meteor.publish("theAdminMessages", async function (options) {
  if (this.userId) {
    var user = await Meteor.users.findOneAsync(this.userId), person = user && await user.fetchPerson(), org = person && await Orgs.findOneAsync({ _id: person.orgId });
    if (options && person.type == "admin" && org && org.hasCustomization("messages/administrativeVisibility/enabled")) {
      var limit = options.limit || 50;
      let query = { orgId: person.orgId };
      var options = { sort: { lastActivity: -1 }, limit, skip: (options.page * limit), readPreference: "secondaryPreferred" };
      return Messages.find(query, options);
    } else {
      this.ready()
    }
  } else {
    this.ready()
  }
})

Meteor.publish("theSingleMessage", async function (options) {
  if (this.userId) {
    var user = await Meteor.users.findOneAsync(this.userId), person = user && await user.fetchPerson();
    if (user && person && options && options.messageId) {
      return Messages.find({ _id: options.messageId }, { readPreference: "secondaryPreferred" });
    } else {
      this.ready();
    }
  } else {
    this.ready();
  }
})

Meteor.publish("theQualifiedRecipientMessages", async function (options) {
  if (this.userId) {
    var user = await Meteor.users.findOneAsync(this.userId), person = user && await user.fetchPerson();
    if (user && person) {
      var limit = options.limit || 25;
      let qualifiedRecipients = [user.personId];
      qualifiedRecipients = qualifiedRecipients.concat(
        _.map(
          await Relationships.find({ orgId: user.orgId, personId: user.personId, relationshipType: "family" }).fetchAsync(),
          (r) => { return r.targetId; }
        )
      );
      var options = { sort: { lastActivity: -1 }, limit, skip: (options.page * limit), readPreference: "secondaryPreferred" };
      let query = {
        orgId: user.orgId,
        currentRecipients: { "$in": qualifiedRecipients },
      };

      return Messages.find(query, options);

    } else {
      this.ready()
    }
  } else {
    this.ready()
  }
})

Meteor.publish("thePersonMessages", async function (options) {
  if (this.userId) {
    var user = await Meteor.users.findOneAsync(this.userId), person = user && await user.fetchPerson();
    if (user && person) {
      var limit = options.limit || 25;
      let query = {
        orgId: user.orgId,
        personId: user.personId,
      };

      var options = { sort: { lastActivity: -1 }, limit, skip: (options.page * limit), readPreference: "secondaryPreferred" };
      return Messages.find(query, options);

    } else {
      this.ready()
    }
  } else {
    this.ready()
  }
})



Meteor.publish("theMessages", async function (options) {
  if (this.userId) {
    var user = await Meteor.users.findOneAsync(this.userId), person = user && await user.fetchPerson(), org = person && await Orgs.findOneAsync({ _id: person.orgId });

    if (user && person) {
      let query = {
        orgId: person.orgId,
      };

      if (options && options.lastThirty && (person.type == "admin" || person.type == "staff")) {
        var startDateNum = new moment().subtract(30, 'days').startOf('day').valueOf();
        var endDateNum = new moment().endOf('day').valueOf();
        query["lastActivity"] = { "$gte": startDateNum, "$lte": endDateNum };
      }

      if (options && (options.administrativeVisibility || options.messageId) && person.type == "admin" && org && org.hasCustomization("messages/administrativeVisibility/enabled")) {
        if (options.messageId) query["_id"] = options.messageId;
        return Messages.find(query, { limit: 100 });
      }

      let qualifiedRecipients = [user.personId];
      qualifiedRecipients = qualifiedRecipients.concat(
        _.map(
          await Relationships.find({ orgId: user.orgId, personId: user.personId, relationshipType: "family" }).fetchAsync(),
          (r) => { return r.targetId; }
        )
      );

      query["$or"] = [
        { personId: user.personId },
        { currentRecipients: { "$in": qualifiedRecipients } }
      ];

      if (options && options.messageId)
        query["_id"] = options.messageId;

      return Messages.find(query, { readPreference: "secondaryPreferred" });
    } else {
      this.ready()
    }
  } else {
    this.ready()
  }
});

Meteor.publish("thePeopleDataValidation", async function (options = {}) {
  if (this.userId) {
    var user = await Meteor.users.findOneAsync(this.userId), org = user && await Orgs.findOneAsync({ _id: user.orgId }), userPerson = user && await user.fetchPerson();
    if (user && userPerson) {
      let query = { orgId: user.orgId };
      if (options._id) query._id = options._id;

      if (options.mobile || userPerson.type == "family") {
        query.active = true;
        query.people = { $in: [userPerson._id] };
        query["responses.personId"] = { $nin: [userPerson._id] };
      }

      return PeopleDataValidations.find(query, { readPreference: "secondaryPreferred" });
    } else {
      this.ready()
    }
  } else {
    this.ready()
  }
});

Meteor.publish("theUserInvitations", async function (options) {
  if (this.userId) {
    var user = await Meteor.users.findOneAsync(this.userId), org = user && await Orgs.findOneAsync({ _id: user.orgId }), userPerson = user && await user.fetchPerson();
    if (user && userPerson && userPerson.type == "admin" && options.personId) {
      return UserInvitations.find({ orgId: org._id, personId: options.personId });
    } else {
      this.ready()
    }
  } else {
    this.ready()
  }
});

Meteor.publish("theTimeCards", async function (options) {
  if (this.userId) {
    var user = await Meteor.users.findOneAsync(this.userId), org = user && await Orgs.findOneAsync({ _id: user.orgId }), userPerson = user && await user.fetchPerson();
    if (user && userPerson && userPerson.type == "admin") {
      return await TimeCards.queryWithOptions(org, options);
    } else {
      this.ready()
    }
  } else {
    this.ready()
  }
});

publishComposite('metaMoments', {
  async find() {
    const user = await Meteor.users.findOneAsync(this.userId), currentPerson = user && await user.fetchPerson();
    if (!user || !currentPerson) return;
    if (!_.contains(["admin", "staff"], currentPerson.type)) return;
    if (!(currentPerson.checkInGroupId || currentPerson.defaultGroupId)) return;

    const query = {
      defaultGroupId: currentPerson.checkInGroupId || currentPerson.defaultGroupId,
      active: true,
      orgId: user.orgId,
    };

    return MetaMoments.find(query, { sort: { sortStamp: -1 } });
  },
  children: [
    {
      find(metaMoment) {
        const sort = (metaMoment.orderChildrenBy) ? metaMoment.orderChildrenBy : { sortStamp: -1 };
        return Moments.find({ _id: { $in: metaMoment.moments } }, { sort })
      }
    },
  ]
});

Meteor.publish("auditTrailByTargetId", async function (options) {
  if (!this.userId) {
    this.ready();
  }
  const currentPerson = await Meteor.users.findOneAsync(this.userId);
  const userPerson = await currentPerson.fetchPerson();
  if (userPerson.type !== 'admin') {
    this.ready();
  }
  return AuditTrails.find({ 'args.personId': options.targetId, orgId: userPerson.orgId }, { sort: { date: -1 } });

});

Meteor.publish('theCheckInRatio', async function (options) {
  if (this.userId) {
    var user = await Meteor.users.findOneAsync(this.userId), org = user && await Orgs.findOneAsync({ _id: user.orgId });
    if (org && org._id ) {
      return CheckinRatios.find({ orgId: org._id });
    } else {
      this.ready()
    }
  } else {
    this.ready()
  }
});

Meteor.publish('theCheckinStatsCount', async function () {

  if (this.userId) {
    await theCheckinStatsCount();
    const user = await Meteor.users.findOneAsync(this.userId);
    const currentPerson = user && await user.fetchPerson();
    if (user && currentPerson) {
      return Counts.find({ orgId: currentPerson.orgId });
    } else {
      this.ready();
    }
  } else {
    this.ready();
  }

});

Meteor.publish('theAbsentPeople', async function () {
  if (this.userId) {
    const user = await Meteor.users.findOneAsync(this.userId);
    const currentPerson = await user.fetchPerson();
    const org = await Orgs.current();
    const timezone = org.getTimezone();
    const startOfDay = new moment.tz(moment().startOf('day'), timezone).valueOf();
    const endOfDay = new moment.tz(moment().endOf('day'), timezone).valueOf();
    const query = await PublicationsUtils.getTheReservationsQuery({ includeCancellations: true }, currentPerson, user.orgId)


    const cancelledReservations = await Reservations.findWithRecurrence({
      startDateValue: startOfDay,
      endDateValue: endOfDay,
      query: {
        orgId: user.orgId,
        cancellationReason: { $exists: true }
      }
    })

    const canceledReservationPeopleIds = cancelledReservations.map(p => p.selectedPerson);

    const peopleQuery = {
      orgId: user.orgId,
      $or: [
        {
          _id: { $in: canceledReservationPeopleIds },
          orgId: org._id,
          type: 'person',
        },
        {
          _id: { $nin: canceledReservationPeopleIds },
          orgId: org._id,
          inActive: { $ne: true },
          checkedIn: { $ne: true },
          type: 'person',
          'familyCheckIn.absent': true,
          'familyCheckIn.checkInTime': { $gte: startOfDay }
        }
      ]
    }
    if (!['admin', 'staff'].includes(currentPerson.type)) {
      peopleQuery._id = { $in: (await RelUtils.getRelatedProfiles(currentPerson)) }
    }
    return People.find(peopleQuery);
  } else {
    this.ready();
  }

});

Meteor.publish("expressDriveUpPeople", async function (options) {
  this.unblock();
  if (this.userId) {
    const user = await Meteor.users.findOneAsync(this.userId);
    const query = {
      orgId: user.orgId,
      $or: [
        { "familyCheckIn.dropOffArrival": true },
        { "familyCheckOut.pickUpArrival": true },
        {
          "familyCheckIn.dropOffTimeEstimate": { $exists: true },
        },
        {
          "familyCheckOut.pickUpTimeEstimate": { $exists: true },
        },
      ],
      inActive: { $ne: true }
    };
    const currentPerson = await user.fetchPerson();
    if (!['admin', 'staff'].includes(currentPerson.type)) {
      query._id = { $in: (await RelUtils.getRelatedProfiles(currentPerson)) }
    }
    return People.find(query);
  } else {
    this.ready();
  }
});

async function getRelationshipGroupIds(familyPerson) {
  let connectedGroups = [];
  const relationships = await Relationships.find({ orgId: familyPerson.orgId, personId: familyPerson._id, relationshipType: "family" }).fetchAsync();

  for (const r of relationships) {
    const targetPerson = await People.findOneAsync({ orgId: familyPerson.orgId, _id: r.targetId });
    if (targetPerson && targetPerson.defaultGroupId) {
      connectedGroups.push(targetPerson.defaultGroupId);
    }
  }
  return connectedGroups;
}

async function personMomentsWithOptions(passedOptions) {

  var valid = false;

  const { personId, limit, startDate, endDate, user, person } = passedOptions;

  const internalOnlyMomentTypes = await MomentDefinitions.find({ internalOnly: true }).mapAsync((md) => md.momentType),
    activeInternalOnlyMomentTypes = internalOnlyMomentTypes.filter((mt) => {
      return !passedOptions.filterState || (passedOptions.filterState && (passedOptions.filterState.showAll || _.contains(passedOptions.filterState.activeFilters, mt)));
    });

  var currentPerson = await user.fetchPerson();
  if (user.personId == personId) valid = true;
  if (currentPerson.type == "family" &&
    await Relationships.find({ targetId: personId, relationshipType: "family", $or: [{ personId: currentPerson._id }, { targetId: currentPerson._id }] }).countAsync() > 0)
    valid = true;
  if ((currentPerson.type == "staff" || currentPerson.type == "admin") && currentPerson.orgId == person.orgId) valid = true;
  if (valid) {
    var options = { sort: { sortStamp: -1 } };
    var query = { orgId: currentPerson.orgId, taggedPeople: personId };
    if (limit) options["limit"] = limit;
    if (startDate && endDate) {
      query.sortStamp = {
        $gte: moment(startDate).startOf('day').valueOf(),
        $lte: moment(endDate).endOf('day').valueOf()
      };
    } else if (currentPerson.type == "staff") {
      //optimization to speed up people detail activity loading
      query.sortStamp = {
        $gte: new moment().startOf('day').subtract(7, 'days').valueOf(),
        $lte: new moment().endOf('day').valueOf()
      }
    }


    if (currentPerson.type == "staff" && internalOnlyMomentTypes) {
      query["$or"] = [
        { "momentType": { "$in": activeInternalOnlyMomentTypes }, "adminOnly": { "$ne": true } },
      ];
      if (passedOptions.filterState && !passedOptions.filterState.showAll) {
        const activeNonInternalMomentTypes = passedOptions.filterState.activeFilters.filter((mt) => !_.contains(activeInternalOnlyMomentTypes, mt));
        if (activeNonInternalMomentTypes.length > 0) query["$or"].push({ "momentType": { "$in": activeNonInternalMomentTypes } });
      } else if (!passedOptions.filterState || passedOptions.filterState.showAll) {
        query["$or"].push({ "momentType": { "$nin": activeInternalOnlyMomentTypes } })
      }
    }

    if (currentPerson.type == "family" || currentPerson.type == "person")
      query["momentType"] = { "$nin": internalOnlyMomentTypes.concat("prospect").concat("portfolio").concat(MomentTypes.NAME_TO_FACE) };

    if (passedOptions.filterState) {
      if (!passedOptions.filterState.showAll && !query["$or"]) {
        query["momentType"] = { "$in": passedOptions.filterState.activeFilters };
      }
      if (passedOptions.filterState.showMediaOnly)
        query["mediaFiles"] = { $exists: true, $ne: [] };
    }

    options["readPreference"] = "secondaryPreferred";
    //console.log(JSON.stringify(query, null, 2))
    return Moments.find(query, options);
  }
}

async function groupMomentsWithOptions(options) {

  const { limit, groupId, user, userPerson } = options;

  var query = {};
  const internalOnlyMomentTypes = await MomentDefinitions.find({ internalOnly: true }).mapAsync((md) => md.momentType),
    activeInternalOnlyMomentTypes = internalOnlyMomentTypes.filter((mt) => {
      return !options.filterState || (options.filterState && (options.filterState.showAll || _.contains(options.filterState.activeFilters, mt)));
    });

  var includeList = await People.find({
    orgId: user.orgId,
    $or:
      [
        { defaultGroupId: groupId },
        { checkInGroupId: groupId }
      ]
  }).mapAsync(function (p) { return p._id; });

  var query = {
    orgId: user.orgId,
    taggedPeople: { $in: includeList },
    momentType: { $ne: "prospect" }
  };

  if (userPerson.type == "staff" && internalOnlyMomentTypes) {
    query["$or"] = [
      { "momentType": { "$in": activeInternalOnlyMomentTypes }, "adminOnly": { "$ne": true } },
    ];
    if (options.filterState && !options.filterState.showAll) {
      const activeNonInternalMomentTypes = options.filterState.activeFilters.filter((mt) => !_.contains(activeInternalOnlyMomentTypes, mt));
      if (activeNonInternalMomentTypes.length > 0) query["$or"].push({ "momentType": { "$in": activeNonInternalMomentTypes } });
    } else if (!options.filterState || options.filterState.showAll) {
      query["$or"].push({ "momentType": { "$nin": activeInternalOnlyMomentTypes } })
    }
  }

  var localLimit = limit || 10;

  if (options.filterState) {
    if (!options.filterState.showAll && !query["$or"]) {
      query["momentType"] = { "$in": options.filterState.activeFilters };
    }
    if (options.filterState.showMediaOnly)
      query["mediaFiles"] = { $exists: true, $ne: [] };
  }

  //console.log(JSON.stringify(query));

  return Moments.find(query, { sort: { sortStamp: -1 }, limit: localLimit, readPreference: "secondaryPreferred" });
}

async function dashboardMomentsWithOptions(options) {

  const { limit, showAll, momentId, user, userPerson } = options;
  var query = {};
  const internalOnlyMomentTypes = await MomentDefinitions.find({ internalOnly: true }).mapAsync((md) => md.momentType),
    activeInternalOnlyMomentTypes = internalOnlyMomentTypes.filter((mt) => {
      return !options.filterState || (options.filterState && (options.filterState.showAll || _.contains(options.filterState.activeFilters, mt)));
    });

  if (userPerson.type == "admin" || userPerson.type == "staff") {
    query = { orgId: user.orgId };
    let staffQuery = {
      orgId: user.orgId
    }, useGroupId;
    if (userPerson.checkInGroupId) {
      useGroupId = userPerson.checkInGroupId;
    } else if (userPerson.type == "staff" && userPerson.defaultGroupId) {
      useGroupId = userPerson.defaultGroupId;
    }
    if (useGroupId) {
      staffQuery['$or'] = [
        { defaultGroupId: useGroupId },
        { checkInGroupId: useGroupId }
      ];
      var includeList = await People.find(staffQuery).mapAsync(function (p) { return p._id; });
      query['taggedPeople'] = { $in: includeList };
    };

    if (userPerson.type == "staff" && internalOnlyMomentTypes) {
      query["$or"] = [
        { "momentType": { "$in": activeInternalOnlyMomentTypes }, "adminOnly": { "$ne": true } },
      ];
      if (options.filterState && !options.filterState.showAll) {
        const activeNonInternalMomentTypes = options.filterState.activeFilters.filter((mt) => !_.contains(activeInternalOnlyMomentTypes, mt));
        if (activeNonInternalMomentTypes.length > 0) query["$or"].push({ "momentType": { "$in": activeNonInternalMomentTypes } });
      } else if (!options.filterState || options.filterState.showAll) {
        query["$or"].push({ "momentType": { "$nin": activeInternalOnlyMomentTypes } })
      }
    }
  }
  else {
    var includeList = [];
    if (user._id) includeList.push(user._id);
    const cuser = await user.fetchPerson();
    includeList = includeList.concat(await Relationships.find({ relationshipType: "family", $or: [{ personId: cuser._id }, { targetId: cuser._id }] }).mapAsync(
      function (m, index) { return m.targetId; }));

    query = { orgId: user.orgId, taggedPeople: { $in: includeList } };
    if (cuser.type == "family" || cuser.type == "person") {
      query["momentType"] = { "$nin": internalOnlyMomentTypes.concat("prospect").concat("portfolio").concat(MomentTypes.NAME_TO_FACE) };
    }

  }

  if (momentId) {
    query = { orgId: user.orgId, _id: momentId };
  }

  var localLimit = limit || 10;

  if (options.filterState) {
    if (!options.filterState.showAll && !query["$or"]) {
      query["momentType"] = { "$in": options.filterState.activeFilters };
    }
    if (options.filterState.showMediaOnly)
      query["mediaFiles"] = { $exists: true, $ne: [] };
  }

  //console.log("query", JSON.stringify(query,null,2));

  return Moments.find(query, { sort: { sortStamp: -1 }, limit: localLimit, readPreference: "secondaryPreferred" });
}
