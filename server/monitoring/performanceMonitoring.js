import { Meteor } from 'meteor/meteor';
import { MongoInternals } from 'meteor/mongo';
import os from 'os';

// In-memory storage for metrics
const metrics = {
    methods: {},
    subscriptions: {},
    routes: {},
    errors: 0,
    totalMethodCalls: 0,
    critical: {
        memoryLeaks: [],
        activeIssues: [],
        recentErrors: []
    },
    heavyQueries: {},
    errorRates: {
        methods: {},
        subscriptions: {},
        routes: {}
    },
    performanceAlerts: [],
    connections: {
        active: 0,
        total: 0,
        lastUpdate: Date.now()
    }
};

const THRESHOLDS = {
    METHOD_TIME_WARNING: 300,
    METHOD_TIME_CRITICAL: 1000,
    METHOD_MEMORY_CRITICAL: 10 * 1024 * 1024,

    SUBSCRIPTION_TIME_WARNING: 500,
    SUBSCRIPTION_TIME_CRITICAL: 2000,
    SUBSCRIPTION_SIZE_CRITICAL: 5 * 1024 * 1024,

    ROUTE_TIME_WARNING: 500,

    QUERY_TIME_WARNING: 1000,
    QUERY_TIME_CRITICAL: 2000,
    QUERY_MEMORY_CRITICAL: 5 * 1024 * 1024,
    QUERY_DOCS_CRITICAL: 1000
};

const PERFORMANCE_THRESHOLDS = {
    MEMORY_CRITICAL: 90,
    CPU_CRITICAL: 90,
    CRITICAL_METHODS_THRESHOLD: 5,
    MIN_MEMORY_GROWTH: 0.1,
    ALERT_COOLDOWN: 30 * 60 * 1000
};

// Track start time for uptime calculation
const serverStartTime = Date.now();

// Add cache for performance metrics
const metricsCache = {
    data: null,
    lastUpdate: 0,
    cacheDuration: 5000 // 5 seconds cache
};

// Set up periodic checks for system health
export function setupPeriodicChecks() {
    // Check for memory issues every 5 minutes
    Meteor.setInterval(() => {
        checkMemoryUsage();
    }, 5 * 60 * 1000);

    // Check for CPU issues every minute
    Meteor.setInterval(() => {
        checkCPUUsage();
    }, 60 * 1000);

    // Check for critical performance issues every 5 minutes
    Meteor.setInterval(() => {
        checkCriticalPerformance();
    }, 5 * 60 * 1000);

    // Reset error rates every hour
    Meteor.setInterval(() => {
        resetErrorRates();
    }, 60 * 60 * 1000);
}

function resetErrorRates() {
    try {
        Object.keys(metrics.errorRates).forEach(type => {
            metrics.errorRates[type].count = 0;
            metrics.errorRates[type].lastReset = Date.now();
        });
    } catch (e) {
        console.error('[Performance Monitor] Error resetting error rates:', e);
    }
}

function checkCriticalPerformance() {
    try {
        const memoryUsage = getMemoryUsage();
        const cpuUsage = getCPUUsage();
        const criticalMethods = Object.values(metrics.methods).filter(method =>
            method.avgTime >= THRESHOLDS.METHOD_TIME_CRITICAL
        );

        if (memoryUsage > PERFORMANCE_THRESHOLDS.MEMORY_CRITICAL ||
            cpuUsage > PERFORMANCE_THRESHOLDS.CPU_CRITICAL ||
            criticalMethods.length > PERFORMANCE_THRESHOLDS.CRITICAL_METHODS_THRESHOLD) {

            addPerformanceAlert(
                'Performance Degradation',
                'critical',
                `System performance degraded: Memory ${memoryUsage}%, CPU ${cpuUsage}%, ${criticalMethods.length} critical methods`
            );
        }
    } catch (e) {
        console.error('[Performance Monitor] Error checking critical performance:', e);
    }
}

function addPerformanceAlert(type, severity, message) {
    try {
        const now = Date.now();
        const lastAlert = metrics.performanceAlerts[metrics.performanceAlerts.length - 1];

        if (lastAlert &&
            lastAlert.type === type &&
            (now - lastAlert.timestamp) < PERFORMANCE_THRESHOLDS.ALERT_COOLDOWN) {
            return;
        }

        metrics.performanceAlerts.push({
            type,
            severity,
            message,
            timestamp: now
        });

        if (metrics.performanceAlerts.length > 100) {
            metrics.performanceAlerts.shift();
        }

        if (severity === 'critical') {
            Meteor.call('addCriticalIssue', type, severity, message);
        }

        return true;
    } catch (e) {
        console.error('[Performance Monitor] Error adding performance alert:', e);
        return false;
    }
}

// Check for memory issues
function checkMemoryUsage() {
    try {
        const memUsage = getMemoryUsage();

        // If memory usage is high, create an issue
        if (memUsage > 85) {
            // Check if we already have a memory issue
            const existingIssue = metrics.critical.activeIssues.find(
                issue => issue.type === 'High Memory Usage' && issue.status !== 'Resolved'
            );

            if (!existingIssue) {
                metrics.critical.activeIssues.push({
                    type: 'High Memory Usage',
                    severity: 'high',
                    details: `Memory usage at ${memUsage}%, potential memory leak`,
                    detected: new Date(),
                    status: 'New'
                });
            }

            // Track potential memory leak
            trackMemoryGrowth();
        }
    } catch (e) {
        console.error('[Performance Monitor] Error checking memory usage:', e);
    }
}

let lastMemorySnapshot = process.memoryUsage();
let lastSnapshotTime = Date.now();

function trackMemoryGrowth() {
    try {
        const currentMem = process.memoryUsage();
        const currentTime = Date.now();

        // Calculate time difference in hours
        const hoursPassed = (currentTime - lastSnapshotTime) / (1000 * 60 * 60);

        if (hoursPassed >= 1) {
            const heapGrowth = currentMem.heapUsed - lastMemorySnapshot.heapUsed;
            const growthRatePerHour = (heapGrowth / lastMemorySnapshot.heapUsed) * 100 / hoursPassed;

            if (growthRatePerHour > PERFORMANCE_THRESHOLDS.MIN_MEMORY_GROWTH) {
                const existingLeak = metrics.critical.memoryLeaks.find(
                    leak => leak.component === 'Node.js Heap'
                );

                if (existingLeak) {
                    existingLeak.size = currentMem.heapUsed;
                    existingLeak.growthRate = growthRatePerHour.toFixed(1);
                    existingLeak.lastUpdated = new Date();
                } else {
                    metrics.critical.memoryLeaks.push({
                        component: 'Node.js Heap',
                        size: currentMem.heapUsed,
                        growthRate: growthRatePerHour.toFixed(1),
                        firstDetected: new Date(),
                        lastUpdated: new Date()
                    });

                    addPerformanceAlert(
                        'Memory Leak',
                        'critical',
                        `New memory leak detected: ${growthRatePerHour.toFixed(1)}% growth per hour`
                    );
                }
            }

            // Check for RSS memory growth
            const rssGrowth = currentMem.rss - lastMemorySnapshot.rss;
            const rssGrowthRatePerHour = (rssGrowth / lastMemorySnapshot.rss) * 100 / hoursPassed;

            if (rssGrowthRatePerHour > PERFORMANCE_THRESHOLDS.MIN_MEMORY_GROWTH) {
                const existingLeak = metrics.critical.memoryLeaks.find(
                    leak => leak.component === 'Process RSS Memory'
                );

                if (existingLeak) {
                    existingLeak.size = currentMem.rss;
                    existingLeak.growthRate = rssGrowthRatePerHour.toFixed(1);
                    existingLeak.lastUpdated = new Date();
                } else {
                    metrics.critical.memoryLeaks.push({
                        component: 'Process RSS Memory',
                        size: currentMem.rss,
                        growthRate: rssGrowthRatePerHour.toFixed(1),
                        firstDetected: new Date(),
                        lastUpdated: new Date()
                    });

                    addPerformanceAlert(
                        'RSS Memory Leak',
                        'critical',
                        `New RSS memory leak detected: ${rssGrowthRatePerHour.toFixed(1)}% growth per hour`
                    );
                }
            }
        }

        lastMemorySnapshot = currentMem;
        lastSnapshotTime = currentTime;
    } catch (e) {
        console.error('[Performance Monitor] Error tracking memory growth:', e);
    }
}

// Check for CPU issues
function checkCPUUsage() {
    try {
        const cpuUsage = getCPUUsage();

        // If CPU usage is high, create an issue
        if (cpuUsage > 85) {
            // Check if we already have a CPU issue
            const existingIssue = metrics.critical.activeIssues.find(
                issue => issue.type === 'High CPU Usage' && issue.status !== 'Resolved'
            );

            if (!existingIssue) {
                metrics.critical.activeIssues.push({
                    type: 'High CPU Usage',
                    severity: 'high',
                    details: `CPU usage at ${cpuUsage}%, potential performance bottleneck`,
                    detected: new Date(),
                    status: 'New'
                });
            }
        }
    } catch (e) {
        console.error('[Performance Monitor] Error checking CPU usage:', e);
    }
}

// Get real memory usage percentage
function getMemoryUsage() {
    try {
        // Get Node.js process memory instead of system memory
        const memoryUsage = process.memoryUsage();

        // Calculate what percentage of the heap is used
        const heapUsed = memoryUsage.heapUsed;
        const heapTotal = memoryUsage.heapTotal;

        return Math.round((heapUsed / heapTotal) * 100);
    } catch (e) {
        console.error('[Performance Monitor] Error getting memory usage:', e);
        return 0;
    }
}

// Get real CPU usage percentage (average across all cores)
let lastCpuInfo = os.cpus();
let lastCpuTime = Date.now();

function getCPUUsage() {
    try {
        const currentCpuInfo = os.cpus();
        const currentTime = Date.now();

        let totalUser = 0;
        let totalSys = 0;
        let totalIdle = 0;

        for (let i = 0; i < currentCpuInfo.length; i++) {
            const cpu = currentCpuInfo[i];
            const lastCpu = lastCpuInfo[i];

            // Calculate CPU time differences
            const userDiff = cpu.times.user - lastCpu.times.user;
            const sysDiff = cpu.times.sys - lastCpu.times.sys;
            const idleDiff = cpu.times.idle - lastCpu.times.idle;

            totalUser += userDiff;
            totalSys += sysDiff;
            totalIdle += idleDiff;
        }

        // Total time spent on all cores
        const totalTime = totalUser + totalSys + totalIdle;

        // Percentage of time spent doing work
        const cpuUsage = Math.round(((totalUser + totalSys) / totalTime) * 100);

        // Update for next calculation
        lastCpuInfo = currentCpuInfo;
        lastCpuTime = currentTime;

        return cpuUsage;
    } catch (e) {
        console.error('[Performance Monitor] Error getting CPU usage:', e);
        return 0;
    }
}

// Check MongoDB connection status
function getMongoStatus() {
    try {
        const mongoDriver = MongoInternals.defaultRemoteCollectionDriver();
        const mongo = mongoDriver.mongo;

        if (mongo && mongo.topology) {
            return mongo.topology.isConnected() ? 'Connected' : 'Disconnected';
        }

        return 'Unknown';
    } catch (e) {
        console.error('[Performance Monitor] Error getting MongoDB status:', e);
        return 'Error';
    }
}

// Get MongoDB statistics
async function getMongoStats() {
    try {
        const mongoDriver = MongoInternals.defaultRemoteCollectionDriver();
        const mongo = mongoDriver.mongo;

        if (mongo && mongo.db) {
            const stats = await mongo.db().stats();
            return stats;
        }

        return null;
    } catch (e) {
        console.error('[Performance Monitor] Error getting MongoDB stats:', e);
        return null;
    }
}

// Get server uptime
function getServerUptime() {
    return Math.floor((Date.now() - serverStartTime) / 1000);
}

// Get process stats
function getProcessStats() {
    try {
        const memoryUsage = process.memoryUsage();

        return {
            rss: memoryUsage.rss, // Resident Set Size - total memory allocated
            heapTotal: memoryUsage.heapTotal, // Total size of allocated heap
            heapUsed: memoryUsage.heapUsed, // Actual memory used in the heap
            external: memoryUsage.external, // Memory used by C++ objects
            arrayBuffers: memoryUsage.arrayBuffers || 0 // Memory for ArrayBuffers and SharedArrayBuffers
        };
    } catch (e) {
        console.error('[Performance Monitor] Error getting process stats:', e);
        return null;
    }
}

// Server methods
Meteor.methods({
    getCriticalMethods() {
        try {
            // Get all methods
            const methods = Object.keys(metrics.methods).map(name => {
                const m = metrics.methods[name] || { count: 0, totalTime: 0, maxTime: 0 };

                // Estimate memory usage (if we have it)
                let memoryUsage = 0;
                if (m.memoryEstimate) {
                    memoryUsage = m.memoryEstimate;
                }

                return {
                    name: name,
                    count: m.count || 0,
                    avgTime: m.count ? Math.round((m.totalTime || 0) / m.count) : 0,
                    maxTime: m.maxTime || 0,
                    memoryUsage: memoryUsage
                };
            });

            // Filter to only critical methods
            const criticalMethods = methods.filter(method =>
                method.avgTime >= 1000 || // Methods taking over 1 second
                method.memoryUsage >= 10 * 1024 * 1024 // Methods using more than 10MB
            );

            return criticalMethods.sort((a, b) => b.avgTime - a.avgTime);
        } catch (e) {
            console.error("[Performance Monitor] Error in getCriticalMethods:", e);
            return [];
        }
    },

    getCriticalSubscriptions() {
        try {
            // Get all subscriptions
            const subArray = Object.keys(metrics.subscriptions).map(name => {
                const s = metrics.subscriptions[name] || { count: 0, totalReadyTime: 0 };

                // Estimate data size if available
                let dataSize = 0;
                let documentCount = 0;

                if (s.dataSize) {
                    dataSize = s.dataSize;
                }

                if (s.documentCount) {
                    documentCount = s.documentCount;
                }

                return {
                    name: name,
                    count: s.count || 0,
                    readyTime: s.count ? Math.round((s.totalReadyTime || 0) / s.count) : 0,
                    dataSize: dataSize,
                    documentCount: documentCount
                };
            });

            // Filter to only critical subscriptions
            const criticalSubs = subArray.filter(sub =>
                sub.readyTime >= 2000 || // Subscriptions taking over 2 seconds to be ready
                sub.dataSize >= 5 * 1024 * 1024 // Subscriptions with more than 5MB of data
            );

            return criticalSubs.sort((a, b) => b.readyTime - a.readyTime);
        } catch (e) {
            console.error("[Performance Monitor] Error in getCriticalSubscriptions:", e);
            return [];
        }
    },
    async getPerformanceMetrics() {
        // Check cache first
        const now = Date.now();
        if (metricsCache.data && (now - metricsCache.lastUpdate) < metricsCache.cacheDuration) {
            return metricsCache.data;
        }

        try {
            const basicMetrics = {
                userCount: 0,
                activeUserCount: 0,
                activeSessionsCount: 0,
                averageResponseTime: 0,
                errorRate: "0.00",
                connectionMetrics: {
                    active: 0,
                    total: 0,
                    lastUpdate: Date.now()
                }
            };

            const [methodMetrics, userCounts] = await Promise.all([
                // Calculate method metrics (optimized)
                new Promise((resolve) => {
                    let totalTime = 0;
                    let methodCount = 0;
                    let totalErrors = 0;

                    const methodValues = Object.values(metrics.methods || {});
                    for (let i = 0; i < methodValues.length; i++) {
                        const method = methodValues[i];
                        if (method.totalTime && method.count) {
                            totalTime += method.totalTime;
                            methodCount += method.count;
                        }
                    }

                    Object.values(metrics.errorRates || {}).forEach(errorRate => {
                        if (errorRate.count) {
                            totalErrors += errorRate.count;
                        }
                    });

                    const avgResponseTime = methodCount > 0 ? Math.round(totalTime / methodCount) : 0;
                    const errorRate = methodCount > 0 ? ((totalErrors / methodCount) * 100).toFixed(2) : "0.00";

                    resolve({
                        averageResponseTime: avgResponseTime,
                        errorRate
                    });
                }),

                new Promise(async (resolve) => {
                    try {
                        if (Meteor.users) {
                            const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);

                            const [totalUsers, activeUsers] = await Promise.all([
                                Meteor.users.find().countAsync(),
                                Meteor.users.find({
                                    'status.online': true,
                                    'status.lastActivity': { $gte: fiveMinutesAgo }
                                }, { fields: { _id: 1 } }).countAsync()
                            ]);

                            resolve({
                                userCount: totalUsers,
                                activeUserCount: activeUsers
                            });
                        } else {
                            resolve({ userCount: 0, activeUserCount: 0 });
                        }
                    } catch (e) {
                        console.error("[PerformanceMonitor] Error getting user counts:", e);
                        resolve({ userCount: 0, activeUserCount: 0 });
                    }
                })
            ]);

            // Get session count
            let sessionCount = 0;
            try {
                if (Meteor.server && Meteor.server.stream_server) {
                    const connections = Meteor.server.stream_server.all_sockets();
                    if (connections) {
                        sessionCount = connections.filter(socket =>
                            socket && !socket.destroyed && socket.readyState === 1
                        ).length;
                    }
                } else if (Meteor.server && Meteor.server.sessions) {
                    if (typeof Meteor.server.sessions === 'object') {
                        sessionCount = Object.values(Meteor.server.sessions).filter(session =>
                            session && !session.isClosed && session.socket && !session.socket.destroyed
                        ).length;
                    }
                } else if (WebApp && WebApp.httpServer) {
                    sessionCount = WebApp.httpServer.connections;
                }
            } catch (e) {
                console.error("[PerformanceMonitor] Error getting session count:", e);
            }

            // Update connection metrics
            if (!metrics.connections) {
                metrics.connections = {
                    active: 0,
                    total: 0,
                    lastUpdate: Date.now()
                };
            }

            metrics.connections.active = sessionCount;
            metrics.connections.lastUpdate = Date.now();

            // Combine all metrics
            const result = {
                ...basicMetrics,
                ...userCounts,
                ...methodMetrics,
                activeSessionsCount: sessionCount,
                connectionMetrics: metrics.connections
            };

            // Update cache
            metricsCache.data = result;
            metricsCache.lastUpdate = now;

            return result;
        } catch (e) {
            console.error("[PerformanceMonitor] Error in getPerformanceMetrics:", e);
            return {
                userCount: 0,
                activeUserCount: 0,
                activeSessionsCount: 0,
                averageResponseTime: 0,
                errorRate: "0.00",
                connectionMetrics: {
                    active: 0,
                    total: 0,
                    lastUpdate: Date.now()
                }
            };
        }
    },
    async getActiveUsers() {
        if (!Meteor.userId()) {
            throw new Meteor.Error('not-authorized', 'User must be logged in');
        }

        // Check if the user is an admin or superadmin
        const currentUser = await Meteor.users.findOneAsync(Meteor.userId());
        if (!currentUser || !['admin', 'superadmin'].includes(currentUser.type)) {
            throw new Meteor.Error('not-authorized', 'User must be an admin or superadmin');
        }

        const activeUsers = await Meteor.users.find(
            { 'status.online': true },
            {
                fields: {
                    username: 1,
                    'status.lastActivity': 1,
                    'status.lastLogin': 1,
                    'status.idle': 1
                }
            }
        ).fetchAsync();

        // Count active sessions
        let activeSessionsCount = 0;
        if (Meteor.server && Meteor.server.sessions) {
            if (typeof Meteor.server.sessions === 'object') {
                activeSessionsCount = Object.keys(Meteor.server.sessions).length;
            } else if (typeof Meteor.server.sessions === 'function') {
                activeSessionsCount = Meteor.server.sessions().length;
            }
        }

        return {
            activeUsers: activeUsers.map(user => ({
                username: user.username,
                lastActivity: user.status.lastActivity,
                sessionDuration: Date.now() - (user.status.lastLogin?.date || Date.now()),
                ipAddress: user.status.lastLogin?.ipAddr || 'Unknown'
            })),
            activeSessionsCount: activeSessionsCount
        };
    },
    getMethodMetrics() {
        try {
            // Convert to array and sort by call count
            const methodArray = Object.keys(metrics.methods).map(name => {
                const m = metrics.methods[name] || {
                    count: 0,
                    totalTime: 0,
                    maxTime: 0,
                    memoryUsage: 0,
                    maxMemoryUsage: 0
                };
                return {
                    name: name,
                    count: m.count || 0,
                    avgTime: m.count ? Math.round((m.totalTime || 0) / m.count) : 0,
                    maxTime: m.maxTime || 0,
                    memoryUsage: m.memoryUsage || 0,
                    maxMemoryUsage: m.maxMemoryUsage || 0
                };
            });

            return methodArray.sort((a, b) => b.count - a.count);
        } catch (e) {
            console.error("[Performance Monitor] Error in getMethodMetrics:", e);
            return [];
        }
    },

    getSubscriptionMetrics() {
        try {
            // Convert to array and sort by count
            const subArray = Object.keys(metrics.subscriptions).map(name => {
                const s = metrics.subscriptions[name] || {
                    count: 0,
                    totalReadyTime: 0,
                    dataSize: 0,
                    documentCount: 0
                };
                return {
                    name: name,
                    count: s.count || 0,
                    readyTime: s.count ? Math.round((s.totalReadyTime || 0) / s.count) : 0,
                    dataSize: s.dataSize || 0,
                    documentCount: s.documentCount || 0
                };
            });

            return subArray.sort((a, b) => b.count - a.count);
        } catch (e) {
            console.error("[Performance Monitor] Error in getSubscriptionMetrics:", e);
            return [];
        }
    },

    getRouteMetrics() {
        try {
            console.log("[Performance Monitor] Current routes data:", metrics.routes);

            // Convert to array and sort by visit count
            const routeArray = Object.keys(metrics.routes).map(path => {
                const r = metrics.routes[path] || { visitCount: 0, totalLoadTime: 0 };
                return {
                    path: path,
                    visitCount: r.visitCount || 0,
                    avgLoadTime: r.visitCount && r.totalLoadTime ? Math.round(r.totalLoadTime / r.visitCount) : 0
                };
            });

            const result = routeArray.sort((a, b) => b.visitCount - a.visitCount);
            console.log("[Performance Monitor] Sending route metrics:", result);
            return result;
        } catch (e) {
            console.error("[Performance Monitor] Error in getRouteMetrics:", e);
            return [];
        }
    },

    getCriticalMetrics() {
        try {
            // Get real system metrics
            const memoryUsage = getMemoryUsage();
            const cpuUsage = getCPUUsage();
            const processUptime = getServerUptime();
            const mongoStatus = getMongoStatus();
            const processStats = getProcessStats();

            // Calculate memory trend
            const memUsed = processStats ? processStats.heapUsed : 0;
            const memTotal = processStats ? processStats.heapTotal : 1;
            const memPercent = Math.round((memUsed / memTotal) * 100);

            let memoryTrend = "stable";
            if (lastMemorySnapshot && lastMemorySnapshot.heapUsed) {
                const memDiff = memUsed - lastMemorySnapshot.heapUsed;
                if (memDiff > 1024 * 1024 * 10) { // 10MB growth
                    memoryTrend = "rising";
                } else if (memDiff < -1024 * 1024 * 10) { // 10MB reduction
                    memoryTrend = "falling";
                }
            }

            // Format memory trend with color
            let memoryTrendFormatted = `${memoryTrend}`;
            if (memoryTrend === 'rising' && memoryUsage > 75) {
                memoryTrendFormatted = `<span style="color: #F44336;">${memoryUsage}% (rising)</span>`;
            } else if (memoryTrend === 'falling' && memoryUsage < 50) {
                memoryTrendFormatted = `<span style="color: #4CAF50;">${memoryUsage}% (falling)</span>`;
            } else {
                memoryTrendFormatted = `${memoryUsage}% (${memoryTrend})`;
            }

            // Determine cluster status based on memory, CPU and MongoDB
            let clusterStatus = "Healthy";
            let clusterStatusClass = "healthy";

            // Check for critical issues
            const criticalIssues = metrics.critical.activeIssues.filter(issue =>
                issue.severity === 'critical' && issue.status !== 'Resolved'
            );

            if (criticalIssues.length > 0 || mongoStatus !== 'Connected') {
                clusterStatus = "Degraded";
                clusterStatusClass = "critical";
            } else if (memoryUsage > 85 || cpuUsage > 85) {
                clusterStatus = "Warning";
                clusterStatusClass = "warning";
            }

            const awsResources = [
                {
                    name: 'Application Server',
                    status: clusterStatus,
                    statusClass: clusterStatusClass,
                    utilization: Math.round((memoryUsage + cpuUsage) / 2),
                    cost: '0.00'
                },
                {
                    name: 'MongoDB Database',
                    status: mongoStatus === 'Connected' ? 'Healthy' : 'Degraded',
                    statusClass: mongoStatus === 'Connected' ? 'healthy' : 'critical',
                    utilization: memPercent,
                    cost: '0.00'
                }
            ];

            // Return all critical metrics
            return {
                memoryUsage,
                memoryTrend: memoryTrendFormatted,
                cpuUsage,
                cpuTrend: `${cpuUsage}% (${cpuUsage > 75 ? 'high' : 'normal'})`,
                processUptime,
                clusterStatus,
                clusterStatusClass,
                activeIssues: metrics.critical.activeIssues,
                memoryLeaks: metrics.critical.memoryLeaks,
                awsResources,
                recentErrors: metrics.critical.recentErrors,
                processStats
            };
        } catch (e) {
            console.error("[Performance Monitor] Error in getCriticalMetrics:", e);
            return {
                memoryUsage: 0,
                memoryTrend: "stable",
                cpuUsage: 0,
                cpuTrend: "stable",
                processUptime: 0,
                clusterStatus: "Unknown",
                clusterStatusClass: "",
                activeIssues: [],
                memoryLeaks: [],
                awsResources: [],
                recentErrors: []
            };
        }
    },

    recordRouteVisit(path) {
        try {
            // Validate input
            if (!path) {
                console.error("[Performance Monitor] No path provided for route tracking");
                return;
            }

            // Create route record if it doesn't exist
            if (!metrics.routes[path]) {
                metrics.routes[path] = {
                    visitCount: 0,
                    totalLoadTime: 0
                };
            }

            // Increment visit count
            metrics.routes[path].visitCount = (metrics.routes[path].visitCount || 0) + 1;

            // Make sure totalLoadTime is initialized
            metrics.routes[path].totalLoadTime = metrics.routes[path].totalLoadTime || 0;

            // Add some initial simulated load time
            metrics.routes[path].totalLoadTime += Math.floor(Math.random() * 250) + 50;

            console.log(`[Performance Monitor] Route visit recorded: ${path} (count: ${metrics.routes[path].visitCount})`);
        } catch (e) {
            console.error("[Performance Monitor] Error in recordRouteVisit:", e);
        }
    },

    recordRouteDuration(path, duration) {
        try {
            if (!path) {
                console.error("[Performance Monitor] No path provided for route duration");
                return;
            }

            if (!metrics.routes[path]) {
                metrics.routes[path] = {
                    visitCount: 1,
                    totalLoadTime: 0
                };
            }

            // Add the duration to the total
            metrics.routes[path].totalLoadTime = (metrics.routes[path].totalLoadTime || 0) + duration;

            console.log(`[Performance Monitor] Route duration recorded: ${path} - ${duration}ms`);
        } catch (e) {
            console.error("[Performance Monitor] Error in recordRouteDuration:", e);
        }
    },

    addCriticalIssue(type, severity, details) {
        try {
            // Add a new critical issue
            const newIssue = {
                type,
                severity,
                details,
                detected: new Date(),
                status: 'New'
            };

            metrics.critical.activeIssues.push(newIssue);

            return true;
        } catch (e) {
            console.error("[Performance Monitor] Error in addCriticalIssue:", e);
            return false;
        }
    },

    recordError(type, message, location) {
        try {
            const existingError = metrics.critical.recentErrors.find(err =>
                err.type === type && err.message === message
            );

            if (existingError) {
                existingError.count++;
                existingError.timestamp = new Date();
            } else {
                metrics.critical.recentErrors.unshift({
                    type,
                    message,
                    count: 1,
                    location,
                    timestamp: new Date()
                });

                if (metrics.critical.recentErrors.length > 20) {
                    metrics.critical.recentErrors.pop();
                }
            }

            if (!metrics.errorRates[type]) {
                metrics.errorRates[type] = {
                    count: 0,
                    total: 0,
                    lastReset: Date.now()
                };
            }
            metrics.errorRates[type].count++;
            metrics.errorRates[type].total++;

            metrics.errors++;

            if (metrics.errorRates[type].count >= 5) {
                addPerformanceAlert(
                    'Error Rate',
                    'high',
                    `High error rate detected for ${type}: ${metrics.errorRates[type].count} errors in last hour`
                );
            }

            return true;
        } catch (e) {
            console.error("[Performance Monitor] Error in recordError:", e);
            return false;
        }
    },
    resetMetrics() {
        try {
            // Reset all metrics
            metrics.methods = {};
            metrics.subscriptions = {};
            metrics.routes = {
                '/': {
                    visitCount: 1,
                    totalLoadTime: 100
                }
            };
            metrics.errors = 0;
            metrics.totalMethodCalls = 0;
            metrics.critical.activeIssues = [];
            metrics.critical.memoryLeaks = [];
            metrics.critical.recentErrors = [];

            console.log("[Performance Monitor] Metrics reset");
            return true;
        } catch (e) {
            console.error("[Performance Monitor] Error in resetMetrics:", e);
            return false;
        }
    },

    getHeavyQueries() {
        try {
            const queryArray = Object.values(metrics.heavyQueries).map(query => ({
                name: `${query.collection} - ${JSON.stringify(query.query).slice(0, 50)}...`,
                collection: query.collection,
                executionCount: query.executionCount,
                avgTime: query.executionCount ? Math.round(query.totalTime / query.executionCount) : 0,
                maxTime: query.maxTime,
                documentsScanned: query.documentsScanned,
                memoryImpact: query.memoryImpact,
                lastExecution: query.lastExecution
            }));

            // Filter and sort heavy queries
            return queryArray
                .filter(query =>
                    query.avgTime >= 1000 || // Queries taking over 1 second
                    query.memoryImpact >= 5 * 1024 * 1024 || // Queries using more than 5MB
                    query.documentsScanned >= 1000 // Queries scanning more than 1000 documents
                )
                .sort((a, b) => b.avgTime - a.avgTime);
        } catch (e) {
            console.error("[Performance Monitor] Error in getHeavyQueries:", e);
            return [];
        }
    },

    getQueryOptimizationSuggestions() {
        try {
            const suggestions = [];
            const heavyQueries = Object.values(metrics.heavyQueries);

            heavyQueries.forEach(query => {
                if (query.executionCount > 100) {
                    const fields = Object.keys(query.query);
                    suggestions.push({
                        title: `Add Compound Index for ${query.collection}`,
                        details: `Query executed ${query.executionCount} times. Consider adding a compound index on: ${fields.join(', ')}`,
                        impact: 'High',
                        queryId: JSON.stringify({ collection: query.collection, query: query.query }),
                        type: 'index'
                    });
                }

                // Add pagination suggestions for large result sets
                if (query.documentsScanned > 1000) {
                    suggestions.push({
                        title: `Implement Pagination for ${query.collection}`,
                        details: `Query scanning ${query.documentsScanned} documents. Consider implementing pagination or limiting result size.`,
                        impact: 'Medium',
                        queryId: JSON.stringify({ collection: query.collection, query: query.query }),
                        type: 'pagination'
                    });
                }

                // Add field projection suggestions
                if (query.memoryImpact > 5 * 1024 * 1024) {
                    suggestions.push({
                        title: `Optimize Field Selection for ${query.collection}`,
                        details: `Query returning large result set (${Math.round(query.memoryImpact / 1024 / 1024)}MB). Consider using field projection to limit returned fields.`,
                        impact: 'Medium',
                        queryId: JSON.stringify({ collection: query.collection, query: query.query }),
                        type: 'projection'
                    });
                }
            });

            return suggestions;
        } catch (e) {
            console.error("[Performance Monitor] Error in getQueryOptimizationSuggestions:", e);
            return [];
        }
    }
});

// Original publish function to wrap
const originalPublish = Meteor.publish;

// Wrap publish to track subscription performance
Meteor.publish = function (name, handler) {
    const wrappedHandler = function (...args) {
        try {
            const self = this;
            const startTime = Date.now();
            let dataSize = 0;
            let documentCount = 0;

            // Override ready to track when subscription is ready
            const originalReady = self.ready;
            self.ready = function () {
                try {
                    const readyTime = Date.now() - startTime;

                    // Record subscription metrics
                    if (!metrics.subscriptions[name]) {
                        metrics.subscriptions[name] = {
                            count: 0,
                            totalReadyTime: 0,
                            dataSize: 0,
                            documentCount: 0
                        };
                    }

                    metrics.subscriptions[name].count = (metrics.subscriptions[name].count || 0) + 1;
                    metrics.subscriptions[name].totalReadyTime = (metrics.subscriptions[name].totalReadyTime || 0) + readyTime;

                    // Update data size and document count
                    metrics.subscriptions[name].dataSize = dataSize;
                    metrics.subscriptions[name].documentCount = documentCount;

                    return originalReady.call(self);
                } catch (e) {
                    console.error(`[Performance Monitor] Error tracking subscription ready time for ${name}:`, e);
                    return originalReady.call(self);
                }
            };

            const originalAdded = self.added;
            self.added = function (collection, id, fields) {
                documentCount++;
                dataSize += JSON.stringify(fields).length;
                return originalAdded.call(this, collection, id, fields);
            };

            const originalChanged = self.changed;
            self.changed = function (collection, id, fields) {
                dataSize += JSON.stringify(fields).length;
                return originalChanged.call(this, collection, id, fields);
            };

            const originalRemoved = self.removed;
            self.removed = function (collection, id) {
                documentCount = Math.max(0, documentCount - 1);
                return originalRemoved.call(this, collection, id);
            };

            return handler.apply(self, args);
        } catch (e) {
            console.error(`[Performance Monitor] Error wrapping publication ${name}:`, e);
            return handler.apply(this, args);
        }
    };

    return originalPublish.call(this, name, wrappedHandler);
};

// Original method handler to wrap
const originalMethodHandler = Meteor.server.method_handlers;
Meteor.server.method_handlers = {};

// Wrap all methods to track performance
Object.keys(originalMethodHandler).forEach(methodName => {
    const originalMethod = originalMethodHandler[methodName];

    Meteor.server.method_handlers[methodName] = async function (...args) {
        // Skip monitoring methods
        if (methodName.startsWith('get') && methodName.includes('Metrics')) {
            return originalMethod.apply(this, args);
        }

        const startTime = Date.now();
        const startMemory = process.memoryUsage().heapUsed;
        let result;

        try {
            if (originalMethod.constructor.name === 'AsyncFunction') {
                result = await originalMethod.apply(this, args);
            } else {
                result = originalMethod.apply(this, args);
            }
        } catch (e) {
            metrics.errors++;

            // Record the error in critical metrics
            Meteor.call('recordError', 'Method Error',
                `${methodName}: ${e.message || 'Unknown error'}`,
                `Method: ${methodName}`);

            throw e;
        } finally {
            try {
                const executionTime = Date.now() - startTime;
                const endMemory = process.memoryUsage().heapUsed;
                const memoryUsage = endMemory - startMemory;

                // Record method metrics
                if (!metrics.methods[methodName]) {
                    metrics.methods[methodName] = {
                        count: 0,
                        totalTime: 0,
                        maxTime: 0,
                        memoryUsage: 0,
                        maxMemoryUsage: 0
                    };
                }

                // Update metrics
                metrics.methods[methodName].count++;
                metrics.methods[methodName].totalTime += executionTime;
                metrics.methods[methodName].memoryUsage = Math.max(metrics.methods[methodName].memoryUsage, memoryUsage);
                metrics.methods[methodName].maxMemoryUsage = Math.max(metrics.methods[methodName].maxMemoryUsage, memoryUsage);
                metrics.methods[methodName].maxTime = Math.max(metrics.methods[methodName].maxTime, executionTime);

                metrics.totalMethodCalls++;

                // Log method execution for debugging
                console.log(`[Performance Monitor] Method ${methodName} executed in ${executionTime}ms (Total time: ${metrics.methods[methodName].totalTime}ms, Count: ${metrics.methods[methodName].count})`);
            } catch (e) {
                console.error(`[Performance Monitor] Error recording metrics for method ${methodName}:`, e);
            }
        }

        return result;
    };
});