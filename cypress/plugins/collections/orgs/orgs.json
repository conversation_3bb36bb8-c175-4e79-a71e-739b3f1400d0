[{"_id": "iSNuHxc6zq3mi8CzZ", "name": "MikeCare", "phoneNumber": "************", "streetAddress": "433 Las Colinas", "cityName": "<PERSON>", "stateName": "Texas", "zipCode": "75039", "websiteUrl": "https://www.google.com", "enableSwitchOrg": true, "createdAt": {"$numberLong": "1588096227268"}, "registrationSource": "app", "registrationIndustry": "childcare", "customizations": {"featureflag/orgHierarchy": true, "moments/voiceDemo/enabled": true, "billing/enabled": true, "inquiries/enabled": true, "moments/activity/enabled": true, "moments/activity/childDefaults": true, "moments/food/hideBottle": false, "moments/food/infantGroupOptions": true, "moments/learning/enabled": true, "moments/medical/enabled": true, "moments/illness/enabled": true, "moments/ouch/enabled": true, "moments/potty/showContinence": false, "moments/sleep/enabled": true, "moments/sleep/showEndSleepButton": true, "moments/supplies/enabled": false, "moments/incident/enabled": true, "moments/incident/extraIncidentFields": true, "people/pinCodeCheckin/enabled": true, "people/showFamilyRelationships": true, "people/types/showAllowPhotos": true, "people/types/showMedicationFormPdf": true, "people/types/showMedicationListPdf": true, "people/types/showCriminalCheckExpiration": true, "people/types/showFobNumber": true, "people/types/showCarmelParentHomeSchool": true, "people/types/showAdvancedAdultProfileFields": false, "people/types/showPayer": true, "people/types/customerSpecificProfileFields": true, "people/types/customerSpecificInquiryProfileFields": true, "reports/enabled": true, "reservations/enabled": true, "forms/enabled": false, "forms/formTypes/formPlaygroundInspection": true, "forms/formTypes/formIncident": true, "people/types/showPrimaryCaregiver": "true", "reports/billingAdminSummaryReport/enabled": true, "messages/administrativeVisibility/enabled": true, "inquiries/registration/enabled": true, "moments/checkin/notifyWithoutReservation": true, "moments/medical/useProfileMedications": true, "modules/curriculum/ageGroups": true, "modules/curriculum/hideHomework": true, "moments/portfolio/enabled": true, "customer/lightbridge/default": true, "people/types/customerSpecificStaffProfileFields": true, "messages/suppressStaffMessageCenterNotifications/enabled": false, "people/immunizationAlerts/enabled": true, "moments/potty/enabled": false, "moments/alert/adminOnly": true, "moments/ouch/adminOnly": true, "moments/illness/adminOnly": true, "moments/learning/adminOnly": false, "moments/supplies/adminOnly": true, "people/staffPay/enabled": true, "reservations/schedulingReport/enabled": true, "moments/food/enabled": true, "moments/mood/enabled": true, "moments/covidHealthCheck/enabled": false, "moments/alert/enabled": true, "report/classList/enabled": true, "timeCards/enabled": true, "moments/checkin/autocheckout": true, "moments/potty/adminOnly": false, "curriculumBank/management": true, "curriculumBank/activities": true, "moments/sleep/distressedCheck": true, "report/classListSchedule/enabled": true, "people/expressDriveUp/enabled": true, "mobile/offlineMode/enabled": true, "mpsurvey/enabled": true, "billing/disableCards/enabled\t": true, "people/qrCodeCheckin/enabled": true, "curriculumBank/globalAndLocal": true, "billing/payments/preventDebitCards": true, "report/waitList/enabled": true, "report/subsidy/enabled": true, "messages/disableStaffMessages/enabled": false, "people/timeConfirmation/enabled": true, "admin/configuration/staffTimeKeeping/timeCardsLock": true, "people/editLockedTimecards": true, "billing/deposits/edit": true, "modules/curriculum/useLastAssessment": true, "people/staffRequiredPinCodeCheckin/enabled": false, "moments/incompleteFTF/enabled": true, "people/staffTimeConfirmation/enabled": true, "customer/CDS/customFTECalculations": true, "billing/configuration/couponCodes": true, "people/chatSupport/enabled": true, "enrollButton/hidden": false, "billing/requireLedgerAccountName/enabled": true, "billing/applyExcessSubsidyOnlyToPayer": true, "billing/configuration/bundles": true, "moments/activity/adminOnly": true, "moments/cdsActivity/enabled": true, "moments/activity/organizationSpecificTypes": true, "people/phonePinCheckin/enabled": false, "billing/autoProrateByDate/enabled": true, "people/kioskMasterPinAdminOnly/enabled": false, "people/timeCardsLocked/enabled": false, "people/familyCheckin/enabled": true, "people/multipleCheckin/enabled": true, "timeCards/propagateLockSettings/enabled": true, "billing/configuration/receiveNonFamilyFunds/enabled": true, "billing/configuration/heldfunds/enabled": true, "billing/configuration/payerCashPostingLedger": true, "people/checkInCheckOutQrCodesExpire/enabled": true, "moments/staffRequiredCheckin/enabled": true, "billing/frequency/monthlyRateBilledWeekly": true, "quickBooksOnlineRevenueExport/enabled": true, "billing/configuration/punchCards": false, "modules/curriculum/requireThemes": true, "people/automaticPin": false, "registrationFlow": true, "people/hideEditRegistration": true, "billing/plans/variableMonthlySubsidies": true, "people/requireRoles": true, "parentLedRegistration/showEmployeeID": true, "billing/weekends/enabled": true, "mySiteDashboard/ShowPastDueAccountsWidget": true, "scheduleTypes/forceLinkToPlans/enabled": true}, "documentDefinitions": [{"_id": "jn34uH45zMgCXEcDa", "name": "Registration Form", "section": "Registration", "repositoryKey": "jn34uH45zMgCXEcDa/Fip7RBqqP8vFeoZkc/kJJ8uwsvuN54qHdZf1kr"}, {"_id": "oNgd4wS78zEdRmWT5", "createdByPersonId": "cm9Xk2iDso3HXCynM", "createdAt": *************, "name": "Enrollment Form", "section": "Waitlist Registration", "templateOption": "", "selectedGroupIds": null, "assignmentType": "all"}, {"_id": "FbP7ARR67mHj7ru2r", "createdByPersonId": "cm9Xk2iDso3HXCynM", "createdAt": *************, "name": "Parent Handbook", "section": "Waitlist Registration", "templateOption": "", "selectedGroupIds": null, "assignmentType": "all"}], "enabledMomentTypes": ["heroMoment"], "language": "translationsEnChildCare", "planDetails": {}, "billing": {"enabled": true, "scheduling": {"generateDay": "monday", "generateMonthDay": "20", "generateWhen": "advance", "gracePeriodDays": "14", "lateFee": 0, "assessLateFee": false, "assessLateFeeDays": null, "assessLateFeeItemId": null, "disablePaymentsBeforeDueDate": false, "monthlyPlanDueDay": null, "weeklyPlanDueDay": "monday", "missedInvoiceInterval": "0", "generateBiWeeklyDate": "07/27/2023"}, "plansAndItems": [{"_id": "pDXgnrGpWy3emx5Ja", "description": "*Infants", "amount": 1600.5, "type": "plan", "frequency": "monthly", "category": "tuition", "ledgerAccountName": "3204", "program": "G5YkoCLXkHcRBrrKk", "programDetails": "<p>test</p>\n<p><span style=\"font-size:18px\">test<em><strong>test<span style=\"color:#2dc26b\">test<span style=\"background-color:#f1c40f\">test</span></span></strong></em></span></p>\n<ul>\n<li><span style=\"font-size:18px\"><em><strong><span style=\"color:#2dc26b\"><span style=\"background-color:#f1c40f\">test</span></span></strong></em></span></li>\n<li><span style=\"font-size:18px\"><em><strong><span style=\"color:#2dc26b\"><span style=\"background-color:#f1c40f\">test</span></span></strong></em></span>\n<ul>\n<li><span style=\"font-size:18px\"><em><strong><span style=\"color:#2dc26b\"><span style=\"background-color:#f1c40f\">test</span></span></strong></em></span>\n<ul>\n<li><span style=\"font-size:18px\"><em><strong><span style=\"color:#2dc26b\"><span style=\"background-color:#f1c40f\">test</span></span></strong></em></span></li>\n</ul>\n</li>\n</ul>\n</li>\n</ul>\n<ol>\n<li><span style=\"font-size:18px\"><em><strong><span style=\"color:#2dc26b\"><span style=\"background-color:#f1c40f\">test</span></span></strong></em></span>\n<ol>\n<li><span style=\"font-size:18px\"><em><strong><span style=\"color:#2dc26b\"><span style=\"background-color:#f1c40f\">test</span></span></strong></em></span>\n<ol>\n<li><span style=\"font-size:18px\"><em><strong><span style=\"color:#2dc26b\"><span style=\"background-color:#f1c40f\">test</span></span></strong></em></span></li>\n<li><span style=\"font-size:18px\"><em><strong><span style=\"color:#2dc26b\"><span style=\"background-color:#f1c40f\">test</span></span></strong></em></span></li>\n</ol>\n</li>\n</ol>\n</li>\n</ol>\n<ol style=\"list-style-type:lower-alpha\">\n<li><span style=\"font-size:18px\"><em><strong><span style=\"color:#2dc26b\"><span style=\"background-color:#f1c40f\">test</span></span></strong></em></span></li>\n<li><a href=\"https://google.com\" target=\"_blank\" rel=\"noopener\"><span style=\"font-size:18px\"><em><strong><span style=\"color:#2dc26b\"><span style=\"background-color:#f1c40f\">test</span></span></strong></em></span></a></li>\n</ol>", "scaledAmounts": [], "details": {"dateType": "timePeriod", "timePeriod": "CqKZFAiYN65Hwghcb"}}, {"_id": "z79X86AbiTB86vfns", "description": "Afternoon Care", "amount": 125, "frequency": "weekly", "type": "plan", "category": "", "expires": {"$numberLong": "*************"}, "suspendUntil": null, "archived": true}, {"_id": "Zkcq536vvL4pJEKYG", "description": "Daily Charge", "amount": 75, "type": "plan", "ledgerAccountName": "3000", "scaledAmounts": [], "program": "dnomBQ6pSbsbc4uMB", "details": {}, "frequency": "hourly"}, {"_id": "kD5fwogv7CJaSxmwa", "description": "Supplies", "amount": 25, "type": "item", "archived": true}, {"_id": "BopeHGZ6zDRhHHgA2", "description": "Late Fee - 1 minute", "amount": 1, "type": "item"}, {"_id": "D6ityoY3ZjvgaBiKE", "description": "Extended Care (1 hr)", "amount": 5.25, "type": "item", "archived": true}, {"_id": "JhME3aGeywsENXXzA", "description": "Monthly 3's", "amount": 500, "type": "plan", "frequency": "monthly", "category": ""}, {"_id": "8dSDSbtKwjqx9Sbgh", "description": "Movie Field Trip Fee", "amount": 20, "type": "item"}, {"_id": "6Ysxfxv5R4u9fpTFi", "description": "Enrollment Fee", "amount": 125, "type": "item", "ledgerAccountName": "4000"}, {"_id": "CkYmX5XGQThD3SjLZ", "description": "Blooms Class MWF", "amount": 90, "type": "plan", "ledgerAccountName": "4029", "scaledAmounts": [100, 20, 30, 40, 90], "program": "dnomBQ6pSbsbc4uMB", "details": {"scheduleType": "kQ3r9uap88hm543xe", "dateType": "timePeriod", "timePeriod": "ToivYzTpLTMeZQtWr"}, "expires": {"$numberLong": "*************"}, "frequency": "scaledWeekly", "category": "tuition"}, {"_id": "jpPFJ5D5wcdnZHgEf", "description": "After school - hourly", "amount": 60, "type": "plan", "frequency": "hourly", "category": "tuition", "ledgerAccountName": "4077", "scaledAmounts": [], "program": "opv8jrETSw74unc6r", "details": {"dateType": "timePeriod", "timePeriod": "CqKZFAiYN65Hwghcb"}}, {"_id": "d4JtkhNMx4r3i2hS5", "description": "2y's Full Time x", "amount": 1600, "type": "plan", "frequency": "monthly", "category": "tuition", "ledgerAccountName": "4023", "program": "Summer program"}, {"_id": "y3u9oM55CW4zSsHwr", "description": "Test Invoice", "amount": 125, "type": "plan", "frequency": "biweekly", "archived": true}, {"_id": "2znutjvtMETZ2AJZ8", "description": "Large Plan", "amount": 1200, "type": "plan", "frequency": "monthly", "category": "", "ledgerAccountName": "4023", "archived": true}, {"_id": "5LgWR5kC3h4F5FyQA", "description": "Testing tuition type", "amount": 150, "type": "plan", "frequency": "weekly", "category": "", "archived": true}, {"_id": "sg5KWmZ22BwA7Hp9Z", "description": "Keystone STARS4 50.25", "amount": 50.25, "type": "plan", "frequency": "weekly", "category": "tuition", "ledgerAccountName": "4023", "archived": true}, {"_id": "ACvBBy7PEtHShHXXa", "description": "Keystone STARS4 47.95", "amount": 47.95, "type": "plan", "frequency": "monthly", "category": "tuition", "ledgerAccountName": "4023"}, {"_id": "tTMQPuxhnJXwc6ogx", "description": "Keystone STARS4 48.98", "amount": 48.98, "type": "plan", "frequency": "weekly", "category": "tuition", "ledgerAccountName": "4023"}, {"_id": "KxQsXSb8a77S7SHG3", "description": "Keystone STARS4 28.83", "amount": 28.83, "type": "plan", "frequency": "weekly", "category": "tuition", "ledgerAccountName": "4023"}, {"_id": "mZ7YWFMP3favuzJaj", "description": "Keystone STARS4 26.26", "amount": 26.26, "type": "plan", "frequency": "weekly_scheduled_daily", "category": "tuition", "ledgerAccountName": "4023"}, {"_id": "qYg5nSEwqdj3Sonbp", "description": "A Plan to Archive", "amount": 1000, "type": "plan", "frequency": "monthly", "category": "tuition", "expires": {"$numberLong": "*************"}, "archived": true, "suspendUntil": {"$numberLong": "*************"}}, {"_id": "Zxm5eJ6f8W3ZmD5L9", "description": "An Item to Archive", "amount": 100, "type": "item"}, {"_id": "yGrWmpwCaqoz8kjKZ", "description": "Nido Academic Installment Plan", "amount": 12390, "type": "plan", "frequency": "monthly", "category": "tuition", "ledgerAccountName": "4023", "archived": true}, {"_id": "pdK9qHsrpeQKKvqdb", "description": "Nido Early Care Installment Plan", "amount": 708, "type": "plan", "frequency": "monthly", "category": "tuition", "ledgerAccountName": "4023", "archived": true}, {"_id": "yWMd8Q7HQAxfdYqqL", "description": "Nido After Care Installment Plan", "amount": 2009.07, "type": "plan", "frequency": "monthly", "category": "tuition", "ledgerAccountName": "4023", "archived": true}, {"_id": "Nf7bdE5y7bJyRoXC2", "description": "<PERSON><PERSON><PERSON><PERSON>", "amount": 125, "type": "item", "suspendUntil": null, "archived": true}, {"_id": "segSaHTxESqYiK9Af", "description": "Mew Plan", "amount": 100, "type": "plan", "frequency": "monthly", "category": "tuition", "ledgerAccountName": "4567", "archived": true}, {"_id": "C3FFhCjH7NTXQd6pw", "description": "Toddlers", "amount": 50, "type": "plan", "frequency": "monthly", "category": "tuition", "ledgerAccountName": "1234", "program": "dnomBQ6pSbsbc4uMB", "scaledAmounts": [], "details": {"scheduleType": "z6NdNWiYFBjfKWi6Q", "dateType": "timePeriod", "timePeriod": "CqKZFAiYN65Hwghcb"}}, {"_id": "hLr5SRmqqcTcRp5fj", "description": "*Preschool", "amount": 1300, "type": "plan", "frequency": "monthly", "category": "tuition", "ledgerAccountName": "1111", "program": "", "scaledAmounts": [], "expires": {"$numberLong": "*************"}, "details": {}}, {"_id": "W2JhJ3Y7ywRCte8cy", "description": "NSF Fee", "amount": 45, "type": "item", "ledgerAccountName": "0001"}, {"_id": "HtGoZAHGeyziRuBsu", "description": "PreK", "amount": 35, "type": "plan", "frequency": "weekly_scheduled_daily", "category": "tuition", "ledgerAccountName": "1004", "archived": true}, {"_id": "5c9Co2Te9cgKffEz8", "description": "Security Deposit", "amount": 250, "type": "item", "ledgerAccountName": "1234", "refundableDeposit": true}, {"_id": "3TWiaiCmq3x4TtPuT", "description": "After School (Monthly)", "amount": 300, "type": "plan", "frequency": "charged_daily_invoiced_monthly", "category": "tuition", "ledgerAccountName": "0989", "scaledAmounts": [], "program": "opv8jrETSw74unc6r", "details": {"dateType": "timePeriod", "timePeriod": "CqKZFAiYN65Hwghcb"}}, {"_id": "NpdymwHYrFtcCR8y6", "description": "Before Caree", "amount": 30, "type": "plan", "frequency": "weekly", "category": "tuition", "ledgerAccountName": "1111", "scaledAmounts": [], "program": "dnomBQ6pSbsbc4uMB", "details": {"dateType": "timePeriod", "timePeriod": "CqKZFAiYN65Hwghcb"}}, {"_id": "w9TjReTNcCwFgGp73", "description": "Late Payment Fee", "amount": 25, "type": "item", "ledgerAccountName": "9834"}, {"_id": "iDYnwdxHFy65oET3S", "description": "Automatic Daily Drop-in", "amount": 75, "type": "plan", "frequency": "daily", "category": "tuition", "ledgerAccountName": "0900"}, {"_id": "W97rpLepJJSjcEn4a", "description": "Infants - Charge daily by schedule", "amount": 170, "type": "plan", "frequency": "weekly_scheduled_daily", "category": "tuition", "ledgerAccountName": "0000", "scaledAmounts": [], "program": "opv8jrETSw74unc6r", "details": {"dateType": "timePeriod", "timePeriod": "CqKZFAiYN65Hwghcb"}}, {"_id": "QrXM47ZvcE8vsG93W", "description": "New Plan", "amount": 1000, "type": "plan", "frequency": "weekly", "category": "tuition", "expires": {"$numberLong": "*************"}, "ledgerAccountName": "3322"}, {"_id": "Bm7YLEZx4ta6adLy4", "description": "2 Center Plan", "amount": 550, "type": "plan", "frequency": "weekly", "category": "tuition", "expires": {"$numberLong": "*************"}, "ledgerAccountName": "6622", "suspendUntil": null, "program": "dnomBQ6pSbsbc4uMB", "scaledAmounts": [], "details": {"startTime": "10:20 am", "endTime": "2:00 pm", "regStartDate": {"$numberLong": "*************"}, "regEndDate": {"$numberLong": "*************"}, "dateType": "timePeriod", "timePeriod": "ToivYzTpLTMeZQtWr"}}, {"_id": "aKwZkZfRtaYjSTjyG", "description": "Billing Plan (Admin Config Test) -monthly", "amount": 1750, "type": "plan", "frequency": "monthly", "category": "tuition", "expires": {"$numberLong": "*************"}, "ledgerAccountName": "9843", "scaledAmounts": [], "program": "opv8jrETSw74unc6r", "details": {"dateType": "timePeriod", "timePeriod": "CqKZFAiYN65Hwghcb"}}, {"_id": "o7MSTsAxgar84RAvT", "description": "Little angels billing", "amount": 4500, "type": "plan", "frequency": "bimonthly", "category": "tuition", "expires": {"$numberLong": "*************"}, "ledgerAccountName": "0002"}, {"_id": "M8mds3LQYkuLfw9pu", "description": "Little Angels Billing Plan (Gr) bi monthly", "amount": 4500, "type": "plan", "frequency": "bimonthly", "category": "tuition", "expires": {"$numberLong": "*************"}, "ledgerAccountName": "0001", "scaledAmounts": [], "program": "opv8jrETSw74unc6r", "details": {"dateType": "timePeriod", "timePeriod": "CqKZFAiYN65Hwghcb"}}, {"_id": "Yqhqox3ANnoRLxH9N", "description": "2nd Little Angels", "amount": 50, "type": "plan", "frequency": "daily", "category": "tuition", "expires": {"$numberLong": "*************"}, "ledgerAccountName": "3456"}, {"_id": "hExN3EhRsbPfDQEk2", "description": "Final Little Angels Billl", "amount": 700, "type": "plan", "frequency": "monthly", "category": "tuition", "expires": {"$numberLong": "*************"}, "ledgerAccountName": "4398"}, {"_id": "zz3wJdhrx3fp2ZQHc", "description": "(Parent to child propagate) Learning Time to Little Angelsi", "amount": 450, "type": "plan", "frequency": "biweekly", "category": "tuition", "expires": {"$numberLong": "*************"}, "ledgerAccountName": "0230", "scaledAmounts": [], "program": ""}, {"_id": "KNQfMiRWNL2DzZTC5", "description": "Childcare Plan", "amount": 80, "type": "plan", "frequency": "weekly", "category": "tuition", "expires": {"$numberLong": "*************"}, "ledgerAccountName": "0230", "suspendUntil": {"$numberLong": "*************"}, "program": "test"}, {"_id": "HHi8SkfJ8bDTHfuCq", "description": "Is it going to be propagated?", "amount": 250, "type": "plan", "frequency": "bimonthly", "category": "tuition", "expires": {"$numberLong": "*************"}}, {"_id": "A2Ykvppt2bqWKSwgM", "description": "LineLeader Billing Plan - bi weekly", "amount": 675, "type": "plan", "frequency": "biweekly", "category": "tuition", "program": "opv8jrETSw74unc6r", "expires": {"$numberLong": "*************"}, "scaledAmounts": [], "ledgerAccountName": "1212", "details": {"dateType": "timePeriod", "timePeriod": "CqKZFAiYN65Hwghcb"}}, {"_id": "osJ6cQgQnCrgDGKyD", "type": "bundle", "plans": ["zAG2nS2SGyfeeZTf7", "rincuWszuj3jvfkmx"], "description": "Galina Plan 2 and Galina Plan 1", "scaledAmounts": [[10, 1, 1, 11, 1], [1, 1, 1, 1, 1], [1, 1, 1, 1, 1], [1, 1, 1, 1, 1], [1, 1, 1, 1, 30]]}, {"_id": "zAG2nS2SGyfeeZTf7", "description": "Galina Plan 2", "type": "plan", "frequency": "scaledWeekly", "category": "tuition", "program": "dnomBQ6pSbsbc4uMB", "amount": 200, "scaledAmounts": [150, 10, 10, 10, 200], "expires": {"$numberLong": "*************"}, "ledgerAccountName": "1212", "details": {"regStartDate": {"$numberLong": "*************"}, "regEndDate": {"$numberLong": "*************"}}}, {"_id": "rincuWszuj3jvfkmx", "description": "Galina Plan 1", "type": "plan", "frequency": "scaledWeekly", "category": "tuition", "program": "dnomBQ6pSbsbc4uMB", "amount": 100, "scaledAmounts": [75, 1, 1, 1, 100], "expires": {"$numberLong": "*************"}, "ledgerAccountName": "1313", "details": {"regStartDate": {"$numberLong": "*************"}, "regEndDate": {"$numberLong": "*************"}}}, {"_id": "3qwzrEdKPABW6cqn4", "type": "bundle", "plans": ["K9SDLaapqZJp4yZ3c", "CkYmX5XGQThD3SjLZ"], "description": "Bundle Test 2 and Blooms Class MWF", "scaledAmounts": [[5, 0, 10, 0, 0], [0, 0, 200, 0, 0], [0, 0, 30, 0, 500], [0, 0, 40, 0, 0], [0, 1000, 50, 0, 300]], "archived": true}, {"_id": "n2yqjLYK5voEKQAkG", "description": "Plan 1 Galina Garaba -weekly", "type": "plan", "frequency": "weekly", "category": "tuition", "program": "opv8jrETSw74unc6r", "amount": 300, "scaledAmounts": [], "ledgerAccountName": "1000", "details": {"dateType": "timePeriod", "timePeriod": "CqKZFAiYN65Hwghcb"}}, {"_id": "2ZauwsSJTvrCNciNK", "description": "Plan 2 Galina  scaled - monthly", "type": "plan", "frequency": "scaledMonthly", "category": "tuition", "program": "opv8jrETSw74unc6r", "amount": 350, "scaledAmounts": [100, 200, 250, 300, 350], "ledgerAccountName": "1000", "details": {"dateType": "timePeriod", "timePeriod": "CqKZFAiYN65Hwghcb"}}, {"_id": "3qwzrEdKPABW6cqn4", "type": "bundle", "plans": ["n2yqjLYK5voEKQAkG", "2ZauwsSJTvrCNciNK"], "description": "Plan 1 Galina Garaba and Plan 2 Galina ", "scaledAmounts": [[10, 0, 10, 0, 0], [10, 0, 200, 0, 0], [10, 0, 30, 0, 500], [10, 0, 40, 0, 0], [10, 0, 50, 0, 300]]}, {"_id": "3qwzrEdKPABW6cqn4", "type": "bundle", "plans": ["n2yqjLYK5voEKQAkG", "2ZauwsSJTvrCNciNK"], "description": "Plan 1 Galina Garaba and Plan 2 Galina ", "scaledAmounts": [[10, 3000, 10, 0, 0], [10, 0, 200, 0, 0], [10, 0, 30, 0, 500], [10, 0, 40, 0, 0], [10, 0, 50, 0, 300]]}, {"_id": "3qwzrEdKPABW6cqn4", "type": "bundle", "plans": ["n2yqjLYK5voEKQAkG", "2ZauwsSJTvrCNciNK"], "description": "Plan 1 Galina Garaba and Plan 2 Galina ", "scaledAmounts": [[10, 3000, 10, 0, 0], [10, 2000, 200, 0, 0], [10, 0, 30, 0, 500], [10, 0, 40, 0, 0], [10, 0, 50, 0, 300]]}, {"_id": "3qwzrEdKPABW6cqn4", "type": "bundle", "plans": ["n2yqjLYK5voEKQAkG", "2ZauwsSJTvrCNciNK"], "description": "Plan 1 Galina Garaba and Plan 2 Galina ", "scaledAmounts": [[1000, 3000, 10, 0, 0], [10, 2000, 200, 0, 0], [10, 0, 30, 0, 500], [10, 0, 40, 0, 0], [10, 0, 50, 0, 300]]}, {"_id": "DEEeFjdrNdxm9zwJL", "type": "bundle", "plans": ["pDGiaD4Cw2RWp5CWd", "LN6WZ3MG3R3JHneee"], "description": "Plan test 1 and Plan Test 2", "scaledAmounts": [[10, 10, 10, 10, 10], [10, 10, 10, 10, 10], [10, 10, 10, 10, 10], [10, 10, 10, 10, 10], [10, 10, 10, 10, 10]]}, {"_id": "pDGiaD4Cw2RWp5CWd", "description": "Plan test 1", "type": "plan", "frequency": "monthly", "category": "tuition", "program": "dnomBQ6pSbsbc4uMB", "amount": 300, "scaledAmounts": [], "ledgerAccountName": "1000", "details": {}}, {"_id": "LN6WZ3MG3R3JHneee", "description": "Plan Test 2", "type": "plan", "frequency": "monthly", "category": "tuition", "program": "dnomBQ6pSbsbc4uMB", "amount": 700, "scaledAmounts": [], "ledgerAccountName": "1000", "details": {}}, {"_id": "ZmCNCJmTQmzo6Gyhu", "type": "bundle", "plans": ["m5fj9HXYfpMLmBjCp", "ASh2pkb6Xrsijd34i"], "description": "GGalina Plan 1(test) - scaled weekly and GGalina Plan 2(test)", "scaledAmounts": [[500, 100, 500, 100, 100], [500, 0, 500, 0, 0], [500, 0, 500, 0, 0], [500, 0, 500, 0, 0], [500, 0, 500, 0, 0]]}, {"_id": "m5fj9HXYfpMLmBjCp", "description": "GGalina Plan 1(test) - scaled weekly", "type": "plan", "frequency": "scaledWeekly", "category": "tuition", "program": "opv8jrETSw74unc6r", "amount": 80, "scaledAmounts": [50, 60, 70, 75, 80], "ledgerAccountName": "1000", "details": {"dateType": "timePeriod", "timePeriod": "CqKZFAiYN65Hwghcb"}}, {"_id": "ASh2pkb6Xrsijd34i", "description": "GGalina Plan 2(test)", "type": "plan", "frequency": "scaledWeekly", "category": "tuition", "program": "dnomBQ6pSbsbc4uMB", "amount": 50, "scaledAmounts": [10, 15, 20, 40, 50], "ledgerAccountName": "1000", "details": {}}, {"_id": "WuFi8zLF87ZmBRf5S", "type": "bundle", "plans": ["Nx38okwXLN3WdNcbq", "sBNKeFnwYZXKA4RpS"], "description": "Bundle Test 1 and Propagate TEST 4 Learning Time(MS)", "scaledAmounts": [[100, 200, 0, 100, 0], [100, 0, 0, 0, 0], [100, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]]}, {"_id": "Nx38okwXLN3WdNcbq", "description": "Bundle Test 1", "type": "plan", "frequency": "monthly", "category": "tuition", "program": "dnomBQ6pSbsbc4uMB", "amount": 780, "scaledAmounts": [], "ledgerAccountName": "1000", "details": {"dateType": "timePeriod", "timePeriod": "ToivYzTpLTMeZQtWr"}}, {"_id": "sBNKeFnwYZXKA4RpS", "description": "Propagate TEST 4 Learning Time(MS)", "type": "plan", "frequency": "scaledWeekly", "category": "tuition", "program": "pbWjfZttJJ4DSN6w4", "amount": 100, "scaledAmounts": [0, 0, 0, 0, 100], "ledgerAccountName": "1000"}, {"_id": "KmaFk7HS2JRFxxEpv", "type": "bundle", "plans": ["eK65bC9K2MaNfdSPE", "mZzQfSxx2Ct4oWzZX"], "description": "Test 1 - scaled bi weekly and Test 2", "scaledAmounts": [[5, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 85]]}, {"_id": "eK65bC9K2MaNfdSPE", "description": "Test 1 - scaled bi weekly", "type": "plan", "frequency": "scaledBiweekly", "category": "tuition", "program": "opv8jrETSw74unc6r", "amount": 230, "scaledAmounts": [100, 150, 190, 210, 230], "ledgerAccountName": "1000", "details": {"dateType": "timePeriod", "timePeriod": "CqKZFAiYN65Hwghcb"}}, {"_id": "mZzQfSxx2Ct4oWzZX", "description": "Test 2", "type": "plan", "frequency": "scaledBiweekly", "category": "tuition", "program": "opv8jrETSw74unc6r", "amount": 50, "scaledAmounts": [10, 20, 30, 40, 50], "ledgerAccountName": "1000", "details": {"dateType": "timePeriod", "timePeriod": "CqKZFAiYN65Hwghcb"}}, {"_id": "wq8KsTwQFPi2pGjkN", "type": "bundle", "plans": ["ryEepYohsrheDMB8P", "Di4tMEror49xYWdLn"], "description": "bundle test 3 and bundle plan 4", "scaledAmounts": [[50, 50, 50, 50, 50], [40, 40, 30, 40, 40], [30, 30, 30, 30, 30], [20, 20, 20, 20, 20], [10, 10, 10, 10, 10]]}, {"_id": "uNQauBQyta4Tm467e", "description": "test 3", "type": "plan", "frequency": "scaledBiweekly", "category": "tuition", "program": "opv8jrETSw74unc6r", "amount": 60, "scaledAmounts": [20, 30, 40, 50, 60], "ledgerAccountName": "1000", "details": {"dateType": "timePeriod", "timePeriod": "CqKZFAiYN65Hwghcb"}}, {"_id": "Gq2cc4BrietqCjRga", "description": "test 4", "type": "plan", "frequency": "scaledWeekly", "category": "tuition", "program": "", "amount": 0, "scaledAmounts": [0, 0, 0, 0, 0], "ledgerAccountName": "1000", "details": {}}, {"_id": "XAWBvxGLjuvmghaat", "type": "bundle", "plans": ["mZzQfSxx2Ct4oWzZX", "uNQauBQyta4Tm467e"], "description": "Test 2 and test 3", "scaledAmounts": [[6, 10, 15, 20, 25], [12, 0, 0, 0, 30], [18, 0, 0, 0, 35], [23, 0, 0, 0, 40], [26, 30, 33, 37, 45]]}, {"_id": "K9SDLaapqZJp4yZ3c", "description": "Bundle Test 2", "type": "plan", "frequency": "monthly", "category": "tuition", "program": "dnomBQ6pSbsbc4uMB", "amount": 220, "scaledAmounts": [], "ledgerAccountName": "0989", "details": {}}, {"_id": "hgJXE2NbGDGd7WFNS", "description": "Propagate billing  plan TEST", "type": "plan", "frequency": "biweekly", "category": "tuition", "program": "pbWjfZttJJ4DSN6w4", "amount": 100, "scaledAmounts": [], "expires": {"$numberLong": "*************"}, "ledgerAccountName": "1212"}, {"_id": "nToYTwXHNpLiAfrjd", "description": "Plan propagation child orgs", "type": "plan", "frequency": "daily", "category": "tuition", "program": "pbWjfZttJJ4DSN6w4", "amount": 100, "scaledAmounts": [], "expires": {"$numberLong": "*************"}, "ledgerAccountName": "1212"}, {"_id": "4dseWxtrKmo7ZYWJk", "description": "Propagate TEST 5", "type": "plan", "frequency": "hourly", "category": "tuition", "program": "pbWjfZttJJ4DSN6w4", "amount": 100, "scaledAmounts": [], "expires": {"$numberLong": "*************"}, "ledgerAccountName": "1212"}, {"_id": "koenfaMdz52hJCYx3", "description": "Plan non user admin TEST", "type": "plan", "frequency": "hourly", "category": "tuition", "program": "pbWjfZttJJ4DSN6w4", "amount": 100, "scaledAmounts": [], "expires": {"$numberLong": "*************"}, "ledgerAccountName": "1212"}, {"_id": "XdoHAPx3St7QdQYfP", "description": "Galina Plan", "type": "plan", "program": "dnomBQ6pSbsbc4uMB", "frequency": "weekly", "category": "tuition", "amount": 100, "scaledAmounts": [], "ledgerAccountName": "1212", "details": {"regStartDate": {"$numberLong": "*************"}, "regEndDate": {"$numberLong": "*************"}, "dateType": "timePeriod", "timePeriod": "CqKZFAiYN65Hwghcb"}}, {"_id": "vkvhzGSuKREpRB4mM", "description": "Galina Plan 3 -Daily", "type": "plan", "program": "opv8jrETSw74unc6r", "frequency": "daily", "category": "tuition", "amount": 450, "scaledAmounts": [], "ledgerAccountName": "1212", "details": {"dateType": "timePeriod", "timePeriod": "CqKZFAiYN65Hwghcb"}}, {"_id": "DQ5vu482drRvyFQTj", "type": "bundle", "plans": ["CkYmX5XGQThD3SjLZ", "Gq2cc4BrietqCjRga"], "description": "Blooms Class MWF and test 4", "scaledAmounts": [[50, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 100]]}, {"_id": "7kZ6GDSFEWwWEk7bF", "description": "<PERSON><PERSON><PERSON>", "type": "punchcard", "program": "bHdK2PhSd7pnitYea", "numberOfDays": "5", "amount": 1500, "scaledAmounts": [], "ledgerAccountName": "1212"}, {"_id": "auv4jLoqiYrmHzKBA", "description": "<PERSON><PERSON><PERSON>", "type": "item", "program": "dnomBQ6pSbsbc4uMB", "amount": 120, "scaledAmounts": [], "ledgerAccountName": "1212", "details": {"dateType": "date<PERSON><PERSON><PERSON>", "serviceStartDate": {"$numberLong": "*************"}, "serviceEndDate": {"$numberLong": "*************"}}, "refundableDeposit": true}, {"_id": "GnpMkgiSdsZ8LZtyi", "description": "<PERSON><PERSON><PERSON> 2", "type": "punchcard", "program": "dnomBQ6pSbsbc4uMB", "numberOfDays": "12", "amount": 350, "scaledAmounts": [], "expires": {"$numberLong": "*************"}, "ledgerAccountName": "1212"}, {"_id": "9ij7T8a5o4kBj6Yf9", "description": "Registration Fee", "type": "item", "program": "", "amount": 0, "scaledAmounts": [], "expires": {"$numberLong": "*************"}, "ledgerAccountName": "1212", "details": {}}, {"_id": "mfh27JDsqer6Q6TXW", "description": "Discount program", "type": "plan", "frequency": "monthly", "category": "tuition", "program": "G5YkoCLXkHcRBrrKk", "amount": 12, "scaledAmounts": [], "expires": {"$numberLong": "*************"}, "ledgerAccountName": "4521"}, {"_id": "gdpR5RJjZNiHWL4zq", "description": "Specialized plan", "type": "plan", "program": "opv8jrETSw74unc6r", "frequency": "monthly", "category": "tuition", "amount": 0, "scaledAmounts": [], "expires": {"$numberLong": "*************"}, "ledgerAccountName": "2658", "details": {"regStartDate": {"$numberLong": "*************"}, "regEndDate": {"$numberLong": "*************"}, "scheduleType": "dLg94DXiwZNDWc6PT", "dateType": "timePeriod", "timePeriod": "DoSB5aGQvQPwnbFi9"}}, {"_id": "3e2DehnB58cqpQdLB", "description": "Item Test Learning all the time", "type": "item", "program": "pbWjfZttJJ4DSN6w4", "amount": 150, "scaledAmounts": [], "expires": {"$numberLong": "*************"}, "ledgerAccountName": "1212", "details": {"startTime": "10:00 am", "endTime": "12:00 pm", "dateType": "date<PERSON><PERSON><PERSON>", "serviceStartDate": {"$numberLong": "*************"}, "serviceEndDate": {"$numberLong": "*************"}}}, {"_id": "HtHxZHvC9hqi3psTu", "description": "Plan learning all the time P", "type": "plan", "program": "pbWjfZttJJ4DSN6w4", "frequency": "monthly", "category": "tuition", "amount": 120, "scaledAmounts": [], "expires": {"$numberLong": "*************"}, "ledgerAccountName": "1398", "details": {}}, {"_id": "eWD3u5f7Jj9Q5cD5j", "description": "Punch card 10 days", "type": "punchcard", "program": "dnomBQ6pSbsbc4uMB", "numberOfDays": "10", "amount": 500, "scaledAmounts": [], "ledgerAccountName": "1212"}, {"_id": "AJRH59hWHiEfARzMh", "description": "Punch card 5 days", "type": "punchcard", "program": "dnomBQ6pSbsbc4uMB", "numberOfDays": "5", "amount": 250, "scaledAmounts": [], "ledgerAccountName": "1212"}, {"_id": "PgKbsZqsG3K32So5D", "description": "Punch Card 1 days", "type": "punchcard", "program": "dnomBQ6pSbsbc4uMB", "numberOfDays": "1", "amount": 100, "scaledAmounts": [], "ledgerAccountName": "1212"}, {"_id": "rTydJuFSxw3xuTJSg", "description": "Punch Card 20 days", "type": "punchcard", "program": "", "numberOfDays": "20", "amount": 2000, "scaledAmounts": [], "ledgerAccountName": "1212", "suspendUntil": null, "archived": true}, {"_id": "48AyEtKujbP37oTiM", "description": "Item - Drop In Daily Rate", "type": "item", "program": "dnomBQ6pSbsbc4uMB", "amount": 120, "scaledAmounts": [], "ledgerAccountName": "1212", "details": {"dateType": "date<PERSON><PERSON><PERSON>", "serviceStartDate": {"$numberLong": "*************"}, "serviceEndDate": {"$numberLong": "*************"}}, "dropInDailyRate": true}, {"_id": "PfkwGv4GkukrvjCmz", "description": "Plan 1", "type": "plan", "program": "T5pAtFmSf6EyRGe7A", "frequency": "monthly", "category": "tuition", "amount": 300, "scaledAmounts": [], "ledgerAccountName": "1212", "details": {"regStartDate": {"$numberLong": "*************"}, "regEndDate": {"$numberLong": "*************"}}}, {"_id": "xiqqNJJv2qqEvRCcE", "description": "Punch card new", "type": "punchcard", "program": "T5pAtFmSf6EyRGe7A", "numberOfDays": "1", "category": "tuition", "amount": 0, "scaledAmounts": [], "ledgerAccountName": "1212"}, {"_id": "KxBAfSECqGhC8wPsn", "description": "PLAN Weekly - Monthly Rate Divisible by 4 Weeks", "type": "plan", "program": "cWNbC4JWohy4tQ93S", "frequency": "weekly_four_in_month", "category": "tuition", "amount": 450, "scaledAmounts": [], "ledgerAccountName": "1212", "details": {"regStartDate": {"$numberLong": "*************"}, "regEndDate": {"$numberLong": "*************"}, "scheduleType": "z6NdNWiYFBjfKWi6Q"}}, {"_id": "FRL8LAYZAFkf7SMWe", "description": "Plan_Week_4", "type": "plan", "program": "ScYkAST2vdKfWZScK", "frequency": "weekly_four_in_month", "category": "tuition", "amount": 201, "scaledAmounts": [], "expires": {"$numberLong": "*************"}, "ledgerAccountName": "1791", "details": {}}, {"_id": "mEuqp5gBbWxL9nYAE", "description": "Plan 4 Weeks", "type": "plan", "program": "cWNbC4JWohy4tQ93S", "frequency": "weekly_four_in_month", "category": "tuition", "amount": 100.5, "scaledAmounts": [], "ledgerAccountName": "1212", "details": {}}, {"_id": "3SkxTZqz2CpQzEWS9", "description": "Plan(2) 4 weeks", "type": "plan", "program": "cWNbC4JWohy4tQ93S", "frequency": "weekly_four_in_month", "category": "tuition", "amount": 200.75, "scaledAmounts": [], "ledgerAccountName": "1212", "details": {}}, {"_id": "3gsoELbnxxszKeTHF", "description": "Greewan Drop in", "type": "item", "program": "T5pAtFmSf6EyRGe7A", "dropInDailyRate": true, "amount": 17, "scaledAmounts": [], "expires": {"$numberLong": "*************"}, "ledgerAccountName": "6574", "details": {}}, {"_id": "WwStXN9YTGgeFm5rT", "description": "Item Refundable Deposit", "type": "item", "program": "G5YkoCLXkHcRBrrKk", "refundableDeposit": true, "amount": 200, "scaledAmounts": [], "expires": {"$numberLong": "*************"}, "ledgerAccountName": "1212", "details": {"scheduleType": "kQ3r9uap88hm543xe", "dateType": "date<PERSON><PERSON><PERSON>", "serviceStartDate": {"$numberLong": "*************"}, "serviceEndDate": {"$numberLong": "*************"}}}, {"_id": "b7JwBPC9jJKZBShBS", "description": "Item Drop In Daily Rate", "type": "item", "program": "dnomBQ6pSbsbc4uMB", "dropInDailyRate": true, "amount": 350, "scaledAmounts": [], "expires": {"$numberLong": "*************"}, "ledgerAccountName": "1212", "details": {"regStartDate": {"$numberLong": "*************"}, "regEndDate": {"$numberLong": "*************"}, "scheduleType": "kQ3r9uap88hm543xe", "dateType": "date<PERSON><PERSON><PERSON>", "serviceStartDate": {"$numberLong": "*************"}, "serviceEndDate": {"$numberLong": "*************"}}}, {"_id": "875TNn49GF3ZRHMT7", "description": "Item - Drop in", "type": "item", "program": "dnomBQ6pSbsbc4uMB", "dropInDailyRate": true, "amount": 300, "scaledAmounts": [], "ledgerAccountName": "1212", "details": {"dateType": "date<PERSON><PERSON><PERSON>", "serviceStartDate": {"$numberLong": "*************"}, "serviceEndDate": {"$numberLong": "*************"}}}, {"_id": "xMoiM3Aqrh3RrRywe", "description": "FTest", "type": "plan", "program": "dnomBQ6pSbsbc4uMB", "frequency": "daily", "amount": 20, "scaledAmounts": [], "ledgerAccountName": "0190", "details": {"regStartDate": {"$numberLong": "*************"}, "regEndDate": {"$numberLong": "*************"}, "scheduleType": "55JQ8Ndev57BCsAwa", "dateType": "timePeriod", "timePeriod": "CqKZFAiYN65Hwghcb"}}, {"_id": "YXt79bJpbQoTYN9gr", "description": "Plan weekly", "type": "plan", "program": "opv8jrETSw74unc6r", "frequency": "weekly", "category": "tuition", "amount": 100, "scaledAmounts": [], "ledgerAccountName": "1212", "details": {}}, {"_id": "LjTCi8iWoJssNK4xS", "description": "<PERSON><PERSON><PERSON> test", "type": "plan", "program": "G5YkoCLXkHcRBrrKk", "frequency": "monthly", "category": "tuition", "amount": 100, "scaledAmounts": [], "ledgerAccountName": "3490", "details": {}, "archived": true}, {"_id": "gX6ey2skz252C6WMZ", "description": "Summer Scaled pricing Plan", "type": "plan", "program": "G5YkoCLXkHcRBrrKk", "frequency": "scaledMonthly", "category": "tuition", "amount": 250, "scaledAmounts": [50, 100, 150, 200, 250], "ledgerAccountName": "8541", "details": {"dateType": "timePeriod", "timePeriod": "CqKZFAiYN65Hwghcb"}}, {"_id": "5z4JtwMdiC9ThFTtf", "description": "Special discount on winter programs", "type": "plan", "program": "opv8jrETSw74unc6r", "frequency": "scaledMonthly", "category": "tuition", "amount": 250, "scaledAmounts": [50, 100, 150, 200, 250], "ledgerAccountName": "2312", "details": {"dateType": "timePeriod", "timePeriod": "ToivYzTpLTMeZQtWr"}}, {"_id": "46Ym9pQYAPgbG927b", "description": "Monthly Test plan - CC", "type": "plan", "program": "G5YkoCLXkHcRBrrKk", "frequency": "monthly", "category": "tuition", "amount": 100, "scaledAmounts": [], "ledgerAccountName": "8421", "details": {"dateType": "timePeriod", "timePeriod": "CqKZFAiYN65Hwghcb"}}, {"_id": "ryEepYohsrheDMB8P", "description": "bundle test 3", "type": "plan", "program": "dnomBQ6pSbsbc4uMB", "frequency": "scaledMonthly", "category": "tuition", "amount": 60, "scaledAmounts": [100, 90, 80, 70, 60], "ledgerAccountName": "9854", "details": {"dateType": "timePeriod", "timePeriod": "ToivYzTpLTMeZQtWr"}, "expires": {"$numberLong": "*************"}}, {"_id": "Di4tMEror49xYWdLn", "description": "bundle plan 4", "type": "plan", "program": "dnomBQ6pSbsbc4uMB", "frequency": "scaledMonthly", "category": "tuition", "amount": 60, "scaledAmounts": [100, 90, 80, 70, 60], "ledgerAccountName": "8255", "details": {"scheduleType": "7CqPJfxLrXRZpwszt", "dateType": "timePeriod", "timePeriod": "ToivYzTpLTMeZQtWr"}, "expires": {"$numberLong": "*************"}}, {"_id": "SJziQZqTWgs6A4iSF", "description": "Test", "type": "plan", "program": "G5YkoCLXkHcRBrrKk", "frequency": "charged_daily_invoiced_monthly", "category": "tuition", "amount": 60, "scaledAmounts": [], "ledgerAccountName": "3250", "details": {"dateType": "timePeriod", "timePeriod": "CqKZFAiYN65Hwghcb"}}, {"_id": "eHyb5HT7D3scwkjRN", "description": "bundle1 aug2", "type": "plan", "program": "dnomBQ6pSbsbc4uMB", "frequency": "scaledMonthly", "category": "tuition", "amount": 160, "scaledAmounts": [200, 190, 180, 170, 160], "ledgerAccountName": "0921", "details": {"scheduleType": "dLg94DXiwZNDWc6PT", "dateType": "timePeriod", "timePeriod": "ToivYzTpLTMeZQtWr"}}, {"_id": "otGRwyPxQoaniE7jC", "description": "bundle2 aug2", "type": "plan", "program": "dnomBQ6pSbsbc4uMB", "frequency": "scaledMonthly", "category": "tuition", "amount": 160, "scaledAmounts": [200, 190, 180, 170, 160], "ledgerAccountName": "0922", "details": {"scheduleType": "dLg94DXiwZNDWc6PT", "dateType": "timePeriod", "timePeriod": "ToivYzTpLTMeZQtWr"}}, {"_id": "sXJS5HMrbeZXYKird", "type": "bundle", "plans": ["eHyb5HT7D3scwkjRN", "otGRwyPxQoaniE7jC"], "description": "bundle1 aug2 and bundle2 aug2", "scaledAmounts": [[100, 100, 100, 100, 100], [90, 90, 90, 90, 90], [80, 80, 80, 80, 80], [70, 70, 70, 70, 70], [60, 60, 60, 60, 60]]}, {"_id": "3Kyb7RauuYHzxjGD9", "description": "Charge daily by schedule plan", "type": "plan", "program": "bHdK2PhSd7pnitYea", "frequency": "weekly_scheduled_daily", "category": "tuition", "amount": 50, "programDetails": "<p>test</p>\n<p><span style=\"font-size:18px\">test<em><strong>test<span style=\"color:#2dc26b\">test<span style=\"background-color:#f1c40f\">test</span></span></strong></em></span></p>\n<ul>\n<li><span style=\"font-size:18px\"><em><strong><span style=\"color:#2dc26b\"><span style=\"background-color:#f1c40f\">test</span></span></strong></em></span></li>\n<li><span style=\"font-size:18px\"><em><strong><span style=\"color:#2dc26b\"><span style=\"background-color:#f1c40f\">test</span></span></strong></em></span>\n<ul>\n<li><span style=\"font-size:18px\"><em><strong><span style=\"color:#2dc26b\"><span style=\"background-color:#f1c40f\">test</span></span></strong></em></span>\n<ul>\n<li><span style=\"font-size:18px\"><em><strong><span style=\"color:#2dc26b\"><span style=\"background-color:#f1c40f\">test</span></span></strong></em></span></li>\n</ul>\n</li>\n</ul>\n</li>\n</ul>\n<ol>\n<li><span style=\"font-size:18px\"><em><strong><span style=\"color:#2dc26b\"><span style=\"background-color:#f1c40f\">test</span></span></strong></em></span>\n<ol>\n<li><span style=\"font-size:18px\"><em><strong><span style=\"color:#2dc26b\"><span style=\"background-color:#f1c40f\">test</span></span></strong></em></span>\n<ol>\n<li><span style=\"font-size:18px\"><em><strong><span style=\"color:#2dc26b\"><span style=\"background-color:#f1c40f\">test</span></span></strong></em></span></li>\n<li><span style=\"font-size:18px\"><em><strong><span style=\"color:#2dc26b\"><span style=\"background-color:#f1c40f\">test</span></span></strong></em></span></li>\n</ol>\n</li>\n</ol>\n</li>\n</ol>\n<ol style=\"list-style-type:lower-alpha\">\n<li><span style=\"font-size:18px\"><em><strong><span style=\"color:#2dc26b\"><span style=\"background-color:#f1c40f\">test</span></span></strong></em></span></li>\n<li><a href=\"https://google.com\" target=\"_blank\" rel=\"noopener\"><span style=\"font-size:18px\"><em><strong><span style=\"color:#2dc26b\"><span style=\"background-color:#f1c40f\">test</span></span></strong></em></span></a></li>\n</ol>", "scaledAmounts": [], "ledgerAccountName": "6645", "details": {"dateType": "timePeriod", "timePeriod": "CqKZFAiYN65Hwghcb"}}, {"_id": "9MYWhrMfirknzfW8n", "description": "Plan - Charged daily, invoiced monthly", "type": "plan", "program": "opv8jrETSw74unc6r", "frequency": "charged_daily_invoiced_monthly", "category": "tuition", "amount": 10, "scaledAmounts": [], "ledgerAccountName": "1212", "details": {"dateType": "timePeriod", "timePeriod": "CqKZFAiYN65Hwghcb"}}, {"_id": "yRuM9duattKdN57Sc", "description": "GG New plan - invoiced monthly", "type": "plan", "program": "opv8jrETSw74unc6r", "frequency": "charged_daily_invoiced_monthly", "category": "tuition", "amount": 120, "scaledAmounts": [], "ledgerAccountName": "1212", "details": {"dateType": "timePeriod", "timePeriod": "CqKZFAiYN65Hwghcb"}}, {"_id": "PymFRFaaXN3DYwake", "description": "bugs1822", "type": "plan", "program": "opv8jrETSw74unc6r", "frequency": "weekly", "category": "tuition", "amount": 99, "scaledAmounts": [], "ledgerAccountName": "0967", "details": {}}, {"_id": "rDEP6vhcxgj5RPMAH", "description": "bugs1920", "type": "plan", "program": "dnomBQ6pSbsbc4uMB", "frequency": "daily", "category": "tuition", "amount": 2, "scaledAmounts": [], "ledgerAccountName": "0900", "details": {}}, {"_id": "hPge6xHbkB8irr8fr", "description": "Item Drop in GG", "type": "item", "program": "dnomBQ6pSbsbc4uMB", "dropInDailyRate": true, "amount": 45, "scaledAmounts": [], "ledgerAccountName": "1212", "details": {"dateType": "date<PERSON><PERSON><PERSON>"}}, {"_id": "nybm55dmimzJ73uxX", "description": "Secuity Deposit GG", "type": "item", "program": "", "refundableDeposit": true, "amount": 130, "scaledAmounts": [], "ledgerAccountName": "1212", "details": {}}, {"_id": "bcv4jLoqiYrmHzKZX", "description": "Item Date Range", "type": "item", "program": "dnomBQ6pSbsbc4uMB", "amount": 120, "scaledAmounts": [], "ledgerAccountName": "1212", "details": {"dateType": "date<PERSON><PERSON><PERSON>", "serviceStartDate": {"$numberLong": "***************"}, "serviceEndDate": {"$numberLong": "***************"}}, "refundableDeposit": true}], "stats": {"totalEnrollmentCount": 2078}, "legalEntity": {"business_name": "Mariposa Kids", "business_tax_id": "U2FsdGVkX19fp8yBSwSzbi+JRE+BqYeBS0sg1uYoVkA=", "address": "120 Rolston Rd", "city": "<PERSON>", "state": "TX", "zipcode": "76041"}, "sage": {"locationCode": "456"}, "couponCodes": [{"_id": "ZfhgnHLXYwBhPbxxK", "code": "345", "description": "20%off", "amountType": "dollars", "amount": 1, "regStartDate": {"$numberLong": "*************"}, "regEndDate": {"$numberLong": "*************"}, "usedWithOtherCoupons": false, "usedWithDiscounts": false, "archived": true, "suspendUntil": {"$numberLong": "*************"}}, {"_id": "FSotWFH6yS6HSJM3P", "code": "DDD", "description": "aaa+_)_+", "amountType": "percent", "amount": 7, "regStartDate": {"$numberLong": "*************"}, "maxNumberOfRegistrations": 12, "usedWithOtherCoupons": true, "usedWithDiscounts": false, "timePeriods": ["jZRwcmDpYcFTaTd5P"], "expirationDate": {"$numberLong": "1686895200000"}, "regEndDate": {"$numberLong": "1677913200000"}}, {"_id": "EZ4CRZeYnbbPKtAqM", "code": "NNN", "description": "", "amountType": "dollars", "amount": 2, "regStartDate": {"$numberLong": "1678086000000"}, "usedWithOtherCoupons": true, "usedWithDiscounts": false, "suspendUntil": {"$numberLong": "1676592000000"}, "regEndDate": {"$numberLong": "1692856800000"}, "billingPlans": ["P7nLxXAAHLCAGpW6i"], "expirationDate": {"$numberLong": "1692856800000"}, "maxNumberOfRegistrations": 20}, {"_id": "BCXLXygbpXhEmpJP3", "code": "LINELEADER COUPON", "description": "LineLeader (first) testing coupon", "amountType": "dollars", "amount": 45, "regStartDate": {"$numberLong": "1676444400000"}, "regEndDate": {"$numberLong": "1684821600000"}, "expirationDate": {"$numberLong": "1685944800000"}, "maxNumberOfRegistrations": 11, "usedWithOtherCoupons": true, "usedWithDiscounts": true, "numberOfRegistrations": 5}, {"_id": "RgYY75g9ey5aPTXrZ", "code": "TEST2 COUPON", "description": "This is another (second) testing coupon", "amountType": "percent", "amount": 8, "regStartDate": {"$numberLong": "1675753200000"}, "regEndDate": {"$numberLong": "*************"}, "expirationDate": {"$numberLong": "*************"}, "maxNumberOfRegistrations": 100, "usedWithOtherCoupons": false, "usedWithDiscounts": false, "numberOfRegistrations": 12}, {"_id": "J6S56qfWq6EcwP4e4", "code": "EXCEPTIONAL CHILD COUPON", "description": "Applied to children with high IQ", "amountType": "dollars", "amount": 50, "regStartDate": {"$numberLong": "1676444400000"}, "regEndDate": {"$numberLong": "1690351200000"}, "expirationDate": {"$numberLong": "*************"}, "maxNumberOfRegistrations": 2, "usedWithOtherCoupons": true, "usedWithDiscounts": false, "numberOfRegistrations": 1}, {"_id": "KsnPRMDfs7Fv5SpSD", "code": "PARENT COUPON", "description": "Special coupons for parents", "amountType": "dollars", "amount": 75, "regStartDate": {"$numberLong": "1675839600000"}, "regEndDate": {"$numberLong": "*************"}, "expirationDate": {"$numberLong": "1695794400000"}, "maxNumberOfRegistrations": 50, "usedWithOtherCoupons": true, "usedWithDiscounts": true, "numberOfRegistrations": 9}, {"_id": "cofYLEygaa6BcjZeT", "code": "CHILD COUPON", "description": "Childcare Plan Coupon", "amountType": "dollars", "amount": 50, "regStartDate": {"$numberLong": "1676962800000"}, "regEndDate": {"$numberLong": "1684908000000"}, "expirationDate": {"$numberLong": "*************"}, "usedWithOtherCoupons": true, "usedWithDiscounts": true, "timePeriods": [], "billingPlans": []}, {"_id": "fmYuGSBWm8dD3H2Ew", "code": "WEDNESDAY COUPON", "description": "Just another testing coupon", "amountType": "dollars", "amount": 90, "regStartDate": {"$numberLong": "1676876400000"}, "regEndDate": {"$numberLong": "1689832800000"}, "expirationDate": {"$numberLong": "1692684000000"}, "maxNumberOfRegistrations": 2, "usedWithOtherCoupons": true, "usedWithDiscounts": true, "billingPlans": ["KNQfMiRWNL2DzZTC5", "AuPkXhDt9YydCQ64B", "bvskh6itHq4Mp4pcp"]}, {"_id": "aZEWbP6e8MM4Z628A", "code": "TIME PERIOD COUPON", "description": "Coupon for testing if time period restriction works or not.", "amountType": "dollars", "amount": 5, "regStartDate": {"$numberLong": "*************"}, "regEndDate": {"$numberLong": "1691042400000"}, "expirationDate": {"$numberLong": "1691042400000"}, "maxNumberOfRegistrations": 8, "usedWithOtherCoupons": true, "usedWithDiscounts": true, "numberOfRegistrations": 2}, {"_id": "oqoMgmfBNz2XpK2tF", "code": "TIME2 PERIOD", "description": "Another testing for time period", "amountType": "dollars", "amount": 90, "regStartDate": {"$numberLong": "1677049200000"}, "regEndDate": {"$numberLong": "1678431600000"}, "expirationDate": {"$numberLong": "*************"}, "maxNumberOfRegistrations": 2, "usedWithOtherCoupons": true, "usedWithDiscounts": true}, {"_id": "zFTzQcDbmD7Ff7WEv", "code": "LOWERD", "description": "lower coupon code", "amountType": "dollars", "amount": 14, "regStartDate": {"$numberLong": "1677654000000"}, "regEndDate": {"$numberLong": "1738393200000"}, "usedWithOtherCoupons": true, "usedWithDiscounts": false, "numberOfRegistrations": 5}, {"_id": "zDBZtTcamNPtsqBZ3", "code": "LOWERP", "description": "lower coupon percentage ", "amountType": "percent", "amount": 3, "regStartDate": {"$numberLong": "1680069600000"}, "regEndDate": {"$numberLong": "1735714800000"}, "usedWithOtherCoupons": true, "usedWithDiscounts": false}, {"_id": "5i5re7qR8gDzuoB9o", "code": "HIGHERP", "description": "higher coupon percentage", "amountType": "percent", "amount": 5, "regStartDate": {"$numberLong": "1677654000000"}, "regEndDate": {"$numberLong": "1735714800000"}, "usedWithOtherCoupons": true, "usedWithDiscounts": false}, {"_id": "TtyjannfiwthBAtiW", "code": "LINETEST1", "description": "", "amountType": "dollars", "amount": 2, "regStartDate": {"$numberLong": "1683612000000"}, "usedWithOtherCoupons": true, "usedWithDiscounts": true, "numberOfRegistrations": 18, "useCouponInBundles": "yes-all", "ledgerCode": "0981", "oneTimeCharges": ["9ij7T8a5o4kBj6Yf9"]}, {"_id": "CrbS8cLNzxJFfTXjG", "code": "districtDiscount", "description": "districtDiscount", "amountType": "dollars", "amount": 100, "regStartDate": {"$numberLong": "1683698400000"}, "expirationDate": {"$numberLong": "*************"}, "maxNumberOfRegistrations": 10, "usedWithOtherCoupons": false, "usedWithDiscounts": false, "timePeriods": [], "billingPlans": []}, {"_id": "nLDDTuaLMM3emSBoe", "code": "REGD", "description": "", "amountType": "dollars", "amount": 55, "regStartDate": {"$numberLong": "1687154400000"}, "usedWithOtherCoupons": true, "usedWithDiscounts": false, "oneTimeCharges": ["9ij7T8a5o4kBj6Yf9"], "numberOfRegistrations": 2}, {"_id": "RkqE6p4r7q47nagmc", "code": "SINGLE FAMILY", "description": "used once per family", "amountType": "dollars", "amount": 10, "regStartDate": {"$numberLong": "*************"}, "usedWithOtherCoupons": true, "usedWithDiscounts": true, "billingPlans": ["mZzQfSxx2Ct4oWzZX", "uNQauBQyta4Tm467e"], "isSingleInstallmentCoupon": true, "usedBy": [{"childId": "Z33hw5GAuXbtcvbzf", "familyId": ["CXM9jgF73ySDsPXRP", "CXM9jgF73ySDsPXRP", "CXM9jgF73ySDsPXRP"], "invoice": "heZHYE4C2fi8CgyoZ"}, {"childId": "JuCPjFxScsQ9W3c8c", "familyId": ["KtzKxaWH3METyWXm7", "KtzKxaWH3METyWXm7", "KtzKxaWH3METyWXm7"], "invoice": "iXfyQEuQCYMmGjMRp"}], "regEndDate": {"$numberLong": "1694239200000"}, "expirationDate": {"$numberLong": "1694239200000"}, "ledgerCode": "1212", "maxNumberOfRegistrations": 100, "numberOfRegistrations": 5}, {"_id": "e5BJJjEm3sGSdrmE6", "code": "USED ONCE PER FAMILY", "description": "", "amountType": "dollars", "amount": 70, "regStartDate": {"$numberLong": "*************"}, "usedWithOtherCoupons": false, "usedWithDiscounts": false, "usedBy": [{"childId": "LBPQnZQRwsMu94reS", "familyId": ["RgcgHBYGGR48zwNQJ", "RgcgHBYGGR48zwNQJ", "RgcgHBYGGR48zwNQJ"], "invoice": "azaqtR4ndDKtzLu9D"}, {"childId": "hwZtRkpigEhyinCQh", "familyId": ["6SwqirQLr3dMygQsu", "6SwqirQLr3dMygQsu", "6SwqirQLr3dMygQsu"], "invoice": "AqxeuNmNQYHbXzeWm"}, {"childId": "yzGAbDzjoPPadZNWC", "familyId": ["nZXb7yDdpJgaxFvyn", "nZXb7yDdpJgaxFvyn", "nZXb7yDdpJgaxFvyn"], "invoice": "D96gtWFjteGx5uY3T"}], "regEndDate": {"$numberLong": "*************"}, "ledgerCode": "1212", "numberOfRegistrations": 5, "isSingleInstallmentCoupon": true}, {"_id": "PRbaPRKJQKjwhR52k", "code": "AA-1199", "description": "Valid for summer plans", "amountType": "dollars", "amount": 40, "regStartDate": {"$numberLong": "1690696800000"}, "usedWithOtherCoupons": false, "usedWithDiscounts": true, "billingPlans": ["3TWiaiCmq3x4TtPuT", "NpdymwHYrFtcCR8y6", "46Ym9pQYAPgbG927b", "C3FFhCjH7NTXQd6pw"], "usedBy": [{"childId": "QpFmpCkSidJYTWwWR", "familyId": ["FQigfnjhmoLmLfMfe", "FQigfnjhmoLmLfMfe", "FQigfnjhmoLmLfMfe"], "invoice": "Gznfd8ddExc5X6yva"}], "ledgerCode": "1212", "numberOfRegistrations": 3, "isSingleInstallmentCoupon": true}, {"_id": "zotaZgjthaoZtrdky", "code": "LINETEST2", "description": "bundle coupon code", "amountType": "percent", "amount": 100, "useCouponInBundles": "yes-least", "regStartDate": {"$numberLong": "1690776000000"}, "ledgerCode": "0982", "usedWithOtherCoupons": true, "usedWithDiscounts": false, "regEndDate": {"$numberLong": "1694232000000"}, "expirationDate": {"$numberLong": "1694232000000"}, "oneTimeCharges": ["Zxm5eJ6f8W3ZmD5L9", "6Ysxfxv5R4u9fpTFi", "auv4jLoqiYrmHzKBA", "7kZ6GDSFEWwWEk7bF", "875TNn49GF3ZRHMT7", "48AyEtKujbP37oTiM", "hPge6xHbkB8irr8fr", "BopeHGZ6zDRhHHgA2", "w9TjReTNcCwFgGp73", "8dSDSbtKwjqx9Sbgh", "W2JhJ3Y7ywRCte8cy", "PgKbsZqsG3K32So5D", "eWD3u5f7Jj9Q5cD5j", "AJRH59hWHiEfARzMh", "xiqqNJJv2qqEvRCcE", "9ij7T8a5o4kBj6Yf9", "5c9Co2Te9cgKffEz8"]}, {"_id": "xEHbYdnLxgutStKaL", "code": "SINGLE USE - A12", "description": "SINGLE USE", "amountType": "dollars", "amount": 40, "regStartDate": {"$numberLong": "1690783200000"}, "usedWithOtherCoupons": false, "usedWithDiscounts": true, "billingPlans": ["3TWiaiCmq3x4TtPuT", "46Ym9pQYAPgbG927b", "C3FFhCjH7NTXQd6pw"], "isSingleInstallmentCoupon": true, "usedBy": [{"childId": "AAGEySA5ftiupBuZ5", "familyId": ["P6qJqgoPvQjK3xoqa", "P6qJqgoPvQjK3xoqa", "P6qJqgoPvQjK3xoqa"], "invoice": "se8TmTrSTRYxtvWi6"}], "numberOfRegistrations": 1}, {"_id": "JhJjqbF3Y5ryoxvbM", "code": "100DIS", "description": "", "amountType": "percent", "amount": 100, "useCouponInBundles": "yes-all", "regStartDate": {"$numberLong": "1691733600000"}, "usedWithOtherCoupons": false, "usedWithDiscounts": false, "timePeriods": [], "billingPlans": [], "oneTimeCharges": ["Zxm5eJ6f8W3ZmD5L9", "6Ysxfxv5R4u9fpTFi", "auv4jLoqiYrmHzKBA", "7kZ6GDSFEWwWEk7bF", "875TNn49GF3ZRHMT7", "48AyEtKujbP37oTiM", "BopeHGZ6zDRhHHgA2", "w9TjReTNcCwFgGp73", "8dSDSbtKwjqx9Sbgh", "W2JhJ3Y7ywRCte8cy", "PgKbsZqsG3K32So5D", "eWD3u5f7Jj9Q5cD5j", "AJRH59hWHiEfARzMh", "xiqqNJJv2qqEvRCcE", "9ij7T8a5o4kBj6Yf9", "5c9Co2Te9cgKffEz8"], "isSingleInstallmentCoupon": false, "usedBy": [], "numberOfRegistrations": 1}, {"_id": "jqbF3Y5ryoxvbMER", "code": "500BUCKS", "amountType": "dollars", "amount": "500", "useCouponInBundles": "yes-all", "regStartDate": {"$numberLong": "1691733600000"}, "usedWithOtherCoupons": false, "usedWithDiscounts": false, "numberOfRegistrations": 3}, {"_id": "gvaYiM96J7JcQXbzB", "code": "EXPIRED", "description": "", "amountType": "percent", "amount": 100, "useCouponInBundles": "yes-all", "regStartDate": {"$numberLong": "1692590400000"}, "usedWithOtherCoupons": true, "usedWithDiscounts": false, "usedBy": [], "ledgerCode": "2150", "regEndDate": {"$numberLong": "1692590400000"}}, {"_id": "zwGXAqdowZNP2nH4F", "code": "10PERCEN", "description": "", "amountType": "percent", "amount": 10, "useCouponInBundles": "yes-all", "regStartDate": {"$numberLong": "1692676800000"}, "usedWithOtherCoupons": true, "usedWithDiscounts": true, "timePeriods": [], "billingPlans": [], "oneTimeCharges": [], "isSingleInstallmentCoupon": false, "usedBy": [], "numberOfRegistrations": 1}, {"_id": "hiKLXttspAZJ7mN1E", "code": "100OFF", "description": "", "amountType": "percent", "amount": 100, "useCouponInBundles": "yes-all", "regStartDate": {"$numberLong": "1692676800000"}, "usedWithOtherCoupons": true, "usedWithDiscounts": true, "timePeriods": [], "billingPlans": [], "oneTimeCharges": [], "isSingleInstallmentCoupon": false, "usedBy": []}], "timePeriods": [{"_id": "zMnCFqMgjpjwe8J8Z", "name": "Test 2", "startDate": {"$numberLong": "1676530800000"}, "endDate": {"$numberLong": "*************"}}, {"_id": "jZRwcmDpYcFTaTd5P", "name": "Test", "startDate": {"$numberLong": "1677481200000"}, "endDate": {"$numberLong": "1677740400000"}}, {"_id": "Z4v47XbvkDrP7iwna", "name": "Test 3", "startDate": {"$numberLong": "1679378400000"}, "endDate": {"$numberLong": "1680069600000"}}, {"_id": "Kjvqx75A9Y9LGQwpY", "name": "Service 1", "startDate": {"$numberLong": "1680501600000"}, "endDate": {"$numberLong": "1683698400000"}}, {"_id": "DoSB5aGQvQPwnbFi9", "name": "Dates Test", "startDate": {"$numberLong": "1684216800000"}, "endDate": {"$numberLong": "*************"}}, {"_id": "dJ5FgKhWf8stGzH8a", "name": "Service 2", "startDate": {"$numberLong": "1685599200000"}, "endDate": {"$numberLong": "1688104800000"}}, {"_id": "CqKZFAiYN65Hwghcb", "name": "Test Summer", "startDate": {"$numberLong": "1690178400000"}, "endDate": {"$numberLong": "1840291448000"}}, {"_id": "ToivYzTpLTMeZQtWr", "name": "GG test", "startDate": {"$numberLong": "1697954400000"}, "endDate": {"$numberLong": "*************"}}], "programs": [{"_id": "dnomBQ6pSbsbc4uMB", "name": "Mariposa Program", "isActive": true, "isRequiredAdvanceNotice": false}, {"_id": "G5YkoCLXkHcRBrrKk", "name": "Summer Program", "isActive": true, "isRequiredAdvanceNotice": false}, {"_id": "bHdK2PhSd7pnitYea", "name": "Learning Time", "isActive": true, "isRequiredAdvanceNotice": false}, {"_id": "opv8jrETSw74unc6r", "name": "Winter Test", "isActive": true, "isRequiredAdvanceNotice": false}], "adyenInfo": {"platform": "classic", "accountCode": "****************"}, "billingMaps": {"accountsReceivable": {"accountName": "1205"}, "creditMemos": {"accountName": "3000"}, "customerLiabilityPayable": {"accountName": "2000"}, "onlinePaymentDeposits": {"accountName": "1305"}, "manualPaymentDeposits": {"accountName": ""}, "manualOtherCredits": {"accountName": "2002"}, "settlementFees": {"accountName": "4805"}, "payrollDeduction": {"accountName": ""}, "undepositedFunds": {"accountName": "4563"}, "agencyOverpayment": {"accountName": "7040"}, "unappliedCash": {"accountName": "8998"}, "badDebt": {"accountName": "1102"}, "agencyWriteOff": {"accountName": "1101"}, "collectionsWriteOff": {"accountName": "1103"}, "otherPlanDiscounts": {"accountName": "2344"}, "otherPayerDiscounts": {"accountName": "7832"}, "securityDepositsLiability": {"accountName": "4222"}, "securityDepositRefunds": {"accountName": "9224"}, "unappliedCashApplied": {"accountName": "2294"}, "securityDepositForfeiture": {"accountName": "1000"}}, "allowDepositReportEdit": false, "excludedManualPayTypes": ["ach"], "toplinePercentDiscounts": false}, "counters": {"invoices": 4533}, "metrics": {"messagesSent": 6045, "momentsPosted": 327}, "engagementCounts": [{"dayTimestamp": {"$numberLong": "*************"}, "providerCount": 1, "familyCount": 0}, {"dayTimestamp": {"$numberLong": "*************"}, "providerCount": 2, "familyCount": 0}, {"dayTimestamp": {"$numberLong": "*************"}, "providerCount": 1, "familyCount": 0}, {"dayTimestamp": {"$numberLong": "*************"}, "providerCount": 10, "familyCount": 0}], "timezone": "UTC", "familyRegistrationSettings": {"requiredContactsCount": "0", "questions": [], "discountsAllowed": ["coupon_codes", "subsidy_approval_process", "district_employee_approval_process", "employee_id"]}, "longName": "Mariposa Staging", "valueOverrides": {"curriculumTypes": [{"label": "Art", "children": [{"label": "Art child"}]}, {"label": "Blocks"}, {"label": "Curriculum"}, {"label": "Dramatic Play"}, {"label": "Gross Motor"}, {"label": "Handwriting Without Tears"}, {"label": "Journal"}, {"label": "Literacy"}, {"label": "Manipulative/Fine Motor"}, {"label": "Math"}, {"label": "Music/Movement"}, {"label": "Quiet/Individual"}, {"label": "STEM"}, {"label": "Science/Discovery"}, {"label": "Sensory"}, {"label": "Talking, Drawing, Writing"}], "pinCodeCheckinFields": [{"fieldType": "timePicker", "label": "Last potty?", "dataId": "lastPotty", "outlook": true}, {"fieldType": "string", "label": "Last food?", "dataId": "lastFood", "outlook": true}, {"fieldType": "text", "label": "Changes in normal care?", "dataId": "changeInCare", "outlook": true}], "inquiryProfileFields": [{"name": "centerPreference", "description": "Center Preference", "type": "select", "values": ["<PERSON><PERSON><PERSON>", "<PERSON>", "Downtown", "Wabash Landing"]}, {"name": "referralSource", "description": "Referral Source", "type": "select", "values": ["Family", "Friend", "Coworker", "Website", "Referral", "Other"]}, {"name": "referralSourceOther", "description": "Referral Source (other)", "type": "text"}, {"name": "preferredContactMethod", "description": "Preferred Contact Method", "type": "select", "values": ["Email", "Phone call", "Text message"]}, {"name": "birthday", "description": "Date of birth", "type": "date"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Sibling", "type": "select", "values": ["Yes", "No"]}, {"name": "employee", "description": "Employee", "type": "select", "values": ["Yes", "No"]}, {"name": "schedule", "description": "Schedule", "type": "select", "values": ["Part-time", "Full-time"]}, {"name": "householdIncome", "description": "Household Income", "type": "string"}, {"name": "householdSize", "description": "Household Size", "type": "string"}, {"name": "onmywayprekEligible", "description": "On My Way PreK Eligible", "type": "select", "values": ["Yes", "No"]}, {"name": "additionalInformation", "description": "Additional Information", "type": "text"}, {"name": "family<PERSON><PERSON>bers", "description": "Family Member", "type": "fieldGroup", "multiple": true, "fields": [{"name": "firstName", "description": "First Name", "type": "string"}, {"name": "lastName", "description": "Last Name", "type": "string"}, {"name": "birthday", "description": "Date of birth", "type": "string"}, {"name": "ssn", "description": "SSN", "type": "string"}, {"name": "address", "description": "Address", "type": "string"}, {"name": "city", "description": "City", "type": "string"}, {"name": "state", "description": "State", "type": "string"}, {"name": "zipcode", "description": "Zipcode", "type": "string"}, {"name": "phone", "description": "Phone", "type": "string"}, {"name": "email", "description": "Email", "type": "string"}]}], "profileFields": [{"name": "nickname", "description": "Nickname", "type": "text", "isFamilyEditable": "True"}, {"name": "schoolId", "description": "School ID", "type": "text"}, {"name": "amBusRoute", "description": "AM Bus Route", "type": "query", "isFamilyEditable": "False", "source": "amBusRoute"}, {"name": "pmBusRoute", "description": "PM Bus Route", "type": "query", "isFamilyEditable": "False", "source": "pmBusRoute"}, {"name": "studentGrade", "description": "Grade", "type": "select", "values": ["Preschool", "Kindergarten", "1", "2", "3", "4", "5", "6", "7", "8"]}, {"name": "language", "description": "language", "type": "text"}, {"name": "enrollmentDate", "description": "Enrollment Date", "type": "date"}, {"name": "startDate", "description": "Start Date", "type": "date"}, {"name": "withdrawDate", "description": "Withdraw Date", "type": "date"}, {"name": "birthday", "description": "Date of Birth", "type": "date"}, {"name": "gender", "description": "Gender", "type": "select", "values": ["Male", "Female", "Unspecified"]}, {"name": "allowMedia", "description": "Allow Social Media", "type": "select", "values": ["Yes", "No"], "visibleOnlyToRoles": ["admin", "family"]}, {"name": "racialIdentity", "description": "Racial Identity", "type": "select", "values": ["American Indian or Alaskan Native", "Asian", "Black or African American", "Hispanic/Latino", "Native Hawaiian or Pacific Islander", "Other race", "Two or more races", "Unspecified", "White"]}, {"name": "ethnicIdentity", "description": "Ethnic Identity", "type": "select", "values": ["Hispanic/Latino", "Non-Hispanic/Latino"]}, {"name": "medicalInfo", "description": "Medical Conditions", "type": "text"}, {"name": "subsidyReason", "description": "Subsidy Reason", "isFamilyEditable": true, "type": "string"}, {"name": "planOfCare", "description": "Plan of Care", "type": "select", "values": ["Yes", "No"], "visibleOnlyToRoles": ["admin"]}, {"name": "notesPublic", "description": "Notes", "type": "text"}, {"name": "notesPrivate", "description": "Notes (Private)", "visibleOnlyToRoles": ["admin", "Staff"], "type": "text"}, {"name": "healthInformation", "description": "Health Information", "type": "fieldGroup", "fields": [{"name": "healthcareProvider", "description": "Healthcare Provider", "type": "text", "isFamilyEditable": "True"}, {"name": "healthcareProviderPhone", "description": "Healthcare Provider's Phone Number", "type": "text", "isFamilyEditable": "True"}, {"name": "preferredHospital", "description": "Preferred Hospital", "type": "text", "isFamilyEditable": "True"}, {"name": "primaryFamily", "description": "Primary Emergency Contact", "type": "query", "isFamilyEditable": "False", "source": "primaryFamily"}, {"name": "restrictedPickUp", "description": "Restricted Pick Up", "type": "fieldGroup", "fields": [{"name": "restrictedPickup1", "description": "Restricted Pickup 1", "type": "string"}, {"name": "restrictedPickup2", "description": "Restricted Pickup 2", "type": "string"}]}]}], "discountTypes": [{"type": "multipleFamily", "description": "Multiple family ", "amount": {"$numberDouble": "NaN"}, "amountType": "dollars", "expiresWithGracePeriod": false, "overrideSingleDiscount": false, "ledgerAccountName": "7000"}, {"type": "other", "description": "Other", "amount": {"$numberDouble": "NaN"}, "amountType": "dollars", "expiresWithGracePeriod": false, "overrideSingleDiscount": false, "ledgerAccountName": "7001"}, {"type": "funstuff", "description": "Fun Stuff", "amount": 10, "amountType": "dollars", "expiresWithGracePeriod": false, "overrideSingleDiscount": false, "ledgerAccountName": "7002"}, {"type": "overrideDiscountOne", "description": "Override Discount 1", "amount": 15, "amountType": "dollars", "overrideSingleDiscount": true, "expiresWithGracePeriod": false, "ledgerAccountName": "7003"}, {"type": "overrideDiscountTwo", "description": "Override Discount Two", "amount": 15, "amountType": "percent", "overrideSingleDiscount": true, "expiresWithGracePeriod": false, "ledgerAccountName": "7004"}, {"type": "overrideDiscount<PERSON><PERSON><PERSON>", "description": "Override Discount Three", "amount": 40, "amountType": "dollars", "overrideSingleDiscount": true, "expiresWithGracePeriod": false, "ledgerAccountName": "7005"}, {"type": "districtDiscount", "description": "districtDiscount", "amount": 50, "amountType": "dollars", "ledgerAccountName": "7006", "expiresWithGracePeriod": false, "overrideSingleDiscount": false}, {"type": "5429", "description": "Sibling discount", "amount": 30, "amountType": "dollars", "ledgerAccountName": "8541", "expiresWithGracePeriod": false, "overrideSingleDiscount": false}, {"type": "staffDiscount", "description": "Staff Discount - 60%", "amount": 60, "amountType": "percent", "ledgerAccountName": "8541", "expiresWithGracePeriod": false, "overrideSingleDiscount": false}], "familyProfileFields": [{"name": "phoneNumberHome", "description": "Phone Number (home)", "type": "string", "isFamilyEditable": true}, {"name": "phoneNumberWork", "description": "Phone Number (work)", "type": "string", "isFamilyEditable": true}, {"name": "notesPrivate", "description": "Notes (Private)", "visibleOnlyToRoles": ["admin", "Staff"], "type": "string"}, {"name": "householdInformation", "description": "Household Information", "type": "fieldGroup", "fields": [{"name": "parentStreetAddress", "description": "Street Address", "type": "string"}, {"name": "parentCity", "description": "City", "type": "string"}, {"name": "parentState", "description": "State", "type": "string"}, {"name": "parentZip", "description": "Zip Code", "type": "string"}, {"name": "addressType", "description": "Is this the mailing or physical address?", "type": "select", "values": ["Mailing Only", "Physical Only", "Mailing and Physical Address"]}]}, {"name": "employerInformation", "description": "Workplace/School Information", "type": "fieldGroup", "fields": [{"name": "employerName", "description": "Workplace/School Name", "type": "string"}, {"name": "workplaceSchoolSchedule", "description": "Workplace/School Schedule", "type": "string"}, {"name": "employerStreetAddress", "description": "Workplace/School's Street Address", "type": "string"}, {"name": "employerCity", "description": "Workplace/School's City", "type": "string"}, {"name": "employersState", "description": "Workplace/School's State", "type": "string"}, {"name": "employersZip", "description": "Workplace/School's Zip", "type": "string"}]}], "payerSources": [{"type": "ccdf", "description": "CCDFDESCRIPTION", "ledgerAccountName": "6000", "cashLedgerAccountName": "1514"}, {"type": "QA", "description": "Testing the archiving of discounts/payers.", "ledgerAccountName": "Little Angels", "archived": false}, {"type": "periodperiod", "description": "Testing.Periods", "ledgerAccountName": "0987"}, {"type": "bugs1822", "description": "bugs1822test", "ledgerAccountName": "0966", "cashLedgerAccountName": ""}], "staffProfileFields": [{"name": "birthdate ", "description": "Birthday", "type": "date"}, {"name": "payRate", "description": "Hourly Pay Rate", "type": "string"}, {"name": "schoolId", "description": "School ID", "type": "text"}, {"name": "title", "description": "Title", "type": "text"}, {"name": "language", "description": "language", "type": "text"}, {"name": "amBusRoute", "description": "AM Bus Route", "type": "query", "isFamilyEditable": "False", "source": "amBusRoute"}, {"name": "pmBusRoute", "description": "PM Bus Route", "type": "query", "isFamilyEditable": "False", "source": "pmBusRoute"}, {"name": "isSchoolAdministrator", "description": "isSchoolAdministrator", "type": "text"}, {"name": "isDistrictAdministrator", "description": "isDistrictAdministrator", "type": "text"}, {"name": "employeeClassification", "description": "Employee Classification", "visibleOnlyToRoles": ["admin"], "type": "select", "values": ["", "Exempt", "Non-Exempt"]}, {"name": "workDepartment", "description": "Work Department", "type": "string"}], "scheduleTypes": [{"_id": "z6NdNWiYFBjfKWi6Q", "type": "Before Care", "startTime": "6:30 AM", "endTime": "11:30 AM", "sortStart": "06:30", "sortEnd": "11:30", "overlaps": ["dLg94DXiwZNDWc6PT", "55JQ8Ndev57BCsAwa"], "fteCount": ".3", "hideInForecasting": true, "maxEnrollment": {"1": "10", "2": "10", "3": "10", "4": "10", "5": "10"}}, {"_id": "kQ3r9uap88hm543xe", "type": "After Care", "startTime": "1:30 PM", "endTime": "3:30 PM", "sortStart": "14:30", "sortEnd": "18:30", "overlaps": ["dLg94DXiwZNDWc6PT", "CFgFue3FZbKJCneQP", "55JQ8Ndev57BCsAwa"], "fteCount": ".3", "hideInForecasting": true}, {"_id": "dLg94DXiwZNDWc6PT", "type": "Full-time", "endTime": false, "fteCount": "1", "startTime": false, "hideInForecasting": false}, {"_id": "7CqPJfxLrXRZpwszt", "type": "Half Day AM", "endTime": "12:30 PM", "startTime": "11:30 AM", "sortStart": "11:30", "sortEnd": "12:30", "overlaps": ["dLg94DXiwZNDWc6PT", "55JQ8Ndev57BCsAwa"], "fteCount": ".5", "hideInForecasting": false}, {"_id": "CFgFue3FZbKJCneQP", "type": "Half Day PM", "startTime": "1:30 PM", "endTime": "6:00 PM", "fteCount": ".5", "hideInForecasting": false, "maxEnrollment": {"1": "", "2": "", "3": "", "4": "", "5": ""}, "sortStart": "12:30", "sortEnd": "18:00", "overlaps": ["kQ3r9uap88hm543xe", "dLg94DXiwZNDWc6PT", "55JQ8Ndev57BCsAwa"]}], "holidays": [{"_id": "MXgkH5SCBK5AdnXEF", "name": "Thanksgiving", "date": "2021-11-25"}, {"_id": "wc5RhZbdXEfs9gksy", "name": "Columbus Day", "date": "2021-10-11"}, {"_id": "H7gTFBJcQQeE7iRDL", "name": "Vetrans Day", "date": "2021-11-11"}, {"_id": "fzEQcm2FA7npuxSMR", "name": "Christmas Eve", "date": "2021-12-24"}, {"_id": "XZMuDZdoj2fKAdyED", "name": "Christmas Day", "date": "2021-12-25"}], "expressDriveUp": {"arrivalText": "Will be there shortly!"}, "designations": ["Wait List"], "alternateServiceChargeFeeDescription": "Convenience fees: For credit card transactions, a convenience fee of 2.50% + $0.10 will be charged. For electronic checks, a convenience fee of $0.25, will be charged. This convenience fee will automatically be applied to your payment amount. Debit Cards will not be accepted. \n \nTo cover the cost of processing a credit or charge card transaction, and pursuant to section 5-2-212, Colorado Revised Statutes, a seller or lessor may impose a processing surcharge in an amount not to exceed the merchant discount fee that the seller or lessor incurs in processing the sales or lease transaction. A seller or lessor shall not impose a processing surcharge on payments made by use of cash, a check, or a debit card or redemption of a gift card.", "timeConfirmation": {"confirmationDay": "TUE"}, "availablePermissionsContexts": ["activities", "activities/curriculumBank", "activities/themeBank", "admin/configuration", "admin/configuration/staffTimeKeeping/timeCardsLock", "admin/mediaRequirements", "admin/configuration/addeditorgs", "announcements", "billing/configuration/plans", "billing/configuration/system", "billing/configuration/override", "billing/configuration/discounts", "billing/creditMemos/create", "billing/deposits/edit", "billing/invoices", "billing/invoices/billingNotes", "billing/invoices/itemCharges", "billing/invoices/planAssignments", "billing/invoices/resend", "billing/invoices/void", "billing/payments", "billing/payments/achGeneration", "billing/payments/create", "billing/payments/creditBadDebt", "billing/payments/creditPayrollDeduction", "billing/payments/manageBankDeposits", "billing/payments/manageChargebacks", "billing/payments/void", "billing/payments/refund", "billing/otherCredits/modify", "billing/reports", "documents", "food", "groups", "integrations/airslate", "integrations/airslate/manualEdit", "moments", "org/propagateSettings", "people/addModifyUsers", "people/manageAdpStaffAndAdmins", "people/manageAllRoles", "people/movement", "people/profile/allergyImmunization", "people/profile", "people/profile/pay", "people/relationships", "people/roleAssignments", "people/editLockedTimeCards", "registrationFlowQuestions", "people/uploadAdp", "reports/classList", "reports/standard", "reservations", "reservations/removeLinkedBillingPlan"], "activitiesAgeGroups": [{"label": "0-12 months"}, {"label": "9-18 months"}, {"label": "16-36 months"}, {"label": "3-4 year olds"}, {"label": "4-5 year olds"}], "availablePermissionsRoles": ["staffLocal", "Administrator"]}, "zkTecoValues": {"areaId": "8a858085865733d701865b11a9f70005", "outerDoorId": "777*lll", "innerDoorId": "8a858085865733d7018693b795d1040a", "accLevelId": "8a85808588e5086a018902d53677008a"}, "whiteLabel": {"assets": {"emailLogo": "https: //assets.momentpath.com/customers/lightbridge/summaryemailv2019/logo.png"}, "primaryColor": "#41748D", "secondaryColor": "#54585A", "pushwooshProfile": "pushwooshLightbridge", "ROOT_URL": "https://mp-staging-indigo.momentpath.com", "androidAppUrl": "https://play.google.com/store/apps/details?id=com.momentpath.sshouse", "iosAppUrl": "https://apps.apple.com/us/app/mysunshinehouse/id1606752193", "smsPostfix": "- read more sshouse://open"}, "shortCode": "222", "ftpCredentials": {"adp": {"url": "18.212.186.243", "filePath": "/home/<USER>/fromADP", "login": "testftpuser", "password": "Password1"}}, "busRoutes": [{"_id": "sNd6PGQwP2Eu8aita", "name": "Bus Route test", "am": false, "pm": true}, {"_id": "bsvKsaMRdAx25XJR3", "name": "iaoisdjf adj fadpo fjasdfpo jadf jadopf jaopdifj aodij aodifj aopsifj apoidsjf poaidfj asopidfjaposidjf aoidjf poaisdjf poasidj aposdifj odif jaopdjf afs", "am": true, "pm": false}, {"_id": "rXLXTswEXzGSeTGqP", "name": "Red Ryder Route", "am": true, "pm": false}, {"_id": "vboXPqS8tTuB7u5uE", "name": "Route from Sunshine House", "am": false, "pm": true}, {"_id": "EDDdtdrkWhSBuudGv", "name": "Route to Sunshine House", "am": true, "pm": false}, {"_id": "fPHYFzWrc5x3Z2sTN", "name": "Test for AM and PM", "am": true, "pm": true}, {"_id": "bBLGBacaikxAKfnSu", "name": "Testing for Add Bus route", "am": true, "pm": true}, {"_id": "RS2DszN3ncqaf2jwj", "name": "Testing from Aladin Daycare to Wazowski Daycare", "am": true, "pm": false}, {"_id": "PN8MsEFh4QX2mNyHW", "name": "Testing from Mexico to USA routes", "am": true, "pm": true}, {"_id": "QPkjgrSM9ygjfSk6i", "name": "Testing from Shrek Daycare ", "am": true, "pm": false}, {"_id": "q4A2FmbLJim5SztEN", "name": "Now Testing new route.Testing new route.Testing new route.Testing new route.Testing new route.Testing new route.Testing new route.Testing new route.Testing new route.Testing new route.", "am": true, "pm": true}], "kioskPinCode": "5555", "brands": [{"_id": "DQeEKyoTnjR9LWocx", "name": "Spanish Butterflies"}], "selectedBrand": "vC7jPEwhduG94pvdW", "curriculumStandard": ["Funshots"], "customStaffPaySettings": {"frequency": "weekly", "overtimeActive": true, "overtimeThreshold": "40", "standardPayAdpCode": "542", "types": [{"_id": "oiaivTHsCZBcSk8bn", "type": "Event", "rate": "12", "archived": false}, {"_id": "R5CgtQdJR2XSru5LY", "type": "Meeting", "rate": "8", "staffProfileRate": false}]}, "mediaRequirement": {"mediaReviewRequired": "Yes"}, "curriculumBankId": "nRMNjvxqNmYLp3pfw", "autoCheckoutTime": "11:00", "crmStatuses": [{"id": 1, "name": "New Family", "code": "New Fam", "child_only": false, "is_archive": false, "order": 0, "is_optional": false, "is_sortable": false, "can_edit_logic": false, "from_statuses": [{"id": 1, "values": {"name": "New Family"}, "links": [{"href": "/api/v3/statuses/1", "rel": "status", "type": "GET"}]}, {"id": 2, "values": {"name": "Engaged"}, "links": [{"href": "/api/v3/statuses/2", "rel": "status", "type": "GET"}]}, {"id": 11, "values": {"name": "Visit Scheduled"}, "links": [{"href": "/api/v3/statuses/11", "rel": "status", "type": "GET"}]}, {"id": 3, "values": {"name": "Visit Completed"}, "links": [{"href": "/api/v3/statuses/3", "rel": "status", "type": "GET"}]}, {"id": 9, "values": {"name": "Lost Opportunity"}, "links": [{"href": "/api/v3/statuses/9", "rel": "status", "type": "GET"}]}, {"id": 4, "values": {"name": "Wait List"}, "links": [{"href": "/api/v3/statuses/4", "rel": "status", "type": "GET"}]}, {"id": 5, "values": {"name": "Registered"}, "links": [{"href": "/api/v3/statuses/5", "rel": "status", "type": "GET"}]}, {"id": 8, "values": {"name": "Withdrawn"}, "links": [{"href": "/api/v3/statuses/8", "rel": "status", "type": "GET"}]}], "to_statuses": [{"id": 1, "values": {"name": "New Family"}, "links": [{"href": "/api/v3/statuses/1", "rel": "status", "type": "GET"}]}, {"id": 2, "values": {"name": "Engaged"}, "links": [{"href": "/api/v3/statuses/2", "rel": "status", "type": "GET"}]}, {"id": 11, "values": {"name": "Visit Scheduled"}, "links": [{"href": "/api/v3/statuses/11", "rel": "status", "type": "GET"}]}, {"id": 3, "values": {"name": "Visit Completed"}, "links": [{"href": "/api/v3/statuses/3", "rel": "status", "type": "GET"}]}, {"id": 9, "values": {"name": "Lost Opportunity"}, "links": [{"href": "/api/v3/statuses/9", "rel": "status", "type": "GET"}]}, {"id": 10, "values": {"name": "Rejected"}, "links": [{"href": "/api/v3/statuses/10", "rel": "status", "type": "GET"}]}, {"id": 4, "values": {"name": "Wait List"}, "links": [{"href": "/api/v3/statuses/4", "rel": "status", "type": "GET"}]}, {"id": 5, "values": {"name": "Registered"}, "links": [{"href": "/api/v3/statuses/5", "rel": "status", "type": "GET"}]}, {"id": 13, "values": {"name": "Registration Fee Paid"}, "links": [{"href": "/api/v3/statuses/13", "rel": "status", "type": "GET"}]}, {"id": 12, "values": {"name": "Place Offered"}, "links": [{"href": "/api/v3/statuses/12", "rel": "status", "type": "GET"}]}]}, {"id": 2, "name": "Engaged", "code": "Engaged", "child_only": false, "is_archive": false, "order": 1, "is_optional": false, "is_sortable": false, "can_edit_logic": true, "from_statuses": [{"id": 1, "values": {"name": "New Family"}, "links": [{"href": "/api/v3/statuses/1", "rel": "status", "type": "GET"}]}, {"id": 2, "values": {"name": "Engaged"}, "links": [{"href": "/api/v3/statuses/2", "rel": "status", "type": "GET"}]}, {"id": 11, "values": {"name": "Visit Scheduled"}, "links": [{"href": "/api/v3/statuses/11", "rel": "status", "type": "GET"}]}, {"id": 3, "values": {"name": "Visit Completed"}, "links": [{"href": "/api/v3/statuses/3", "rel": "status", "type": "GET"}]}, {"id": 9, "values": {"name": "Lost Opportunity"}, "links": [{"href": "/api/v3/statuses/9", "rel": "status", "type": "GET"}]}, {"id": 4, "values": {"name": "Wait List"}, "links": [{"href": "/api/v3/statuses/4", "rel": "status", "type": "GET"}]}, {"id": 5, "values": {"name": "Registered"}, "links": [{"href": "/api/v3/statuses/5", "rel": "status", "type": "GET"}]}], "to_statuses": [{"id": 1, "values": {"name": "New Family"}, "links": [{"href": "/api/v3/statuses/1", "rel": "status", "type": "GET"}]}, {"id": 2, "values": {"name": "Engaged"}, "links": [{"href": "/api/v3/statuses/2", "rel": "status", "type": "GET"}]}, {"id": 11, "values": {"name": "Visit Scheduled"}, "links": [{"href": "/api/v3/statuses/11", "rel": "status", "type": "GET"}]}, {"id": 3, "values": {"name": "Visit Completed"}, "links": [{"href": "/api/v3/statuses/3", "rel": "status", "type": "GET"}]}, {"id": 9, "values": {"name": "Lost Opportunity"}, "links": [{"href": "/api/v3/statuses/9", "rel": "status", "type": "GET"}]}, {"id": 10, "values": {"name": "Rejected"}, "links": [{"href": "/api/v3/statuses/10", "rel": "status", "type": "GET"}]}, {"id": 4, "values": {"name": "Wait List"}, "links": [{"href": "/api/v3/statuses/4", "rel": "status", "type": "GET"}]}, {"id": 5, "values": {"name": "Registered"}, "links": [{"href": "/api/v3/statuses/5", "rel": "status", "type": "GET"}]}, {"id": 13, "values": {"name": "Registration Fee Paid"}, "links": [{"href": "/api/v3/statuses/13", "rel": "status", "type": "GET"}]}, {"id": 12, "values": {"name": "Place Offered"}, "links": [{"href": "/api/v3/statuses/12", "rel": "status", "type": "GET"}]}]}, {"id": 11, "name": "Visit Scheduled", "code": "Tour Sched", "child_only": false, "is_archive": false, "order": 2, "is_optional": false, "is_sortable": false, "can_edit_logic": false, "from_statuses": [{"id": 1, "values": {"name": "New Family"}, "links": [{"href": "/api/v3/statuses/1", "rel": "status", "type": "GET"}]}, {"id": 2, "values": {"name": "Engaged"}, "links": [{"href": "/api/v3/statuses/2", "rel": "status", "type": "GET"}]}, {"id": 11, "values": {"name": "Visit Scheduled"}, "links": [{"href": "/api/v3/statuses/11", "rel": "status", "type": "GET"}]}, {"id": 3, "values": {"name": "Visit Completed"}, "links": [{"href": "/api/v3/statuses/3", "rel": "status", "type": "GET"}]}, {"id": 4, "values": {"name": "Wait List"}, "links": [{"href": "/api/v3/statuses/4", "rel": "status", "type": "GET"}]}, {"id": 5, "values": {"name": "Registered"}, "links": [{"href": "/api/v3/statuses/5", "rel": "status", "type": "GET"}]}], "to_statuses": [{"id": 1, "values": {"name": "New Family"}, "links": [{"href": "/api/v3/statuses/1", "rel": "status", "type": "GET"}]}, {"id": 2, "values": {"name": "Engaged"}, "links": [{"href": "/api/v3/statuses/2", "rel": "status", "type": "GET"}]}, {"id": 11, "values": {"name": "Visit Scheduled"}, "links": [{"href": "/api/v3/statuses/11", "rel": "status", "type": "GET"}]}, {"id": 3, "values": {"name": "Visit Completed"}, "links": [{"href": "/api/v3/statuses/3", "rel": "status", "type": "GET"}]}, {"id": 9, "values": {"name": "Lost Opportunity"}, "links": [{"href": "/api/v3/statuses/9", "rel": "status", "type": "GET"}]}, {"id": 10, "values": {"name": "Rejected"}, "links": [{"href": "/api/v3/statuses/10", "rel": "status", "type": "GET"}]}, {"id": 4, "values": {"name": "Wait List"}, "links": [{"href": "/api/v3/statuses/4", "rel": "status", "type": "GET"}]}, {"id": 5, "values": {"name": "Registered"}, "links": [{"href": "/api/v3/statuses/5", "rel": "status", "type": "GET"}]}, {"id": 13, "values": {"name": "Registration Fee Paid"}, "links": [{"href": "/api/v3/statuses/13", "rel": "status", "type": "GET"}]}, {"id": 12, "values": {"name": "Place Offered"}, "links": [{"href": "/api/v3/statuses/12", "rel": "status", "type": "GET"}]}]}, {"id": 3, "name": "Visit Completed", "code": "Tour Compl", "child_only": false, "is_archive": false, "order": 3, "is_optional": false, "is_sortable": false, "can_edit_logic": false, "from_statuses": [{"id": 1, "values": {"name": "New Family"}, "links": [{"href": "/api/v3/statuses/1", "rel": "status", "type": "GET"}]}, {"id": 2, "values": {"name": "Engaged"}, "links": [{"href": "/api/v3/statuses/2", "rel": "status", "type": "GET"}]}, {"id": 11, "values": {"name": "Visit Scheduled"}, "links": [{"href": "/api/v3/statuses/11", "rel": "status", "type": "GET"}]}, {"id": 3, "values": {"name": "Visit Completed"}, "links": [{"href": "/api/v3/statuses/3", "rel": "status", "type": "GET"}]}, {"id": 4, "values": {"name": "Wait List"}, "links": [{"href": "/api/v3/statuses/4", "rel": "status", "type": "GET"}]}, {"id": 5, "values": {"name": "Registered"}, "links": [{"href": "/api/v3/statuses/5", "rel": "status", "type": "GET"}]}], "to_statuses": [{"id": 1, "values": {"name": "New Family"}, "links": [{"href": "/api/v3/statuses/1", "rel": "status", "type": "GET"}]}, {"id": 2, "values": {"name": "Engaged"}, "links": [{"href": "/api/v3/statuses/2", "rel": "status", "type": "GET"}]}, {"id": 11, "values": {"name": "Visit Scheduled"}, "links": [{"href": "/api/v3/statuses/11", "rel": "status", "type": "GET"}]}, {"id": 3, "values": {"name": "Visit Completed"}, "links": [{"href": "/api/v3/statuses/3", "rel": "status", "type": "GET"}]}, {"id": 9, "values": {"name": "Lost Opportunity"}, "links": [{"href": "/api/v3/statuses/9", "rel": "status", "type": "GET"}]}, {"id": 10, "values": {"name": "Rejected"}, "links": [{"href": "/api/v3/statuses/10", "rel": "status", "type": "GET"}]}, {"id": 4, "values": {"name": "Wait List"}, "links": [{"href": "/api/v3/statuses/4", "rel": "status", "type": "GET"}]}, {"id": 5, "values": {"name": "Registered"}, "links": [{"href": "/api/v3/statuses/5", "rel": "status", "type": "GET"}]}, {"id": 13, "values": {"name": "Registration Fee Paid"}, "links": [{"href": "/api/v3/statuses/13", "rel": "status", "type": "GET"}]}, {"id": 12, "values": {"name": "Place Offered"}, "links": [{"href": "/api/v3/statuses/12", "rel": "status", "type": "GET"}]}]}, {"id": 4, "name": "Wait List", "code": "Wait List", "child_only": true, "is_archive": false, "order": 4, "is_optional": true, "is_sortable": false, "can_edit_logic": false, "from_statuses": [{"id": 2, "values": {"name": "Engaged"}, "links": [{"href": "/api/v3/statuses/2", "rel": "status", "type": "GET"}]}, {"id": 11, "values": {"name": "Visit Scheduled"}, "links": [{"href": "/api/v3/statuses/11", "rel": "status", "type": "GET"}]}, {"id": 3, "values": {"name": "Visit Completed"}, "links": [{"href": "/api/v3/statuses/3", "rel": "status", "type": "GET"}]}, {"id": 4, "values": {"name": "Wait List"}, "links": [{"href": "/api/v3/statuses/4", "rel": "status", "type": "GET"}]}, {"id": 5, "values": {"name": "Registered"}, "links": [{"href": "/api/v3/statuses/5", "rel": "status", "type": "GET"}]}, {"id": 1, "values": {"name": "New Family"}, "links": [{"href": "/api/v3/statuses/1", "rel": "status", "type": "GET"}]}], "to_statuses": [{"id": 1, "values": {"name": "New Family"}, "links": [{"href": "/api/v3/statuses/1", "rel": "status", "type": "GET"}]}, {"id": 2, "values": {"name": "Engaged"}, "links": [{"href": "/api/v3/statuses/2", "rel": "status", "type": "GET"}]}, {"id": 11, "values": {"name": "Visit Scheduled"}, "links": [{"href": "/api/v3/statuses/11", "rel": "status", "type": "GET"}]}, {"id": 3, "values": {"name": "Visit Completed"}, "links": [{"href": "/api/v3/statuses/3", "rel": "status", "type": "GET"}]}, {"id": 4, "values": {"name": "Wait List"}, "links": [{"href": "/api/v3/statuses/4", "rel": "status", "type": "GET"}]}, {"id": 5, "values": {"name": "Registered"}, "links": [{"href": "/api/v3/statuses/5", "rel": "status", "type": "GET"}]}, {"id": 6, "values": {"name": "Enrolled (Started)"}, "links": [{"href": "/api/v3/statuses/6", "rel": "status", "type": "GET"}]}, {"id": 9, "values": {"name": "Lost Opportunity"}, "links": [{"href": "/api/v3/statuses/9", "rel": "status", "type": "GET"}]}, {"id": 10, "values": {"name": "Rejected"}, "links": [{"href": "/api/v3/statuses/10", "rel": "status", "type": "GET"}]}, {"id": 13, "values": {"name": "Registration Fee Paid"}, "links": [{"href": "/api/v3/statuses/13", "rel": "status", "type": "GET"}]}, {"id": 12, "values": {"name": "Place Offered"}, "links": [{"href": "/api/v3/statuses/12", "rel": "status", "type": "GET"}]}]}, {"id": 12, "name": "Place Offered", "code": "Offered", "child_only": true, "is_archive": false, "order": 5, "is_optional": false, "is_sortable": true, "can_edit_logic": true, "from_statuses": [{"id": 1, "values": {"name": "New Family"}, "links": [{"href": "/api/v3/statuses/1", "rel": "status", "type": "GET"}]}, {"id": 2, "values": {"name": "Engaged"}, "links": [{"href": "/api/v3/statuses/2", "rel": "status", "type": "GET"}]}, {"id": 11, "values": {"name": "Visit Scheduled"}, "links": [{"href": "/api/v3/statuses/11", "rel": "status", "type": "GET"}]}, {"id": 3, "values": {"name": "Visit Completed"}, "links": [{"href": "/api/v3/statuses/3", "rel": "status", "type": "GET"}]}, {"id": 4, "values": {"name": "Wait List"}, "links": [{"href": "/api/v3/statuses/4", "rel": "status", "type": "GET"}]}, {"id": 12, "values": {"name": "Place Offered"}, "links": [{"href": "/api/v3/statuses/12", "rel": "status", "type": "GET"}]}], "to_statuses": [{"id": 12, "values": {"name": "Place Offered"}, "links": [{"href": "/api/v3/statuses/12", "rel": "status", "type": "GET"}]}, {"id": 13, "values": {"name": "Registration Fee Paid"}, "links": [{"href": "/api/v3/statuses/13", "rel": "status", "type": "GET"}]}]}, {"id": 5, "name": "Registered", "code": "Registered", "child_only": true, "is_archive": false, "order": 6, "is_optional": false, "is_sortable": false, "can_edit_logic": false, "from_statuses": [{"id": 2, "values": {"name": "Engaged"}, "links": [{"href": "/api/v3/statuses/2", "rel": "status", "type": "GET"}]}, {"id": 11, "values": {"name": "Visit Scheduled"}, "links": [{"href": "/api/v3/statuses/11", "rel": "status", "type": "GET"}]}, {"id": 3, "values": {"name": "Visit Completed"}, "links": [{"href": "/api/v3/statuses/3", "rel": "status", "type": "GET"}]}, {"id": 4, "values": {"name": "Wait List"}, "links": [{"href": "/api/v3/statuses/4", "rel": "status", "type": "GET"}]}, {"id": 5, "values": {"name": "Registered"}, "links": [{"href": "/api/v3/statuses/5", "rel": "status", "type": "GET"}]}, {"id": 6, "values": {"name": "Enrolled (Started)"}, "links": [{"href": "/api/v3/statuses/6", "rel": "status", "type": "GET"}]}, {"id": 1, "values": {"name": "New Family"}, "links": [{"href": "/api/v3/statuses/1", "rel": "status", "type": "GET"}]}], "to_statuses": [{"id": 1, "values": {"name": "New Family"}, "links": [{"href": "/api/v3/statuses/1", "rel": "status", "type": "GET"}]}, {"id": 2, "values": {"name": "Engaged"}, "links": [{"href": "/api/v3/statuses/2", "rel": "status", "type": "GET"}]}, {"id": 11, "values": {"name": "Visit Scheduled"}, "links": [{"href": "/api/v3/statuses/11", "rel": "status", "type": "GET"}]}, {"id": 3, "values": {"name": "Visit Completed"}, "links": [{"href": "/api/v3/statuses/3", "rel": "status", "type": "GET"}]}, {"id": 4, "values": {"name": "Wait List"}, "links": [{"href": "/api/v3/statuses/4", "rel": "status", "type": "GET"}]}, {"id": 5, "values": {"name": "Registered"}, "links": [{"href": "/api/v3/statuses/5", "rel": "status", "type": "GET"}]}, {"id": 6, "values": {"name": "Enrolled (Started)"}, "links": [{"href": "/api/v3/statuses/6", "rel": "status", "type": "GET"}]}, {"id": 9, "values": {"name": "Lost Opportunity"}, "links": [{"href": "/api/v3/statuses/9", "rel": "status", "type": "GET"}]}, {"id": 13, "values": {"name": "Registration Fee Paid"}, "links": [{"href": "/api/v3/statuses/13", "rel": "status", "type": "GET"}]}]}, {"id": 13, "name": "Registration Fee Paid", "code": "<PERSON>", "child_only": true, "is_archive": false, "order": 7, "is_optional": false, "is_sortable": true, "can_edit_logic": true, "from_statuses": [{"id": 1, "values": {"name": "New Family"}, "links": [{"href": "/api/v3/statuses/1", "rel": "status", "type": "GET"}]}, {"id": 2, "values": {"name": "Engaged"}, "links": [{"href": "/api/v3/statuses/2", "rel": "status", "type": "GET"}]}, {"id": 11, "values": {"name": "Visit Scheduled"}, "links": [{"href": "/api/v3/statuses/11", "rel": "status", "type": "GET"}]}, {"id": 3, "values": {"name": "Visit Completed"}, "links": [{"href": "/api/v3/statuses/3", "rel": "status", "type": "GET"}]}, {"id": 4, "values": {"name": "Wait List"}, "links": [{"href": "/api/v3/statuses/4", "rel": "status", "type": "GET"}]}, {"id": 5, "values": {"name": "Registered"}, "links": [{"href": "/api/v3/statuses/5", "rel": "status", "type": "GET"}]}, {"id": 13, "values": {"name": "Registration Fee Paid"}, "links": [{"href": "/api/v3/statuses/13", "rel": "status", "type": "GET"}]}, {"id": 12, "values": {"name": "Place Offered"}, "links": [{"href": "/api/v3/statuses/12", "rel": "status", "type": "GET"}]}], "to_statuses": [{"id": 13, "values": {"name": "Registration Fee Paid"}, "links": [{"href": "/api/v3/statuses/13", "rel": "status", "type": "GET"}]}]}, {"id": 6, "name": "Enrolled (Started)", "code": "Enrolled", "child_only": true, "is_archive": true, "order": 8, "is_optional": false, "is_sortable": false, "can_edit_logic": false, "from_statuses": [{"id": 4, "values": {"name": "Wait List"}, "links": [{"href": "/api/v3/statuses/4", "rel": "status", "type": "GET"}]}, {"id": 5, "values": {"name": "Registered"}, "links": [{"href": "/api/v3/statuses/5", "rel": "status", "type": "GET"}]}, {"id": 6, "values": {"name": "Enrolled (Started)"}, "links": [{"href": "/api/v3/statuses/6", "rel": "status", "type": "GET"}]}, {"id": 7, "values": {"name": "Temporary Leave"}, "links": [{"href": "/api/v3/statuses/7", "rel": "status", "type": "GET"}]}], "to_statuses": [{"id": 5, "values": {"name": "Registered"}, "links": [{"href": "/api/v3/statuses/5", "rel": "status", "type": "GET"}]}, {"id": 6, "values": {"name": "Enrolled (Started)"}, "links": [{"href": "/api/v3/statuses/6", "rel": "status", "type": "GET"}]}, {"id": 7, "values": {"name": "Temporary Leave"}, "links": [{"href": "/api/v3/statuses/7", "rel": "status", "type": "GET"}]}, {"id": 8, "values": {"name": "Withdrawn"}, "links": [{"href": "/api/v3/statuses/8", "rel": "status", "type": "GET"}]}]}, {"id": 7, "name": "Temporary Leave", "code": "TempLeave ", "child_only": true, "is_archive": true, "order": 9, "is_optional": false, "is_sortable": false, "can_edit_logic": false, "from_statuses": [{"id": 6, "values": {"name": "Enrolled (Started)"}, "links": [{"href": "/api/v3/statuses/6", "rel": "status", "type": "GET"}]}, {"id": 7, "values": {"name": "Temporary Leave"}, "links": [{"href": "/api/v3/statuses/7", "rel": "status", "type": "GET"}]}], "to_statuses": [{"id": 6, "values": {"name": "Enrolled (Started)"}, "links": [{"href": "/api/v3/statuses/6", "rel": "status", "type": "GET"}]}, {"id": 7, "values": {"name": "Temporary Leave"}, "links": [{"href": "/api/v3/statuses/7", "rel": "status", "type": "GET"}]}, {"id": 8, "values": {"name": "Withdrawn"}, "links": [{"href": "/api/v3/statuses/8", "rel": "status", "type": "GET"}]}]}, {"id": 8, "name": "Withdrawn", "code": "Withdrawn", "child_only": true, "is_archive": true, "order": 10, "is_optional": false, "is_sortable": false, "can_edit_logic": false, "from_statuses": [{"id": 6, "values": {"name": "Enrolled (Started)"}, "links": [{"href": "/api/v3/statuses/6", "rel": "status", "type": "GET"}]}, {"id": 7, "values": {"name": "Temporary Leave"}, "links": [{"href": "/api/v3/statuses/7", "rel": "status", "type": "GET"}]}, {"id": 8, "values": {"name": "Withdrawn"}, "links": [{"href": "/api/v3/statuses/8", "rel": "status", "type": "GET"}]}], "to_statuses": [{"id": 1, "values": {"name": "New Family"}, "links": [{"href": "/api/v3/statuses/1", "rel": "status", "type": "GET"}]}, {"id": 8, "values": {"name": "Withdrawn"}, "links": [{"href": "/api/v3/statuses/8", "rel": "status", "type": "GET"}]}]}, {"id": 9, "name": "Lost Opportunity", "code": "LostOpp", "child_only": false, "is_archive": true, "order": 11, "is_optional": false, "is_sortable": true, "can_edit_logic": false, "from_statuses": [{"id": 1, "values": {"name": "New Family"}, "links": [{"href": "/api/v3/statuses/1", "rel": "status", "type": "GET"}]}, {"id": 2, "values": {"name": "Engaged"}, "links": [{"href": "/api/v3/statuses/2", "rel": "status", "type": "GET"}]}, {"id": 11, "values": {"name": "Visit Scheduled"}, "links": [{"href": "/api/v3/statuses/11", "rel": "status", "type": "GET"}]}, {"id": 3, "values": {"name": "Visit Completed"}, "links": [{"href": "/api/v3/statuses/3", "rel": "status", "type": "GET"}]}, {"id": 9, "values": {"name": "Lost Opportunity"}, "links": [{"href": "/api/v3/statuses/9", "rel": "status", "type": "GET"}]}, {"id": 4, "values": {"name": "Wait List"}, "links": [{"href": "/api/v3/statuses/4", "rel": "status", "type": "GET"}]}, {"id": 5, "values": {"name": "Registered"}, "links": [{"href": "/api/v3/statuses/5", "rel": "status", "type": "GET"}]}], "to_statuses": [{"id": 1, "values": {"name": "New Family"}, "links": [{"href": "/api/v3/statuses/1", "rel": "status", "type": "GET"}]}, {"id": 2, "values": {"name": "Engaged"}, "links": [{"href": "/api/v3/statuses/2", "rel": "status", "type": "GET"}]}, {"id": 9, "values": {"name": "Lost Opportunity"}, "links": [{"href": "/api/v3/statuses/9", "rel": "status", "type": "GET"}]}]}, {"id": 10, "name": "Rejected", "code": "Rejected", "child_only": false, "is_archive": true, "order": 12, "is_optional": false, "is_sortable": false, "can_edit_logic": false, "from_statuses": [{"id": 1, "values": {"name": "New Family"}, "links": [{"href": "/api/v3/statuses/1", "rel": "status", "type": "GET"}]}, {"id": 2, "values": {"name": "Engaged"}, "links": [{"href": "/api/v3/statuses/2", "rel": "status", "type": "GET"}]}, {"id": 3, "values": {"name": "Visit Completed"}, "links": [{"href": "/api/v3/statuses/3", "rel": "status", "type": "GET"}]}, {"id": 11, "values": {"name": "Visit Scheduled"}, "links": [{"href": "/api/v3/statuses/11", "rel": "status", "type": "GET"}]}, {"id": 10, "values": {"name": "Rejected"}, "links": [{"href": "/api/v3/statuses/10", "rel": "status", "type": "GET"}]}, {"id": 4, "values": {"name": "Wait List"}, "links": [{"href": "/api/v3/statuses/4", "rel": "status", "type": "GET"}]}], "to_statuses": [{"id": 10, "values": {"name": "Rejected"}, "links": [{"href": "/api/v3/statuses/10", "rel": "status", "type": "GET"}]}]}], "school": [{"_id": "Yctwg5CSt4mMzgCqY", "name": "Dallas"}, {"_id": "QHqnqzpjfCp7KyJ2c", "name": "Paragon Public School"}, {"_id": "Riyhav3ajjcE6i6Xk", "name": "Texas School"}, {"_id": "2mpMSqGoauFJuoint", "name": "India School"}], "checkInCheckOutQrCodesExpireAfterHours": 1, "checkInCheckOutQrCodesExpireAfterMinutes": 1, "cancellationReasons": [{"_id": "urai8EiXcabcBCwCg", "reason": "Price is high", "archived": false, "order": 5}, {"_id": "6zQY79mDFhzm2HWNs", "reason": "Found another school", "archived": false, "order": 4}, {"_id": "hNFSksKZ9eJwM6Ee9", "reason": "When I move an empty text box up then the save button is  disabled", "archived": true, "order": 6}, {"_id": "F3hGGGkd3gQeAem9k", "reason": "Fee is high so want to cancel  the registration and want to search for another school that is cheap and gives sibling discount", "archived": false, "order": 2}, {"_id": "awJ2sccYpcwKvjem8", "reason": "Do not like the school and do not want to enroll this kid in this class anymoreeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeee", "archived": false, "order": 1}, {"_id": "5LG7khmXZfzf25b7L", "reason": "Invalid/test data", "archived": true, "order": 3}, {"_id": "APtJRDHRZhXhuKAH8", "reason": "Test this", "archived": false, "order": 7}], "pickDropReasons": [{"_id": "urai8EiXcabcBCwCg", "reason": "Price is high", "archived": false, "order": 5}, {"_id": "6zQY79mDFhzm2HWNs", "reason": "Found another school", "archived": false, "order": 4}, {"_id": "hNFSksKZ9eJwM6Ee9", "reason": "When I move an empty text box up then the save button is  disabled", "archived": true, "order": 6}, {"_id": "F3hGGGkd3gQeAem9k", "reason": "Fee is high so want to cancel  the registration and want to search for another school that is cheap and gives sibling discount", "archived": false, "order": 2}, {"_id": "awJ2sccYpcwKvjem8", "reason": "Do not like the school and do not want to enroll this kid in this class anymoreeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeee", "archived": false, "order": 1}, {"_id": "5LG7khmXZfzf25b7L", "reason": "Invalid/test data", "archived": true, "order": 3}, {"_id": "APtJRDHRZhXhuKAH8", "reason": "Test this", "archived": false, "order": 7}], "earlyPickDropMinute": "5"}, {"_id": "iSNuHxc6zq3mi8CsZ", "name": "Org Test 1", "reportsTo": "TEST12345", "shortCode": "111", "language": "", "timezone": "UTC", "legalFacilityName": "Legal Name test", "facilityLicenseNumber": "111", "phoneNumber": "111-111-111112345", "schoolHighGrade": "11", "schoolLowGrade": "1", "streetAddress": "111 Drive Yellow", "cityName": "City 11", "stateName": "tx11", "zipCode": "1111", "websiteUrl": "https://www.orgtest111academy.com", "parentOrgId": "iSNuHxc6zq3mi8CzZ", "createdAt": 1707366074544, "customizations": {}, "enableSwitchOrg": true}]