import people from 'cypress/pages/people';
import search from 'cypress/pages/search';
import billing from 'cypress/pages/billing';
import header from 'cypress/pages/header';
import {
  BASE_URLS,
  BILLING_HISTORY,
  CHILD_DATA,
  GROUPS,
  MY_SITE_WEEK_SCHEDULE_TYPE,
  NEW_PLAN_CHILD,
  PLAN_FORM
} from 'cypress/support/constants';
import { getFormattedDatesForActivities } from 'cypress/e2e/utils';
import { CHILD_ENROLLED_ITEMS } from 'cypress/fixtures/enrollment-items';
import { COLLECTIONS } from 'enums/collections';
import People from '../../../../pages/people';
import admin from 'cypress/pages/admin';

context('Validate people data', () => {
  it('should add item billing plan', () => {
    cy.setDefaultDatabase();
    cy.removeCustomization('scheduleTypes/forceLinkToPlans/enabled');
    addItemBillingPlan();
  });

  it('should verify child program', () => {
    checkEnrollmentPlan();
  });

  it('should check if child item displays correctly', () => {
    cy.task('executeMongoCommand', {
      collection: COLLECTIONS.RESERVATIONS,
      operation: 'insertMany',
      query: CHILD_ENROLLED_ITEMS
    }).then((result) => {
      checkEnrollmentItem();
      addCheckEnrollDateRangeItem();
    });
  });

  it('should send invoices for pending charges', () => {
    cy.setDefaultDatabase();
    cy.removeCustomization('registrationFlow');
    addPendingCharges();
  });
});

const dates = getFormattedDatesForActivities();
const today = dates.today;

const addItemBillingPlan = () => {
  cy.login(BASE_URLS.PEOPLE_DIRECTORY);
  search.searchPeopleIcon.click();
  search.searchPeopleInp.clear().type(CHILD_DATA.FIRST_NAME, { force: true });
  search.searchManageButton.first().click({ force: true });
  people.schedulingTab.click();
  cy.wait(500);
  people.newScheduleItem.click();
  people.scheduleDate.clear();
  people.scheduleDate.type(today, { force: true });
  people.scheduleTargetGroup.select(GROUPS.KINDERGARDEN.NAME, {
    force: true
  });
  people.scheduleType.select(MY_SITE_WEEK_SCHEDULE_TYPE[0]);
  const addPlanButton = People.linkedBillingPlanBtn;
  addPlanButton.should('be.visible').click({ force: true });
  billing.selectPlanName.select(NEW_PLAN_CHILD.PLAN_NAME, { force: true });
  people.saveScheduleEntry.click();
  cy.containsOkAndClick();
  header.quickUserToggle.click();
  header.signOutBtn.click();
};

const checkEnrollmentPlan = () => {
  cy.login(BASE_URLS.DEFAULT, 'Parent');
  search.searchPeopleIcon.click();
  search.searchPeopleInp.clear().type(CHILD_DATA.FIRST_NAME, { force: true });
  search.searchManageButton.first().click({ force: true });
  people.programsHeaderNavigation.click();
  people.enrolledProgram.click();
  cy.wait(500);
  cy.get('.show').should('not.exist');
  people.enrolledProgram.click();
  cy.wait(500);
  cy.get('.show').should('exist');
  people.enrolledProgram.each(($card) => {
    cy.wrap($card).within(() => {
      const dataId = $card.attr('data-id');
      cy.get('.col-2')
        .eq(3)
        .should('contain', 'Days')
        .should('contain', 'M')
        .should('contain', 'T')
        .should('contain', 'W')
        .should('contain', 'R')
        .should('contain', 'F');
      cy.get('.show').eq(0).should('contain', 'Program Details:');
      people.editEnrolledProgramBtn(dataId).should('be.visible').click();
    });
  });

  people.editEnrolledProgramForm.within(() => {
    cy.get('h4').should('contain', PLAN_FORM.NAME);

    cy.get('h6').should('contain', PLAN_FORM.PRICING);
    cy.get('.mt-1').contains(PLAN_FORM.FREQUENCY).should('exist');

    cy.get('.select-multi-group').within(() => {
      cy.get('input[type="checkbox"]').each(($checkbox) => {
        if ($checkbox.is(':checked')) {
          cy.wrap($checkbox).uncheck();
        }
      });
    });
    cy.get('.invalid-feedback').should('be.visible').and('contain', PLAN_FORM.MSG);
    cy.get('.select-multi-group').within(() => {
      cy.get('input[type="checkbox"]').eq(0).check({ force: true });
      cy.get('input[type="checkbox"]').eq(1).check({ force: true });
    });
  });
  people.saveEditEnrollmentProgramBtn.click({ force: true });
  cy.containsOkAndClick();
};

const checkEnrollmentItem = () => {
  // Go to child profile and check programs
  cy.login(BASE_URLS.DEFAULT, 'Parent');
  search.searchPeopleIcon.click();
  search.searchPeopleInp.clear().type(CHILD_DATA.FIRST_NAME, { force: true });
  search.searchManageButton.first().click({ force: true });
  people.programsHeaderNavigation.click();

  // Check if child's enrolled item shows up
  people.enrolledItem.eq(0).should('be.visible');

  // Check if child's enrolled item collapses and expands
  people.enrolledItemExpandedDates.eq(0).should('be.visible');
  cy.wait(500);

  // Check if child's enrolled item correctly strikes past dates, all dates except one should be striked
  people.enrolledItemDateStriked.eq(0).should('be.visible');
  cy.wait(500);

  // Check if collapse correctly hides proper information
  people.enrolledItemExpand.eq(0).click();
  cy.wait(500);
  people.enrolledItemExpandedDates.eq(0).should('not.be.visible');
  people.enrolledItemDateStriked.eq(0).should('not.be.visible');
  cy.wait(500);

  people.enrolledItemExpand.eq(0).click();
  cy.wait(500);
  people.enrolledItemExpandedDates.eq(0).should('be.visible');
};

const addCheckEnrollDateRangeItem = () => {
  cy.get('[data-id="bcv4jLoqiYrmHzKZX"]').click();
  people.continueBtn.click();
  people.addProgramsPayNow.click();
  people.cardOption.then(($option) => {
    const value = $option.attr('value');
    people.paymentSelect.select(value);
  });
  people.payNowBtnMModal.click();
  cy.containsOkAndClick();
  cy.wait(500);
  people.enrolledItem.eq(1).should('be.visible');
  people.enrolledItem.eq(1).within(() => {
    people.enrolledItemExpandedDates.should('be.visible');
    people.enrolledItemExpandedDates.contains('04/19/13982').should('exist');
    people.enrolledItemExpandedDates.contains('04/20/13982').should('exist');
  });
};

const addPendingCharges = () => {
  cy.login(BASE_URLS.PEOPLE_DIRECTORY);
  search.searchPeopleIcon.click();
  search.searchPeopleInp.clear().type(CHILD_DATA.FIRST_NAME, { force: true });
  search.searchManageButton.first().click({ force: true });

  billing.billingTabParents.click();
  billing.addChargeBtn.click();
  billing.selectCharge.select(NEW_PLAN_CHILD.CHARGES);
  admin.saveEdits.click();
  billing.noSecurityDeposits.should('be.visible');
  billing.noHeldFunds.should('be.visible');

  billing.pendingChargeDescription.should('include.text', NEW_PLAN_CHILD.CHARGES);
  billing.pendingChargeItem.should('include.text', NEW_PLAN_CHILD.PENDING_CHARGE_ITEM);
  billing.pendingChargeAmount.should('include.text', NEW_PLAN_CHILD.PENDING_CHARGE_AMOUNT);
  billing.manualInvoiceCharge.click();
  cy.contains('Generate Invoice').click();
  cy.containsOkAndClick();
  cy.wait(2000);

  billing.billingHistoryAction.eq(0).should('include.text', BILLING_HISTORY[1].action);
  billing.billingHistoryPerformedBy.eq(0).should('include.text', BILLING_HISTORY[1].performedBy);
  cy.performLogout();

  cy.addCustomization('registrationFlow');
  cy.login(BASE_URLS.PEOPLE_DIRECTORY);
  search.searchPeopleIcon.click();
  search.searchPeopleInp.clear().type(CHILD_DATA.FIRST_NAME, { force: true });
  search.searchManageButton.first().click({ force: true });

  billing.billingTabParents.click();
  billing.pendingChargeDescription.should('include.text', NEW_PLAN_CHILD.CHARGES);
  billing.pendingChargeItem.should('include.text', NEW_PLAN_CHILD.PENDING_CHARGE_ITEM);
  billing.pendingChargeAmount.should('include.text', NEW_PLAN_CHILD.PENDING_CHARGE_AMOUNT);
};
