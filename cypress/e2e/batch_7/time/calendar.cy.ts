import calendar from 'cypress/pages/calendar';
import food from 'cypress/pages/food';
import {
  BASE_URLS,
  FOOD_DATA,
  FOOD_EVENTS,
  WEEKS_FOOD,
  FOOD_DATES,
  GROUPS,
  FOOD_EDIT
} from 'cypress/support/constants';
import { navigateToSpecificMonth } from '../../utils';
import navigation from 'cypress/pages/navigation';

context('Validate calendar', () => {
  beforeEach(() => {
    cy.setDefaultDatabase();
    cy.login(BASE_URLS.FOOD);
    addingNewFood();
    cy.visit(BASE_URLS.CALENDAR);
    navigation.loadingSpinner.should('not.exist');
  });

  it('Validate filters', () => {
    checkTodayDetail();
    checkDateFilter();
    checkFilterByGroup();
    checkDetail();
    viewLessonPlanSummary();
    checkScheduleActivities();
  });
});

const addingNewFood = () => {
  food.noFoodMessage.should('be.visible');

  //Adding Breakfast
  food.addFoodBtnMsg.click();
  food.breakfastBtn.click();
  cy.wait(500);
  food.foodItemDescription.type(FOOD_DATA[0].item);
  food.addFoodItemModal.click();
  food.descriptionFoodInput.type(FOOD_DATA[0].description);
  food.scheduledFoodFuture.click({ force: true });
  food.scheduledFoodFuture.click({ force: true });
  food.foodDateInput.clear();
  food.foodDateInput.type(FOOD_DATES[0].date);
  food.recurrenceOption.select('Repeats');
  food.repeatsEveryWeek.type('1');

  food.repeatSunday.check({ force: true });
  food.repeatMonday.check({ force: true });
  food.repeatTuesday.check({ force: true });
  food.repeatWednesday.check({ force: true });
  food.setEndDate.click({ force: true });
  food.setEndDateInput.type(FOOD_DATES[1].date);
  food.selectedGroupsChk.click({ force: true });
  cy.wait(500);
  cy.get('.btn-group .dropdown-toggle').contains('None selected').click();
  cy.get('.multiselect-container .multiselect-option').contains(FOOD_DATA[5].recipients).click();
  food.saveFoodBtn.click();

  food.addFoodBtnMsg.click();
  food.breakfastBtn.click();
  food.foodItemDescription.type(FOOD_DATA[0].item);
  food.addFoodItemModal.click();
  food.descriptionFoodInput.type(FOOD_DATA[0].description);
  food.scheduledFoodToday.click({ force: true });
  food.saveFoodBtn.click();

  //Adding AM Snack
  food.addNewFood.click();
  food.amSnackBtn.click();
  food.foodItemDescription.type(FOOD_DATA[1].item);
  food.addFoodItemModal.click();
  food.descriptionFoodInput.type(FOOD_DATA[1].description);
  food.scheduledFoodFuture.click({ force: true });
  food.scheduledFoodFuture.click({ force: true });
  food.foodDateInput.clear();
  food.foodDateInput.type(FOOD_DATES[0].date);
  food.recurrenceOption.select('Repeats');
  food.repeatsEveryWeek.type('1');

  food.repeatSunday.check({ force: true });
  food.repeatMonday.check({ force: true });
  food.repeatTuesday.check({ force: true });
  food.repeatWednesday.check({ force: true });
  food.setEndDate.click({ force: true });
  food.setEndDateInput.type(FOOD_DATES[1].date);
  food.selectedGroupsChk.click({ force: true });
  cy.wait(500);
  cy.get('.btn-group .dropdown-toggle').contains('None selected').click();
  cy.get('.multiselect-container .multiselect-option').contains(FOOD_DATA[5].recipients).click();
  food.saveFoodBtn.click();

  food.addNewFood.click();
  food.amSnackBtn.click();
  food.foodItemDescription.type(FOOD_DATA[1].item);
  food.addFoodItemModal.click();
  food.descriptionFoodInput.type(FOOD_DATA[1].description);
  food.scheduledFoodFuture.click({ force: true });
  food.foodDateInput.clear();
  food.foodDateInput.type(FOOD_DATES[2].date);
  food.saveFoodBtn.click();

  //Adding Lunch
  food.addNewFood.click();
  food.lunchBtn.click();
  food.foodItemDescription.type(FOOD_DATA[2].item);
  food.addFoodItemModal.click();
  food.descriptionFoodInput.type(FOOD_DATA[2].description);
  food.scheduledFoodFuture.click({ force: true });
  food.scheduledFoodFuture.click({ force: true });
  food.foodDateInput.clear();
  food.foodDateInput.type(FOOD_DATES[0].date);
  food.recurrenceOption.select('Repeats');
  food.repeatsEveryWeek.type('1');

  food.repeatSunday.check({ force: true });
  food.repeatMonday.check({ force: true });
  food.repeatTuesday.check({ force: true });
  food.repeatWednesday.check({ force: true });
  food.setEndDate.click({ force: true });
  food.setEndDateInput.type(FOOD_DATES[1].date);
  food.selectedGroupsChk.click({ force: true });
  food.selectGroupsInput.select(FOOD_DATA[2].recipients, { force: true });
  cy.wait(500);
  cy.get('.btn-group .dropdown-toggle').contains('None selected').click();
  cy.get('.multiselect-container .multiselect-option').contains(FOOD_DATA[2].recipients).click();
  food.saveFoodBtn.click();

  //Add PM Snack
  food.addNewFood.click();
  food.pmSnackBtn.click();
  food.foodItemDescription.type(FOOD_DATA[3].item);
  food.addFoodItemModal.click();
  food.descriptionFoodInput.type(FOOD_DATA[3].description);
  food.scheduledFoodFuture.click({ force: true });
  food.scheduledFoodFuture.click({ force: true });
  food.foodDateInput.clear();
  food.foodDateInput.type(FOOD_DATES[0].date);
  food.recurrenceOption.select('Repeats');
  food.repeatsEveryWeek.type('1');

  food.repeatSunday.check({ force: true });
  food.repeatMonday.check({ force: true });
  food.repeatTuesday.check({ force: true });
  food.setEndDate.click({ force: true });
  food.setEndDateInput.type(FOOD_DATES[1].date);
  food.selectedGroupsChk.click({ force: true });
  cy.wait(500);
  cy.get('.btn-group .dropdown-toggle').contains('None selected').click();
  cy.get('.multiselect-container .multiselect-option').contains(FOOD_DATA[5].recipients).click();
  food.saveFoodBtn.click();

  //Add PM Snack
  food.addNewFood.click();
  food.pmSnackBtn.click();
  food.foodItemDescription.type(FOOD_DATA[3].item);
  food.addFoodItemModal.click();
  food.descriptionFoodInput.type(FOOD_DATA[3].description);
  food.scheduledFoodFuture.click({ force: true });
  food.foodDateInput.clear();
  food.foodDateInput.type(FOOD_DATES[3].date);
  food.selectedGroupsChk.click({ force: true });
  cy.wait(500);
  cy.get('.btn-group .dropdown-toggle').contains('None selected').click();
  cy.get('.multiselect-container .multiselect-option').contains(FOOD_DATA[3].recipients).click();
  food.saveFoodBtn.click();

  //Add Late Snack
  food.addNewFood.click();
  food.lateSnackBtn.click();
  food.foodItemDescription.type(FOOD_DATA[4].item);
  food.addFoodItemModal.click();
  food.descriptionFoodInput.type(FOOD_DATA[4].description);
  food.scheduledFoodFuture.click({ force: true });
  food.foodDateInput.clear();
  food.foodDateInput.type(FOOD_DATES[0].date);
  food.recurrenceOption.select('Repeats');
  food.repeatsEveryWeek.type('1');

  food.repeatSunday.check({ force: true });
  food.repeatMonday.check({ force: true });
  food.repeatTuesday.check({ force: true });
  food.repeatWednesday.check({ force: true });

  food.setEndDate.click({ force: true });
  food.setEndDateInput.type(FOOD_DATES[1].date);
  food.saveFoodBtn.click();

  //Add Dinner
  food.addNewFood.click();
  food.dinnerBtn.click();
  food.foodItemDescription.type(FOOD_DATA[5].item);
  food.addFoodItemModal.click();
  food.descriptionFoodInput.type(FOOD_DATA[5].description);

  food.scheduledFoodFuture.click({ force: true });
  food.foodDateInput.clear();
  food.foodDateInput.type(FOOD_DATES[0].date);
  food.recurrenceOption.select('Repeats');
  food.repeatsEveryWeek.type('1');

  food.repeatSunday.check({ force: true });
  food.repeatMonday.check({ force: true });
  food.repeatTuesday.check({ force: true });
  food.repeatWednesday.check({ force: true });
  food.setEndDate.click({ force: true });
  food.setEndDateInput.type(FOOD_DATES[1].date);
  food.selectedGroupsChk.click({ force: true });
  cy.wait(500);
  cy.get('.btn-group .dropdown-toggle').contains('None selected').click();
  cy.get('.multiselect-container .multiselect-option').contains(FOOD_DATA[5].recipients).click();
  food.saveFoodBtn.click();
};

const checkTodayDetail = () => {
  calendar.calendarView.select('day');
  cy.wait(1000);
  calendar.calendarDayItem.should('have.length', 1);
  calendar.calendarItemTitle.should('contain.text', FOOD_EVENTS[0]);
};

const checkDateFilter = () => {
  calendar.calendarFilterGroup.select(GROUPS.TODDLER.NAME);
  cy.wait(1000);
  calendar.calendarFilterGroup.select('All Groups');
  cy.wait(1000);
  calendar.calendarBtn.click();
  cy.wait(1000);
  navigateToSpecificMonth('June 2024', '18');
  cy.get('body').click(0, 0);
  cy.wait(500);
  FOOD_EVENTS.forEach((event) => {
    calendar.calendarDayItem.contains(event).should('exist');
  });
  calendar.calendarView.select('3day');
  cy.wait(1000);
  FOOD_EVENTS.forEach((event) => {
    calendar.calendarWeekItem.contains(event).should('exist');
  });

  calendar.calendarView.select('week');
  cy.wait(1000);
  calendar.calendarWeekItem.should('have.length', 13);

  cy.get('[data-cy="calendar-week-table"]').within(() => {
    cy.get('.calendar-grid-row').eq(1).find('.calendar-grid-col').each(($col, index) => {
      const day = $col.text();
      const dayData = WEEKS_FOOD.find((d) => d.day === day);
      if (dayData) {
        dayData.events.forEach((event) => {
          cy.wrap($col).find(`[data-cy="calendar-week-group-${event}"]`).should('exist');
        });
      }
    });
  });

  calendar.calendarView.select('month');
  cy.wait(1000);
  calendar.calendarItem.should('have.length', 14);
};

const checkFilterByGroup = () => {
  calendar.calendarView.select('week');
  cy.wait(1000);
  calendar.calendarBtn.click();
  cy.wait(1000);
  navigateToSpecificMonth('June 2024', '18');
  calendar.calendarFilterGroup.select(GROUPS.KINDERGARDEN.NAME);
  cy.wait(1000);
  calendar.calendarWeekItem.should('have.length', 11);
  calendar.calendarFilterGroup.select(GROUPS.TODDLER.NAME);
  cy.wait(1000);
  calendar.calendarWeekItem.should('have.length', 5);
};

const checkDetail = () => {
  calendar.calendarFilterGroup.select('All Groups');
  cy.wait(1000);
  calendar.calendarView.select('day');
  cy.wait(1000);
  cy.get('[data-cy="calendar-detail-table"] .list-table').eq(1).within(() => {
    calendar.calendarDayItem.first().click();
  });
  calendar.calendarDetailTitle.should('contain.text', FOOD_EVENTS[0]);
  calendar.detailGoToBtn.click();
  food.mealTypeLabel.should('include.text', FOOD_DATA[0].type);
  food.foodEntryLabel.should('include.text', FOOD_DATA[0].item);
  food.scheduledDateLabel.should('include.text', FOOD_DATES[4].date);
  food.descriptionLabel.should('include.text', FOOD_DATA[0].description);
  food.recurringDescriptionLabel.should('include.text', FOOD_EDIT.RECURRING_DESCRIPTION_LABEL);
  food.selectedGroupNamesLabel.should('include.text', FOOD_DATA[3].recipients);
};

const viewLessonPlanSummary = () => {
  cy.visit(BASE_URLS.CALENDAR); // Go to calendar
  navigation.loadingSpinner.should('not.exist');
  calendar.calendarView.select('week');
  navigation.loadingSpinner.should('not.exist');
  cy.get('[data-cy="print-pdf-btn"]').click();
  cy.get('[data-cy="print-lesson-plan-summary-btn"]').should('be.visible').click();
};

const checkScheduleActivities = () => {
    navigation.loadingSpinner.should('not.exist');
    cy.wait(1000);
    calendar.calendarBtn.click();
    cy.wait(1000);
    navigateToSpecificMonth('June 2024', '18');
    calendar.calendarFilterGroup.select('KinderGarden');
    cy.wait(1000);
    calendar.calendarView.select('day', { force: true });
    cy.get('[data-cy="calendar-day-item"] [data-cy="title"]')
      .should('be.visible')
      .and('contain.text', 'Testing curriculum');
}
